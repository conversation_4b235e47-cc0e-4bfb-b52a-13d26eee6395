package com.sqx.modules.taking.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.taking.dao.GameDao;
import com.sqx.modules.taking.entity.Game;
import com.sqx.modules.taking.service.GameService;
import com.sqx.modules.taking.service.OrderTakingService;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class GameServiceImpl extends ServiceImpl<GameDao, Game> implements GameService {
    private OrderTakingService orderTakingService;

    @Override
    public Result queryGameName() {
        QueryWrapper<Game> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0);
        List<Game> games = baseMapper.selectList(queryWrapper);
        return Result.success().put("data", games);
    }



    @Override
    public Result addGameName(String gameName,String gameImg) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (gameName == null) {
            return Result.error("游戏分类信息为null");
        } else {
            Game game = new Game();
            game.setCreateTime(simpleDateFormat.format(new Date()));
            game.setUpdateTime(simpleDateFormat.format(new Date()));
            game.setGameName(gameName);
            game.setGameImg(gameImg);
            game.setStatus((long) 0);
            int i = baseMapper.insert(game);
            if (i > 0) {
                return Result.success();
            } else {
                return Result.error();
            }
        }

    }

    @Override
    public Result updateGameName(Long id, String gameName, String gameImg,Long status) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Game game = baseMapper.selectById(id);
        if (game == null) {
            return Result.error("游戏分类信息为null");
        } else {
            game.setUpdateTime(simpleDateFormat.format(new Date()));
            game.setGameName(gameName);
            game.setStatus(status);
            game.setGameImg(gameImg);
            int i = baseMapper.updateById(game);
            if (i > 0) {
                return Result.success();
            } else {
                return Result.error();
            }
        }
    }

    @Override
    public Result deleteGameName(Long id) {

        Game game = baseMapper.selectById(id);
        if (game != null) {
            baseMapper.deleteById(id);
            return Result.success();
        } else {
            return Result.error("游戏信息不存在！");

        }
    }

    @Override
    public Result queryAllGameName(Long page, Long limit) {
        if (page == null || limit == null) {
            return Result.error("分页条件为空！");
        } else {
            Page<Game> page1 = new Page<>(page, limit);
            return Result.success().put("data", baseMapper.selectPage(page1, null));
        }
    }

    @Override
    public Result enableGameName(Long status, Long id) {
        Game game = baseMapper.selectById(id);
        if (game == null) {
            return Result.error("游戏分类信息不存在");
        } else {
            game.setStatus(status);
            game.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            baseMapper.updateById(game);
            return Result.success();
        }

    }


}
