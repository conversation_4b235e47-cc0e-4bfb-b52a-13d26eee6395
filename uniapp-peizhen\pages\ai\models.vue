<template>
	<view class="models-container">
		<view class="header">
			<text class="title">选择AI模型</text>
			<text class="subtitle">不同模型具有不同的特点和能力</text>
		</view>

		<view class="models-list">
			<view 
				v-for="model in modelList" 
				:key="model.id" 
				class="model-item" 
				:class="{ active: selectedModel === model.modelCode }"
				@click="selectModel(model)"
			>
				<view class="model-info">
					<view class="model-header">
						<text class="model-name">{{ model.modelName }}</text>
						<u-tag 
							:text="model.status === 1 ? '可用' : '不可用'" 
							:type="model.status === 1 ? 'success' : 'error'"
							size="mini"
						></u-tag>
					</view>
					<text class="model-description">{{ model.description || '暂无描述' }}</text>
					<view class="model-params">
						<text class="param-item">温度: {{ model.temperature || 0.7 }}</text>
						<text class="param-item">最大Token: {{ model.maxTokens || 4000 }}</text>
					</view>
				</view>
				<view class="model-select">
					<u-icon 
						:name="selectedModel === model.modelCode ? 'checkmark-circle-fill' : 'circle'" 
						:color="selectedModel === model.modelCode ? '#0175FE' : '#ccc'"
						size="24"
					></u-icon>
				</view>
			</view>
		</view>

		<view class="bottom-actions">
			<u-button type="primary" @click="confirmSelection" :disabled="!selectedModel">
				确认选择
			</u-button>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-overlay">
			<view class="loading-content">
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				modelList: [],
				selectedModel: '',
				loading: false
			}
		},
		onLoad() {
			this.loadModels()
			// 获取当前选中的模型
			this.selectedModel = uni.getStorageSync('selectedAiModel') || ''
		},
		methods: {
			// 加载模型列表
			loadModels() {
				this.loading = true
				this.$Request.get('/app/ai/chat/models').then(res => {
					console.log('模型列表:', res)
					if (res.code === 0) {
						this.modelList = res.data || []
						// 如果没有选中的模型，默认选择第一个可用的
						if (!this.selectedModel && this.modelList.length > 0) {
							const firstAvailable = this.modelList.find(m => m.status === 1)
							if (firstAvailable) {
								this.selectedModel = firstAvailable.modelCode
							}
						}
					} else {
						this.$queue.showToast(res.msg || '加载模型失败')
					}
				}).catch(error => {
					console.error('加载模型失败:', error)
					this.$queue.showToast('加载模型失败，请重试')
				}).finally(() => {
					this.loading = false
				})
			},

			// 选择模型
			selectModel(model) {
				if (model.status !== 1) {
					this.$queue.showToast('该模型暂不可用')
					return
				}
				this.selectedModel = model.modelCode
			},

			// 确认选择
			confirmSelection() {
				if (!this.selectedModel) {
					this.$queue.showToast('请选择一个模型')
					return
				}

				// 保存选择的模型
				uni.setStorageSync('selectedAiModel', this.selectedModel)
				
				// 显示选择成功提示
				const selectedModelInfo = this.modelList.find(m => m.modelCode === this.selectedModel)
				this.$queue.showToast(`已选择 ${selectedModelInfo?.modelName || this.selectedModel}`)
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1000)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.models-container {
		background: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	.header {
		background: #fff;
		padding: 40rpx 30rpx;
		text-align: center;
		border-bottom: 1rpx solid #eee;
	}

	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 16rpx;
	}

	.subtitle {
		font-size: 28rpx;
		color: #666;
	}

	.models-list {
		padding: 20rpx;
	}

	.model-item {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;

		&.active {
			border: 2rpx solid #0175FE;
			box-shadow: 0 4rpx 16rpx rgba(1, 117, 254, 0.2);
		}
	}

	.model-info {
		flex: 1;
	}

	.model-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
	}

	.model-name {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.model-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
		margin-bottom: 20rpx;
		display: block;
	}

	.model-params {
		display: flex;
		gap: 30rpx;
	}

	.param-item {
		font-size: 24rpx;
		color: #999;
		background: #f8f9fa;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}

	.model-select {
		margin-left: 20rpx;
	}

	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		padding: 30rpx;
		border-top: 1rpx solid #eee;
		z-index: 100;
	}

	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.loading-content {
		background: #fff;
		padding: 40rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		font-size: 28rpx;
		color: #666;
	}
</style>
