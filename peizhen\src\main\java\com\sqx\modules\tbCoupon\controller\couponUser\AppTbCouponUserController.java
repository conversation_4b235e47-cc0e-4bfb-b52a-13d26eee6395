package com.sqx.modules.tbCoupon.controller.couponUser;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.sys.controller.AbstractController;
import com.sqx.modules.tbCoupon.entity.TbCouponUser;
import com.sqx.modules.tbCoupon.service.TbCouponUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;


@RestController
@Api(value = "用户端-优惠券", tags = {"用户端-优惠券"})
@RequestMapping(value = "/app/couponUser/")
public class AppTbCouponUserController extends AbstractController {

    @Autowired
    private TbCouponUserService couponUserService;


    @Login
    @ApiOperation("查看我的优惠券列表")
    @GetMapping(value = "getMyCouponList")
    public Result getMyCouponList(@RequestAttribute Long userId, Integer page, Integer limit, TbCouponUser couponUser) {
        couponUser.setUserId(userId);
        return Result.success().put("data", couponUserService.getMyCouponList(page, limit, couponUser));
    }

    @Login
    @ApiOperation("领取新人优惠券")
    @GetMapping("receiveEnvelope")
    public Result receiveEnvelope(@RequestAttribute("userId") Long userId, Long couponId, Integer num) {

        return couponUserService.receiveEnvelope(userId, couponId, num);
    }


    @Login
    @ApiOperation("领取活动优惠券")
    @GetMapping("receiveActivity")
    public Result receiveActivity(@RequestAttribute("userId") Long userId, Long couponId) {
        return couponUserService.receiveActivity(userId, couponId);
    }


    /**
     * 检测使优惠券过期
     */
    @Scheduled(cron = "0/2 * * * * ?", zone = "Asia/Shanghai")
    public void couponOverdue() {
        List<TbCouponUser> couponUserList = couponUserService.list(new QueryWrapper<TbCouponUser>().eq("status", 0).isNotNull("expiration_time"));
        for (TbCouponUser tbCouponUser : couponUserList) {
            if (tbCouponUser.getExpirationTime().before(new Date())) {
                tbCouponUser.setStatus(2);
                couponUserService.updateById(tbCouponUser);
            }
        }

    }
}