{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/zhifubao.vue?28aa", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/zhifubao.vue?70a5", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/zhifubao.vue?dd30", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/zhifubao.vue?8631", "uni-app:///my/wallet/zhifubao.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/zhifubao.vue?d007", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/zhifubao.vue?3fc4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "listCell", "wButton", "data", "XCXIsSelect", "zhiFuBao", "zhiFuBaoName", "logining", "onLoad", "uni", "title", "methods", "inputChange", "navBack", "<PERSON><PERSON><PERSON><PERSON>", "icon", "complete", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACc3vB;AAAA,eACA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;EAEA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACAJ;IACA;IAEAK;MAAA;MACA;MACA;MACA,IACAT,WAEA,KAFAA;QACAC,eACA,KADAA;MAEA;QACA;MACA;QACA;MACA;QACA;QACA;UACAD;UACAC;QACA;QACA;UACA;YACAG;YACAA;YACAA;cACAC;cACAK;cACAC;gBACAC;kBACAR;gBACA;cACA;YACA;UACA;YACA;UACA;UACAA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC5GA;AAAA;AAAA;AAAA;AAA03C,CAAgB,+tCAAG,EAAC,C;;;;;;;;;;;ACA94C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/wallet/zhifubao.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/wallet/zhifubao.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./zhifubao.vue?vue&type=template&id=f3030972&\"\nvar renderjs\nimport script from \"./zhifubao.vue?vue&type=script&lang=js&\"\nexport * from \"./zhifubao.vue?vue&type=script&lang=js&\"\nimport style0 from \"./zhifubao.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/wallet/zhifubao.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zhifubao.vue?vue&type=template&id=f3030972&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zhifubao.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zhifubao.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\" v-if=\"XCXIsSelect != '否'\">\n\t\t<list-cell title=\"收款人姓名\" type=\"text\" placeholder=\"请输入支付宝收款人姓名\" v-model=\"zhiFuBaoName\" ></list-cell>\n\n\t\t<list-cell title=\"支付宝账号\" type=\"number\" placeholder=\"请输入要绑定的支付宝手机号\" v-model=\"zhiFuBao\"></list-cell>\n\n        <wButton text=\"绑定账户\" :rotate=\"logining\" @click.native=\"toLogin()\"></wButton>\n\t    <view style=\"padding: 32upx 64upx;font-size: 24upx;color: #999999;\">提示：请正确填写收款人的支付宝账户和真实的收款人姓名，否则将无法正常收款</view>\n\t</view>\n\t</view>\n</template>\n\n<script>\n\timport listCell from '@/components/com-input';\n\timport wButton from '../components/watch-login/watch-button.vue'; //button\n\texport default {\n\t\tcomponents: {\n\t\t\tlistCell,\n\t\t\twButton\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tXCXIsSelect: '否',\n\t\t\t\tzhiFuBao: '',\n\t\t\t\tzhiFuBaoName: '',\n\t\t\t\tlogining: false\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.XCXIsSelect = this.$queue.getData(\"XCXIsSelect\");\n\t\t\tif (this.XCXIsSelect == '否') {\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '隐私政策'\n\t\t\t\t})\n\t\t\t}\n\t\t\tlet userId = this.$queue.getData(\"userId\");\n\t\t\tif (userId) {\n\t\t\t\t// this.$Request.getT(\"/app/cash/userinfo/\" + userId).then(res => {\n\t\t\t\t// \tif (res.status === 0) {\n\t\t\t\t// \t\tif (res.data.zhiFuBao) {\n\t\t\t\t// \t\t\tthis.zhiFuBao = res.data.zhiFuBao;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t\tif (res.data.zhiFuBaoName) {\n\t\t\t\t// \t\t\tthis.zhiFuBaoName = res.data.zhiFuBaoName;\n\t\t\t\t// \t\t}\n\t\t\t\t// \t}\n\t\t\t\t// });\n\t\t\t\tthis.$Request.get(\"/app/user/selectUserById\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif (res.data.zhiFuBao) {\n\t\t\t\t\t\t\tthis.zhiFuBao = res.data.zhiFuBao;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (res.data.zhiFuBaoName) {\n\t\t\t\t\t\t\tthis.zhiFuBaoName = res.data.zhiFuBaoName;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t},\n\t\tmethods: {\n\t\t\tinputChange(e) {\n\t\t\t\tconst key = e.currentTarget.dataset.key;\n\t\t\t\tthis[key] = e.detail.value;\n\t\t\t},\n\t\t\tnavBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\n\t\t\ttoLogin() {\n\t\t\t\tlet userId = this.$queue.getData(\"userId\");\n\t\t\t\tlet token = uni.getStorageSync(\"token\");\n\t\t\t\tconst {\n\t\t\t\t\tzhiFuBao,\n\t\t\t\t\tzhiFuBaoName\n\t\t\t\t} = this;\n\t\t\t\tif (!zhiFuBao) {\n\t\t\t\t\tthis.$queue.showToast(\"请设置收款人支付宝账号\");\n\t\t\t\t} else if (!zhiFuBaoName) {\n\t\t\t\t\tthis.$queue.showToast(\"请设置收款人姓名\");\n\t\t\t\t} else {\n\t\t\t\t\tthis.$queue.showLoading(\"修改中...\");\n\t\t\t\t\tlet data = {\n\t\t\t\t\t\tzhiFuBao: this.zhiFuBao,\n\t\t\t\t\t\tzhiFuBaoName: this.zhiFuBaoName\n\t\t\t\t\t}\n\t\t\t\t\tthis.$Request.postJson('/app/user/updateUser',data).then(res => {\n\t\t\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\t\t\tuni.setStorageSync('zhiFuBao',zhiFuBao)\n\t\t\t\t\t\t\t\tuni.setStorageSync('zhiFuBaoName',zhiFuBaoName)\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle:'修改成功',\n\t\t\t\t\t\t\t\t\ticon:'none',\n\t\t\t\t\t\t\t\t\tcomplete() {\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t\t},1000)\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.$queue.showToast(res.msg)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\n\t}\n</script>\n\n<style lang='scss'>\n\tpage {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.container {\n\t\tpadding-top: 32upx;\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: hidden;\n\t\tbackground: #FFFFFF;\n\t}\n\n\t.confirm-btn {\n\t\twidth: 300px;\n\t\theight: 42px;\n\t\tline-height: 42px;\n\t\tborder-radius: 30px;\n\t\tmargin-top: 70upx;\n\t\tbackground: #e10a07;\n\t\tcolor: #fff;\n\t\tfont-size: 32rpx;\n\t\n\t\t&:after {\n\t\t\tborder-radius: 60px;\n\t\t}\n\t}\n\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zhifubao.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zhifubao.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627380\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}