<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.app.dao.UserVisitorDao">
    <select id="selectMyVisitor" resultType="com.sqx.modules.app.response.UserFollowResponse">
    SELECT
    b.id,
	t.user_id,
	t.user_name,
	t.avatar,
	b.update_time
FROM
	user_visitor b
	LEFT JOIN tb_user t ON b.user_id = t.user_id
WHERE
	b.by_user_id = #{userId} and t.user_id is not null
	order by b.update_time desc
</select>
    <select id="selectMyVisitor1" resultType="com.sqx.modules.app.response.UserFollowResponse">
    SELECT
	t.user_id,
	t.user_name,
	t.avatar,
	b.update_time
FROM
	user_visitor b
	LEFT JOIN tb_user t ON b.user_id = t.user_id
WHERE
	b.by_user_id = #{userId} and t.user_id is not null
	order by b.update_time desc
	</select>

</mapper>