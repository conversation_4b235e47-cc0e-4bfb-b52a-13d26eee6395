/*
 健康状况记录表设计
 用于跟踪患者的健康状况变化记录
 
 Date: 2025-08-05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for health_status_record
-- ----------------------------
DROP TABLE IF EXISTS `health_status_record`;
CREATE TABLE `health_status_record`  (
  `record_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `patient_id` int(11) NOT NULL COMMENT '患者ID，关联patient_info表',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `record_date` date NOT NULL COMMENT '记录日期',
  `record_time` time NULL DEFAULT NULL COMMENT '记录时间',
  `record_type` int(1) NOT NULL DEFAULT 1 COMMENT '记录类型(1日常记录 2体检记录 3就诊记录 4复查记录 5紧急记录)',
  `health_status` int(1) NOT NULL DEFAULT 2 COMMENT '健康状态(1优秀 2良好 3一般 4较差 5差)',
  `symptoms` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '症状描述',
  `weight` decimal(5,2) NULL DEFAULT NULL COMMENT '体重(kg)',
  `height` decimal(5,2) NULL DEFAULT NULL COMMENT '身高(cm)',
  `bmi` decimal(4,2) NULL DEFAULT NULL COMMENT 'BMI指数',
  `blood_pressure_systolic` int(3) NULL DEFAULT NULL COMMENT '收缩压(mmHg)',
  `blood_pressure_diastolic` int(3) NULL DEFAULT NULL COMMENT '舒张压(mmHg)',
  `heart_rate` int(3) NULL DEFAULT NULL COMMENT '心率(次/分)',
  `body_temperature` decimal(3,1) NULL DEFAULT NULL COMMENT '体温(℃)',
  `blood_sugar` decimal(4,1) NULL DEFAULT NULL COMMENT '血糖(mmol/L)',
  `medication_taken` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '当日用药情况',
  `medication_adherence` int(1) NULL DEFAULT NULL COMMENT '用药依从性(1完全依从 2基本依从 3部分依从 4不依从)',
  `exercise_duration` int(3) NULL DEFAULT NULL COMMENT '运动时长(分钟)',
  `exercise_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运动类型',
  `sleep_hours` decimal(3,1) NULL DEFAULT NULL COMMENT '睡眠时长(小时)',
  `sleep_quality` int(1) NULL DEFAULT NULL COMMENT '睡眠质量(1很好 2好 3一般 4差 5很差)',
  `mood_status` int(1) NULL DEFAULT NULL COMMENT '情绪状态(1很好 2好 3一般 4差 5很差)',
  `pain_level` int(1) NULL DEFAULT 0 COMMENT '疼痛等级(0无痛 1-10级)',
  `pain_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '疼痛部位',
  `diet_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '饮食记录',
  `special_events` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '特殊事件记录',
  `doctor_advice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '医生建议',
  `next_followup_date` date NULL DEFAULT NULL COMMENT '下次随访日期',
  `risk_assessment` int(1) NULL DEFAULT 1 COMMENT '风险评估(1低风险 2中风险 3高风险)',
  `data_source` int(1) NOT NULL DEFAULT 1 COMMENT '数据来源(1手动录入 2设备同步 3医院导入 4第三方接口)',
  `device_info` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备信息(如果是设备同步)',
  `is_abnormal` int(1) NULL DEFAULT 0 COMMENT '是否异常(0正常 1异常)',
  `abnormal_indicators` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '异常指标说明',
  `created_by` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) NULL DEFAULT NULL COMMENT '更新人ID',
  `is_delete` int(1) NULL DEFAULT 0 COMMENT '是否删除(0未删除 1已删除)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`record_id`) USING BTREE,
  INDEX `idx_patient_date`(`patient_id` ASC, `record_date` ASC) USING BTREE,
  INDEX `idx_user_date`(`user_id` ASC, `record_date` ASC) USING BTREE,
  INDEX `idx_record_type_status`(`record_type` ASC, `health_status` ASC) USING BTREE,
  INDEX `idx_abnormal_risk`(`is_abnormal` ASC, `risk_assessment` ASC) USING BTREE,
  INDEX `idx_followup_date`(`next_followup_date` ASC) USING BTREE,
  CONSTRAINT `fk_health_record_patient` FOREIGN KEY (`patient_id`) REFERENCES `patient_info` (`patient_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '健康状况记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for health_record_attachments
-- ----------------------------
DROP TABLE IF EXISTS `health_record_attachments`;
CREATE TABLE `health_record_attachments`  (
  `attachment_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `record_id` int(11) NOT NULL COMMENT '健康记录ID，关联health_status_record表',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件名',
  `file_original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件存储路径',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件访问URL',
  `file_size` bigint(20) NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件类型(image/document/video等)',
  `file_extension` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件扩展名',
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'MIME类型',
  `attachment_type` int(1) NOT NULL DEFAULT 1 COMMENT '附件类型(1检查报告 2化验单 3影像资料 4用药照片 5症状照片 6其他)',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件描述',
  `upload_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `uploaded_by` int(11) NULL DEFAULT NULL COMMENT '上传人ID',
  `is_delete` int(1) NULL DEFAULT 0 COMMENT '是否删除(0未删除 1已删除)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`attachment_id`) USING BTREE,
  INDEX `idx_record_id`(`record_id` ASC) USING BTREE,
  INDEX `idx_attachment_type`(`attachment_type` ASC) USING BTREE,
  INDEX `idx_file_type`(`file_type` ASC) USING BTREE,
  INDEX `idx_upload_time`(`upload_time` ASC) USING BTREE,
  CONSTRAINT `fk_attachment_health_record` FOREIGN KEY (`record_id`) REFERENCES `health_status_record` (`record_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '健康记录附件表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- 插入相关字典数据
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (280, '记录类型', '记录类型', NULL, NULL, 1, '健康记录类型分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (281, '日常记录', NULL, '1', '日常记录', 1, '日常健康记录', 280);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (282, '体检记录', NULL, '2', '体检记录', 2, '体检健康记录', 280);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (283, '就诊记录', NULL, '3', '就诊记录', 3, '就诊健康记录', 280);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (284, '复查记录', NULL, '4', '复查记录', 4, '复查健康记录', 280);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (285, '紧急记录', NULL, '5', '紧急记录', 5, '紧急健康记录', 280);

INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (286, '用药依从性', '用药依从性', NULL, NULL, 1, '患者用药依从性评估', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (287, '完全依从', NULL, '1', '完全依从', 1, '完全按医嘱用药', 286);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (288, '基本依从', NULL, '2', '基本依从', 2, '基本按医嘱用药', 286);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (289, '部分依从', NULL, '3', '部分依从', 3, '部分按医嘱用药', 286);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (290, '不依从', NULL, '4', '不依从', 4, '不按医嘱用药', 286);

INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (291, '睡眠质量', '睡眠质量', NULL, NULL, 1, '睡眠质量评估', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (292, '很好', NULL, '1', '很好', 1, '睡眠质量很好', 291);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (293, '好', NULL, '2', '好', 2, '睡眠质量好', 291);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (294, '一般', NULL, '3', '一般', 3, '睡眠质量一般', 291);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (295, '差', NULL, '4', '差', 4, '睡眠质量差', 291);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (296, '很差', NULL, '5', '很差', 5, '睡眠质量很差', 291);

INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (297, '情绪状态', '情绪状态', NULL, NULL, 1, '患者情绪状态评估', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (298, '很好', NULL, '1', '很好', 1, '情绪状态很好', 297);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (299, '好', NULL, '2', '好', 2, '情绪状态好', 297);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (300, '一般', NULL, '3', '一般', 3, '情绪状态一般', 297);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (301, '差', NULL, '4', '差', 4, '情绪状态差', 297);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (302, '很差', NULL, '5', '很差', 5, '情绪状态很差', 297);

INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (303, '数据来源', '数据来源', NULL, NULL, 1, '健康数据来源分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (304, '手动录入', NULL, '1', '手动录入', 1, '用户手动录入', 303);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (305, '设备同步', NULL, '2', '设备同步', 2, '智能设备同步', 303);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (306, '医院导入', NULL, '3', '医院导入', 3, '医院系统导入', 303);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (307, '第三方接口', NULL, '4', '第三方接口', 4, '第三方平台接口', 303);

INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (308, '附件类型', '附件类型', NULL, NULL, 1, '健康记录附件类型分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (309, '检查报告', NULL, '1', '检查报告', 1, '医院检查报告', 308);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (310, '化验单', NULL, '2', '化验单', 2, '医院化验单', 308);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (311, '影像资料', NULL, '3', '影像资料', 3, 'X光、CT、MRI等影像', 308);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (312, '用药照片', NULL, '4', '用药照片', 4, '药品照片记录', 308);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (313, '症状照片', NULL, '5', '症状照片', 5, '症状相关照片', 308);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (314, '其他', NULL, '6', '其他', 6, '其他类型附件', 308);
