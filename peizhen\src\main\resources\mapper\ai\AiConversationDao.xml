<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.ai.dao.AiConversationDao">

    <!-- 分页查询用户的对话会话列表 -->
    <select id="selectUserConversations" resultType="com.sqx.modules.ai.entity.AiConversation">
        SELECT c.id, c.user_id, c.title, c.model_id, c.model_code,
               c.message_count, c.total_tokens, c.status, c.create_time, c.update_time,
               m.model_name,
               (SELECT content FROM ai_message
                WHERE conversation_id = c.id
                ORDER BY id DESC LIMIT 1) as last_message
        FROM ai_conversation c
        LEFT JOIN ai_model_config m ON c.model_id = m.id
        WHERE c.user_id = #{userId}
        AND c.status = 1
        ORDER BY c.update_time DESC
    </select>

    <!-- 管理后台分页查询对话会话列表（包含用户信息） -->
    <select id="selectConversationListWithUser" resultType="com.sqx.modules.ai.entity.AiConversation">
        SELECT c.id, c.user_id, c.title, c.model_id, c.model_code,
               c.message_count, c.total_tokens, c.status, c.create_time, c.update_time,
               m.model_name,
               u.user_name, u.phone
        FROM ai_conversation c
        LEFT JOIN ai_model_config m ON c.model_id = m.id
        LEFT JOIN tb_user u ON c.user_id = u.user_id
        WHERE c.status = 1
        <if test="title != null and title != ''">
            AND c.title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND c.model_code = #{modelCode}
        </if>
        <if test="userSearch != null and userSearch != ''">
            AND (c.user_id = #{userSearch} OR u.user_name LIKE CONCAT('%', #{userSearch}, '%') OR u.phone LIKE CONCAT('%', #{userSearch}, '%'))
        </if>
        ORDER BY c.update_time DESC
    </select>

    <!-- 更新会话的消息统计 -->
    <update id="updateConversationStats">
        UPDATE ai_conversation
        SET message_count = #{messageCount},
            total_tokens = #{totalTokens},
            update_time = NOW()
        WHERE id = #{conversationId}
    </update>

</mapper>
