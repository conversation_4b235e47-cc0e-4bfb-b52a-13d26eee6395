package com.sqx.modules.creditRecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.jdbc.SQL;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * 信用分记录表
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Data
public class CreditRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录id
     */
    @ApiModelProperty("记录id")
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    /**
     * 操作人用户
     */
    @ApiModelProperty("操作人用户")
    private String sysUserName;

    /**
     * 操作人id
     */
    @ApiModelProperty("操作人id")
    private Long sysUserId;

    /**
     * 师傅id
     */
    @ApiModelProperty("师傅id")
    private Long riderUserId;
    /**
     * 师傅昵称
     */
    @TableField(condition = SqlCondition.LIKE)
    @ApiModelProperty("师傅昵称")
    private String riderUserName;
    /**
     * 师傅手机号
     */
    @TableField(condition = SqlCondition.LIKE)
    @ApiModelProperty("师傅手机号")
    private String riderPhone;
    /**
     * 修改前的信用分
     */
    @ApiModelProperty("修改前的信用分")
    private Integer lastCreditScore;

    /**
     * 1增加 2减少
     */
    @ApiModelProperty("1增加 2减少")
    private Integer type;

    /**
     * 分数
     */
    @ApiModelProperty("分数")
    private Integer num;

    /**
     * 修改后的信用分
     */
    @ApiModelProperty("修改后的信用分")
    private Integer nextCreditScore;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String reason;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


}
