package com.sqx.modules.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Query;
import com.sqx.common.utils.Result;
import com.sqx.common.utils.ShiroUtils;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.creditRecord.entity.CreditRecord;
import com.sqx.modules.creditRecord.service.CreditRecordService;
import com.sqx.modules.message.dao.MessageInfoDao;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.sys.entity.SysUserEntity;
import com.sqx.modules.taking.dao.OrderTakingDao;
import com.sqx.modules.taking.entity.OrderTaking;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 消息
 */
@Service
public class MessageServiceImpl extends ServiceImpl<MessageInfoDao, MessageInfo> implements MessageService {

    @Autowired
    private MessageInfoDao messageInfoDao;
    @Autowired
    private OrdersService ordersService;
    @Autowired
    private OrderTakingDao orderTakingDao;
    @Autowired
    private UserService userService;
    @Autowired
    private CreditRecordService recordService;

    @Override
    public Result auditMessage(Long messageId, Integer status, String auditContent, Integer score) {
        MessageInfo messageInfo = baseMapper.selectById(messageId);
        messageInfo.setStatus(status);
        UserEntity byId = userService.getById(messageInfo.getByUserId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (status == 2) {
            if (byId.getStatus().equals(1)) {
                //2封号
                byId.setStatus(2);
                userService.updateById(byId);
                orderTakingDao.updateTakingStatusByUserId(byId.getUserId());
                MessageInfo messageInfos = new MessageInfo();
                messageInfos.setContent("当前账号存在违规行为，已被系统封禁，如有疑问，请联系客服！");
                messageInfos.setTitle("账号封禁通知");
                messageInfos.setState(String.valueOf(4));
                messageInfos.setUserName(byId.getUserName());
                messageInfos.setUserId(String.valueOf(byId.getUserId()));
                messageInfos.setCreateAt(sdf.format(new Date()));
                messageInfos.setIsSee("0");
                baseMapper.insert(messageInfos);
            }
        } else if (status == 3) {
            //扣分
            if (score <= 0) {
                return Result.error("请输入大于0的数");
            }
            if (byId.getCreditScore() < score) {
                return Result.error("当前用户最多扣除" + byId.getCreditScore() + "分");
            }
            recordService.updateUserCreditRecord(byId, 2, score, auditContent);
            MessageInfo riderMessageInfo = new MessageInfo();
            riderMessageInfo.setContent("用户投诉成功,扣除" + score + "信用分,当前信用分" + byId.getCreditScore());
            riderMessageInfo.setTitle("信用分扣除通知");
            riderMessageInfo.setState(String.valueOf(5));
            riderMessageInfo.setUserName(byId.getUserName());
            riderMessageInfo.setUserId(String.valueOf(byId.getUserId()));
            riderMessageInfo.setCreateAt(sdf.format(new Date()));
            riderMessageInfo.setIsSee("0");
            baseMapper.insert(riderMessageInfo);
        }
        messageInfo.setAuditContent(auditContent);
        baseMapper.updateById(messageInfo);
        return Result.success();
    }

    @Override
    public PageUtils selectMessageList(Map<String, Object> params) {
        Long userId = (Long) params.get("userId");
        Integer state = (Integer) params.get("state");
        Integer type = (Integer) params.get("type");
        IPage<MessageInfo> page = this.page(
                new Query<MessageInfo>().getPage(params),
                new QueryWrapper<MessageInfo>()
                        .eq(userId != null, "user_id", userId)
                        .eq(state != null, "state", state)
                        .eq(type != null, "type", type).orderByDesc("create_at")
        );
        List<MessageInfo> records = page.getRecords();
        if (records.size() > 0) {
            for (MessageInfo messageInfo : records) {
                if (state != null && state == 9) {
                    if (StringUtils.isNotEmpty(messageInfo.getType())) {
                        Orders orders = ordersService.getById(Long.parseLong(messageInfo.getType()));
                        messageInfo.setOrders(orders);
                        if (orders != null) {
                            OrderTaking orderTaking = orderTakingDao.selectById(orders.getOrderTakingId());
                            messageInfo.setOrderTaking(orderTaking);
                        }
                    }
                }
                if (messageInfo.getUserId() != null) {
                    UserEntity user = userService.getById(messageInfo.getUserId());
                    if (user != null) {
                        messageInfo.setUserName(user.getUserName());
                    }
                }
                if (messageInfo.getByUserId() != null) {
                    UserEntity user = userService.getById(messageInfo.getByUserId());
                    if (user != null) {
                        messageInfo.setByUserName(user.getUserName());
                    }
                }

            }
        }

        return new PageUtils(page);
    }

    @Override
    public int saveBody(MessageInfo messageInfo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        messageInfo.setCreateAt(sdf.format(now));
        messageInfo.setStatus(0);
        return messageInfoDao.insert(messageInfo);
    }

    @Override
    public int update(MessageInfo messageInfo) {
        return messageInfoDao.updateById(messageInfo);
    }

    @Override
    public int delete(Long id) {
        return messageInfoDao.deleteById(id);
    }

    @Override
    public MessageInfo selectMessageById(Long id) {
        return messageInfoDao.selectById(id);
    }

    @Override
    public int updateSendState(Long userId, Integer state) {
        return messageInfoDao.updateSendState(userId, state);
    }


}
