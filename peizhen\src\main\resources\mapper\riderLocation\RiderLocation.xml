<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.riderLocation.dao.RiderLocationDao">

    <select id="getDistanceNearRide" resultType="java.lang.Long">
        SELECT user_id,
               a.distance
        FROM (SELECT t.user_id,
                     (
                             st_distance(
                                     POINT(t.lng, t.lat),
                                     POINT(#{longitude}, #{latitude})
                                 ) * 111195
                         ) AS distance
              FROM `rider_location` t,
                   user_money m
              WHERE m.`safety_money` &gt;= #{cashDeposit}
                and t.user_id NOT IN
                    (SELECT order_taking_user_id
                     FROM orders o
                     WHERE o.state IN (1, 5))
             ) a
        WHERE distance &lt;= #{distance}
        ORDER BY distance ASC LIMIT 1
    </select>
    <select id="selectAllRiderLocation" resultType="com.sqx.modules.riderLocation.entity.RiderLocation">
        SELECT r.*,
        u.user_name,
        u.phone
        FROM rider_location r,
        tb_user u
        WHERE u.user_id = r.user_id and u.status = 1
        <if test="riderLocation.province!=null and riderLocation.province!=''">
            and r.province like concat("%",#{riderLocation.province},"%")
        </if>
        <if test="riderLocation.city!=null and riderLocation.city!=''">
            and r.city like concat("%",#{riderLocation.city},"%")
        </if>
        <if test="riderLocation.district!=null and riderLocation.district!=''">
            and r.district like concat("%",#{riderLocation.district},"%")
        </if>
        <if test="riderLocation.phone!=null and riderLocation.phone!=''">
            and u.phone like concat("%",#{riderLocation.phone},"%")
        </if>
        <if test="riderLocation.userName!=null and riderLocation.userName!=''">
            and u.user_name like concat("%",#{riderLocation.userName},"%")
        </if>
    </select>
    <select id="selectRiderList" resultType="com.sqx.modules.riderLocation.entity.RiderLocation">
        SELECT
        r.*
        FROM
        rider_location r,
        tb_user u,
        orders o
        WHERE u.user_id = r.user_id and u.user_id = order_taking_user_id
        AND u.status = 1
        <if test="city!=null and city!=''">
            AND (r.city LIKE CONCAT("%",#{city},"%") OR r.province LIKE CONCAT("%",#{city},"%"))
        </if>
        <if test="riderPhone!=null and riderPhone!=''">
            and u.phone like concat("%",#{riderPhone},"%")
        </if>
        <if test="ordersNo!=null and ordersNo!=''">
            AND o.orders_no LIKE CONCAT("%", #{ordersNo}, "%")
        </if>
    </select>
</mapper>