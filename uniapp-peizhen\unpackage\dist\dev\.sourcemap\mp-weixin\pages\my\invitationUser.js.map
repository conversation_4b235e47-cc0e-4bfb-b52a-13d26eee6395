{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/invitationUser.vue?c335", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/invitationUser.vue?20c5", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/invitationUser.vue?42c4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/invitationUser.vue?bd19", "uni-app:///pages/my/invitationUser.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/invitationUser.vue?c1f9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/invitationUser.vue?64ab"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tkiQrcode", "wm<PERSON><PERSON>er", "data", "<PERSON><PERSON><PERSON><PERSON>", "poster", "qrShow", "haibaoImg", "haibaoShow", "modalName", "canvasId", "imageUrl", "userImageUrl", "isShowWxAPPShare", "nick<PERSON><PERSON>", "invitationCode", "backgroundImage", "tuiguang", "tuiguang1", "url", "phoneWidth", "onLoad", "uni", "success", "that", "onShareAppMessage", "path", "title", "methods", "posterSuccess", "showModal", "hideModal", "qrR", "getBackImageList", "make", "uQRCode", "componentInstance", "text", "size", "margin", "backgroundColor", "foregroundColor", "fileType", "correctLevel", "console", "shareWeiXin", "shareUrl", "shareTitle", "shareContent", "shareImg", "type", "share", "sharAPPUrl", "content", "showCancel", "cancelText", "confirmText", "s<PERSON><PERSON>l", "logoTime", "current", "urls", "loop", "longPressActions", "itemList", "itemColor", "goList", "shareFc", "_this", "getSharePoster", "posterCanvasId", "delayTimeScale", "drawArray", "bgObj", "bgScale", "rs", "setDraw", "Context", "height", "fontStyle", "color", "alpha", "textAlign", "textBaseline", "infoCallBack", "dx", "dy", "serialNum", "id", "setCanvasWH", "d", "_app", "saveImage", "filePath", "hideQr", "onSaveImg", "imgUrl", "settingWritePhotosAlbum", "scope", "getImageInfo", "imgSrc", "src", "resolve", "fail", "errs", "createPoster", "_imgInfo", "ctx", "imgWs", "imgH", "upx2px", "width", "log", "tempFile<PERSON>ath", "hideLoading", "reject", "icon", "complete", "config", "info"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6uB,CAAgB,8rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2FjwB;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAOA;AACA;AACA;AAAA,eACA;EACAC;IACAC;IACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IAEA;IACA;MACA;IACA;MACA;IACA;IAEA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;UACA;QACA;MACA;IACA;IAEA;IAEA;MACA;QACA;UACA;QACA;MACA;IACA;IAqBA;IACA;MACA;IACA;MACA;IACA;EAEA;EACAC;IACA;MACAC;MAAA;MACAC;MACAhB;IACA;EACA;EACAiB;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;YACA;UACA;QACA;MACA;MACA;IAEA;IACAC;MACAC;QACAzB;QACA0B;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACApB;UACAqB;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;YACA;YACA;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;YACA;cACAN;YACA;UACA;QACA;MACA;IAEA;IACAO;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA9B;QACAK;QACA0B;QACAC;QACAC;QACAC;QACAjC;UACA;YACAD;cACAnB;cACAoB;gBACAqB;gBACApB;cACA;YACA;UACA;QACA;MACA;IACA;IACAiC;MAAA;MACA;MACA;MACA;MACAnC;QACAK;QACA0B;QACAC;QACAC;QACAC;QACAjC;UACA;YACAD;cACAnB;cACAoB;gBACAqB;gBACApB;cACA;YACA;UACA;QACA;MACA;IACA;IACAkC;MACApC;QACAqC;QACAC;QACAC;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;YACA;UACA;YACA;UACA;UAEA;YACA;UACA;YACA;UACA;UACA;UACA;QAEA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA;gBAAA,OAEAC;kBACAlB;kBACAlC;kBACAqD;kBAAA;kBACAC;kBAAA;kBACAC,oCAIA;oBAAA,IAHAC;sBACAtB;sBACAuB;oBAEA;oBACA;oBACA;oBACA;oBACA;sBACAC;wBACAxB;wBACAyB;0BACAC;0BACAA;0BACAA,yCACAC,iCACAA;0BACAD;wBACA;sBACA,GACA;wBACA1B;wBACA4B;wBACAzC;wBACAC;wBACAyC;wBACAC;wBACAC;wBACAC;wBACAC;0BACA;4BACAC;4BACAC;0BACA;wBACA;wBACAC;wBACAC;sBACA,GACA;wBACArC;wBACAb;wBACAC;wBACA8C;wBACAC;sBACA,EACA;oBACA;kBACA;kBACAG,yCAIA;oBAAA,IAHAhB;sBACAtB;sBACAuB;oBACA;oBACAN;kBACA;gBACA;cAAA;gBA7DAsB;gBA8DA;gBACAtB;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAuB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACArE;QACAsE;QACArE;UACAmE;QACA;MACA;IACA;IACAG;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAzE;kBACAK;gBACA;gBACA;kBACAL;oBACAC;sBACA;wBACAD;0BACAsE;0BACArE;4BACAD;4BACAA;8BACAK;4BACA;0BACA;wBACA;sBACA;wBACAL;0BACAK;0BACA0B;0BACAG;0BACAD;0BACAhC;4BACA;8BACAD;8BACAA;4BACA;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;kBACAA;kBACA0E;kBACA1E;oBACA2E;oBACA1E;sBACAD;wBACAsE;wBACArE;0BACAD;0BACAA;4BACAK;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAuE,2CAEA;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBADAC;gBAEA3E;gBAAA,kCACA;kBACAF;oBACA8E;oBACA7E;sBACA8E;oBACA;oBACAC;sBACAC;sBACA/E;sBACAF;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAkF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAhF;gBACAuE,+BACA;gBAAA;gBAAA,OACAvE;kBACA2E;gBACA;cAAA;gBAFAM;gBAAA,kCAIA;kBACAnF;oBACAK;kBACA;kBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAGA;kBACA;kBACA;kBACAiB;kBACAA;kBACA;kBACA8D;kBACAA;kBAGApF;oBACAH;oBACAI;sBACA;wBACAqB,sCACA,gDACA7B;wBACAO;0BACAH,+BACA,gDACAJ;0BACAQ;4BACAqB;4BACAtB;4BACA;8BAGA;8BACAoF,sCACAC,OACAC;8BACA;8BACA;8BACAF;8BACAA;8BACA;8BACA;8BACAA,yCACAG;8BACA;8BACAH,sCACAtF,8BACAwF,mCACA,UACAC;8BACAH;gCACA;gCACApF;kCACAZ;kCACAoG;kCACAjC;kCACAtD;oCACAqB,QACAmE,IACA,UACA;oCACAV,YACAW,aACA;kCACA;kCACAV;oCACAhF,IACA2F;oCACAC;kCACA;gCACA;8BACA;4BACA;8BACA5F;8BACAA;gCACAK;gCACAwF;8BACA;4BACA;0BACA;0BACAb;4BACA1D;4BACAtB;4BACAA;8BACAK;8BACAwF;4BACA;0BACA;0BACAC;4BACAxE;4BACAtB;8BACAK;8BACAwF;4BACA;0BACA;wBACA;sBACA;wBACA7F;wBACAA;0BACAK;0BACAwF;wBACA;sBACA;oBACA;oBACAb;sBACA;sBACA1D;sBACAtB;sBACAA;wBACAK;wBACAwF;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MACA;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;UACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9pBA;AAAA;AAAA;AAAA;AAAojC,CAAgB,+8BAAG,EAAC,C;;;;;;;;;;;ACAxkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/invitationUser.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/invitationUser.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./invitationUser.vue?vue&type=template&id=11b5b31e&\"\nvar renderjs\nimport script from \"./invitationUser.vue?vue&type=script&lang=js&\"\nexport * from \"./invitationUser.vue?vue&type=script&lang=js&\"\nimport style0 from \"./invitationUser.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/invitationUser.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./invitationUser.vue?vue&type=template&id=11b5b31e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./invitationUser.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./invitationUser.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"view1\" v-bind:style=\"{backgroundImage: 'url('+backgroundImage+')'}\">\r\n\t\t\t<view style=\"padding-top: 820upx;\" @longpress=\"logoTime(userImageUrl)\">\r\n\t\t\t\t<view style=\"width: 100%;height: 150upx;display: flex;background: #FFFFFF;padding: 20upx 10upx;\">\r\n\t\t\t\t\t<image :src=\"userImageUrl\"\r\n\t\t\t\t\t\tstyle=\"border-radius: 100upx;width: 100upx;height: 100upx;margin-left: 30upx;\"></image>\r\n\t\t\t\t\t<view class=\"login-view-text1\" style=\"margin-left: 30upx;width: 59%;\">\r\n\t\t\t\t\t\t<view style=\"font-size: 16px;\">{{ nickName }}</view>\r\n\t\t\t\t\t\t<view style=\" font-size: 12px;margin-top: 20upx;\">ID:{{invitationCode}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<canvas canvas-id=\"qrcode\" style=\"width: 140upx;height: 130upx;\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"display: flex;flex-direction: row; padding: 40upx;justify-content: center;\">\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<button @click=\"share()\" type=\"default\"\r\n\t\t\t\tstyle=\"background-color: #FFCB49;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 50%;\">文案推广</button>\r\n\t\t\t<button @tap=\"showModal()\" type=\"default\"\r\n\t\t\t\tstyle=\"background-color: #557EFD;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 50%; margin-left: 40upx;\">生成海报</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t<button v-if=\"isShowWxAPPShare=='是'\" @click=\"shareWeiXin()\" type=\"default\"\r\n\t\t\t\tstyle=\"background-color: #FFCB49;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 50%;\">文案推广</button>\r\n\t\t\t<button v-if=\"isShowWxAPPShare=='否'\" @click=\"sharAPPUrl()\" type=\"default\"\r\n\t\t\t\tstyle=\"background-color: #FFCB49;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 50%;\">文案推广</button>\r\n\r\n\t\t\t<button @tap=\"showModal()\" type=\"default\"\r\n\t\t\t\tstyle=\"background-color: #557EFD;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 50%; margin-left: 40upx;\">生成海报</button>\r\n\t\t\t<!-- #endif -->\r\n\r\n\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t<button open-type=\"share\"\r\n\t\t\t\tstyle=\"background-color: #FFCB49;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 40%;\">一键分享</button>\r\n\t\t\t<!-- <button @click=\"share()\" type=\"default\" style=\"background-color: #FFCB49;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 40%;\">文案推广</button> -->\r\n\t\t\t<button @tap=\"onSaveImg()\" type=\"default\"\r\n\t\t\t\tstyle=\"background-color: #557EFD;font-size: 16px;font-weight: bold;color: #FFFFFF; width: 40%;\">生成海报</button>\r\n\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- 生成海报 -->\r\n\t\t\t<!-- 图片展示由自己实现 -->\r\n\t\t\t<view class=\"flex_row_c_c modalView\" :class=\"qrShow?'show':''\" @tap=\"hideQr()\">\r\n\t\t\t\t<view class=\"flex_column\">\r\n\t\t\t\t\t<view class=\"backgroundColor-white padding1vh border_radius_10px\">\r\n\t\t\t\t\t\t<image :src=\"poster.finalPath || ''\" mode=\"widthFix\" class=\"posterImage\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex_row marginTop2vh\">\r\n\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t<button type=\"primary\" size=\"mini\">长按上方图片保存</button>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t\t<button type=\"primary\" size=\"mini\" @tap.prevent.stop=\"saveImage()\">保存图片</button>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 画布 -->\r\n\t\t\t<view class=\"hideCanvasView\">\r\n\t\t\t\t<canvas class=\"hideCanvas\" canvas-id=\"default_PosterCanvasId\"\r\n\t\t\t\t\t:style=\"{width: (poster.width||10) + 'px', height: (poster.height||10) + 'px'}\"></canvas>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t<tki-qrcode ref=\"qrcode\" :val=\"url\" :size=\"200\" background=\"#fff\" foreground=\"#000\" pdground=\"#000\"\r\n\t\t\t:onval=\"true\" :loadMake=\"true\" @result=\"qrR\" :show=\"false\"></tki-qrcode>\r\n\t\t<view class=\"cu-modal\" :class=\"modalName == 'Image' ? 'show' : ''\" @tap=\"hideModal\">\r\n\t\t\t<view class=\"cu-dialog\" v-if=\"backgroundImage && erweimapath && haibaoShow\" @tap=\"hideModal\">\r\n\t\t\t\t<view class=\"bg-img\">\r\n\t\t\t\t\t<wm-poster @success=\"posterSuccess\" :imgSrc=\"backgroundImage\" :Referrer=\"'我的邀请码:'+invitationCode\"\r\n\t\t\t\t\t\t:QrSrc=\"erweimapath\" :Title=\"tuiguang\" :LineType=\"false\"></wm-poster>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<view @tap=\"hideModal\" :class=\"modalName == 'Image' ? 'show' : ''\" class=\"modal\"\r\n\t\t\tstyle=\"text-align: center;display: flex;justify-content: center;\">\r\n\t\t\t<view style=\"width:100%;margin: auto;\">\r\n\t\t\t\t<image :src=\"h5SaveImg\" mode=\"widthFix\" style=\"width: 90%;\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<canvas canvas-id=\"poster\" class=\"poster_canvas\"></canvas>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet settingWritePhotosAlbum = false;\r\n\timport tkiQrcode from '@/components/tki-qrcode/tki-qrcode.vue';\r\n\timport appShare from '@/utils/share.js';\r\n\timport wmPoster from '@/components/wm-poster/wm-posterorders.vue';\r\n\timport uQRCode from \"../../js_sdk/Sansnn-uQRCode/uqrcode.js\"\r\n\timport _app from '../../js_sdk/QuShe-SharerPoster/QS-SharePoster/app.js';\r\n\timport configdata from '../../common/config.js';\r\n\t// import {\r\n\t// \tgetSharePoster\r\n\t// } from '../../js_sdk/QuShe-SharerPoster/QS-SharePoster/QS-SharePoster.js';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttkiQrcode,\r\n\t\t\twmPoster,\r\n\t\t\t// getSharePoster\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\terweimapath: '',\r\n\t\t\t\tposter: {},\r\n\t\t\t\tqrShow: false,\r\n\t\t\t\thaibaoImg: null,\r\n\t\t\t\thaibaoShow: false,\r\n\t\t\t\tmodalName: '',\r\n\t\t\t\tcanvasId: 'default_PosterCanvasId',\r\n\t\t\t\timageUrl: '',\r\n\t\t\t\tuserImageUrl: '',\r\n\t\t\t\tisShowWxAPPShare: '否',\r\n\t\t\t\tnickName: '',\r\n\t\t\t\tinvitationCode: '',\r\n\t\t\t\tbackgroundImage: '',\r\n\t\t\t\ttuiguang: '',\r\n\t\t\t\ttuiguang1: '',\r\n\t\t\t\turl: '',\r\n\t\t\t\tphoneWidth: 375, //获取手机宽度 默认375\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tlet that = this\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tthat.phoneWidth = res.screenWidth\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tthis.getBackImageList();\r\n\r\n\t\t\tlet avatar = this.$queue.getData('avatar');\r\n\t\t\tif (avatar && avatar !== 'undefined') {\r\n\t\t\t\tthis.userImageUrl = avatar;\r\n\t\t\t} else {\r\n\t\t\t\tthis.userImageUrl = '/static/logo.png';\r\n\t\t\t}\r\n\r\n\t\t\tthis.$Request.getT('/app/common/type/276').then(res => {\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\tthis.tuiguang = res.data.value;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//文案推广\r\n\t\t\tthis.$Request.getT('/app/common/type/276').then(res => {\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\tthis.tuiguang1 = res.data.value;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//检测书否开启APP微信分享\r\n\t\t\tthis.$Request.getT('/app/common/type/136').then(res => {\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\tthis.isShowWxAPPShare = res.data.value;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tthis.invitationCode = this.$queue.getData('invitationCode');\r\n\t\t\t// #ifndef H5\r\n\t\t\tthis.$Request.getT('/app/common/type/25').then(res => {\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\tthis.url = res.data.value;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//#endif\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.$Request.getT('/app/common/type/141').then(res => {\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\tif (res.data && res.data.value && res.data.value == '是') {\r\n\t\t\t\t\t\tthis.$Request.getT('/app/common/type/25').then(ress => {\r\n\t\t\t\t\t\t\tif (ress.code === 0) {\r\n\t\t\t\t\t\t\t\tif (ress.data && ress.data.value) {\r\n\t\t\t\t\t\t\t\t\tthis.url = ress.data.value;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.url = this.$queue.publicYuMing() + '/?invitation=' + this.invitationCode;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t//#endif\r\n\r\n\t\t\tlet userName = this.$queue.getData('userName');\r\n\t\t\tif (userName && userName !== 'undefined') {\r\n\t\t\t\tthis.nickName = userName;\r\n\t\t\t} else {\r\n\t\t\t\tthis.nickName = '游客';\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/pages/index/index?invitation=' + this.invitationCode, //这是为了传参   onload(data){let id=data.id;} \r\n\t\t\t\ttitle: this.tuiguang,\r\n\t\t\t\timageUrl: this.backgroundImage\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tposterSuccess(haibaoImg) {\r\n\t\t\t\tthis.haibaoImg = haibaoImg;\r\n\t\t\t\tthis.modalName = 'Image';\r\n\t\t\t},\r\n\t\t\tshowModal() {\r\n\t\t\t\tif (!this.haibaoImg) {\r\n\t\t\t\t\tthis.haibaoShow = true;\r\n\t\t\t\t\tthis.$queue.showLoading('海报生成中...');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.modalName = 'Image';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thideModal() {\r\n\t\t\t\tthis.modalName = null;\r\n\t\t\t},\r\n\t\t\tqrR(path) {\r\n\t\t\t\tthis.erweimapath = path;\r\n\t\t\t},\r\n\t\t\tgetBackImageList() {\r\n\t\t\t\tthis.$Request.getT('/app/common/type/277').then(res => {\r\n\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\t\tthis.backgroundImage = res.data.value;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tthis.make();\r\n\r\n\t\t\t},\r\n\t\t\tmake() {\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'default_PosterCanvasId',\r\n\t\t\t\t\tcomponentInstance: this,\r\n\t\t\t\t\ttext: this.url,\r\n\t\t\t\t\tsize: 68,\r\n\t\t\t\t\tmargin: 4,\r\n\t\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\t\tforegroundColor: '#000000',\r\n\t\t\t\t\tfileType: 'jpg',\r\n\t\t\t\t\tcorrectLevel: uQRCode.errorCorrectLevel.H,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshareWeiXin() {\r\n\t\t\t\tthis.$Request.getT('/app/common/type/103').then(res => {\r\n\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\t\tlet relationId = this.invitationCode;\r\n\t\t\t\t\t\t\tlet shareData = {\r\n\t\t\t\t\t\t\t\tshareUrl: this.url,\r\n\t\t\t\t\t\t\t\tshareTitle: res.data.value,\r\n\t\t\t\t\t\t\t\tshareContent: '邀请码：' + relationId + '，' + res.data.value,\r\n\t\t\t\t\t\t\t\tshareImg: this.$queue.publicYuMing() + '/logo.png',\r\n\t\t\t\t\t\t\t\ttype: 0\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tappShare(shareData, res => {\r\n\t\t\t\t\t\t\t\tconsole.log('分享成功回调', res);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\tshare() {\r\n\t\t\t\tthis.sharurl();\r\n\t\t\t},\r\n\t\t\tsharAPPUrl() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet relationId = this.invitationCode;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '文案推广',\r\n\t\t\t\t\tcontent: this.tuiguang1 + relationId + '\\n' + this.url,\r\n\t\t\t\t\tshowCancel: true,\r\n\t\t\t\t\tcancelText: '关闭',\r\n\t\t\t\t\tconfirmText: '一键复制',\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\tdata: this.tuiguang1 + relationId + '\\n' + this.url,\r\n\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\tconsole.log('success');\r\n\t\t\t\t\t\t\t\t\tthat.$queue.showToast('文案复制成功');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsharurl() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// this.$queue.showLoading('加载中...');\r\n\t\t\t\tlet relationId = this.invitationCode;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '文案推广',\r\n\t\t\t\t\tcontent: this.tuiguang1 + relationId + '\\n' + this.url,\r\n\t\t\t\t\tshowCancel: true,\r\n\t\t\t\t\tcancelText: '关闭',\r\n\t\t\t\t\tconfirmText: '一键复制',\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\tdata: this.tuiguang1 + relationId + '\\n' + this.url,\r\n\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\tconsole.log('success');\r\n\t\t\t\t\t\t\t\t\tthat.$queue.showToast('复制成功');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tlogoTime(urlList) {\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\tcurrent: 0,\r\n\t\t\t\t\turls: urlList,\r\n\t\t\t\t\tloop: true,\r\n\t\t\t\t\tlongPressActions: {\r\n\t\t\t\t\t\titemList: ['收藏'],\r\n\t\t\t\t\t\titemColor: \"#007AFF\"\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoList() {\r\n\t\t\t\tlet userId = this.$queue.getData('userId');\r\n\t\t\t\tthis.$Request.getT('/app/invite/selectInviteAndPoster?userId=' + userId).then(res => {\r\n\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\tif (res.data.user.imageUrl) {\r\n\t\t\t\t\t\t\tthis.userImageUrl = res.data.user.imageUrl;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.userImageUrl = '/static/img/common/logo.jpg';\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (res.data.user.nickName) {\r\n\t\t\t\t\t\t\tthis.nickName = res.data.user.nickName;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.nickName = res.data.user.phone;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.invitationCode = res.data.user.invitationCode;\r\n\t\t\t\t\t\tthis.imageUrl = res.data.url;\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync shareFc() {\r\n\t\t\t\tlet _this = this;\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst d = await getSharePoster({\r\n\t\t\t\t\t\ttype: 'testShareType',\r\n\t\t\t\t\t\tbackgroundImage: _this.backgroundImage,\r\n\t\t\t\t\t\tposterCanvasId: _this.canvasId, //canvasId\r\n\t\t\t\t\t\tdelayTimeScale: 20, //延时系数\r\n\t\t\t\t\t\tdrawArray: ({\r\n\t\t\t\t\t\t\tbgObj,\r\n\t\t\t\t\t\t\ttype,\r\n\t\t\t\t\t\t\tbgScale\r\n\t\t\t\t\t\t}) => {\r\n\t\t\t\t\t\t\tconst dx = bgObj.width * 0.3;\r\n\t\t\t\t\t\t\tconst fontSize = bgObj.width * 0.045;\r\n\t\t\t\t\t\t\tconst lineHeight = bgObj.height * 0.04;\r\n\t\t\t\t\t\t\t//可直接return数组，也可以return一个promise对象, 但最终resolve一个数组, 这样就可以方便实现后台可控绘制海报\r\n\t\t\t\t\t\t\treturn new Promise((rs, rj) => {\r\n\t\t\t\t\t\t\t\trs([{\r\n\t\t\t\t\t\t\t\t\t\ttype: 'custom',\r\n\t\t\t\t\t\t\t\t\t\tsetDraw(Context) {\r\n\t\t\t\t\t\t\t\t\t\t\tContext.setFillStyle('black');\r\n\t\t\t\t\t\t\t\t\t\t\tContext.setGlobalAlpha(0.3);\r\n\t\t\t\t\t\t\t\t\t\t\tContext.fillRect(0, bgObj.height - bgObj\r\n\t\t\t\t\t\t\t\t\t\t\t\t.height * 0.2, bgObj.width, bgObj\r\n\t\t\t\t\t\t\t\t\t\t\t\t.height * 0.2);\r\n\t\t\t\t\t\t\t\t\t\t\tContext.setGlobalAlpha(1);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\t\t\tfontStyle: 'italic',\r\n\t\t\t\t\t\t\t\t\t\ttext: '邀请码:' + _this.invitationCode,\r\n\t\t\t\t\t\t\t\t\t\tsize: fontSize,\r\n\t\t\t\t\t\t\t\t\t\tcolor: 'white',\r\n\t\t\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\t\t\tinfoCallBack(textLength) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\t\t\t\tdx: bgObj.width - textLength - fontSize,\r\n\t\t\t\t\t\t\t\t\t\t\t\tdy: bgObj.height - lineHeight * 3\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tserialNum: 0,\r\n\t\t\t\t\t\t\t\t\t\tid: 'tag1' //自定义标识\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\ttype: 'qrcode',\r\n\t\t\t\t\t\t\t\t\t\ttext: _this.url,\r\n\t\t\t\t\t\t\t\t\t\tsize: bgObj.width * 0.2,\r\n\t\t\t\t\t\t\t\t\t\tdx: bgObj.width * 0.05,\r\n\t\t\t\t\t\t\t\t\t\tdy: bgObj.height - bgObj.width * 0.25\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t]);\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsetCanvasWH: ({\r\n\t\t\t\t\t\t\tbgObj,\r\n\t\t\t\t\t\t\ttype,\r\n\t\t\t\t\t\t\tbgScale\r\n\t\t\t\t\t\t}) => { // 为动态设置画布宽高的方法，\r\n\t\t\t\t\t\t\t_this.poster = bgObj;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t//_app.log('海报生成成功, 时间:' + new Date() + '， 临时路径: ' + d.poster.tempFilePath)\r\n\t\t\t\t\t_this.poster.finalPath = d.poster.tempFilePath;\r\n\t\t\t\t\t_this.qrShow = true;\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t_app.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsaveImage() {\r\n\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\tfilePath: this.poster.finalPath,\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t_app.showToast('保存成功');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thideQr() {\r\n\t\t\t\tthis.qrShow = false;\r\n\t\t\t},\r\n\r\n\t\t\t// 微信小程序保存图片\r\n\t\t\tasync onSaveImg() {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet imgUrl = await this.createPoster();\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '海报下载中'\r\n\t\t\t\t});\r\n\t\t\t\tif (settingWritePhotosAlbum) {\r\n\t\t\t\t\tuni.getSetting({\r\n\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\tif (res.authSetting['scope.writePhotosAlbum']) {\r\n\t\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\t\tfilePath: imgUrl,\r\n\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '保存成功'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\t\tcontent: '请先在设置页面打开“保存相册”使用权限',\r\n\t\t\t\t\t\t\t\t\tconfirmText: '去设置',\r\n\t\t\t\t\t\t\t\t\tcancelText: '算了',\r\n\t\t\t\t\t\t\t\t\tsuccess: data => {\r\n\t\t\t\t\t\t\t\t\t\tif (data.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t\tuni.openSetting();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tsettingWritePhotosAlbum = true;\r\n\t\t\t\t\tuni.authorize({\r\n\t\t\t\t\t\tscope: 'scope.writePhotosAlbum',\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\tfilePath: imgUrl,\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存成功'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tasync getImageInfo({\r\n\t\t\t\timgSrc\r\n\t\t\t}) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\treturn new Promise((resolve, errs) => {\r\n\t\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\t\tsrc: imgSrc,\r\n\t\t\t\t\t\tsuccess: function(image) {\r\n\t\t\t\t\t\t\tresolve(image);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\terrs(err);\r\n\t\t\t\t\t\t\tthat.$queue.showToast('海报生成失败');\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//生成海报\r\n\t\t\tasync createPoster() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet imgUrl = that.backgroundImage;\r\n\t\t\t\t//使用await获取一下图片的信息\r\n\t\t\t\tlet _imgInfo = await that.getImageInfo({\r\n\t\t\t\t\timgSrc: imgUrl\r\n\t\t\t\t});\r\n\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '海报生成中'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconst ctx = uni.createCanvasContext('poster');\r\n\t\t\t\t\tlet r = [_imgInfo.width, _imgInfo.height]\r\n\t\t\t\t\tvar imageAspectRatio = Math.floor(r[1] / r[0]);\r\n\t\t\t\t\tlet imgW = that.phoneWidth\r\n\t\t\t\t\tlet imgWs = imgW\r\n\t\t\t\t\tlet imgH = Math.floor((imgWs / r[0]) * r[1]);\r\n\r\n\r\n\t\t\t\t\t//获取一下画布的高度\r\n\t\t\t\t\t// let canvasH = imgH + 120 + 80;\r\n\t\t\t\t\tlet canvasH = imgH;\r\n\t\t\t\t\tconsole.log(imgH, '图片高度')\r\n\t\t\t\t\tconsole.log(canvasH, '画布高度')\r\n\t\t\t\t\t// ctx.fillRect(0, 0, imgW, canvasH);\r\n\t\t\t\t\tctx.setFillStyle(\"#FFF\");\r\n\t\t\t\t\tctx.fillRect(0, 0, imgW, canvasH);\r\n\r\n\r\n\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\turl: imgUrl,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t\tconsole.log(that.config(\"APIHOST1\") +\r\n\t\t\t\t\t\t\t\t\t'/app/invite/mpCreateQr?invitationCode=' + that\r\n\t\t\t\t\t\t\t\t\t.invitationCode)\r\n\t\t\t\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\t\t\t\turl: that.config(\"APIHOST1\") +\r\n\t\t\t\t\t\t\t\t\t\t'/app/invite/mpCreateQr?invitationCode=' + that\r\n\t\t\t\t\t\t\t\t\t\t.invitationCode + '&type=1',\r\n\t\t\t\t\t\t\t\t\tsuccess: (res2) => {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(res2)\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t//海报背景\r\n\t\t\t\t\t\t\t\t\t\t\tctx.drawImage(res.tempFilePath, 0, 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\timgWs,\r\n\t\t\t\t\t\t\t\t\t\t\t\timgH);\r\n\t\t\t\t\t\t\t\t\t\t\t// 长按识别二维码访问\r\n\t\t\t\t\t\t\t\t\t\t\tlet textTop = 0;\r\n\t\t\t\t\t\t\t\t\t\t\tctx.setFontSize(19);\r\n\t\t\t\t\t\t\t\t\t\t\tctx.setFillStyle('#333');\r\n\t\t\t\t\t\t\t\t\t\t\t// ctx.fillText(\"长按识别图中二维码\", 17, imgH +\r\n\t\t\t\t\t\t\t\t\t\t\t// \t40 + (120 / 2));\r\n\t\t\t\t\t\t\t\t\t\t\tctx.fillText(\"长按识别图中二维码\", 15, imgH - uni\r\n\t\t\t\t\t\t\t\t\t\t\t\t.upx2px(80));\r\n\t\t\t\t\t\t\t\t\t\t\t// 二维码\r\n\t\t\t\t\t\t\t\t\t\t\tctx.drawImage(res2.tempFilePath, that\r\n\t\t\t\t\t\t\t\t\t\t\t\t.phoneWidth - uni.upx2px(140),\r\n\t\t\t\t\t\t\t\t\t\t\t\timgH - uni.upx2px(140), uni.upx2px(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t120), uni\r\n\t\t\t\t\t\t\t\t\t\t\t\t.upx2px(120));\r\n\t\t\t\t\t\t\t\t\t\t\tctx.draw(true, () => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// canvas画布转成图片并返回图片地址\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcanvasId: 'poster',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twidth: imgW,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\theight: canvasH,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.log(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"海报制作成功！\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tresolve(res\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.tempFilePath\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\treject();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '海报制作失败，图片下载失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(err)\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '海报制作失败，图片下载失败',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tcomplete: com => {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(com)\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: com,\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '海报制作失败，图片下载失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\t\t// that.yu.toast(err)\r\n\t\t\t\t\t\t\tconsole.log(err)\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '海报制作失败，图片下载失败',\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tconfig: function(name) {\r\n\t\t\t\tvar info = null;\r\n\t\t\t\tif (name) {\r\n\t\t\t\t\tvar name2 = name.split(\".\"); //字符分割\r\n\t\t\t\t\tif (name2.length > 1) {\r\n\t\t\t\t\t\tinfo = configdata[name2[0]][name2[1]] || null;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tinfo = configdata[name] || null;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (info == null) {\r\n\t\t\t\t\t\tlet web_config = cache.get(\"web_config\");\r\n\t\t\t\t\t\tif (web_config) {\r\n\t\t\t\t\t\t\tif (name2.length > 1) {\r\n\t\t\t\t\t\t\t\tinfo = web_config[name2[0]][name2[1]] || null;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tinfo = web_config[name] || null;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn info;\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.modal {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 1110;\r\n\t\topacity: 0;\r\n\t\toutline: 0;\r\n\t\ttext-align: center;\r\n\t\t-ms-transform: scale(1.185);\r\n\t\ttransform: scale(1.185);\r\n\t\tbackface-visibility: hidden;\r\n\t\tperspective: 2000upx;\r\n\t\t/* background: rgba(0, 0, 0, 0.6); */\r\n\t\tbackground: #FFFFFF;\r\n\t\ttransition: all 0.3s ease-in-out 0s;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.modal.show {\r\n\t\topacity: 1;\r\n\t\ttransition-duration: 0.3s;\r\n\t\t-ms-transform: scale(1);\r\n\t\ttransform: scale(1);\r\n\t\toverflow-x: hidden;\r\n\t\toverflow-y: auto;\r\n\t\tpointer-events: auto;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground: #FFFFFF;\r\n\t}\r\n\r\n\t.view1 {\r\n\t\tborder-radius: 15upx;\r\n\t\tbackground-size: 100%;\r\n\t\tmargin: 20upx 20upx 0 20upx;\r\n\t}\r\n\r\n\t.hideCanvasView {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.hideCanvas {\r\n\t\tposition: fixed;\r\n\t\ttop: -99999upx;\r\n\t\tleft: -99999upx;\r\n\t\tz-index: -99999;\r\n\t}\r\n\r\n\t.flex_row_c_c {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.modalView {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\topacity: 0;\r\n\t\toutline: 0;\r\n\t\ttransform: scale(1.2);\r\n\t\tperspective: 2500upx;\r\n\t\t/* background: rgba(0, 0, 0, 0.6); */\r\n\t\tbackground: #FFFFFF;\r\n\t\ttransition: all .3s ease-in-out;\r\n\t\tpointer-events: none;\r\n\t\tbackface-visibility: hidden;\r\n\t\tz-index: 999;\r\n\t}\r\n\r\n\t.modalView.show {\r\n\t\topacity: 1;\r\n\t\ttransform: scale(1);\r\n\t\tpointer-events: auto;\r\n\t}\r\n\r\n\t.flex_column {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.backgroundColor-white {\r\n\t\tbackground-color: white;\r\n\t}\r\n\r\n\t.border_radius_10px {\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\r\n\t.padding1vh {\r\n\t\tpadding: 1vh;\r\n\t}\r\n\r\n\t.posterImage {\r\n\t\twidth: 60vw;\r\n\t}\r\n\r\n\t.flex_row {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.marginTop2vh {\r\n\t\tmargin-top: 2vh;\r\n\t}\r\n\r\n\t.poster_canvas {\r\n\t\twidth: 750upx;\r\n\t\theight: 1334upx;\r\n\t\tposition: fixed;\r\n\t\ttop: -10000upx;\r\n\t\tleft: 0;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./invitationUser.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./invitationUser.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447625847\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}