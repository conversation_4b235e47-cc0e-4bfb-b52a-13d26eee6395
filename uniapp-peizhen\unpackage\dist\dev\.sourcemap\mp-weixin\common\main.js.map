{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/App.vue?f135", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/App.vue?85c1", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/App.vue?6740", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/App.vue?a56a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "prototype", "$Request", "HttpRequest", "$queue", "queue", "$aiRequest", "aiRequest", "$aiStore", "aiStore", "$Sysconf", "$SysCache", "HttpCache", "App", "mpType", "use", "uView", "app", "$mount", "globalData", "tabIndex", "onLaunch", "setInterval", "uni", "index", "onShow", "console", "onHide"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AAEA;AACA;AACA;AACA;AACA;AAeA;AAA6B;AAAA;AAvB7B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAS1DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCF,YAAG,CAACG,SAAS,CAACC,QAAQ,GAAGC,oBAAW;AACpCL,YAAG,CAACG,SAAS,CAACG,MAAM,GAAGC,cAAK;AAC5BP,YAAG,CAACG,SAAS,CAACK,UAAU,GAAGC,kBAAS;AACpCT,YAAG,CAACG,SAAS,CAACO,QAAQ,GAAGC,gBAAO;AAEhCX,YAAG,CAACG,SAAS,CAACS,QAAQ,GAAGP,oBAAW,CAACJ,MAAM;AAC3CD,YAAG,CAACG,SAAS,CAACU,SAAS,GAAGC,cAAS;AAGnCC,YAAG,CAACC,MAAM,GAAG,KAAK;;AAElB;;AAEAhB,YAAG,CAACiB,GAAG,CAACC,gBAAK,CAAC;AAEd,IAAMC,GAAG,GAAG,IAAInB,YAAG,mBACZe,YAAG,EACR;AAEF,UAAAI,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;AC/BZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACuL;AACvL,gBAAgB,2LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAosB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCCxtB;EACAC;IACAC;EACA;EACAC;IAAA;IACAC;MAAA;MACA;MACA;QACA;UACA;YACA;YACA;YAEAC;YAEA;YACA;cACAA;gBACAC;cACA;cACA;YACA;YACA;YACA;YACA;YACA;UAEA;QACA;MACA;IACA;EAmPA;EACAC;IAAA;IAEA;IACA;MACA;QACA;MACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAGA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;;IAEA;IACA;MACA;QACA;MACA;IACA;IAEAC;EA+EA;EACAC;IACAD;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5ZA;AAAA;AAAA;AAAA;AAA+zC,CAAgB,0tCAAG,EAAC,C;;;;;;;;;;;ACAn1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\nimport App from './App'\n\nimport HttpRequest from './common/httpRequest'\nimport HttpCache from './common/cache'\nimport queue from './common/queue'\nimport aiRequest from './common/aiRequest'\nimport aiStore from './common/aiStore'\n\nVue.config.productionTip = false\nVue.prototype.$Request = HttpRequest;\nVue.prototype.$queue = queue;\nVue.prototype.$aiRequest = aiRequest;\nVue.prototype.$aiStore = aiStore;\n\nVue.prototype.$Sysconf = HttpRequest.config;\nVue.prototype.$SysCache = HttpCache;\n\n\nApp.mpType = 'app'\n\n// 引入全局uView\nimport uView from \"uview-ui\";\nVue.use(uView);\n\nconst app = new Vue({\n    ...App\n})\n\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tglobalData: {\r\n\t\t\ttabIndex: ''\r\n\t\t},\r\n\t\tonLaunch: function() {\r\n\t\t\tsetInterval(d => { //定时器，定时去调取聊天未读消息\r\n\t\t\t\tlet userId = uni.getStorageSync('userId')\r\n\t\t\t\tif (userId) {\r\n\t\t\t\t\tthis.$Request.get('/app/chat/selectChatCount').then(res => {\r\n\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\tlet chatCount = res.data.chatCount\r\n\t\t\t\t\t\t\tlet messageCount = res.data.messageCount\r\n\r\n\t\t\t\t\t\t\tuni.setStorageSync('messageCount', messageCount)\r\n\r\n\t\t\t\t\t\t\tlet num = chatCount + messageCount\r\n\t\t\t\t\t\t\tif (num == 0) {\r\n\t\t\t\t\t\t\t\tuni.removeTabBarBadge({\r\n\t\t\t\t\t\t\t\t\tindex: 1\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// uni.setTabBarBadge({\r\n\t\t\t\t\t\t\t// \tindex: 1,\r\n\t\t\t\t\t\t\t// \ttext: num + \"\"\r\n\t\t\t\t\t\t\t// })\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}, 3000);\r\n\r\n\t\t\t//#ifdef APP-PLUS\r\n\t\t\t// APP检测更新 具体打包流程可以参考：https://ask.dcloud.net.cn/article/35667\r\n\t\t\tplus.screen.lockOrientation('portrait-primary'); //竖屏正方向锁定\r\n\t\t\t//获取是否热更新过\r\n\t\t\tconst updated = uni.getStorageSync('updated'); // 尝试读取storage\r\n\r\n\t\t\tif (updated.completed === true) {\r\n\t\t\t\t// 如果上次刚更新过\r\n\t\t\t\t// 删除安装包及安装记录\r\n\t\t\t\tconsole.log('安装记录被删除，更新成功');\r\n\t\t\t\tuni.removeSavedFile({\r\n\t\t\t\t\tfilePath: updated.packgePath,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tuni.removeStorageSync('updated');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else if (updated.completed === false) {\r\n\t\t\t\tuni.removeStorageSync('updated');\r\n\t\t\t\tplus.runtime.install(updated.packgePath, {\r\n\t\t\t\t\tforce: true\r\n\t\t\t\t});\r\n\t\t\t\tuni.setStorage({\r\n\t\t\t\t\tkey: 'updated',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tcompleted: true,\r\n\t\t\t\t\t\tpackgePath: updated.packgePath\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tconsole.log('成功安装上次的更新，应用需要重启才能继续完成');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '温馨提示',\r\n\t\t\t\t\tcontent: '应用将重启以完成更新',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\tplus.runtime.restart();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t//获取当前系统版本信息\r\n\t\t\t\tplus.runtime.getProperty(plus.runtime.appid, widgetInfo => {\r\n\t\t\t\t\t//请求后台接口 解析数据 对比版本\r\n\t\t\t\t\tthat.$Request.getT('/app/user/selectNewApp').then(res => {\r\n\t\t\t\t\t\tres = res.data[0];\r\n\t\t\t\t\t\tif (res.wgtUrl && widgetInfo.version < res.version) {\r\n\t\t\t\t\t\t\tlet downloadLink = '';\r\n\t\t\t\t\t\t\tlet androidLink = res.androidWgtUrl;\r\n\t\t\t\t\t\t\tlet iosLink = res.iosWgtUrl;\r\n\t\t\t\t\t\t\tlet ready = false;\r\n\t\t\t\t\t\t\t//校验是是不是热更新\r\n\t\t\t\t\t\t\tif (res.wgtUrl.match(RegExp(/.wgt/))) {\r\n\t\t\t\t\t\t\t\t// 判断系统类型\r\n\t\t\t\t\t\t\t\tif (plus.os.name.toLowerCase() === 'android') {\r\n\t\t\t\t\t\t\t\t\tconsole.log('安卓系统');\r\n\t\t\t\t\t\t\t\t\tif (androidLink && androidLink !== '#') {\r\n\t\t\t\t\t\t\t\t\t\t// 我这里默认#也是没有地址，请根据业务自行修改\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('发现下载地址');\r\n\t\t\t\t\t\t\t\t\t\t// 安卓：创建下载任务\r\n\t\t\t\t\t\t\t\t\t\tif (androidLink.match(RegExp(/.wgt/))) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('确认wgt热更新包');\r\n\t\t\t\t\t\t\t\t\t\t\tdownloadLink = androidLink;\r\n\t\t\t\t\t\t\t\t\t\t\tready = true;\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('安卓推荐.wgt强制更新，.apk的强制更新请您自行修改程序');\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('下载地址是空的，无法继续');\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log('苹果系统');\r\n\t\t\t\t\t\t\t\t\tif (iosLink && iosLink !== '#') {\r\n\t\t\t\t\t\t\t\t\t\t// 我这里默认#也是没有地址，请根据业务自行修改\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('发现下载地址');\r\n\t\t\t\t\t\t\t\t\t\t// 苹果(A)：进行热更新（如果iosLink是wgt更新包的下载地址）判断文件名中是否含有.wgt\r\n\t\t\t\t\t\t\t\t\t\tif (iosLink.match(RegExp(/.wgt/))) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('确认wgt热更新包');\r\n\t\t\t\t\t\t\t\t\t\t\tdownloadLink = iosLink;\r\n\t\t\t\t\t\t\t\t\t\t\tready = true;\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('苹果只支持.wgt强制更新');\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('下载地址是空的，无法继续');\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (ready) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('任务开始');\r\n\t\t\t\t\t\t\t\t\tlet downloadTask = uni.downloadFile({\r\n\t\t\t\t\t\t\t\t\t\turl: downloadLink,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// 保存下载的安装包\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('保存安装包');\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.saveFile({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttempFilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst packgePath = res\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.savedFilePath;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 保存更新记录到stroage，下次启动app时安装更新\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey: 'updated',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcompleted: false,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpackgePath: packgePath\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.log(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'成功保存记录'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// 任务完成，关闭下载任务\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'任务完成，关闭下载任务，下一次启动应用时将安装更新'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdownloadTask.abort();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdownloadTask = null;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log('下载地址未准备，无法开启下载任务');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t//不是热更新是在线更新 校验是否强制升级\r\n\t\t\t\t\t\t\t\tif (res.method == 'true') {\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\t\tconfirmText: '立即更新',\r\n\t\t\t\t\t\t\t\t\t\ttitle: '发现新版本',\r\n\t\t\t\t\t\t\t\t\t\tcontent: res.des,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.$queue.showLoading('下载中...');\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (uni.getSystemInfoSync().platform ==\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'android') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: androidLink,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: downloadResult => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (downloadResult\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.statusCode ===\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.install(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdownloadResult\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.tempFilePath, {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tforce: false\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\td => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.log(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'install success...'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.restart();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\te => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.error(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'install fail...'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (uni.getSystemInfoSync().platform ==\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'ios') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime.openURL(iosLink, function(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tres) {});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('取消');\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '发现新版本',\r\n\t\t\t\t\t\t\t\t\t\tconfirmText: '立即更新',\r\n\t\t\t\t\t\t\t\t\t\tcancelText: '下次更新',\r\n\t\t\t\t\t\t\t\t\t\tcontent: res.des,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.$queue.showLoading('下载中...');\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (uni.getSystemInfoSync().platform ==\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'android') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: androidLink,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: downloadResult => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (downloadResult\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.statusCode ===\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t200) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.install(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdownloadResult\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.tempFilePath, {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tforce: false\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\td => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.log(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'install success...'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.restart();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\te => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconsole\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.error(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'install fail...'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (uni.getSystemInfoSync().platform ==\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'ios') {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime.openURL(iosLink, function(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tres) {});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('取消');\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t//#endif\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t//小程序是否上线\r\n\t\t\tthis.$Request.get('/app/common/type/238').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.$queue.setData('XCXIsSelect', res.data.value);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t// this.$Request.get('/app/common/type/257').then(res => {\r\n\t\t\t// \tif (res.code == 0) {\r\n\t\t\t// \t\tthis.$queue.setData('XCXIsSelect', res.data.value);\r\n\t\t\t// \t}\r\n\t\t\t// });\r\n\t\t\t// #endif\r\n\r\n\t\t\t//企业微信客服链接\r\n\t\t\tthis.$Request.get('/app/common/type/274').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.$queue.setData('kefu', res.data.value);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//企业微信客服appid\r\n\t\t\tthis.$Request.get('/app/common/type/275').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.$queue.setData('kefuAppId', res.data.value);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//代理商开关 260\r\n\t\t\tthis.$Request.get('/app/common/type/260').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.$queue.setData('dailishang', res.data.value);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//客服类型  328\r\n\t\t\tthis.$Request.get('/app/common/type/328').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.$queue.setData('SelKeFu', res.data.value);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//客服电话  329\r\n\t\t\tthis.$Request.get('/app/common/type/329').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.$queue.setData('kefuPhone', res.data.value);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t//是否开启钱包充值  332\r\n\t\t\tthis.$Request.get('/app/common/type/332').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.$queue.setData('czSel', res.data.value);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tconsole.log('App Show')\r\n\r\n\t\t\t// #ifdef APP-PLUS\r\n\r\n\t\t\tif (uni.getSystemInfoSync().platform == 'android') {\r\n\t\t\t\tlet clientid = plus.push.getClientInfo().clientid;\r\n\r\n\t\t\t\tlet userId = this.$queue.getData('userId');\r\n\t\t\t\tif (userId) {\r\n\t\t\t\t\tthis.$Request.postT('/app/user/updateClientId?clientId=' + clientid + '&userId=' + userId).then(\r\n\t\t\t\t\t\tres => {});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t//#endif\r\n\t\t\t//#ifdef H5\r\n\t\t\t// let isopen = false\r\n\t\t\t// this.$Request.get('/app/common/type/237').then(res => {\r\n\t\t\t// \tif (res.data.value == '是') {\r\n\t\t\t// \t\tisopen = true\r\n\t\t\t// \t}\r\n\t\t\t// });\r\n\r\n\t\t\t// if (isopen) {\r\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\r\n\t\t\t\tlet openId = uni.getStorageSync('openId');\r\n\t\t\t\tlet userId = uni.getStorageSync('userId');\r\n\t\t\t\tconsole.log(uni.getStorageSync('openId'), '9999999openId')\r\n\t\t\t\tlet that = this;\r\n\r\n\t\t\t\tif (!openId) {\r\n\t\t\t\t\tif (window.location.href.indexOf('?code=') !== -1 || window.location.href.indexOf('&code=') !==\r\n\t\t\t\t\t\t-1) {\r\n\t\t\t\t\t\tlet code;\r\n\t\t\t\t\t\tif (window.location.href.indexOf('?code=') !== -1) {\r\n\t\t\t\t\t\t\tcode = window.location.href.split('?code=')[1].split('&')[0];\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tcode = window.location.href.split('&code=')[1].split('&')[0];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.$Request.get('/app/Login/getOpenId?code=' + code).then(ret => {\r\n\t\t\t\t\t\t\tuni.setStorageSync('openId', ret.data)\r\n\r\n\t\t\t\t\t\t\tthat.$Request.get('/app/Login/openid/login?openId=' + ret.data).then(res => {\r\n\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"userId\", res.user.userId);\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"token\", res.token);\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"phone\", res.user.phone);\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"userName\", res.user.userName);\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"avatar\", res.user.avatar);\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"invitationCode\", res.user.invitationCode);\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"inviterCode\", res.user.inviterCode);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.$Request.get('/app/common/type/108').then(res => {\r\n\t\t\t\t\t\t\tif (res.data.value == '是') {\r\n\t\t\t\t\t\t\t\twindow.location.href =\r\n\t\t\t\t\t\t\t\t\t'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +\r\n\t\t\t\t\t\t\t\t\tthat.$queue.getWxAppid() +\r\n\t\t\t\t\t\t\t\t\t'&redirect_uri=' +\r\n\t\t\t\t\t\t\t\t\twindow.location.href.split('#')[0] +\r\n\t\t\t\t\t\t\t\t\t'&response_type=code&scope=snsapi_userinfo#wechat_redirect';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (userId && openId) {\r\n\t\t\t\t\tconsole.log('9090', '--')\r\n\t\t\t\t\tthat.$Request.get('/app/Login/bindOpenId?userId=' + userId + '&openId=' + openId).then(res => {\r\n\t\t\t\t\t\t// 省钱兄陪玩 https://pw.xianmxkj.com\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// }\r\n\r\n\r\n\t\t\t//#endif\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/*每个页面公共css */\r\n\t@import \"uview-ui/index.scss\";\r\n\t@import 'components/colorui/main.css';\r\n\t@import 'components/colorui/icon.css';\r\n\r\n\tpage {\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tcolor: #343546;\r\n\t}\r\n\r\n\t.bg {\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447628102\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}