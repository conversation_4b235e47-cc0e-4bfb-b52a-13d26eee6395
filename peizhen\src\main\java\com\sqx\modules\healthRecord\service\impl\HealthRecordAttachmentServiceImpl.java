package com.sqx.modules.healthRecord.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.healthRecord.dao.HealthRecordAttachmentDao;
import com.sqx.modules.healthRecord.entity.HealthRecordAttachment;
import com.sqx.modules.healthRecord.service.HealthRecordAttachmentService;
import com.sqx.modules.oss.cloud.CloudStorageService;
import com.sqx.modules.oss.cloud.OSSFactory;
import com.sqx.modules.oss.entity.SysOssEntity;
import com.sqx.modules.oss.service.SysOssService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 健康记录附件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class HealthRecordAttachmentServiceImpl extends ServiceImpl<HealthRecordAttachmentDao, HealthRecordAttachment> implements HealthRecordAttachmentService {

    @Autowired
    private HealthRecordAttachmentDao attachmentDao;
	@Autowired
	private SysOssService sysOssService;


    @Override
    public Result uploadAttachment(Long recordId, MultipartFile file, Integer attachmentType, String description, Long uploadedBy) throws IOException {
        if (file.isEmpty()) {
            return Result.error("上传文件不能为空");
        }
            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

		String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		//上传文件
		String url = OSSFactory.build().uploadSuffix(file.getBytes(), suffix);

		//保存文件信息
		SysOssEntity ossEntity = new SysOssEntity();
		ossEntity.setUrl(url);
		ossEntity.setCreateDate(new Date());
		sysOssService.save(ossEntity);

            // 保存附件信息到数据库
            HealthRecordAttachment attachment = new HealthRecordAttachment();
            attachment.setRecordId(recordId);
            attachment.setFileName(originalFilename);
            attachment.setFileOriginalName(originalFilename);
            attachment.setFilePath(originalFilename); // OSS 中使用 fileName 作为路径
            attachment.setFileUrl(url);
            attachment.setFileSize(file.getSize());
            attachment.setFileType(getFileType(file.getContentType()));
            attachment.setFileExtension(fileExtension);
            attachment.setMimeType(file.getContentType());
            attachment.setAttachmentType(attachmentType);
            attachment.setDescription(description);
            attachment.setUploadTime(LocalDateTime.now());
            attachment.setUploadedBy(uploadedBy);
            attachment.setIsDelete(0);
            attachment.setCreateTime(LocalDateTime.now());
            attachment.setUpdateTime(LocalDateTime.now());

            attachmentDao.insert(attachment);

            return Result.success("文件上传成功").put("data", attachment);

    }

    @Override
    public List<HealthRecordAttachment> getAttachmentsByRecordId(Long recordId) {
        return attachmentDao.getAttachmentsByRecordId(recordId);
    }

    @Override
    public Result deleteAttachment(Long attachmentId, Long operatorId) {
        HealthRecordAttachment attachment = attachmentDao.selectById(attachmentId);
        if (attachment == null) {
            return Result.error("附件不存在");
        }

        attachment.setIsDelete(1);
        attachment.setUpdateTime(LocalDateTime.now());
        attachmentDao.updateById(attachment);

        // OSS 文件删除可以在这里调用 cloudStorageService.delete() 方法
        // cloudStorageService.delete(attachment.getFilePath());

        return Result.success("附件删除成功");
    }

    @Override
    public Result deleteAttachmentsByRecordId(Long recordId, Long operatorId) {
        int count = attachmentDao.deleteAttachmentsByRecordId(recordId, operatorId);
        return Result.success("删除了 " + count + " 个附件");
    }

    @Override
    public HealthRecordAttachment getAttachmentForDownload(Long attachmentId) {
        return attachmentDao.selectById(attachmentId);
    }

    /**
     * 根据MIME类型判断文件类型
     */
    private String getFileType(String mimeType) {
        if (mimeType == null) {
            return "other";
        }

        if (mimeType.startsWith("image/")) {
            return "image";
        } else if (mimeType.startsWith("video/")) {
            return "video";
        } else if (mimeType.startsWith("audio/")) {
            return "audio";
        } else if (mimeType.contains("pdf") || mimeType.contains("document") ||
                   mimeType.contains("text") || mimeType.contains("sheet")) {
            return "document";
        } else {
            return "other";
        }
    }
}
