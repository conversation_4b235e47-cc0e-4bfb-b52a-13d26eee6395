-- AI流式消息表
CREATE TABLE `ai_stream_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(100) NOT NULL COMMENT '流式任务ID',
  `conversation_id` bigint(20) NOT NULL COMMENT '对话ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `message_id` bigint(20) DEFAULT NULL COMMENT '消息ID（关联ai_message表）',
  `chunk_index` int(11) NOT NULL COMMENT '分块序号，从1开始',
  `chunk_content` text COMMENT '分块内容',
  `accumulated_content` longtext COMMENT '累积内容（从开始到当前分块的完整内容）',
  `is_last` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为最后一个分块',
  `status` varchar(20) NOT NULL DEFAULT 'processing' COMMENT '任务状态：processing-处理中, completed-已完成, error-错误, cancelled-已取消',
  `error_message` text COMMENT '错误信息（如果有）',
  `create_time` varchar(20) COMMENT '创建时间',
  `update_time` varchar(20)  COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_task_chunk` (`task_id`, `chunk_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI流式消息表';

-- 添加索引优化查询性能
ALTER TABLE `ai_stream_message` ADD INDEX `idx_task_status` (`task_id`, `status`);
ALTER TABLE `ai_stream_message` ADD INDEX `idx_user_task` (`user_id`, `task_id`);
