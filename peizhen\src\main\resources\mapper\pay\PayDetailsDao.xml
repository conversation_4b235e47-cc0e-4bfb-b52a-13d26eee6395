<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.pay.dao.PayDetailsDao">

	<select id="selectById" resultType="com.sqx.modules.pay.entity.PayDetails">
		  select * from pay_details d where d.id=#{id}
	</select>

	<select id="selectByRemark" resultType="com.sqx.modules.pay.entity.PayDetails">
		select * from pay_details d where d.remark=#{remark}
	</select>

	<select id="selectByOrderId" resultType="com.sqx.modules.pay.entity.PayDetails">
		  select * from pay_details d where d.order_id=#{orderId}
	</select>

	<update id="updateState">
		update pay_details set `state`=#{state},pay_time=#{time},trade_no=#{tradeNo} where id=#{id}
	</update>


	<select id="selectPayDetails" resultType="Map">
		select s.id,s.classify,s.order_id as orderId,s.money,s.user_id as userId,
		s.state,s.create_time as createTime,s.pay_time as payTime,u.user_name as userName,u.phone
		from pay_details s
		left join tb_user u on u.user_id=s.user_id
		where 1=1
		<if test="startTime!=null and startTime!=''and endTime!=null and endTime!='' ">
			and  str_to_date(s.create_time, '%Y-%m-%d') between str_to_date(#{startTime}, '%Y-%m-%d') AND str_to_date(#{endTime}, '%Y-%m-%d')
		</if>
		<if test="userId!=null">
			and  u.user_id=#{userId}
		</if>
		<if test="state!=null and state!=-1">
			and s.state=#{state}
		</if>
		<if test="state==null or state==-1">
			and s.state!=-1
		</if>
		group by s.id
		order by s.create_time desc
	</select>


	<select id="selectSumPay" resultType="Double">
		select sum(money) from pay_details
		where str_to_date(create_time, '%Y-%m-%d') BETWEEN str_to_date(#{createTime}, '%Y-%m-%d') AND str_to_date(#{endTime}, '%Y-%m-%d')
		<if test="userId!=null">
			and user_id=#{userId}
		</if>
		and state=1
	</select>

	<select id="selectSumMember" resultType="Double">
		select sum(money) from pay_details where state=1 and `type`=2
		<if test="flag!=null and flag==1">
			and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(create_time,'%Y')=date_format(#{time},'%Y')
		</if>
	</select>

	<select id="selectSumPayByState" resultType="Double">
		select sum(money) from pay_details where  1=1
		<if test="state!=null">
			and state=#{state}
		</if>
		<if test="flag!=null and flag==1">
			and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(create_time,'%Y')=date_format(#{time},'%Y')
		</if>
	</select>

	<select id="selectSumPayByClassify" resultType="Double">
		select sum(money) from pay_details where  1=1
		<if test="classify!=null">
			and classify=#{classify}
		</if>
		<if test="flag!=null and flag==1">
			and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(create_time,'%Y')=date_format(#{time},'%Y')
		</if>
		<if test="payClassify!=null and payClassify!=0">
			and pay_classify=#{payClassify}
		</if>
		and state=1
	</select>


	<select id="payMemberAnalysis" resultType="Map">
		SELECT
			d.classify,d.money,d.pay_time as payTime,d.remark,d.user_id as userId,u.user_name as nickName
		FROM
			`pay_details` d
			left join tb_user u on u.user_id=d.user_id
		WHERE
			d.type = 2
			AND d.state =1
		<if test="flag!=null and flag==1">
			and date_format(d.create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(d.create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(d.create_time,'%Y')=date_format(#{time},'%Y')
		</if>
		order by d.pay_time desc
	</select>
     <select id="instantselectSumPay" resultType="Double">
		 select sum(money) from pay_details where  1=1
			 and date_format(create_time,'%Y-%m')=date_format(#{date},'%Y-%m')
		 and state=1 and user_id=#{userId}
	 </select>


	<select id="selectUserMemberList" resultType="Map">
		select s.classify,s.money,s.remark,u.nick_name as nickName,u.user_id as userId,u.member from pay_details s
		left join tb_user u on s.user_id=u.user_id
		where s.state=-1 and s.`type`=2
		<if test="phone!=null and phone!=''">
			and u.phone=#{phone}
		</if>
		order by s.create_time desc
	</select>


	<select id="selectPayCount" resultType="int">
		select count(*) from pay_details
		where `state`=1 and user_id=#{userId}
	</select>


</mapper>