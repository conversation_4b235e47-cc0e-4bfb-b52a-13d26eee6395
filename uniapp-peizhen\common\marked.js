// marked.js - Markdown parser and compiler
// 简化版的marked实现，专门用于微信小程序

function marked(src) {
  // 基本的markdown语法转换
  let html = src
    // 处理标题
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    
    // 处理强调
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // 处理列表
    .replace(/^\s*\n\*/gm, '<ul>\n*')
    .replace(/^(\*.+)\s*\n([^\*])/gm, '$1\n</ul>\n\n$2')
    .replace(/^\*(.+)/gm, '<li>$1</li>')
    
    // 处理有序列表
    .replace(/^\s*\n\d\./gm, '<ol>\n1.')
    .replace(/^(\d\..+)\s*\n([^\d\.])/gm, '$1\n</ol>\n\n$2')
    .replace(/^\d\.(.+)/gm, '<li>$1</li>')
    
    // 处理引用
    .replace(/^\> (.*$)/gm, '<blockquote>$1</blockquote>')
    
    // 处理代码块
    .replace(/```([\s\S]*?)```/g, function(match, code) {
      return '<pre><code>' + code.trim() + '</code></pre>';
    })
    
    // 处理行内代码
    .replace(/`(.*?)`/g, '<code>$1</code>')
    
    // 处理链接
    .replace(/\[([^\]]+)\]\(([^\)]+)\)/g, '<a href="$2">$1</a>')
    
    // 处理图片
    .replace(/!\[([^\]]+)\]\(([^\)]+)\)/g, '<img src="$2" alt="$1">')
    
    // 处理分隔线
    .replace(/^\-\-\-$/gm, '<hr>')
    
    // 处理段落
    .replace(/^\s*(\n)?(.+)/gm, function(match) {
      return /\<(\/)?(h\d|ul|ol|li|blockquote|pre|img)/.test(match) ? match : '<p>' + match + '</p>';
    })
    
    // 清理空行
    .replace(/^\s*\n\s*$/gm, '');

  // 添加表格支持
  html = html.replace(/\|(.+)\|/g, function(match, content) {
    const cells = content.split('|').map(cell => cell.trim());
    return '<tr>' + cells.map(cell => '<td>' + cell + '</td>').join('') + '</tr>';
  });
  
  // 将表格行包装在table标签中
  if (html.includes('<tr>')) {
    html = '<table>' + html + '</table>';
  }

  return html;
}

module.exports = marked; 