<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.app.dao.UserMoneyDetailsDao">
    <select id="monthIncome" resultType="Double">
        select sum(money)
        from user_money_details
        where user_id = #{userId}
          and classify = 4
          and type = 2
          and state = 2
          and date_format(create_time, '%Y-%m') = date_format(#{date}, '%Y-%m')
    </select>
    <select id="selectMyProfit" resultType="Double">
        select sum(money)
        from user_money_details
        where user_id = #{userId}
          and classify!=3
    </select>
    <select id="getRankingList" resultType="java.util.HashMap">
        SELECT SUM(d.money) AS money,
               u.user_id,
               u.user_name,
               u.avatar
        FROM user_money_details d,
             tb_user u
        WHERE d.user_id = u.user_id
          AND d.classify = #{classify}
        GROUP BY d.user_id
        ORDER BY money DESC
    </select>

    <select id="getAgentProfitList" resultType="java.util.HashMap">
        SELECT u.user_name AS userName,
               u.avatar,
               d.money,
               d.create_time AS createTime
        FROM user_money_details d,
             tb_user u
        WHERE u.user_id = d.by_user_id
          AND classify = 20
          AND d.user_id = #{userId}
        ORDER BY d.create_time DESC
    </select>

</mapper>