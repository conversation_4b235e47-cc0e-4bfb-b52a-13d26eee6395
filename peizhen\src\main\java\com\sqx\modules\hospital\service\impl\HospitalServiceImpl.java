package com.sqx.modules.hospital.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.service.DepartmentService;
import com.sqx.modules.hospital.dao.HospitalDao;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.entity.LetterCity;
import com.sqx.modules.hospital.service.HospitalService;
import com.sqx.modules.utils.PinYinUti;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Service
public class HospitalServiceImpl extends ServiceImpl<HospitalDao, Hospital> implements HospitalService {

    @Autowired
    private HospitalDao hospitalDao;
    @Autowired
    private DepartmentService departmentService;

    @Override
    public Result addHospital(Hospital hospital) {
        Integer count = hospitalDao.selectCount(new QueryWrapper<Hospital>().eq("hospital_name", hospital.getHospitalName()));
        if (count > 0) {
            return Result.error("医院名称不能重复");
        }
        hospital.setCityInitial(PinYinUti.getFirstPinYin(hospital.getCity()));
        return Result.upStatus(hospitalDao.insert(hospital));
    }

    @Override
    public Result updateHospital(Hospital hospital) {
        if (hospital.getHospitalId() == null) {
            return Result.error("医院id不能为空!");
        }
        Integer count = hospitalDao.selectCount(new QueryWrapper<Hospital>().eq("hospital_name", hospital.getHospitalName()).ne("hospital_id", hospital.getHospitalId()));
        if (count > 0) {
            return Result.error("医院名称不能重复");
        }
        hospital.setCityInitial(PinYinUti.getFirstPinYin(hospital.getCity()));
        return Result.upStatus(hospitalDao.updateById(hospital));

    }

    @Override
    public IPage<Hospital> getHospitalList(Integer page, Integer limit, Hospital hospital) {
        Page<Hospital> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return hospitalDao.selectPage(pages, new QueryWrapper<>(hospital).orderByAsc("sort"));
    }

    @Override
    public Hospital getHospitalInfo(Long hospitalId) {
        return hospitalDao.selectById(hospitalId);
    }

    @Override
    public Result deleteHospital(Long hospitalId) {
        int count = departmentService.count(new QueryWrapper<Department>().eq("hospital_id", hospitalId));
        if (count > 0) {
            return Result.error("请先删除医院所属科室");
        }
        hospitalDao.deleteById(hospitalId);
        departmentService.remove(new QueryWrapper<Department>().eq("hospital_id", hospitalId));
        log.error("删除医院成功:医院id为:" + hospitalId);
        return Result.success();
    }

    @Override
    public List<LetterCity> getCityList() {
        List<LetterCity> letterCityList = hospitalDao.getLetter();
        for (LetterCity letterCity : letterCityList) {
            letterCity.setCity(hospitalDao.getCityList(letterCity.getLetter()));
        }
        return letterCityList;
    }

    @Override
    public List<Hospital> getHospitalOrderCount(String riderPhone, String hospitalName, String city, String ordersNo) {

        return baseMapper.getHospitalOrderCount(riderPhone, hospitalName, city, ordersNo);


    }
}
