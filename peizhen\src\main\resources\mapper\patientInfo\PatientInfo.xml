<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.patientInfo.dao.PatientInfoDao">
    <select id="getPatientList" resultType="com.sqx.modules.patientInfo.entity.PatientInfo">
        select i.*,u.user_name as userName from patient_info i,tb_user u where
        u.user_id = i.user_id and i.is_delete=0
        <if test="patientInfo.phone!=null and patientInfo.phone!=''">
            and i.phone = #{patientInfo.phone}
        </if>
        <if test="patientInfo.userId!=null">
            and i.user_id = #{patientInfo.userId}
        </if>
        <if test="patientInfo.userName!=null  and patientInfo.userName!=''">
            AND (u.user_name LIKE CONCAT("%",#{patientInfo.userName},"%") OR i.real_name LIKE CONCAT("%",#{patientInfo.userName},"%"))
        </if>
    </select>
</mapper>
