package com.sqx.modules.hospitalEmploy.controller.app;


import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalEmploy.entity.HospitalEmploy;
import com.sqx.modules.hospitalEmploy.service.HospitalEmployService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务管理-用户端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@RestController
@Api(value = "服务管理-用户端", tags = {"服务管理-用户端"})
@RequestMapping("/app/hospitalEmploy/")
public class AppHospitalEmployController {
    @Autowired
    private HospitalEmployService employService;

    /**
     * 查看服务详情
     *
     * @param serviceId 服务id
     * @return
     */
    @ApiOperation("查看服务详情")
    @GetMapping("getHospitalEmployInfo")
    public Result getHospitalEmployInfo(Long serviceId) {
        return Result.success().put("data", employService.getHospitalEmployInfo(serviceId));
    }

    /**
     * 查看服务列表
     *
     * @param page
     * @param limit
     * @param hospitalEmploy
     * @return
     */
    @ApiOperation("查看服务列表")
    @GetMapping("getHospitalEmployList")
    public Result getHospitalEmployList(Integer page, Integer limit, HospitalEmploy hospitalEmploy) {
        hospitalEmploy.setIsEnable(1);
        return Result.success().put("data", employService.getHospitalEmployList(page, limit, hospitalEmploy));
    }

    @ApiOperation("获取服务列表")
    @GetMapping("getEmployList")
    public Result getEmployList(Integer authentication) {
        return Result.success().put("data", employService.getEmployList(authentication));
    }
}

