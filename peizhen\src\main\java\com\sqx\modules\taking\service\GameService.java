package com.sqx.modules.taking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.taking.entity.Game;
import io.swagger.annotations.ApiParam;

public interface GameService extends IService<Game> {

    /**
     * 查询首页游戏分类
     *
     * @return
     */
    Result queryGameName();


    /**
     * 添加游戏分类
     *
     * @param
     * @return
     */
    Result addGameName(String gameName,String gameImg);

    /**
     * 修改游戏分类
     *
     * @param
     * @return
     */
    Result updateGameName(Long id, String gameName,String gameImg, Long status);

    /**
     * 删除游戏分类
     *
     * @param id
     * @return
     */
    Result deleteGameName(Long id);

    /**
     * 查看所有游戏分类信息
     *
     * @param page
     * @param limit
     * @return
     */
    Result queryAllGameName(Long page, Long limit);

    /**
     * 是否启用游戏分类
     */
    Result enableGameName(Long status, Long id);
}
