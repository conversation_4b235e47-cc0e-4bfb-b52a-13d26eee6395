{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/userphone.vue?dda7", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/userphone.vue?83a2", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/userphone.vue?5919", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/userphone.vue?a9c5", "uni-app:///my/setting/userphone.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/userphone.vue?2604", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/userphone.vue?7c5f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "code", "phone", "password", "sending", "sendTime", "count", "logining", "methods", "sendMsg", "uni", "showCancel", "title", "content", "countDown", "setTimeout", "inputChange", "navBack", "navTo", "url", "<PERSON><PERSON><PERSON><PERSON>", "userId", "msg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwuB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqB5vB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MAAA;MACA,IACAP,QACA,KADAA;MAEA;QACA;MACA;QACA;MACA;QACA;QACA;UACA;YACA;YACA;YACA;YACAQ;UACA;YACAA;YACAA;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MACA,IACAR,QACA,KADAA;MAEA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACAS;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAP;IACA;IAGAQ;MACAR;QACAS;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;QACA,IACAlB,QAGA,KAHAA;UACAC,WAEA,KAFAA;UACAF,OACA,KADAA;QAEA;UACA;QACA;UACA;UACA;UACA;YACAoB;YACAnB;YACAoB;UACA;YACAZ;YACA;cACAA;gBACAS;cACA;YACA;cACAT;gBACAC;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjIA;AAAA;AAAA;AAAA;AAA23C,CAAgB,guCAAG,EAAC,C;;;;;;;;;;;ACA/4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/setting/userphone.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/setting/userphone.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userphone.vue?vue&type=template&id=aa10ec22&\"\nvar renderjs\nimport script from \"./userphone.vue?vue&type=script&lang=js&\"\nexport * from \"./userphone.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userphone.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/setting/userphone.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userphone.vue?vue&type=template&id=aa10ec22&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userphone.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userphone.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"wrapper\">\n\t\t\t<view class=\"input-content\">\n\t\t\t\t<view class=\"cu-form-group\" style=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\n\t\t\t\t\t<view class=\"title\">手机号</view>\n\t\t\t\t\t<input type=\"number\" :value=\"phone\" placeholder=\"请输入新手机号\" maxlength=\"11\" data-key=\"phone\" @input=\"inputChange\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cu-form-group\" style=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\n\t\t\t\t\t<text class=\"title\">验证码</text>\n\t\t\t\t\t<input type=\"number\" :value=\"code\" placeholder=\"请输入验证码\" maxlength=\"6\" data-key=\"code\" @input=\"inputChange\"\n\t\t\t\t\t @confirm=\"toLogin\" />\n\t\t\t\t\t<button class=\"send-msg\" @click=\"sendMsg\" :disabled=\"sending\">{{sendTime}}</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<button class=\"confirm-btn\" @click=\"toLogin\">保存</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcode: '',\n\t\t\t\tphone: '',\n\t\t\t\tpassword: '',\n\t\t\t\tsending: false,\n\t\t\t\tsendTime: '获取验证码',\n\t\t\t\tcount: 60,\n\t\t\t\tlogining: false\n\t\t\t}\n\t\t},\n\n\t\tmethods: {\n\t\t\tsendMsg() {\n\t\t\t\tconst {\n\t\t\t\t\tphone\n\t\t\t\t} = this;\n\t\t\t\tif (!phone) {\n\t\t\t\t\tthis.$queue.showToast(\"请输入手机号\");\n\t\t\t\t} else if (phone.length !== 11) {\n\t\t\t\t\tthis.$queue.showToast(\"请输入正确的手机号\");\n\t\t\t\t} else {\n\t\t\t\t\tthis.$queue.showLoading(\"正在发送验证码...\");\n\t\t\t\t\tthis.$Request.getT(\"/msg/sendMsg/\" + phone + \"/bind\").then(res => {\n\t\t\t\t\t\tif (res.status === 0) {\n\t\t\t\t\t\t\tthis.sending = true;\n\t\t\t\t\t\t\tthis.$queue.showToast('验证码发送成功请注意查收');\n\t\t\t\t\t\t\tthis.countDown();\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\ttitle: '短信发送失败',\n\t\t\t\t\t\t\t\tcontent: res.msg ? res.msg : '请一分钟后再获取验证码'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tcountDown() {\n\t\t\t\tconst {\n\t\t\t\t\tcount\n\t\t\t\t} = this;\n\t\t\t\tif (count === 1) {\n\t\t\t\t\tthis.count = 60;\n\t\t\t\t\tthis.sending = false;\n\t\t\t\t\tthis.sendTime = '获取验证码'\n\t\t\t\t} else {\n\t\t\t\t\tthis.count = count - 1;\n\t\t\t\t\tthis.sending = true;\n\t\t\t\t\tthis.sendTime = count - 1 + '秒后重新获取';\n\t\t\t\t\tsetTimeout(this.countDown.bind(this), 1000);\n\t\t\t\t}\n\t\t\t},\n\t\t\tinputChange(e) {\n\t\t\t\tconst key = e.currentTarget.dataset.key;\n\t\t\t\tthis[key] = e.detail.value;\n\t\t\t},\n\t\t\tnavBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\n\n\t\t\tnavTo(url) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl\n\t\t\t\t})\n\t\t\t},\n\t\t\ttoLogin() {\n\t\t\t\tif(this.code == ''){\n\t\t\t\t\tthis.$queue.showToast(\"请输入验证码\");\n\t\t\t\t}else{\n\t\t\t\t\tlet userId = this.$queue.getData(\"userId\");\n\t\t\t\t\tconst {\n\t\t\t\t\t\tphone,\n\t\t\t\t\t\tpassword,\n\t\t\t\t\t\tcode\n\t\t\t\t\t} = this;\n\t\t\t\t\tif (!phone) {\n\t\t\t\t\t\tthis.$queue.showToast(\"请输入手机号\");\n\t\t\t\t\t}else {\n\t\t\t\t\t\tthis.logining = true;\n\t\t\t\t\t\tthis.$queue.showLoading(\"加载中...\");\n\t\t\t\t\t\tthis.$Request.getT(\"/user/changePhone\", {\n\t\t\t\t\t\t\tuserId : userId ,\n\t\t\t\t\t\t\tphone: phone,\n\t\t\t\t\t\t\tmsg: code\n\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tif (res.status === 0) {\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/my/userstatus'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\t\t\ttitle: '绑定手机号失败',\n\t\t\t\t\t\t\t\t\tcontent: res.msg,\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\n\t}\n</script>\n\n<style lang='scss'>\n\tpage {\n\t\tbackground: #1c1b20;\n\t}\n\n\t.send-msg {\n\t\tborder-radius: 30px;\n\t\tcolor: white;\n\t\theight: 30px;\n\t\tfont-size: 14px;\n\t\tline-height: 30px;\n\t\tbackground: #5E81F9;\n\t}\n\n\t.container {\n\t\tpadding-top: 32upx;\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: hidden;\n\t\tbackground: #1c1b20;\n\t}\n\n\t.wrapper {\n\t\tposition: relative;\n\t\tz-index: 90;\n\t\tbackground: #1c1b20;\n\t\tpadding-bottom: 20px;\n\t}\n\n\n\t.input-content {\n\t\tpadding: 32upx 80upx;\n\t}\n\n\n\t.confirm-btn {\n\t\twidth: 600upx;\n\t\theight: 80upx;\n\t\tline-height: 80upx;\n\t\tborder-radius: 60upx;\n\t\tmargin-top: 32upx;\n\t\tbackground: #5E81F9;\n\t\tcolor: #fff;\n\t\tfont-size: 32upx;\n\n\t\t&:after {\n\t\t\tborder-radius: 60px;\n\t\t}\n\t}\n\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userphone.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userphone.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627444\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}