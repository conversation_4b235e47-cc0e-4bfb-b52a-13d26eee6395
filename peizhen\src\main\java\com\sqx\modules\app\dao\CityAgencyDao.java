package com.sqx.modules.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.app.entity.CityAgency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @date 2021/7/19
 */
@Mapper
public interface CityAgencyDao extends BaseMapper<CityAgency> {

    IPage<CityAgency> selectCityAgencyList(Page<CityAgency> page,@Param("userName") String userName,@Param("phone") String phone,@Param("classify") Integer classify);

}
