package com.sqx.modules.rewardLevel.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 分佣比例奖励表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Data
public class RewardLevel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 等级id
     */
    @ApiModelProperty("等级id")
    @TableId(value = "reward_id", type = IdType.AUTO)
    private Long rewardId;

    /**
     * 分佣比例
     */
    @ApiModelProperty("分佣比例")
    private BigDecimal scale;
    /**
     * 等级名称
     */
    @ApiModelProperty("等级名称")
    private String levelName;
    /**
     * 等级图标
     */
    @ApiModelProperty("等级图标")
    private String iconImg;

    /**
     * 是否开启 1是 0否
     */
    @ApiModelProperty("是否开启 1是 0否")
    private Integer isEnable;
    /**
     * 需完成订单数
     */
    @ApiModelProperty("需完成订单数")
    private Integer orderCount;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


}
