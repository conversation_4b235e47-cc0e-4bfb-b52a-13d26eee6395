package com.sqx.modules.evaluate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.Result;
import com.sqx.modules.evaluate.entity.SysEvaluate;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
public interface SysEvaluateService extends IService<SysEvaluate> {

    Result addEvaluate(SysEvaluate evaluate);

    IPage<SysEvaluate> getUserEvaluateList(Integer page, Integer limit, SysEvaluate evaluate);

    /**
     *
     * @param userId
     * @param evaluateId
     * @param auth 1管理员删除 2用户删除
     * @return
     */
    Result deleteEvaluate(Long userId, Long evaluateId,Integer auth);
}
