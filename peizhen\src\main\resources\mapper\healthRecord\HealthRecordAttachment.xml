<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.healthRecord.dao.HealthRecordAttachmentDao">
    
    <!-- 根据健康记录ID查询附件列表 -->
    <select id="getAttachmentsByRecordId" resultType="com.sqx.modules.healthRecord.entity.HealthRecordAttachment">
        SELECT 
            attachment_id,
            record_id,
            file_name,
            file_original_name,
            file_path,
            file_url,
            file_size,
            file_type,
            file_extension,
            mime_type,
            attachment_type,
            description,
            upload_time,
            uploaded_by,
            is_delete,
            create_time,
            update_time
        FROM health_record_attachments
        WHERE record_id = #{recordId} AND is_delete = 0
        ORDER BY upload_time DESC
    </select>

    <!-- 根据健康记录ID删除所有附件（逻辑删除） -->
    <update id="deleteAttachmentsByRecordId">
        UPDATE health_record_attachments 
        SET is_delete = 1, update_time = NOW()
        WHERE record_id = #{recordId} AND is_delete = 0
    </update>

</mapper>
