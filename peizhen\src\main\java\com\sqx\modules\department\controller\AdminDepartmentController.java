package com.sqx.modules.department.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.common.annotation.SysLog;
import com.sqx.common.utils.Result;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.service.DepartmentService;
import com.sqx.modules.department.vo.DepartmentOut;
import com.sqx.modules.pay.entity.CashOut;
import com.sqx.modules.utils.EasyPoi.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
/**
 * 科室管理-管理端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@RestController
@Api(value = "科室管理-管理端", tags = {"科室管理-管理端"})
@RequestMapping(value = "/admin/department/")
@Slf4j
public class AdminDepartmentController {
    @Autowired
    private DepartmentService departmentService;

    /**
     * 添加科室
     * @param department
     * @return
     */
    @ApiOperation("添加科室")
    @PostMapping("addDepartment")
    public Result addDepartment(Department department) {
        return departmentService.addDepartment(department);
    }

    /**
     * 修改科室
     * @param department
     * @return
     */
    @ApiOperation("修改科室")
    @PostMapping("updateDepartment")
    public Result updateDepartment(Department department) {
        return departmentService.updateDepartment(department);
    }

    /**
     * 获取科室列表
     * @param department
     * @return
     */
    @ApiOperation("获取科室列表")
    @GetMapping("getDepartmentList")
    public Result getDepartmentList(Department department) {
        return Result.success().put("data", departmentService.getDepartmentList(department));
    }

    /**
     * 删除科室
     * @param departmentId 科室id
     * @return
     */
    @SysLog("删除科室")
    @ApiOperation("删除科室")
    @GetMapping("deleteDepartment")
    public Result deleteDepartment(Long departmentId) {
        return departmentService.deleteDepartment(departmentId);
    }

    /**
     * 根据医院ID获取科室列表（不分页）
     * @param hospitalId 医院id
     * @return
     */
    @ApiOperation("根据医院ID获取科室列表")
    @GetMapping("selectDepartmentList")
    public Result selectDepartmentList(Long hospitalId) {
        QueryWrapper<Department> wrapper = new QueryWrapper<Department>()
                .eq("is_enable", 1)
                .orderByAsc("sort")
                .orderByDesc("create_time");

        if (hospitalId != null) {
            wrapper.eq("hospital_id", hospitalId);
        }

        List<Department> departmentList = departmentService.list(wrapper);
        return Result.success().put("data", departmentList);
    }

    /**
     * 获取科室详情
     * @param departmentId 科室id
     * @return
     */
    @ApiOperation("获取科室详情")
    @GetMapping("getDepartmentInfo")
    public Result getDepartmentInfo(Long departmentId) {
        return Result.success().put("data", departmentService.getDepartmentInfo(departmentId));
    }

    /**
     * 获取科室列表
     * @param page
     * @param limit
     * @param department
     * @return
     */
    @ApiOperation("获取科室列表")
    @GetMapping("getDepartmentPageList")
    public Result getDepartmentPageList(Integer page, Integer limit, Department department) {
        return Result.success().put("data", departmentService.getDepartmentPageList(page, limit, department));
    }

    /**
     * 科室信息列表--导出
     * @param department
     * @param startTime 筛选开始时间
     * @param endTime 筛选结束时间
     * @param response
     * @throws IOException
     */
    @ApiOperation("科室信息列表--导出")
    @GetMapping("/departmentExcelOut")
    public void departmentExcelOut(Department department, String startTime, String endTime, HttpServletResponse response) throws IOException {
        List<DepartmentOut> list = departmentService.departmentExcelOut(startTime, endTime, department);
        ExcelUtils.exportExcel(list, "科室统计表", "科室统计Sheet", DepartmentOut.class, "科室统计表", response);
    }

    /**
     * 科室信息列表--导入
     * @param file
     * @param hospitalId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "科室信息列表--导入")
    @PostMapping(value = "/departmentListExcelIn")
    public Result departmentListExcelIn(@ApiParam(name = "file", value = "excel文件") @RequestPart MultipartFile file, Long hospitalId) throws Exception {
        try {
            if (file == null) {
                return Result.error("文件不能为空！");
            }
            return departmentService.departmentListExcelIn(file, hospitalId);
        } catch (Exception e) {
            log.error("科室信息列表--导入异常：", e);
        }

        // 返回结果
        return Result.error("导入失败");
    }
}

