package com.sqx.modules.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.entity.AiConversation;

import java.util.Map;

/**
 * AI对话会话服务接口
 */
public interface AiConversationService extends IService<AiConversation> {

    /**
     * 创建新的对话会话
     */
    Result createConversation(Long userId, String modelCode, String title);

    /**
     * 获取用户的对话会话列表
     */
    Result getUserConversations(Long userId, Integer page, Integer limit);

    /**
     * 获取对话会话详情
     */
    Result getConversationDetail(Long conversationId, Long userId);

    /**
     * 更新对话标题
     */
    Result updateConversationTitle(Long conversationId, Long userId, String title);

    /**
     * 删除对话会话
     */
    Result deleteConversation(Long conversationId, Long userId);

    /**
     * 切换对话使用的模型
     */
    Result switchModel(Long conversationId, Long userId, String modelCode);

    /**
     * 更新会话统计信息
     */
    void updateConversationStats(Long conversationId);

    /**
     * 管理后台分页查询对话会话
     */
    PageUtils queryPage(Map<String, Object> params);
}
