{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-line/u-line.vue?75a3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-line/u-line.vue?d811", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-line/u-line.vue?eb63", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-line/u-line.vue?1142", "uni-app:///uview-ui/components/u-line/u-line.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-line/u-line.vue?e748", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-line/u-line.vue?5a77"], "names": ["name", "props", "color", "type", "default", "length", "direction", "hairLine", "margin", "borderStyle", "computed", "lineStyle", "style"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAovB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACOxwB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACAC;MACA;MACAC;MACA;MACA;QACA;QACAA;QACAA;QACAA;QACA;MACA;QACA;QACAA;QACAA;QACAA;QACA;MACA;MACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1EA;AAAA;AAAA;AAAA;AAA26C,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACA/7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-line/u-line.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-line.vue?vue&type=template&id=60c80200&scoped=true&\"\nvar renderjs\nimport script from \"./u-line.vue?vue&type=script&lang=js&\"\nexport * from \"./u-line.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-line.vue?vue&type=style&index=0&id=60c80200&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"60c80200\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-line/u-line.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line.vue?vue&type=template&id=60c80200&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.lineStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-line\" :style=\"[lineStyle]\">\n\t\t\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * line 线条\n\t * @description 此组件一般用于显示一根线条，用于分隔内容块，有横向和竖向两种模式，且能设置0.5px线条，使用也很简单\n\t * @tutorial https://www.uviewui.com/components/line.html\n\t * @property {String} color 线条的颜色(默认#e4e7ed)\n\t * @property {String} length 长度，竖向时表现为高度，横向时表现为长度，可以为百分比，带rpx单位的值等\n\t * @property {String} direction 线条的方向，row-横向，col-竖向(默认row)\n\t * @property {String} border-style 线条的类型，solid-实线，dashed-方形虚线，dotted-圆点虚线(默认solid)\n\t * @property {Boolean} hair-line 是否显示细线条(默认true)\n\t * @property {String} margin 线条与上下左右元素的间距，字符串形式，如\"30rpx\"\n\t * @example <u-line color=\"red\"></u-line>\n\t */\n\texport default {\n\t\tname: 'u-line',\n\t\tprops: {\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#e4e7ed'\n\t\t\t},\n\t\t\t// 长度，竖向时表现为高度，横向时表现为长度，可以为百分比，带rpx单位的值等\n\t\t\tlength: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '100%'\n\t\t\t},\n\t\t\t// 线条方向，col-竖向，row-横向\n\t\t\tdirection: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'row'\n\t\t\t},\n\t\t\t// 是否显示细边框\n\t\t\thairLine: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 线条与上下左右元素的间距，字符串形式，如\"30rpx\"、\"20rpx 30rpx\"\n\t\t\tmargin: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '0'\n\t\t\t},\n\t\t\t// 线条的类型，solid-实线，dashed-方形虚线，dotted-圆点虚线\n\t\t\tborderStyle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'solid'\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tlineStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\tstyle.margin = this.margin;\n\t\t\t\t// 如果是水平线条，边框高度为1px，再通过transform缩小一半，就是0.5px了\n\t\t\t\tif(this.direction == 'row') {\n\t\t\t\t\t// 此处采用兼容分开写，兼容nvue的写法\n\t\t\t\t\tstyle.borderBottomWidth = '1px';\n\t\t\t\t\tstyle.borderBottomStyle = this.borderStyle;\n\t\t\t\t\tstyle.width = this.$u.addUnit(this.length);\n\t\t\t\t\tif(this.hairLine) style.transform = 'scaleY(0.5)';\n\t\t\t\t} else {\n\t\t\t\t\t// 如果是竖向线条，边框宽度为1px，再通过transform缩小一半，就是0.5px了\n\t\t\t\t\tstyle.borderLeftWidth = '1px';\n\t\t\t\t\tstyle.borderLeftStyle = this.borderStyle;\n\t\t\t\t\tstyle.height = this.$u.addUnit(this.length);\n\t\t\t\t\tif(this.hairLine) style.transform = 'scaleX(0.5)';\n\t\t\t\t}\n\t\t\t\tstyle.borderColor = this.color;\n\t\t\t\treturn style;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t@import \"../../libs/css/style.components.scss\";\n\t\n\t.u-line {\n\t\tvertical-align: middle;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line.vue?vue&type=style&index=0&id=60c80200&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line.vue?vue&type=style&index=0&id=60c80200&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627846\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}