package com.sqx.modules.task.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.task.entity.HelpOrder;
import com.sqx.modules.task.entity.HelpTake;
import com.sqx.modules.task.service.HelpOrderService;
import com.sqx.modules.task.service.HelpTakeService;
import com.sqx.modules.utils.excel.ExcelData;
import com.sqx.modules.utils.excel.ExportExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021/1/8
 */
@RestController
@Api(value = "跑腿订单", tags = {"跑腿订单"})
@RequestMapping(value = "/help")
public class HelpController {


    @Autowired
    private HelpOrderService helpOrderService;
    @Autowired
    private HelpTakeService helpTakeService;


    @PostMapping("/saveHelpOrder")
    @ApiOperation("发布跑腿订单")
    @ResponseBody
    public Result saveHelpOrder(@RequestBody HelpOrder helpOrder){
        return helpOrderService.saveBodys(helpOrder);
    }


    @PostMapping("/updateHelpOrderByStatus/{ids}/{status}/{content}")
    @ApiOperation("审核跑腿订单")
    @ResponseBody
    public Result updateHelpOrderByStatus(@PathVariable("ids") String ids,@PathVariable("status") Integer status,@PathVariable("content") String content){
        return helpOrderService.updateHelpOrderByStatus(ids, status, content);
    }

    @PostMapping("/updateHelpOrder")
    @ApiOperation("完善需求")
    @ResponseBody
    public Result updateHelpOrder(@RequestBody HelpOrder helpOrder){
        return helpOrderService.updateHelpOrderByIds(helpOrder);
    }

    @PostMapping("/deleteHelpOrder")
    @ApiOperation("删除")
    @ResponseBody
    public Result deleteHelpOrder(Long helpOrderId){
        return helpOrderService.deleteById(helpOrderId);
    }


    @PostMapping("/closeOrder")
    @ApiOperation("确认送达")
    @ResponseBody
    public Result closeOrder(Long helpOrderId){
        return helpTakeService.closeOrders(helpOrderId);
    }

    @PostMapping("/saveHelpTake")
    @ApiOperation("接单")
    @ResponseBody
    public Result saveHelpTake(@RequestBody HelpTake helpTake){
        return helpTakeService.saveBody(helpTake);
    }

    @PostMapping("/endHelpTake")
    @ApiOperation("放弃接单")
    @ResponseBody
    public Result endHelpTake(Long id){
        return helpTakeService.endHelpTake(id);
    }


    @GetMapping("/selectNewHelpOrderList")
    @ApiOperation("最新跑腿")
    @ResponseBody
    public Result selectNewHelpOrderList(Integer page,Integer limit,Long gameId,String latitude,String longitude,Integer sort){
        return helpOrderService.selectNewHelpOrderList(page,limit,gameId,latitude,longitude,sort);
    }


    @GetMapping("/selectHelpOrderByClassifyList")
    @ApiOperation("根据分类查找跑腿")
    @ResponseBody
    public Result selectHelpOrderByClassifyList(Integer page,Integer limit,Long classifyId,Long gameId){
        return helpOrderService.selectHelpOrderByClassifyList(page,limit,classifyId,gameId);
    }


    @GetMapping("/selectHelpOrderByNameList")
    @ApiOperation("根据名称模糊查找跑腿")
    @ResponseBody
    public Result selectHelpOrderByContentList(Integer page,Integer limit,String content,Long gameId){
        return helpOrderService.selectHelpOrderByContentList(page,limit,content,gameId);
    }

    @GetMapping("/selectHelpOrderDetails")
    @ApiOperation("查看跑腿详细信息")
    @ResponseBody
    public Result selectHelpOrderDetails(Long helpOrderId){
        HelpOrder helpOrder = helpOrderService.selectHelpOrderById(helpOrderId);
        return Result.success().put("data",helpOrder);
    }

    @GetMapping("/selectRunHelpOrder")
    @ApiOperation("我的订单-跑腿订单")
    @ResponseBody
    public Result selectRunHelpOrder(Integer page,Integer limit,Integer status,Long userId){
        return helpTakeService.selectRunHelpOrder(page,limit,status,userId);
    }

    @GetMapping("/selectCreateHelpOrder")
    @ApiOperation("我的订单-发布订单")
    @ResponseBody
    public Result selectCreateHelpOrder(Integer page,Integer limit,Integer status,Long userId,Long gameId){
        return helpOrderService.selectCreateHelpOrder(page,limit,status,userId,gameId);

    }

    @GetMapping("/selectStatusHelpOrder")
    @ApiOperation("查询待审核的订单")
    @ResponseBody
    public Result selectStatusHelpOrder(Integer page,Integer limit,String phone,String content,Integer status,Long gameId){
        return helpOrderService.selectStatusHelpOrder(page,limit,phone,content,status,gameId);
    }

    @GetMapping("/selectHelpTakeList")
    @ApiOperation("查询已接单的接单订单")
    @ResponseBody
    public Result selectHelpTakeList(Integer page,Integer limit,Integer status,String phone,String startTime,String endTime){
        return helpTakeService.selectRunHelpOrder(page,limit,status,phone,startTime,endTime);
    }

    @GetMapping("/helpTakeListExcel")
    @ApiOperation("订单导出")
    public void helpTakeListExcel(Integer status,String phone,String startTime,String endTime, HttpServletResponse response) throws Exception {
        ExcelData data = helpTakeService.helpTakeListExcel( status,phone,startTime,endTime);
        ExportExcelUtils.exportExcel(response,"接单列表.xlsx",data);
    }

}