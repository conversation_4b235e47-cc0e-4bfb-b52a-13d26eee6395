package com.sqx.modules.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @description address
 * <AUTHOR>
 * @date 2021-11-10
 */
@Data
@TableName("address")
public class Address implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 地址id
     */
    private Integer addressId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String detailsAddress;

    /**
     * 是否是默认地址
     */
    private Integer isDefault;

    /**
     * 时间
     */
    private String createTime;

    private Long userId;

    private String longitude;

    private String latitude;

    public Address() {}
}
