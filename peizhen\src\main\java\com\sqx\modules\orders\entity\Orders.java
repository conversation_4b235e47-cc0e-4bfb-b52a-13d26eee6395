package com.sqx.modules.orders.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.sqx.modules.evaluate.entity.SysEvaluate;
import com.sqx.modules.taking.entity.Game;
import com.sqx.modules.taking.entity.OrderTaking;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("订单表")
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class Orders implements Serializable {
    @ApiModelProperty("订单id")
    @TableId(type = IdType.AUTO)
    private Long ordersId;
    @ApiModelProperty("订单编号")
    private String ordersNo;
    @ApiModelProperty("用户id")
    private Long userId;
    @TableField(exist = false)
    private UserEntity user;
    @ApiModelProperty("接单id")
    private Long orderTakingId;
    @ApiModelProperty("支付金额")
    private BigDecimal payMoney;
    @ApiModelProperty("订单状态0待支付1进行中2已完成3已退款 4待抢单 5待服务")
    private String state;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("退款原因")
    private String refundContent;
    @ApiModelProperty("订单种类1接单2会员")
    private Integer ordersType;
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 分佣比例
     */
    private BigDecimal rateProportion;
    @ApiModelProperty("订单数量")
    private Integer orderNumber;
    @ApiModelProperty("评分")
    private Double orderScore;
    @ApiModelProperty("修改时间")
    private String updateTime;
    @ApiModelProperty("会员类型id")
    private Long vipDetailsId;
    @ApiModelProperty("用户类型")
    private Long type;
    @ApiModelProperty("假删除")
    private Long isdelete;
    @TableField(exist = false)
    private Game game;
    /**
     * 是否已支付 0未支付 1已支付
     */
    private Integer isPay;
    /**
     * 单价
     */
    @TableField(exist = false)
    private BigDecimal money;


    @TableField(exist = false)
    private OrderTaking orderTaking;
    @ApiModelProperty("省")
    private String province;
    @ApiModelProperty("市")
    private String city;
    @ApiModelProperty("区")
    private String district;
    @ApiModelProperty("详细地址")
    private String detailsAddress;
    @ApiModelProperty("纬度")
    private String longitude;
    @ApiModelProperty("经度")
    private String latitude;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("电话")
    private String phone;
    @ApiModelProperty("上门时间")
    private String startTime;
    private Integer isRemind;
    @ApiModelProperty("技师佣金")
    private BigDecimal rate;
    @ApiModelProperty("一级佣金")
    private BigDecimal zhiRate;
    private Long zhiUserId;
    @ApiModelProperty("二级佣金")
    private BigDecimal feiRate;
    private Long feiUserId;
    @ApiModelProperty("平台利润")
    private BigDecimal pingRate;
    private String endTime;
    private Long orderTakingUserId;
    private String code;
    @ApiModelProperty("支付方式 1零钱  2微信  3支付宝")
    private Integer payWay;
    private String carNo;
    private String carType;
    private String carColor;
    private String carName;
    private String carPhone;
    private String startImg;
    private String endImg;
    /**
     * 1是转单 2是派单
     */
    private Integer isTransfer;

    /**
     * 科室id
     */
    private Long departmentId;
    /**
     * 服务id
     */
    private Long serviceId;
    /**
     * 医院id
     */
    private Long hospitalId;
    /**
     * 优惠券id
     */
    private Long couponId;

    private Long sfZhiUserId;

    private BigDecimal sfZhiRate;

    /**
     * 订单金额(不计算任何优惠或减免时的价格)
     */
    private BigDecimal orderMoney;
    @TableField(exist = false)
    private String distance;
    @TableField(exist = false)
    private String ordersUserName;
    @TableField(exist = false)
    private AppointInformation appointInformation;

    @TableField(exist = false)
    private SysEvaluate evaluate;

    @TableField(exist = false)
    private Integer commentCount;

    @TableField(exist = false)
    private String rideUserName;
    @TableField(exist = false)
    private String ridePhone;

    @TableField(exist = false)
    private String rideAvatar;
}
