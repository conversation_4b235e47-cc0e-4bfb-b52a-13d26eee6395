{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/index.vue?75e1", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/index.vue?96bd", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/index.vue?13c5", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/index.vue?9c24", "uni-app:///my/wallet/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/index.vue?5ba9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/index.vue?75e1*"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "title_color", "money", "avatar", "Profit", "openLists", "image", "text", "id", "openWay", "wallet", "moneyNum", "thisSelect", "charge", "max<PERSON><PERSON>", "minMoney", "ratio", "placeholder", "sp", "onLoad", "onShow", "methods", "getMoneyPage", "res", "item", "getUserInfo", "uni", "getCharge", "get<PERSON>in<PERSON>oney", "getMaxMoney", "getRatio", "re", "cut", "goNav", "url", "active", "getMoney", "console", "selectWay", "pay", "that", "title", "orderId", "payClassifyId", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "icon", "setTimeout", "fail", "callPay", "document", "onBridgeReady", "WeixinJSBridge", "isCheckPay", "setPayment", "orderInfo", "complete", "cashMoney", "content"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6DxvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IAgBA;MACAb;MACAC;MACAC;IACA;IACA;EA0BA;EACAY;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;UACAC;YACA;cACAC;YACA;cACAA;YACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA,qEACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA,qEACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA,qEACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA,qEACA;UACA;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAP;QACAQ;MACA;IACA;IACAC;MAAA;MACA;QACA;UACAX;UACA;QACA;UACAA;QACA;MACA;IACA;IACA;IACAY;MAAA;MACA;QACA;UACAC;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACArC;MACA;MACAmC;MACA;MACAG;QACAH;QACAX;UACAe;QACA;QACA;UACA;UACA;YAcA;YACA;cACAC;cACAC;YACA;YACAH;cACAd;cACAA;gBACAkB;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;kBACAb;kBACAX;oBACAe;oBACAU;kBACA;kBACAC;oBACA1B;kBACA;gBACA;gBACA2B;kBACAhB;kBACAX;oBACAe;oBACAU;kBACA;gBACA;cACA;YACA;UAwBA,QAkCA;QACA;MACA;IACA;IACAG;MACA;QACA;UACAC;QACA;UACAA;UACAA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACAnB;MACAoB,sBACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;QAAA;QACA;MACA,GACA;QACApB;QACA;UACA;UACA;UACAX;YACAe;UACA;UACAW;YACA1B;UACA;QACA;UACAA;QACA;QACA+B;MACA,EACA;IACA;IACAC;MACA;QACA;MACA;QACAhC;QACAA;UACAe;UACAU;QACA;MACA;IACA;IACAQ;MACAtB;MACAX;QACAkB;QACAgB;QAAA;QACAV;UACAb;UACAX;UACAA;YACAe;UACA;QACA;QACAY;UACAhB;UACAX;QACA;QACAmC;UACAnC;QACA;MACA;IACA;IACA;IACAoC;MACA;QACApC;UACAyB;UACAV;QACA;QACA;MACA;MACA;QACAf;UACAyB;UACAV;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MAEA;QACAf;UACAQ;QACA;QACA;MACA;MAEA;MACAM;MACAd;QACAe;QACAsB;QACAb;UACA;YACAV;cACAtC;YACA;cACA;gBACAwB;kBACAyB;kBACAV;gBACA;gBACAD;cACA;gBACAd;kBACAyB;kBACAV;gBACA;cACA;cACAD;YACA;UACA;YACAH;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9fA;AAAA;AAAA;AAAA;AAA2iC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACA/jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/wallet/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/wallet/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1efc2e21&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/wallet/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1efc2e21&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-avatar/u-avatar\" */ \"@/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<!-- <view class=\"flex text-center text-white text-lg bg\">\n\t\t\t<view class=\"title_btn flex-sub bg\" @click=\"cut(1)\" :class=\"title_color==1?'bgCol2': ''\">我的余额\n\t\t\t</view>\n\t\t\t<view class=\"title_btn flex-sub bg\" @click=\"cut(2)\" :class=\"title_color==2?'bgCol2': ''\">我的收益\n\t\t\t</view>\n\t\t</view> -->\n\t\t<view class=\"padding\">\n\t\t\t<view class=\"flex  justify-between radius bg \">\n\t\t\t\t<view class=\" u-flex u-p-l-30 u-p-t-30 u-p-b-30\">\n\t\t\t\t\t<view class=\"u-m-r-20\">\n\t\t\t\t\t\t<u-avatar :src=\"avatar\" size=\"80\"></u-avatar>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-flex-1 \">\n\t\t\t\t\t\t<view class=\"u-font-16 text-white text-bold\">{{money}}元</view>\n\t\t\t\t\t\t<view class=\"u-font-14 u-m-t-10\">可用于平台消费</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"margin-right margin-top-sm\" @click=\"goNav('/my/wallet/mymoneydetail')\">钱包明细</view>\n\t\t\t</view>\n\t\t\t<view class=\"margin-top radius bg flex justify-between flex-wrap padding-lr padding-bottom\">\n\t\t\t\t<view v-for=\"(item,index) in wallet\" :key='index'\n\t\t\t\t\tclass=\"flex justify-between padding-sm radius margin-top\"\n\t\t\t\t\tstyle=\"color: #1E1F31;background-color: #F7F7F7; width: 46%;border: 1px solid #F7F7F7;\"\n\t\t\t\t\t@click=\"active(item)\" :class=\"{active:item.isSelect}\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t{{item.price}}元\n\t\t\t\t\t\t<view class=\"\" v-if=\"item.giveNum\" style=\"font-size: 24rpx;\">\n\t\t\t\t\t\t\t赠送{{item.giveNum}}张{{item.coupon.money}}元优惠券\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"bg margin-top padding-lr radius\">\n\t\t\t\t<view >\n\t\t\t\t\t<view style=\"display: flex;height: 100upx;align-items: center;padding: 20upx 0;\"\n\t\t\t\t\t\tv-for=\"(item,index) in openLists\" :key='index'>\n\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\n\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item)'>\n\t\t\t\t\t\t\t<label class=\"tui-radio\">\n\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\n\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"btn\" @click=\"pay\">确认支付</view>\n\t\t</view>\n\n\n\t\t<view class=\"flex justify-around margin-top text-white\">\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttitle_color: 1,\n\t\t\t\tmoney: 0,\n\t\t\t\tavatar: '',\n\t\t\t\tProfit: 0,\n\t\t\t\topenLists: [{\n\t\t\t\t\timage: '../static/zhifubao.png',\n\t\t\t\t\ttext: '支付宝',\n\t\t\t\t\tid: 1\n\t\t\t\t}, {\n\t\t\t\t\timage: '../static/icon_weixin.png',\n\t\t\t\t\ttext: '微信',\n\t\t\t\t\tid: 2\n\t\t\t\t}],\n\t\t\t\topenWay: 1,\n\t\t\t\twallet: [],\n\t\t\t\tmoneyNum: null,\n\t\t\t\tthisSelect: {},\n\t\t\t\tcharge: 0, //提现手续费\n\t\t\t\tmaxMoney: 0, //最高提现额度\n\t\t\t\tminMoney: 0, //最低提现额度\n\t\t\t\tratio: '', //金元比例\n\t\t\t\tplaceholder: '',\n\t\t\t\tsp:0\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.avatar = uni.getStorageSync('avatar')\n\n\t\t\t// #ifdef APP-PLUS\n\t\t\tthis.openLists = [{\n\t\t\t\timage: '../../static/images/my/zhifubao.png',\n\t\t\t\ttext: '支付宝',\n\t\t\t\tid: 1\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/my/icon_weixin.png',\n\t\t\t\ttext: '微信',\n\t\t\t\tid: 2\n\t\t\t}];\n\t\t\tthis.openWay = 1;\n\t\t\t// #endif\n\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.openLists = [{\n\t\t\t\timage: '../../static/share/icon_weixin.png',\n\t\t\t\ttext: '微信',\n\t\t\t\tid: 2\n\t\t\t}];\n\t\t\tthis.openWay = 2;\n\t\t\t// #endif\n\t\t\t\n\t\t\t// #ifdef H5\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\n\t\t\t\tthis.openLists = [{\n\t\t\t\t\timage: '../../static/share/icon_weixin.png',\n\t\t\t\t\ttext: '微信',\n\t\t\t\t\tid: 2\n\t\t\t\t},{\n\t\t\t\t\timage: '../../static/images/my/zhifubao.png',\n\t\t\t\t\ttext: '支付宝',\n\t\t\t\t\tid: 1\n\t\t\t\t}];\n\t\t\t\tthis.openWay = 1;\n\t\t\t}else{\n\t\t\t\tthis.openLists = [{\n\t\t\t\t\timage: '../../static/images/my/zhifubao.png',\n\t\t\t\t\ttext: '支付宝',\n\t\t\t\t\tid: 1\n\t\t\t\t}];\n\t\t\t\tthis.openWay = 1;\n\t\t\t}\n\t\t\t\n\t\t\t// #endif\n\t\t},\n\t\tonShow() {\n\t\t\tthis.getMoney()\n\t\t\tthis.getCharge()\n\t\t\tthis.getMinMoney()\n\t\t\tthis.getMaxMoney()\n\t\t\tthis.getRatio()\n\t\t\tthis.getUserInfo()\n\t\t\tthis.getMoneyPage();\n\t\t},\n\t\tmethods: {\n\t\t\t//获取充值配置\n\t\t\tgetMoneyPage(){\n\t\t\t\tthis.$Request.get(\"/app/payClassify/selectPayClassifyList\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tres.data.map((item,index)=>{\n\t\t\t\t\t\t\tif(index==0){\n\t\t\t\t\t\t\t\titem.isSelect = true\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\titem.isSelect = false\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.wallet = res.data\n\t\t\t\t\t\tthis.thisSelect = res.data[0]\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetUserInfo() {\n\t\t\t\tthis.$Request.get(\"/app/user/selectUserById\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.avatar = res.data.avatar?res.data.avatar: '../../static/logo.png'\n\t\t\t\t\t\tuni.setStorageSync('avatar', res.data.avatar)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 提现手续费\n\t\t\tgetCharge() {\n\t\t\t\tthis.$Request.get(\"/app/common/type/152\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.charge = res.data.value\n\t\t\t\t\t\tthis.placeholder = '服务费:' + this.charge * 100 + '%' + ',最低提现:' +\n\t\t\t\t\t\t\tthis.minMoney * 1 + ',最高提现:' + this.maxMoney * 1\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 最低提现额度\n\t\t\tgetMinMoney() {\n\t\t\t\tthis.$Request.get(\"/app/common/type/112\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.minMoney = res.data.value\n\t\t\t\t\t\tthis.placeholder = '服务费:' + this.charge * 100 + '%' + ',最低提现:' +\n\t\t\t\t\t\t\tthis.minMoney * 1 + ',最高提现:' + this.maxMoney * 1\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 最高提现额度\n\t\t\tgetMaxMoney() {\n\t\t\t\tthis.$Request.get(\"/app/common/type/153\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.maxMoney = res.data.value\n\t\t\t\t\t\tthis.placeholder = '服务费:' + this.charge * 100 + '%' + ',最低提现:' +\n\t\t\t\t\t\t\tthis.minMoney * 1 + ',最高提现:' + this.maxMoney * 1\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 金元比例\n\t\t\tgetRatio() {\n\t\t\t\tthis.$Request.get(\"/app/common/type/154\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.ratio = res.data.value\n\t\t\t\t\t\tthis.placeholder = '服务费:' + this.charge * 100 + '%' + ',最低提现:' +\n\t\t\t\t\t\t\tthis.minMoney * 1 + ',最高提现:' + this.maxMoney * 1\n\t\t\t\t\t\tthis.wallet.forEach(re => {\n\t\t\t\t\t\t\tre.num = re.price * res.data.value\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tcut(e) {\n\t\t\t\tthis.title_color = e\n\t\t\t},\n\t\t\tgoNav(url) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl\n\t\t\t\t})\n\t\t\t},\n\t\t\tactive(e) {\n\t\t\t\tthis.wallet.map(item=>{\n\t\t\t\t\tif (item.payClassifyId == e.payClassifyId) {\n\t\t\t\t\t\titem.isSelect = true\n\t\t\t\t\t\tthis.thisSelect = e\n\t\t\t\t\t} else {\n\t\t\t\t\t\titem.isSelect = false\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 我的余额\n\t\t\tgetMoney() {\n\t\t\t\tthis.$Request.get(\"/app/userMoney/selectMyMoney\").then(res => {\n\t\t\t\t\tif (res.code == 0 && res.data) {\n\t\t\t\t\t\tconsole.log(res.data.money)\n\t\t\t\t\t\tthis.money = res.data.money\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tselectWay: function(item) {\n\t\t\t\tthis.openWay = item.id;\n\t\t\t},\n\n\t\t\tpay() {\n\t\t\t\tlet that = this\n\t\t\t\tlet data = {\n\t\t\t\t\tmoney: that.thisSelect.price\n\t\t\t\t}\n\t\t\t\tconsole.log(data)\n\t\t\t\t// 获取订单编号\n\t\t\t\tthat.$Request.post(\"/app/appOrder/insertOrder\", data).then(res => {\n\t\t\t\t\tconsole.log(res, '********')\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '支付中...'\n\t\t\t\t\t});\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t// this.Profit = res.data\n\t\t\t\t\t\tif (that.openWay == 2) {\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\t\tlet datawa = {\n\t\t\t\t\t\t\t\torderId:res.data.id,\n\t\t\t\t\t\t\t\tpayClassifyId:this.thisSelect.payClassifyId\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 微信APP支付 根据订单id获取支付信息\n\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/payAppOrder\", datawa).then(ret => {\n\t\t\t\t\t\t\t\tconsole.log(ret,'retretretretretret')\n\t\t\t\t\t\t\t\tthat.isCheckPay(ret.code, 'wxpay', JSON.stringify(ret.data));\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t\t\t// 微信小程序支付\n\t\t\t\t\t\t\tlet datawx ={\n\t\t\t\t\t\t\t\torderId:res.data.id,\n\t\t\t\t\t\t\t\tpayClassifyId:this.thisSelect.payClassifyId\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.post('/app/wxPay/wxPayJsApiOrder',datawx).then(ret => {\n\t\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\t\t\t\tprovider: 'wxpay',\n\t\t\t\t\t\t\t\t\ttimeStamp: ret.data.timestamp,\n\t\t\t\t\t\t\t\t\tnonceStr: ret.data.noncestr,\n\t\t\t\t\t\t\t\t\tpackage: ret.data.package,\n\t\t\t\t\t\t\t\t\tsignType: ret.data.signType,\n\t\t\t\t\t\t\t\t\tpaySign: ret.data.sign,\n\t\t\t\t\t\t\t\t\tsuccess: function(suc) {\n\t\t\t\t\t\t\t\t\t\tconsole.log('success:' + JSON.stringify(suc));\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\tsetTimeout(d => {\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack(1)\n\t\t\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\t\t\tconsole.log('fail:' + JSON.stringify(err));\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付失败',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\tlet ua = navigator.userAgent.toLowerCase();\n\t\t\t\t\t\t\tconsole.log(ua)\n\t\t\t\t\t\t\tif (ua.indexOf('micromessenger') !== -1) {\n\t\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\t\torderId:res.data.id,\n\t\t\t\t\t\t\t\t\tpayClassifyId:this.thisSelect.payClassifyId\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthat.$Request.post('/app/wxPay/wxPayMpOrder',data).then(res => {\n\t\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\t\tthat.callPay(res.data);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付失败!'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} \n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\torderId:res.data.id,\n\t\t\t\t\t\t\t\tclassify:2,\n\t\t\t\t\t\t\t\tpayClassifyId:this.thisSelect.payClassifyId\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.post('/app/aliPay/payMoneyOrder',data).then(\n\t\t\t\t\t\t\tres => {\n\t\t\t\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\t\t\t\tconst div = document.createElement('div')\n\t\t\t\t\t\t\t\t\tdiv.innerHTML = res.data\n\t\t\t\t\t\t\t\t\tdocument.body.appendChild(div)\n\t\t\t\t\t\t\t\t\tdocument.forms[0].submit()\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\ttitle: '支付失败!'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t// #ifdef APP\n\t\t\t\t\t\t\t// APP支付宝支付\n\t\t\t\t\t\t\tlet dataz = {\n\t\t\t\t\t\t\t\torderId:res.data.id,\n\t\t\t\t\t\t\t\tclassify:1,\n\t\t\t\t\t\t\t\tpayClassifyId:this.thisSelect.payClassifyId\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payMoneyOrder\",dataz).then(ret => {\n\t\t\t\t\t\t\t\tconsole.log(ret)\n\t\t\t\t\t\t\t\tthat.isCheckPay(ret.code, 'alipay', ret.data);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tcallPay: function(response) {\n\t\t\t\tif (typeof WeixinJSBridge === \"undefined\") {\n\t\t\t\t\tif (document.addEventListener) {\n\t\t\t\t\t\tdocument.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);\n\t\t\t\t\t} else if (document.attachEvent) {\n\t\t\t\t\t\tdocument.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));\n\t\t\t\t\t\tdocument.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.onBridgeReady(response);\n\t\t\t\t}\n\t\t\t},\n\t\t\tonBridgeReady: function(response) {\n\t\t\t\tlet that = this;\n\t\t\t\tif (!response.package) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconsole.log(response,'++++++++')\n\t\t\t\tWeixinJSBridge.invoke(\n\t\t\t\t\t'getBrandWCPayRequest', {\n\t\t\t\t\t\t\"appId\": response.appid, //公众号名称，由商户传入\n\t\t\t\t\t\t\"timeStamp\": response.timestamp, //时间戳，自1970年以来的秒数\n\t\t\t\t\t\t\"nonceStr\": response.noncestr, //随机串\n\t\t\t\t\t\t\"package\": response.package,\n\t\t\t\t\t\t\"signType\": response.signType, //微信签名方式：\n\t\t\t\t\t\t\"paySign\": response.sign //微信签名\n\t\t\t\t\t},\n\t\t\t\t\tfunction(res) {\n\t\t\t\t\t\tconsole.log(res,'/*-/*-/*-')\n\t\t\t\t\t\tif (res.err_msg === \"get_brand_wcpay_request:ok\") {\n\t\t\t\t\t\t\t// 使用以上方式判断前端返回,微信团队郑重提示：\n\t\t\t\t\t\t\t//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '支付成功'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tWeixinJSBridge.log(response.err_msg);\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t},\n\t\t\tisCheckPay(status, name, order) {\n\t\t\t\tif (status == 0) {\n\t\t\t\t\tthis.setPayment(name, order);\n\t\t\t\t} else {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '支付信息有误',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tsetPayment(name, order) {\n\t\t\t\tconsole.log('*-*-*')\n\t\t\t\tuni.requestPayment({\n\t\t\t\t\tprovider: name,\n\t\t\t\t\torderInfo: order, //微信、支付宝订单数据\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '支付成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\tconsole.log(err)\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t},\n\t\t\t\t\tcomplete() {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 提现\n\t\t\tcashMoney() {\n\t\t\t\tif (!this.moneyNum) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入提现金额'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (this.moneyNum > this.money * 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '您的余额不足'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t// if (this.moneyNum*1+this.charge*this.moneyNum > this.money*1) {\n\t\t\t\t// \tuni.showToast({\n\t\t\t\t// \t\ticon: 'none',\n\t\t\t\t// \t\ttitle: '您的手续费不足'\n\t\t\t\t// \t})\n\t\t\t\t// \treturn\n\t\t\t\t// }\n\n\t\t\t\tlet zhiFuBao = uni.getStorageSync('zhiFuBao')\n\t\t\t\tlet zhiFuBaoName = uni.getStorageSync('zhiFuBaoName')\n\n\t\t\t\tif (!zhiFuBao && !zhiFuBaoName) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/my/wallet/zhifubao'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tlet that = this\n\t\t\t\tthat.sp = (that.moneyNum*this.charge).toFixed(2)\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '本次提现'+that.moneyNum+'元，服务费'+this.sp +'元，是否确认提现？',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthat.$Request.get(\"/app/cash/cashMoney\", {\n\t\t\t\t\t\t\t\tmoney: that.moneyNum\n\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\tthat.moneyNum = null\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthat.getMoney()\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n.bgCol2{\n\tcolor: #557EFD;\n}\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\t.active {\n\t\tborder: 1px solid #557EFD !important;\n\t\tcolor: #557EFD !important;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.title_btn {\n\t\theight: 78upx;\n\t\tline-height: 78upx;\n\t\t/* background: #f7f7f7; */\n\t}\n\n\t.btn {\n\t\twidth: 100%;\n\t\theight: 88upx;\n\t\tbackground: #557EFD;\n\t\tborder-radius: 44upx;\n\t\ttext-align: center;\n\t\tline-height: 88upx;\n\t\tmargin-top: 40upx;\n\t\tfont-size: 28upx;\n\t\tcolor: #FFF;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447622947\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}