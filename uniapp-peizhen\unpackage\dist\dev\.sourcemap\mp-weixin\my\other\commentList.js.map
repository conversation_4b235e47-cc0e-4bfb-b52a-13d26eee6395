{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/commentList.vue?a1dd", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/commentList.vue?a489", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/commentList.vue?da5a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/commentList.vue?731c", "uni-app:///my/other/commentList.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/commentList.vue?4dc3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/commentList.vue?20f5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "commentList", "id", "page", "limit", "onLoad", "onShow", "methods", "getOrderComment", "uni", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA0uB,CAAgB,2rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCsB9vB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;MACA;MACA;IACA;EAEA;EACAC,2BAEA;EACAC;IACA;IACAC;MAAA;MACA;QACAN;QACAC;QACAC;MACA;QACA;UACA;UACA;UACAK;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAijC,CAAgB,48BAAG,EAAC,C;;;;;;;;;;;ACArkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/other/commentList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/other/commentList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./commentList.vue?vue&type=template&id=dac4d6ee&\"\nvar renderjs\nimport script from \"./commentList.vue?vue&type=script&lang=js&\"\nexport * from \"./commentList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./commentList.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/other/commentList.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commentList.vue?vue&type=template&id=dac4d6ee&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.commentList.length\n  var g1 = _vm.commentList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commentList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commentList.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"padding bg text-white radius u-skeleton-fillet\">\n\t\t\t<view class=\"margin-tb-sm\" v-for=\"(item, index) in commentList\" :key='index'>\n\t\t\t\t<view class=\"flex justify-between\">\n\t\t\t\t\t<!-- <u-avatar :src=\"item.avatar\" size=\"48\"></u-avatar> -->\n\t\t\t\t\t<image :src=\"item.avatar?item.avatar:'../../static/logo.png'\" style=\"width: 50upx;height: 50upx;border-radius: 50upx;\"></image>\n\t\t\t\t\t<view class=\"flex-sub margin-left-sm\" style=\"line-height: 46upx;\">{{item.userName}}</view>\n\t\t\t\t\t<view class=\"flex\">\n\t\t\t\t\t\t<u-icon v-for=\"ite in item.score\" :key='ite' color=\"#2087fe\" name=\"star-fill\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"margin-top-sm\">{{item.content}}</view>\n\t\t\t\t<view v-if=\"commentList.length-1!=index\" style=\"width: 100%;height: 1rpx;background: #f5f5f5;margin-top: 20upx;\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<empty v-if=\"commentList.length == 0\"></empty>\n\t</view>\n</template>\n\n<script>\n\timport empty from '../../components/empty.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcommentList: [],\n\t\t\t\tid: '',\n\t\t\t\tpage: '1',\n\t\t\t\tlimit: '10',\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tonLoad(option) {\n\t\t\tif (option.id) {\n\t\t\t\tthis.id = option.id\n\t\t\t\tthis.getOrderComment()\n\t\t\t}\n\n\t\t},\n\t\tonShow() {\n\n\t\t},\n\t\tmethods: {\n\t\t\t// 评论\n\t\t\tgetOrderComment() {\n\t\t\t\tthis.$Request.get(\"/app/takingComment/selectOrderTakingComment\", {\n\t\t\t\t\tid: this.id,\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit\n\t\t\t\t}).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif(this.page==1)this.commentList=[]\n\t\t\t\t\t\tthis.commentList = [...this.commentList, ...res.data.list]\n\t\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tthis.page = this.page + 1;\n\t\t\tthis.getOrderComment()\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\tthis.getOrderComment()\n\t\t},\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commentList.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./commentList.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621951\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}