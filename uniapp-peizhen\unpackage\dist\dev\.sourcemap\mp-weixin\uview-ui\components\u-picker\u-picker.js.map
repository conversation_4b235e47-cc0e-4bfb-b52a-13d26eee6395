{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-picker/u-picker.vue?42de", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-picker/u-picker.vue?6f82", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-picker/u-picker.vue?dcfe", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-picker/u-picker.vue?bbd8", "uni-app:///uview-ui/components/u-picker/u-picker.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-picker/u-picker.vue?b7c0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-picker/u-picker.vue?420a"], "names": ["name", "props", "params", "type", "default", "year", "month", "day", "hour", "minute", "second", "province", "city", "area", "timestamp", "range", "defaultSelector", "rangeKey", "mode", "startYear", "endYear", "cancelColor", "confirmColor", "defaultTime", "defaultRegion", "showTimeTag", "areaCode", "safeAreaInsetBottom", "maskCloseAble", "value", "zIndex", "title", "cancelText", "confirmText", "data", "years", "months", "days", "hours", "minutes", "seconds", "reset", "startDate", "endDate", "valueArr", "provinces", "citys", "areas", "moving", "mounted", "computed", "props<PERSON><PERSON>e", "regionChange", "yearAndMonth", "uZIndex", "watch", "setTimeout", "methods", "pickstart", "pickend", "getItemValue", "formatNumber", "generateArray", "start", "end", "getIndex", "initTimeValue", "fdate", "time", "init", "set<PERSON>ears", "setMonths", "setDays", "index", "setHours", "setMinutes", "setSeconds", "setProvinces", "tmp", "useCode", "setCitys", "<PERSON><PERSON><PERSON><PERSON>", "close", "change", "column", "getResult", "result", "getTimestamp"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkG1wB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,gBA0BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACAZ;MACAC;QACA;MACA;IACA;IACA;IACAY;MACAb;MACAC;QACA;MACA;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;QACA;MACA;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;IACA;IACAyB;MACA1B;MACAC;IACA;IACA;IACA0B;MACA3B;MACAC;IACA;IACA;IACA2B;MACA5B;MACAC;IACA;IACA;IACA4B;MACA7B;MACAC;IACA;IACA;IACA6B;MACA9B;MACAC;IACA;EACA;EACA8B;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAnC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA+B;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACApC;MACAC;MACAC;MACAmC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACAJ;MAAA;MACA;MACAK;QAAA;MAAA;IACA;IACA;IACAJ;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAxB;MAAA;MACA;QACA;QACA2B;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAEA;IAEA;IACA;IACAC;MAEA;IAEA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAC;MACAC;MACAA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAC;MACA;MACA,uCACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;MACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA,0DACA,sCACA,qCACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA,uEACAD;MACA;MACAjC;QACA;UACAiC;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;QACAF;QACAC;MACA,uEACAD;MACAhC;QACA;UACAgC;QACA;MACA;MACA;MACA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACAH;QACAC;MACA,uEACAD;MACA/B;QACA;UACA+B;QACA;MACA;MACA;MACA;MACA;IACA;IACAI;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;UACA;YACAC;YACAX;UACA;QACA;MACA;IACA;IACA;IACAY;MAAA;MAEA;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChlBA;AAAA;AAAA;AAAA;AAA66C,CAAgB,uvCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-picker/u-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-picker.vue?vue&type=template&id=70102400&scoped=true&\"\nvar renderjs\nimport script from \"./u-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./u-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-picker.vue?vue&type=style&index=0&id=70102400&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"70102400\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-picker/u-picker.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-picker.vue?vue&type=template&id=70102400&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.month\n      ? _vm.__map(_vm.months, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.day\n      ? _vm.__map(_vm.days, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var l2 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.hour\n      ? _vm.__map(_vm.hours, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var l3 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.minute\n      ? _vm.__map(_vm.minutes, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var l4 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.second\n      ? _vm.__map(_vm.seconds, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  var l5 =\n    !(_vm.mode == \"region\") &&\n    !(_vm.mode == \"time\") &&\n    _vm.mode == \"selector\" &&\n    !_vm.reset\n      ? _vm.__map(_vm.range, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m5 = _vm.getItemValue(item, \"selector\")\n          return {\n            $orig: $orig,\n            m5: m5,\n          }\n        })\n      : null\n  var l7 =\n    !(_vm.mode == \"region\") &&\n    !(_vm.mode == \"time\") &&\n    !(_vm.mode == \"selector\") &&\n    _vm.mode == \"multiSelector\"\n      ? _vm.__map(_vm.range, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var l6 = !_vm.reset\n            ? _vm.__map(item, function (item1, index1) {\n                var $orig = _vm.__get_orig(item1)\n                var m6 = _vm.getItemValue(item1, \"multiSelector\")\n                return {\n                  $orig: $orig,\n                  m6: m6,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            l6: l6,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        l4: l4,\n        l5: l5,\n        l7: l7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-picker.vue?vue&type=script&lang=js&\"", "<template>\n\t<u-popup :maskCloseAble=\"maskCloseAble\" mode=\"bottom\" :popup=\"false\" v-model=\"value\" length=\"auto\" :safeAreaInsetBottom=\"safeAreaInsetBottom\" @close=\"close\" :z-index=\"uZIndex\">\n\t\t<view class=\"u-datetime-picker\">\n\t\t\t<view class=\"u-picker-header\" @touchmove.stop.prevent=\"\">\n\t\t\t\t<view class=\"u-btn-picker u-btn-picker--tips\" \n\t\t\t\t\t:style=\"{ color: cancelColor }\" \n\t\t\t\t\thover-class=\"u-opacity\" \n\t\t\t\t\t:hover-stay-time=\"150\" \n\t\t\t\t\t@tap=\"getResult('cancel')\"\n\t\t\t\t>{{cancelText}}</view>\n\t\t\t\t<view class=\"u-picker__title\">{{ title }}</view>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"u-btn-picker u-btn-picker--primary\"\n\t\t\t\t\t:style=\"{ color: moving ? cancelColor : confirmColor }\"\n\t\t\t\t\thover-class=\"u-opacity\"\n\t\t\t\t\t:hover-stay-time=\"150\"\n\t\t\t\t\****************=\"\"\n\t\t\t\t\**********=\"getResult('confirm')\"\n\t\t\t\t>\n\t\t\t\t\t{{confirmText}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"u-picker-body\">\n\t\t\t\t<picker-view v-if=\"mode == 'region'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.province\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in provinces\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item.label }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.city\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in citys\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item.label }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.area\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in areas\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item.label }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t</picker-view>\n\t\t\t\t<picker-view :immediate-change=\"true\" v-else-if=\"mode == 'time'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.year\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in years\" :key=\"index\">\n\t\t\t\t\t\t\t{{ item }}\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">年</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.month\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in months\" :key=\"index\">\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">月</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.day\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in days\" :key=\"index\">\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">日</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.hour\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in hours\" :key=\"index\">\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">时</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.minute\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in minutes\" :key=\"index\">\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">分</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.second\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in seconds\" :key=\"index\">\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">秒</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t</picker-view>\n\t\t\t\t<picker-view v-else-if=\"mode == 'selector'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\n\t\t\t\t\t<picker-view-column v-if=\"!reset\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in range\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ getItemValue(item, 'selector') }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t</picker-view>\n\t\t\t\t<picker-view v-else-if=\"mode == 'multiSelector'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\n\t\t\t\t\t<picker-view-column v-if=\"!reset\" v-for=\"(item, index) in range\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item1, index1) in item\" :key=\"index1\">\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ getItemValue(item1, 'multiSelector') }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker-view-column>\n\t\t\t\t</picker-view>\n\t\t\t</view>\n\t\t</view>\n\t</u-popup>\n</template>\n\n<script>\nimport provinces from '../../libs/util/province.js';\nimport citys from '../../libs/util/city.js';\nimport areas from '../../libs/util/area.js';\n\n/**\n * picker picker弹出选择器\n * @description 此选择器有两种弹出模式：一是时间模式，可以配置年，日，月，时，分，秒参数 二是地区模式，可以配置省，市，区参数\n * @tutorial https://www.uviewui.com/components/picker.html\n * @property {Object} params 需要显示的参数，见官网说明\n * @property {String} mode 模式选择，region-地区类型，time-时间类型（默认time）\n * @property {String Number} start-year 可选的开始年份，mode=time时有效（默认1950）\n * @property {String Number} end-year 可选的结束年份，mode=time时有效（默认2050）\n * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配（默认false）\n * @property {Boolean} show-time-tag 时间模式时，是否显示后面的年月日中文提示\n * @property {String} cancel-color 取消按钮的颜色（默认#606266）\n * @property {String} confirm-color 确认按钮的颜色（默认#2979ff）\n * @property {String} default-time 默认选中的时间，mode=time时有效\n * @property {String} confirm-text 确认按钮的文字\n * @property {String} cancel-text 取消按钮的文字\n * @property {String} default-region 默认选中的地区，中文形式，mode=region时有效\n * @property {String} default-code 默认选中的地区，编号形式，mode=region时有效\n * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭Picker（默认true）\n * @property {String Number} z-index 弹出时的z-index值（默认1075）\n * @property {Array} default-selector 数组形式，其中每一项表示选择了range对应项中的第几个\n * @property {Array} range 自定义选择的数据，mode=selector或mode=multiSelector时有效\n * @property {String} range-key 当range参数的元素为对象时，指定Object中的哪个key的值作为选择器显示内容\n * @event {Function} confirm 点击确定按钮，返回当前选择的值\n * @event {Function} cancel 点击取消按钮，返回当前选择的值\n * @example <u-picker v-model=\"show\" mode=\"time\"></u-picker>\n */\nexport default {\n\tname: 'u-picker',\n\tprops: {\n\t\t// picker中需要显示的参数\n\t\tparams: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {\n\t\t\t\t\tyear: true,\n\t\t\t\t\tmonth: true,\n\t\t\t\t\tday: true,\n\t\t\t\t\thour: false,\n\t\t\t\t\tminute: false,\n\t\t\t\t\tsecond: false,\n\t\t\t\t\tprovince: true,\n\t\t\t\t\tcity: true,\n\t\t\t\t\tarea: true,\n\t\t\t\t\ttimestamp: true,\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\t// 当mode=selector或者mode=multiSelector时，提供的数组\n\t\trange: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\t\t// 当mode=selector或者mode=multiSelector时，提供的默认选中的下标\n\t\tdefaultSelector: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn [0];\n\t\t\t}\n\t\t},\n\t\t// 当 range 是一个 Array＜Object＞ 时，通过 range-key 来指定 Object 中 key 的值作为选择器显示内容\n\t\trangeKey: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 模式选择，region-地区类型，time-时间类型，selector-单列模式，multiSelector-多列模式\n\t\tmode: {\n\t\t\ttype: String,\n\t\t\tdefault: 'time'\n\t\t},\n\t\t// 年份开始时间\n\t\tstartYear: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 1950\n\t\t},\n\t\t// 年份结束时间\n\t\tendYear: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 2050\n\t\t},\n\t\t// \"取消\"按钮的颜色\n\t\tcancelColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#606266'\n\t\t},\n\t\t// \"确定\"按钮的颜色\n\t\tconfirmColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#2979ff'\n\t\t},\n\t\t// 默认显示的时间，2025-07-02 || 2025-07-02 13:01:00 || 2025/07/02\n\t\tdefaultTime: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 默认显示的地区，可传类似[\"河北省\", \"秦皇岛市\", \"北戴河区\"]\n\t\tdefaultRegion: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\t\t// 时间模式时，是否显示后面的年月日中文提示\n\t\tshowTimeTag: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 默认显示地区的编码，defaultRegion和areaCode同时存在，areaCode优先，可传类似[\"13\", \"1303\", \"130304\"]\n\t\tareaCode: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\t\tsafeAreaInsetBottom: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 是否允许通过点击遮罩关闭Picker\n\t\tmaskCloseAble: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 通过双向绑定控制组件的弹出与收起\n\t\tvalue: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 弹出的z-index值\n\t\tzIndex: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 0\n\t\t},\n\t\t// 顶部标题\n\t\ttitle: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 取消按钮的文字\n\t\tcancelText: {\n\t\t\ttype: String,\n\t\t\tdefault: '取消'\n\t\t},\n\t\t// 确认按钮的文字\n\t\tconfirmText: {\n\t\t\ttype: String,\n\t\t\tdefault: '确认'\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tyears: [],\n\t\t\tmonths: [],\n\t\t\tdays: [],\n\t\t\thours: [],\n\t\t\tminutes: [],\n\t\t\tseconds: [],\n\t\t\tyear: 0,\n\t\t\tmonth: 0,\n\t\t\tday: 0,\n\t\t\thour: 0,\n\t\t\tminute: 0,\n\t\t\tsecond: 0,\n\t\t\treset: false,\n\t\t\tstartDate: '',\n\t\t\tendDate: '',\n\t\t\tvalueArr: [],\n\t\t\tprovinces: provinces,\n\t\t\tcitys: citys[0],\n\t\t\tareas: areas[0][0],\n\t\t\tprovince: 0,\n\t\t\tcity: 0,\n\t\t\tarea: 0,\n\t\t\tmoving: false // 列是否还在滑动中，微信小程序如果在滑动中就点确定，结果可能不准确\n\t\t};\n\t},\n\tmounted() {\n\t\tthis.init();\n\t},\n\tcomputed: {\n\t\tpropsChange() {\n\t\t\t// 引用这几个变量，是为了监听其变化\n\t\t\treturn `${this.mode}-${this.defaultTime}-${this.startYear}-${this.endYear}-${this.defaultRegion}-${this.areaCode}`;\n\t\t},\n\t\tregionChange() {\n\t\t\t// 引用这几个变量，是为了监听其变化\n\t\t\treturn `${this.province}-${this.city}`;\n\t\t},\n\t\tyearAndMonth() {\n\t\t\treturn `${this.year}-${this.month}`;\n\t\t},\n\t\tuZIndex() {\n\t\t\t// 如果用户有传递z-index值，优先使用\n\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\n\t\t}\n\t},\n\twatch: {\n\t\tpropsChange() {\n\t\t\tthis.reset = true;\n\t\t\tsetTimeout(() => this.init(), 10);\n\t\t},\n\t\t// 如果地区发生变化，为了让picker联动起来，必须重置this.citys和this.areas\n\t\tregionChange(val) {\n\t\t\tthis.citys = citys[this.province];\n\t\t\tthis.areas = areas[this.province][this.city];\n\t\t},\n\t\t// watch监听月份的变化，实时变更日的天数，因为不同月份，天数不一样\n\t\t// 一个月可能有30，31天，甚至闰年2月的29天，平年2月28天\n\t\tyearAndMonth(val) {\n\t\t\tif (this.params.year) this.setDays();\n\t\t},\n\t\t// 微信和QQ小程序由于一些奇怪的原因(故同时对所有平台均初始化一遍)，需要重新初始化才能显示正确的值\n\t\tvalue(n) {\n\t\t\tif (n) {\n\t\t\t\tthis.reset = true;\n\t\t\t\tsetTimeout(() => this.init(), 10);\n\t\t\t}\n\t\t}\n\t},\n\tmethods: {\n\t\t// 标识滑动开始，只有微信小程序才有这样的事件\n\t\tpickstart() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.moving = true;\n\t\t\t// #endif\n\t\t},\n\t\t// 标识滑动结束\n\t\tpickend() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.moving = false;\n\t\t\t// #endif\n\t\t},\n\t\t// 对单列和多列形式的判断是否有传入变量的情况\n\t\tgetItemValue(item, mode) {\n\t\t\t// 目前(2020-05-25)uni-app对微信小程序编译有错误，导致v-if为false中的内容也执行，错误导致\n\t\t\t// 单列模式或者多列模式中的getItemValue同时被执行，故在这里再加一层判断\n\t\t\tif (this.mode == mode) {\n\t\t\t\treturn typeof item == 'object' ? item[this.rangeKey] : item;\n\t\t\t}\n\t\t},\n\t\t// 小于10前面补0，用于月份，日期，时分秒等\n\t\tformatNumber(num) {\n\t\t\treturn +num < 10 ? '0' + num : String(num);\n\t\t},\n\t\t// 生成递进的数组\n\t\tgenerateArray: function(start, end) {\n\t\t\t// 转为数值格式，否则用户给end-year等传递字符串值时，下面的end+1会导致字符串拼接，而不是相加\n\t\t\tstart = Number(start);\n\t\t\tend = Number(end);\n\t\t\tend = end > start ? end : start;\n\t\t\t// 生成数组，获取其中的索引，并剪出来\n\t\t\treturn [...Array(end + 1).keys()].slice(start);\n\t\t},\n\t\tgetIndex: function(arr, val) {\n\t\t\tlet index = arr.indexOf(val);\n\t\t\t// 如果index为-1(即找不到index值)，~(-1)=-(-1)-1=0，导致条件不成立\n\t\t\treturn ~index ? index : 0;\n\t\t},\n\t\t//日期时间处理\n\t\tinitTimeValue() {\n\t\t\t// 格式化时间，在IE浏览器(uni不存在此情况)，无法识别日期间的\"-\"间隔符号\n\t\t\tlet fdate = this.defaultTime.replace(/\\-/g, '/');\n\t\t\tfdate = fdate && fdate.indexOf('/') == -1 ? `2020/01/01 ${fdate}` : fdate;\n\t\t\tlet time = null;\n\t\t\tif (fdate) time = new Date(fdate);\n\t\t\telse time = new Date();\n\t\t\t// 获取年日月时分秒\n\t\t\tthis.year = time.getFullYear();\n\t\t\tthis.month = Number(time.getMonth()) + 1;\n\t\t\tthis.day = time.getDate();\n\t\t\tthis.hour = time.getHours();\n\t\t\tthis.minute = time.getMinutes();\n\t\t\tthis.second = time.getSeconds();\n\t\t},\n\t\tinit() {\n\t\t\tthis.valueArr = [];\n\t\t\tthis.reset = false;\n\t\t\tif (this.mode == 'time') {\n\t\t\t\tthis.initTimeValue();\n\t\t\t\tif (this.params.year) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setYears();\n\t\t\t\t}\n\t\t\t\tif (this.params.month) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setMonths();\n\t\t\t\t}\n\t\t\t\tif (this.params.day) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setDays();\n\t\t\t\t}\n\t\t\t\tif (this.params.hour) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setHours();\n\t\t\t\t}\n\t\t\t\tif (this.params.minute) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setMinutes();\n\t\t\t\t}\n\t\t\t\tif (this.params.second) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setSeconds();\n\t\t\t\t}\n\t\t\t} else if (this.mode == 'region') {\n\t\t\t\tif (this.params.province) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setProvinces();\n\t\t\t\t}\n\t\t\t\tif (this.params.city) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setCitys();\n\t\t\t\t}\n\t\t\t\tif (this.params.area) {\n\t\t\t\t\tthis.valueArr.push(0);\n\t\t\t\t\tthis.setAreas();\n\t\t\t\t}\n\t\t\t} else if (this.mode == 'selector') {\n\t\t\t\tthis.valueArr = this.defaultSelector;\n\t\t\t} else if (this.mode == 'multiSelector') {\n\t\t\t\tthis.valueArr = this.defaultSelector;\n\t\t\t\tthis.multiSelectorValue = this.defaultSelector;\n\t\t\t}\n\t\t\tthis.$forceUpdate();\n\t\t},\n\t\t// 设置picker的某一列值\n\t\tsetYears() {\n\t\t\t// 获取年份集合\n\t\t\tthis.years = this.generateArray(this.startYear, this.endYear);\n\t\t\t// 设置this.valueArr某一项的值，是为了让picker预选中某一个值\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.years, this.year));\n\t\t},\n\t\tsetMonths() {\n\t\t\tthis.months = this.generateArray(1, 12);\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.months, this.month));\n\t\t},\n\t\tsetDays() {\n\t\t\tlet totalDays = new Date(this.year, this.month, 0).getDate();\n\t\t\tthis.days = this.generateArray(1, totalDays);\n\t\t\tlet index = 0;\n\t\t\t// 这里不能使用类似setMonths()中的this.valueArr.splice(this.valueArr.length - 1, xxx)做法\n\t\t\t// 因为this.month和this.year变化时，会触发watch中的this.setDays()，导致this.valueArr.length计算有误\n\t\t\tif (this.params.year && this.params.month) index = 2;\n\t\t\telse if (this.params.month) index = 1;\n\t\t\telse if (this.params.year) index = 1;\n\t\t\telse index = 0;\n\t\t\t// 当月份变化时，会导致日期的天数也会变化，如果原来选的天数大于变化后的天数，则重置为变化后的最大值\n\t\t\t// 比如原来选中3月31日，调整为2月后，日期变为最大29，这时如果day值继续为31显然不合理，于是将其置为29(picker-column从1开始)\n\t\t\tif(this.day > this.days.length) this.day = this.days.length;\n\t\t\tthis.valueArr.splice(index, 1, this.getIndex(this.days, this.day));\n\t\t},\n\t\tsetHours() {\n\t\t\tthis.hours = this.generateArray(0, 23);\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.hours, this.hour));\n\t\t},\n\t\tsetMinutes() {\n\t\t\tthis.minutes = this.generateArray(0, 59);\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.minutes, this.minute));\n\t\t},\n\t\tsetSeconds() {\n\t\t\tthis.seconds = this.generateArray(0, 59);\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.seconds, this.second));\n\t\t},\n\t\tsetProvinces() {\n\t\t\t// 判断是否需要province参数\n\t\t\tif (!this.params.province) return;\n\t\t\tlet tmp = '';\n\t\t\tlet useCode = false;\n\t\t\t// 如果同时配置了defaultRegion和areaCode，优先使用areaCode参数\n\t\t\tif (this.areaCode.length) {\n\t\t\t\ttmp = this.areaCode[0];\n\t\t\t\tuseCode = true;\n\t\t\t} else if (this.defaultRegion.length) tmp = this.defaultRegion[0];\n\t\t\telse tmp = 0;\n\t\t\t// 历遍省份数组匹配\n\t\t\tprovinces.map((v, k) => {\n\t\t\t\tif (useCode ? v.value == tmp : v.label == tmp) {\n\t\t\t\t\ttmp = k;\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.province = tmp;\n\t\t\tthis.provinces = provinces;\n\t\t\t// 设置默认省份的值\n\t\t\tthis.valueArr.splice(0, 1, this.province);\n\t\t},\n\t\tsetCitys() {\n\t\t\tif (!this.params.city) return;\n\t\t\tlet tmp = '';\n\t\t\tlet useCode = false;\n\t\t\tif (this.areaCode.length) {\n\t\t\t\ttmp = this.areaCode[1];\n\t\t\t\tuseCode = true;\n\t\t\t} else if (this.defaultRegion.length) tmp = this.defaultRegion[1];\n\t\t\telse tmp = 0;\n\t\t\tcitys[this.province].map((v, k) => {\n\t\t\t\tif (useCode ? v.value == tmp : v.label == tmp) {\n\t\t\t\t\ttmp = k;\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.city = tmp;\n\t\t\tthis.citys = citys[this.province];\n\t\t\tthis.valueArr.splice(1, 1, this.city);\n\t\t},\n\t\tsetAreas() {\n\t\t\tif (!this.params.area) return;\n\t\t\tlet tmp = '';\n\t\t\tlet useCode = false;\n\t\t\tif (this.areaCode.length) {\n\t\t\t\ttmp = this.areaCode[2];\n\t\t\t\tuseCode = true;\n\t\t\t} else if (this.defaultRegion.length) tmp = this.defaultRegion[2];\n\t\t\telse tmp = 0;\n\t\t\tareas[this.province][this.city].map((v, k) => {\n\t\t\t\tif (useCode ? v.value == tmp : v.label == tmp) {\n\t\t\t\t\ttmp = k;\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.area = tmp;\n\t\t\tthis.areas = areas[this.province][this.city];\n\t\t\tthis.valueArr.splice(2, 1, this.area);\n\t\t},\n\t\tclose() {\n\t\t\tthis.$emit('input', false);\n\t\t},\n\t\t// 用户更改picker的列选项\n\t\tchange(e) {\n\t\t\tthis.valueArr = e.detail.value;\n\t\t\tlet i = 0;\n\t\t\tif (this.mode == 'time') {\n\t\t\t\t// 这里使用i++，是因为this.valueArr数组的长度是不确定长度的，它根据this.params的值来配置长度\n\t\t\t\t// 进入if规则，i会加1，保证了能获取准确的值\n\t\t\t\tif (this.params.year) this.year = this.years[this.valueArr[i++]];\n\t\t\t\tif (this.params.month) this.month = this.months[this.valueArr[i++]];\n\t\t\t\tif (this.params.day) this.day = this.days[this.valueArr[i++]];\n\t\t\t\tif (this.params.hour) this.hour = this.hours[this.valueArr[i++]];\n\t\t\t\tif (this.params.minute) this.minute = this.minutes[this.valueArr[i++]];\n\t\t\t\tif (this.params.second) this.second = this.seconds[this.valueArr[i++]];\n\t\t\t} else if (this.mode == 'region') {\n\t\t\t\tif (this.params.province) this.province = this.valueArr[i++];\n\t\t\t\tif (this.params.city) this.city = this.valueArr[i++];\n\t\t\t\tif (this.params.area) this.area = this.valueArr[i++];\n\t\t\t} else if (this.mode == 'multiSelector') {\n\t\t\t\tlet index = null;\n\t\t\t\t// 对比前后两个数组，寻找变更的是哪一列，如果某一个元素不同，即可判定该列发生了变化\n\t\t\t\tthis.defaultSelector.map((val, idx) => {\n\t\t\t\t\tif (val != e.detail.value[idx]) index = idx;\n\t\t\t\t});\n\t\t\t\t// 为了让用户对多列变化时，对动态设置其他列的变更\n\t\t\t\tif (index != null) {\n\t\t\t\t\tthis.$emit('columnchange', {\n\t\t\t\t\t\tcolumn: index,\n\t\t\t\t\t\tindex: e.detail.value[index]\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 用户点击确定按钮\n\t\tgetResult(event = null) {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tif (this.moving) return;\n\t\t\t// #endif\n\t\t\tlet result = {};\n\t\t\t// 只返回用户在this.params中配置了为true的字段\n\t\t\tif (this.mode == 'time') {\n\t\t\t\tif (this.params.year) result.year = this.formatNumber(this.year || 0);\n\t\t\t\tif (this.params.month) result.month = this.formatNumber(this.month || 0);\n\t\t\t\tif (this.params.day) result.day = this.formatNumber(this.day || 0);\n\t\t\t\tif (this.params.hour) result.hour = this.formatNumber(this.hour || 0);\n\t\t\t\tif (this.params.minute) result.minute = this.formatNumber(this.minute || 0);\n\t\t\t\tif (this.params.second) result.second = this.formatNumber(this.second || 0);\n\t\t\t\tif (this.params.timestamp) result.timestamp = this.getTimestamp();\n\t\t\t} else if (this.mode == 'region') {\n\t\t\t\tif (this.params.province) result.province = provinces[this.province];\n\t\t\t\tif (this.params.city) result.city = citys[this.province][this.city];\n\t\t\t\tif (this.params.area) result.area = areas[this.province][this.city][this.area];\n\t\t\t} else if (this.mode == 'selector') {\n\t\t\t\tresult = this.valueArr;\n\t\t\t} else if (this.mode == 'multiSelector') {\n\t\t\t\tresult = this.valueArr;\n\t\t\t}\n\t\t\tif (event) this.$emit(event, result);\n\t\t\tthis.close();\n\t\t},\n\t\t// 获取时间戳\n\t\tgetTimestamp() {\n\t\t\t// yyyy-mm-dd为安卓写法，不支持iOS，需要使用\"/\"分隔，才能二者兼容\n\t\t\tlet time = this.year + '/' + this.month + '/' + this.day + ' ' + this.hour + ':' + this.minute + ':' + this.second;\n\t\t\treturn new Date(time).getTime() / 1000;\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../libs/css/style.components.scss';\n\n.u-datetime-picker {\n\tposition: relative;\n\tz-index: 999;\n}\n\n.u-picker-view {\n\theight: 100%;\n\tbox-sizing: border-box;\n}\n\n.u-picker-header {\n\twidth: 100%;\n\theight: 90rpx;\n\tpadding: 0 40rpx;\n\t@include vue-flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tbox-sizing: border-box;\n\tfont-size: 30rpx;\n\tbackground: #fff;\n\tposition: relative;\n}\n\n.u-picker-header::after {\n\tcontent: '';\n\tposition: absolute;\n\tborder-bottom: 1rpx solid #eaeef1;\n\t-webkit-transform: scaleY(0.5);\n\ttransform: scaleY(0.5);\n\tbottom: 0;\n\tright: 0;\n\tleft: 0;\n}\n\n.u-picker__title {\n\tcolor: $u-content-color;\n}\n\n.u-picker-body {\n\twidth: 100%;\n\theight: 500rpx;\n\toverflow: hidden;\n\tbackground-color: #fff;\n}\n\n.u-column-item {\n\t@include vue-flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 32rpx;\n\tcolor: $u-main-color;\n\tpadding: 0 8rpx;\n}\n\n.u-text {\n\tfont-size: 24rpx;\n\tpadding-left: 8rpx;\n}\n\n.u-btn-picker {\n\tpadding: 16rpx;\n\tbox-sizing: border-box;\n\ttext-align: center;\n\ttext-decoration: none;\n}\n\n.u-opacity {\n\topacity: 0.5;\n}\n\n.u-btn-picker--primary {\n\tcolor: $u-type-primary;\n}\n\n.u-btn-picker--tips {\n\tcolor: $u-tips-color;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-picker.vue?vue&type=style&index=0&id=70102400&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-picker.vue?vue&type=style&index=0&id=70102400&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627637\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}