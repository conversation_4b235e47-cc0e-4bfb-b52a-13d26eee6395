package com.sqx.modules.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserMoney;
import com.sqx.modules.app.entity.UserMoneyDetails;
import org.springframework.web.bind.annotation.RequestAttribute;

import java.math.BigDecimal;

public interface UserMoneyService extends IService<UserMoney> {

    UserMoney selectUserMoneyByUserId(Long userId);

    void updateSafetyMoneyWay(Long id,Integer safetyMoneyWay,String orderNo);

    void updateMoney(int i, Long userId, BigDecimal money);

    void updateSafetyMoney(int i, Long userId, BigDecimal money);

    Double selectMyProfit(Long userId);

    Result payTakingOrder(Long userId, Long orderId);

    PageUtils balanceDetailed(@RequestAttribute Long userId, Integer classify, Page<UserMoneyDetails> ipage);


    Result refundSafetMoney(Long userId);

    Result paySafetyMoney(Long userId);

}
