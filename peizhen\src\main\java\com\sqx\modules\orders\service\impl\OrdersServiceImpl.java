package com.sqx.modules.orders.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.DateUtils;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.*;
import com.sqx.modules.app.service.*;
import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.sqx.modules.appointInformation.service.AppointInformationService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.creditRecord.service.CreditRecordService;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.service.DepartmentService;
import com.sqx.modules.evaluate.entity.SysEvaluate;
import com.sqx.modules.evaluate.service.SysEvaluateService;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.service.HospitalService;
import com.sqx.modules.hospitalEmploy.entity.HospitalEmploy;
import com.sqx.modules.hospitalEmploy.service.HospitalEmployService;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.orders.dao.OrdersDao;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.response.OrderAllResponse;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import com.sqx.modules.patientInfo.service.PatientInfoService;
import com.sqx.modules.pay.controller.app.AliPayController;
import com.sqx.modules.pay.dao.PayDetailsDao;
import com.sqx.modules.pay.entity.PayDetails;
import com.sqx.modules.pay.service.WxService;
import com.sqx.modules.rewardLevel.service.RewardLevelService;
import com.sqx.modules.tbCoupon.entity.TbCouponUser;
import com.sqx.modules.tbCoupon.service.TbCouponUserService;
import com.sqx.modules.utils.AliPayOrderUtil;
import com.sqx.modules.utils.HttpClientUtil;
import com.sqx.modules.utils.PinYinUti;
import com.sqx.modules.utils.SenInfoCheckUtil;
import com.sqx.modules.utils.excel.ExcelData;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Service
@Slf4j
public class OrdersServiceImpl extends ServiceImpl<OrdersDao, Orders> implements OrdersService {
    @Autowired
    private AppointInformationService informationService;
    @Autowired
    private SysEvaluateService evaluateService;
    @Autowired
    private OrdersDao ordersDao;
    @Autowired
    private PayDetailsDao payDetailsDao;
    @Autowired
    private OrdersService ordersService;
    @Autowired
    private UserMoneyService userMoneyService;
    @Autowired
    private UserMoneyDetailsService userMoneyDetailsService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private UserService userService;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private AddressService addressService;
    @Autowired
    private WxService wxService;
    @Autowired
    private HospitalEmployService employService;

    @Autowired
    private AliPayController aliPayController;
    @Autowired
    private HospitalService hospitalService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private PatientInfoService patientInfoService;
    @Autowired
    private TbCouponUserService couponUserService;
    @Autowired
    private UserCertificationService certificationService;
    @Autowired
    private RewardLevelService levelService;
    @Autowired
    private CreditRecordService recordService;


    private ReentrantReadWriteLock reentrantReadWriteLock = new ReentrantReadWriteLock(true);

    @Override
    public int selectMyOrdersCount(Long userId, String time) {
        return baseMapper.selectMyOrdersCount(userId, time);
    }

    @Override
    public int selectTakeOrdersCount(Long userId, String time) {
        return baseMapper.selectTakeOrdersCount(userId, time);
    }

    @Override
    public Result updateOrdersAddress(Long ordersId, Long addressId) {
        Orders orders = baseMapper.selectById(ordersId);
        if ("0".equals(orders.getState())) {
            Address byId = addressService.getById(addressId);
            orders.setProvince(byId.getProvince());
            orders.setCity(byId.getCity());
            orders.setDistrict(byId.getDistrict());
            orders.setDetailsAddress(byId.getDetailsAddress());
            orders.setName(byId.getName());
            orders.setPhone(byId.getPhone());
            orders.setLatitude(byId.getLatitude());
            orders.setLongitude(byId.getLongitude());
            baseMapper.updateById(orders);
            return Result.success();
        }
        return Result.error("当前订单状态不允许修改地址！");
    }

    /**
     * 生成订单
     *
     * @param userId
     * @param couponId
     * @param serviceType
     * @return
     */
    @Override
    public Result generateOrder(Long userId, Long couponId, Integer serviceType, Integer orderType, Long orderTakingUserId, AppointInformation information) {


        information.setType(orderType);
        Orders peiZhenOrder = new Orders();
        //判断订单类型
        if (orderType != null) {
            //服务类型
            HospitalEmploy employ = employService.getById(information.getServiceId());
            peiZhenOrder.setServiceId(employ.getServiceId());
            peiZhenOrder.setOrdersNo(AliPayOrderUtil.createOrderId());
            peiZhenOrder.setUserId(userId);
            peiZhenOrder.setPhone(information.getPhone());
            peiZhenOrder.setOrderNumber(information.getServiceNum());
            peiZhenOrder.setOrderMoney(employ.getMoney().multiply(new BigDecimal(information.getServiceNum())));
            peiZhenOrder.setCouponId(couponId);
            peiZhenOrder.setOrdersType(orderType);
            peiZhenOrder.setUserId(userId);
            peiZhenOrder.setIsdelete(0L);
            peiZhenOrder.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            peiZhenOrder.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            int code = (int) ((Math.random() * 9 + 1) * 1000);
            peiZhenOrder.setCode(String.valueOf(code));
            peiZhenOrder.setState("0");
            //陪护和陪诊订单均绑定医院
            if (orderType == 1 || orderType == 2) {
                if (information.getHospitalId() == null) {
                    Hospital hospital = hospitalService.getOne(new QueryWrapper<Hospital>().eq("hospital_name", information.getHospitalName()).eq("province", information.getProvince()).eq("city", information.getCity()).eq("district", information.getDistrict()));
                    if (hospital == null) {
                        hospital = new Hospital();
                        hospital.setHospitalName(information.getHospitalName());
                        hospital.setHospitalLng(information.getLng());
                        hospital.setHospitalLat(information.getLat());
                        hospital.setProvince(information.getProvince());
                        hospital.setCity(information.getCity());
                        hospital.setDistrict(information.getDistrict());
                        hospital.setAddressDetails(information.getDetailsAddress());
                        hospital.setIsEnable(1);
                        hospital.setSort(0);
                        hospital.setHospitalType(information.getHospitalType());
                        hospital.setHospitalLevel(information.getHospitalLevel());
                        hospital.setHospitalDetails(hospital.getHospitalName());
                        hospital.setDepartmentDetails(information.getDepartmentOneName());
                        hospital.setCityInitial(PinYinUti.getFirstPinYin(hospital.getCity()));
                        hospitalService.save(hospital);

                    }
                    information.setHospitalId(hospital.getHospitalId());
                    if (StringUtils.isNotBlank(information.getDepartmentOneName()) || StringUtils.isNotBlank(information.getDepartmentTwoName())) {

                        Department oneDepart = departmentService.getOne(new QueryWrapper<Department>().eq("hospital_id", hospital.getHospitalId()).eq("department_name", information.getDepartmentOneName()).eq("parent_id", 0));
                        if (oneDepart == null) {
                            oneDepart = new Department();
                            oneDepart.setDepartmentName(information.getDepartmentOneName());
                            oneDepart.setSort(0);
                            oneDepart.setCreateTime(new Date());
                            oneDepart.setIsEnable(1);
                            oneDepart.setHospitalId(hospital.getHospitalId());
                            oneDepart.setParentId(0L);
                            departmentService.save(oneDepart);
                        }

                        Department twoDepart = departmentService.getOne(new QueryWrapper<Department>().eq("hospital_id", hospital.getHospitalId()).eq("department_name", information.getDepartmentTwoName()).ne("parent_id", 0));

                        if (twoDepart == null) {
                            twoDepart = new Department();
                            twoDepart.setDepartmentName(information.getDepartmentTwoName());
                            twoDepart.setSort(0);
                            twoDepart.setCreateTime(new Date());
                            twoDepart.setIsEnable(1);
                            twoDepart.setHospitalId(oneDepart.getHospitalId());
                            twoDepart.setParentId(oneDepart.getDepartmentId());
                            departmentService.save(twoDepart);
                        }
                        information.setDepartmentId(twoDepart.getDepartmentId());
                    }
                }


                //医院信息
                Hospital hospital = hospitalService.getById(information.getHospitalId());
                //科室id
                Department department = departmentService.getById(information.getDepartmentId());
                peiZhenOrder.setCity(hospital.getCity());
                peiZhenOrder.setProvince(hospital.getProvince());
                peiZhenOrder.setLatitude(hospital.getHospitalLat().toString());
                peiZhenOrder.setLongitude(hospital.getHospitalLng().toString());
                peiZhenOrder.setDistrict(hospital.getDistrict());
                peiZhenOrder.setDetailsAddress(hospital.getAddressDetails());
                if (department != null) {
                    peiZhenOrder.setDepartmentId(department.getDepartmentId());
                    information.setDepartmentName(department.getDepartmentName());
                }
                peiZhenOrder.setHospitalId(hospital.getHospitalId());
            } else {
                if (information.getAddressId() != null) {
                    Address address = addressService.getById(information.getAddressId());
                    peiZhenOrder.setCity(address.getCity());
                    peiZhenOrder.setProvince(address.getProvince());
                    peiZhenOrder.setLatitude(address.getLatitude());
                    peiZhenOrder.setLongitude(address.getLongitude());
                    peiZhenOrder.setDistrict(address.getDistrict());
                    peiZhenOrder.setDetailsAddress(address.getDetailsAddress());
                }

            }
            //优惠券判断
            if (couponId != null) {
                TbCouponUser tbCouponUser = couponUserService.getById(couponId);
                if (tbCouponUser == null) {
                    return Result.error("你未持有当前优惠券");
                }
                if (tbCouponUser.getStatus() == 1) {
                    return Result.error("当前优惠券已使用");
                }
                if (tbCouponUser.getStatus() == 2) {
                    return Result.error("当前优惠券已失效");
                }
                //如果订单金额大于优惠券最小订单金额
                if (peiZhenOrder.getOrderMoney().compareTo(tbCouponUser.getMinMoney()) >= 0) {
                    //写入使用了优惠券之后的订单金额
                    BigDecimal money = peiZhenOrder.getOrderMoney().subtract(tbCouponUser.getMoney());
                    if (money.compareTo(new BigDecimal("0.01")) <= 0) {
                        peiZhenOrder.setPayMoney(new BigDecimal("0.01"));
                    } else {
                        peiZhenOrder.setPayMoney(money);
                    }
                    tbCouponUser.setStatus(1);
                    tbCouponUser.setEmployTime(new Date());
                    couponUserService.updateById(tbCouponUser);
                } else {
                    return Result.error("订单金额不满足最低满减金额");
                }
            } else {
                peiZhenOrder.setPayMoney(peiZhenOrder.getOrderMoney());
            }
            peiZhenOrder.setIsTransfer(2);
            if (orderTakingUserId != null) {
                UserEntity riderUserEntity = userService.getById(orderTakingUserId);
                if (riderUserEntity == null) {
                    return Result.error("你选择的护工不存在");
                }
                peiZhenOrder.setRateProportion(riderUserEntity.getRate());

                //获取护工的预约时间内是否有其他订单
                Integer count = ordersDao.getUserNowHasOrder(orderTakingUserId, DateUtils.stringToDate(information.getHopeTime(), DateUtils.DATE_TIME_PATTERN));
                if (count > 0) {
                    return Result.error("护工在当前选择的预约时间已有订单,请预约其他时间或选择其他护工");
                }
                peiZhenOrder.setOrderTakingUserId(orderTakingUserId);

            }
            baseMapper.insert(peiZhenOrder);
            if (orderType == 1 || orderType == 2) {
                Hospital hospital = hospitalService.getById(information.getHospitalId());
                //写入预约信息
                information.setLat(hospital.getHospitalLat());
                information.setLng(hospital.getHospitalLng());
                information.setCity(hospital.getCity());
                information.setProvince(hospital.getProvince());
                information.setDistrict(hospital.getDistrict());
                information.setHospitalName(hospital.getHospitalName());
                information.setDetailsAddress(hospital.getAddressDetails());
            }
            if (information.getAddressId() != null) {
                Address address = addressService.getById(information.getAddressId());
                information.setUserCity(address.getCity());
                information.setUserProvince(address.getProvince());
                information.setUserDistrict(address.getDistrict());
                information.setUserDetailsAddress(address.getDetailsAddress());
                information.setUserName(address.getName());
                information.setUserLat(Double.valueOf(address.getLatitude()));
                information.setUserLng(Double.valueOf(address.getLongitude()));
                information.setUserPhone(address.getPhone());
            }
            information.setServiceName(employ.getServiceName());
            information.setOrdersId(peiZhenOrder.getOrdersId());
            informationService.save(information);
            return Result.success().put("data", peiZhenOrder);
        } else {
            return Result.error("订单类型不能为空");
        }
    }


    @Override
    public Result selectOrderList(Integer page, Integer limit, Integer createTime, Integer distance, String longitude, String latitude) {
        return Result.success().put("data", new PageUtils(baseMapper.selectOrderList(new Page(page, limit), createTime, distance, longitude, latitude)));
    }

    @Override
    public Result insertMyOrders(Long ordersId, Long userId, String startImg) {
        reentrantReadWriteLock.writeLock().lock();
        try {
            Orders orders = baseMapper.selectById(ordersId);
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
            if ("4".equals(orders.getState())) {
                UserEntity userEntity = userService.selectUserById(userId);
                CommonInfo commonInfo = commonInfoService.findOne(338);
                if (commonInfo != null && StringUtils.isNotBlank(commonInfo.getValue())) {
                    Integer value = Integer.parseInt(commonInfo.getValue());
                    if (userEntity.getShopState() != 1) {
                        return Result.error("请先上线");
                    }
                    if (userEntity.getCreditScore() < value) {
                        return Result.error("你的信用分不足无法接单,请联系管理员");
                    }
                }
                if (userEntity.getIsAuthentication() == null) {
                    return Result.error("您还未实名认证，请实名认证后进行接单！");
                }
                String isSafetyMoney = commonInfoService.findOne(343).getValue();
                if ("是".equals(isSafetyMoney)) {
                    if (userEntity.getIsSafetyMoney() == null || userEntity.getIsSafetyMoney() != 1) {
                        return Result.error("您还未缴纳保证金，请缴纳后进行接单！");
                    }
                }
                UserCertification userCertification = certificationService.getOne(new QueryWrapper<UserCertification>().eq("user_id", userEntity.getUserId()));
                if (userCertification == null || userCertification.getStatus() != 1) {
                    return Result.error("您还未实名认证或未通过审核,请实名认证后进行接单！");
                }
                UserEntity riderUserEntity = userService.getById(userId);
                orders.setOrderTakingUserId(userId);
                orders.setState("5");
                orders.setStartImg(startImg);
                orders.setIsTransfer(3);
                orders.setRateProportion(riderUserEntity.getRate());

                baseMapper.updateById(orders);
                UserEntity ordersUser = userService.selectUserById(orders.getUserId());
                if (StringUtils.isNotBlank(ordersUser.getOpenId())) {
                    ordersService.sendOrderMeg(orders, ordersUser, information, 2, 2, 1);
                }
                return Result.success();
            } else {
                return Result.error("订单已经被抢走了！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("抢单异常，订单id：" + ordersId, e);
        } finally {
            reentrantReadWriteLock.writeLock().unlock();
        }
        return Result.error("系统繁忙，请稍后再试！");
    }


    @Override
    public Result investOrder(Long userId, Double money, Integer classify) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        PayDetails payDetails = new PayDetails();
        //分类（ 1app微信 2微信公众号 3微信小程序 4支付宝）
        payDetails.setClassify(classify);
        //订单编号
        payDetails.setOrderId(AliPayOrderUtil.createOrderId());
        //金额
        payDetails.setMoney(money);
        //用户id
        payDetails.setUserId(userId);
        //状态0待支付 1支付成功 2失败
        payDetails.setState(0);
        //创建时间
        payDetails.setCreateTime(simpleDateFormat.format(new Date()));
        payDetailsDao.insert(payDetails);
        return Result.success();
    }

    @Override
    public Result giveOrdersUser(UserEntity user, Long orderId) {
        Orders orders = baseMapper.selectById(orderId);
        UserEntity userEntity = userService.selectUserById(orders.getUserId());
        UserEntity oldRider = userService.selectUserById(orders.getOrderTakingUserId());

        if (user == null) {
            return Result.error("用户不存在！");
        }
        if (user.getIsAuthentication() == null) {
            return Result.error("该用户尚未实名认证，请实名认证后进行接单！");
        }
        if (user.getIsSafetyMoney() == null || user.getIsSafetyMoney() != 1) {
            return Result.error("该用户尚未缴纳保证金，请缴纳后进行接单！");
        }

        if (!"5".equals(orders.getState()) && !"1".equals(orders.getState())) {
            return Result.error("订单当前状态不允许转接！");
        }
        orders.setOrderTakingUserId(user.getUserId());
        orders.setRateProportion(user.getRate());
        orders.setIsTransfer(1);
        orders.setState("5");
        baseMapper.updateById(orders);
        //下单人消息推送
        if (StringUtils.isNotBlank(userEntity.getOpenId())) {
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
            sendOrderMeg(orders, userEntity, information, 1, 3, 1);
        }
        if (StringUtils.isNotBlank(user.getShopOpenId())) {
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
            sendOrderMeg(orders, user, information, 2, 14, 2);
        }
        //老的接单人消息推送
        if (StringUtils.isNotBlank(oldRider.getShopOpenId())) {
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
            sendOrderMeg(orders, oldRider, information, 2, 4, 2);
        }
        return Result.success();
    }


    @Override
    public Result cancelOrder(Long id, String status, String code, String endImg, String startImg, BigDecimal refundMoney) {
        reentrantReadWriteLock.writeLock().lock();
        try {
            return cancelOrders(id, status, code, endImg, startImg, refundMoney);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("修改订单异常：" + e.getMessage(), e);
        } finally {
            reentrantReadWriteLock.writeLock().unlock();
        }
        return Result.error("系统繁忙，请稍后再试！");
    }

    @Transactional
    public Result cancelOrders(Long id, String status, String code, String endImg, String startImg, BigDecimal refundMoney) {
        Orders orders = baseMapper.selectById(id);
        if (orders != null) {
            UserEntity riderUser = userService.selectUserById(orders.getOrderTakingUserId());
            UserEntity userEntity = userService.selectUserById(orders.getUserId());
            if ("1".equals(orders.getState()) || "4".equals(orders.getState()) || "5".equals(orders.getState())) {
                if ("4".equals(status)) {
                    orders.setState("4");
                    baseMapper.updateById(orders);
                } else if ("2".equals(status)) {
                    if ("2".equals(orders.getState())) {
                        return Result.error("当前订单已完成!,请勿重复提交");
                    }
                    if (!code.equals(orders.getCode())) {
                        return Result.error("确认码不正确！");
                    }
                    orders.setEndImg(endImg);
                    //完成订单
                    BigDecimal payMoney = orders.getPayMoney();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String time = sdf.format(new Date());
                    BigDecimal peiMoney = payMoney.multiply(orders.getRateProportion()).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                    //判断是否开启分销
                    String value = commonInfoService.findOne(209).getValue();
                    if ("是".equals(value)) {
                        BigDecimal pingRate = payMoney.subtract(peiMoney);
                        orders.setRate(peiMoney);
                        userMoneyService.updateMoney(1, orders.getOrderTakingUserId(), peiMoney);
                        UserMoneyDetails userMoneyDetails = new UserMoneyDetails();
                        userMoneyDetails.setUserId(orders.getOrderTakingUserId());
                        userMoneyDetails.setTitle("订单完成：" + orders.getOrdersNo());
                        userMoneyDetails.setContent("订单完成，订单金额：" + payMoney + "，平台服务费:" + pingRate + "，实际到账金额：" + peiMoney);
                        userMoneyDetails.setType(1);
                        userMoneyDetails.setMoney(peiMoney);
                        userMoneyDetails.setCreateTime(time);
                        userMoneyDetailsService.save(userMoneyDetails);
                        //推广员
                        UserEntity zhiUser = userService.queryByInvitationCode(userEntity.getInviterCode());
                        if (zhiUser != null && zhiUser.getIsPromotion() != null && zhiUser.getIsPromotion() == 1) {
                            if (zhiUser.getZhiRate().doubleValue() != 0 && zhiUser.getZhiRate().doubleValue() > 0) {
                                peiMoney = payMoney.multiply(zhiUser.getZhiRate()).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                                orders.setZhiRate(peiMoney);
                                orders.setZhiUserId(zhiUser.getUserId());
                                userMoneyService.updateMoney(1, zhiUser.getUserId(), peiMoney);
                                userMoneyDetails = new UserMoneyDetails();
                                userMoneyDetails.setUserId(zhiUser.getUserId());
                                userMoneyDetails.setTitle("用户推广佣金订单完成：" + orders.getOrdersNo());
                                userMoneyDetails.setContent("用户推广佣金订单完成，到账金额：" + peiMoney);
                                userMoneyDetails.setType(1);
                                userMoneyDetails.setClassify(10);
                                userMoneyDetails.setMoney(peiMoney);
                                userMoneyDetails.setCreateTime(time);
                                userMoneyDetailsService.save(userMoneyDetails);
                            }
                        }
                        //师傅邀请人
                        UserEntity sfZhiUser = userService.getOne(new QueryWrapper<UserEntity>().eq("invitation_code", riderUser.getInviterCode()));
                        if (sfZhiUser != null && sfZhiUser.getIsPromotion() != null && sfZhiUser.getIsPromotion() == 1) {
                            if (sfZhiUser.getZhiRate().doubleValue() != 0 && sfZhiUser.getZhiRate().doubleValue() > 0) {
                                peiMoney = payMoney.multiply(sfZhiUser.getZhiRate()).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                                orders.setSfZhiRate(peiMoney);
                                orders.setSfZhiUserId(sfZhiUser.getUserId());
                                userMoneyService.updateMoney(1, sfZhiUser.getUserId(), peiMoney);
                                userMoneyDetails = new UserMoneyDetails();
                                userMoneyDetails.setUserId(sfZhiUser.getUserId());
                                userMoneyDetails.setTitle("师傅推广佣金订单完成：" + orders.getOrdersNo());
                                userMoneyDetails.setContent("师傅推广佣金订单完成，到账金额：" + peiMoney);
                                userMoneyDetails.setType(1);
                                userMoneyDetails.setClassify(10);
                                userMoneyDetails.setMoney(peiMoney);
                                userMoneyDetails.setCreateTime(time);
                                userMoneyDetailsService.save(userMoneyDetails);
                            }
                        }
                        //代理商
                        String s = SenInfoCheckUtil.addressCutting(orders.getProvince() + "" + orders.getCity() + "" + orders.getDistrict());
                        String[] split = s.split("-");
                        UserEntity feiUser = userService.queryAgentUser(split[0], null, null);
                        if (feiUser == null) {
                            feiUser = userService.queryAgentUser(split[0], split[1], null);
                            if (feiUser == null) {
                                feiUser = userService.queryAgentUser(split[0], split[1], split[2]);
                            }
                        }
                        if (feiUser != null) {
                            if (feiUser.getFeiRate().doubleValue() != 0 && feiUser.getFeiRate().doubleValue() > 0) {
                                peiMoney = payMoney.multiply(feiUser.getFeiRate());
                                peiMoney = peiMoney.setScale(2, BigDecimal.ROUND_HALF_DOWN);
                                orders.setFeiRate(peiMoney);
                                orders.setFeiUserId(feiUser.getUserId());
                                userMoneyService.updateMoney(1, feiUser.getUserId(), peiMoney);
                                userMoneyDetails = new UserMoneyDetails();
                                userMoneyDetails.setByUserId(orders.getUserId());
                                userMoneyDetails.setUserId(feiUser.getUserId());
                                userMoneyDetails.setTitle("代理商佣金订单完成：" + orders.getOrdersNo());
                                userMoneyDetails.setContent("代理商佣金订单完成，到账金额：" + peiMoney);
                                userMoneyDetails.setType(1);
                                userMoneyDetails.setClassify(20);
                                userMoneyDetails.setMoney(peiMoney);
                                userMoneyDetails.setCreateTime(time);
                                userMoneyDetailsService.save(userMoneyDetails);
                            }
                        }


                    } else {
                        orders.setRate(peiMoney);
                        userMoneyService.updateMoney(1, orders.getOrderTakingUserId(), peiMoney);
                        UserMoneyDetails userMoneyDetails = new UserMoneyDetails();
                        userMoneyDetails.setUserId(orders.getOrderTakingUserId());
                        userMoneyDetails.setTitle("订单完成：" + orders.getOrdersNo());
                        userMoneyDetails.setContent("订单完成，到账金额：" + peiMoney);
                        userMoneyDetails.setType(1);
                        userMoneyDetails.setMoney(peiMoney);
                        userMoneyDetails.setCreateTime(time);
                        userMoneyDetailsService.save(userMoneyDetails);
                    }
                    BigDecimal pingRate = orders.getPayMoney().subtract(orders.getRate());
                    if (orders.getZhiRate() != null) {
                        pingRate = pingRate.subtract(orders.getZhiRate());
                    }
                    if (orders.getFeiRate() != null) {
                        pingRate = pingRate.subtract(orders.getFeiRate());
                    }
                    if (orders.getSfZhiRate() != null) {
                        pingRate = pingRate.subtract(orders.getSfZhiRate());
                    }
                    orders.setPingRate(pingRate);
                    orders.setEndTime(DateUtils.format(new Date()));
                    AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
                    if (StringUtils.isNotBlank(userEntity.getOpenId())) {
                        ordersService.sendOrderMeg(orders, userEntity, information, 1, 12, 1);

                    }
                    if (StringUtils.isNotBlank(riderUser.getShopOpenId())) {
                        ordersService.sendOrderMeg(orders, riderUser, information, 2, 12, 2);
                    }
                    //计算单数奖励分佣比例
                    levelService.checkOrderCount(orders.getOrderTakingUserId());
                } else if ("3".equals(status)) {
                    if (orders.getCouponId() != null) {
                        TbCouponUser tbCouponUser = couponUserService.getById(orders.getCouponId());
                        tbCouponUser.setId(orders.getCouponId());
                        tbCouponUser.setStatus(0);
                        couponUserService.updateById(tbCouponUser);
                    }
                    if (refundMoney == null) {
                        refundMoney = orders.getPayMoney();
                    }
                    if (orders.getIsPay() == 1) {
                        if (orders.getPayWay() == null || orders.getPayWay() == 1) {
                            //退款
                            userMoneyService.updateMoney(1, orders.getUserId(), refundMoney);
                        } else if (orders.getPayWay() == 2) {
                            //微信
                            boolean refund = wxService.refund(orders.getOrdersNo(), refundMoney);
                            if (!refund) {
                                return Result.error("退款失败，请联系客服处理！");
                            }
                        } else {
                            //支付宝
                            String data = aliPayController.alipayRefund(orders.getOrdersNo(), refundMoney);
                            if (StringUtils.isNotBlank(data)) {
                                log.error(data);
                                JSONObject jsonObject = JSON.parseObject(data);
                                JSONObject alipay_trade_refund_response = jsonObject.getJSONObject("alipay_trade_refund_response");
                                String code1 = alipay_trade_refund_response.getString("code");
                                if (!"10000".equals(code1)) {
                                    return Result.error("退款失败！" + alipay_trade_refund_response.getString("sub_msg"));
                                }
                            } else {
                                return Result.error("退款失败！");
                            }
                        }

                        AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));

                        UserMoneyDetails userMoneyDetails = new UserMoneyDetails();
                        userMoneyDetails.setUserId(orders.getUserId());
                        userMoneyDetails.setTitle("订单退款：" + orders.getOrdersNo());
                        userMoneyDetails.setContent("订单已原路退款：" + refundMoney);
                        userMoneyDetails.setType(1);
                        userMoneyDetails.setMoney(refundMoney);
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        userMoneyDetails.setCreateTime(sdf.format(new Date()));
                        userMoneyDetailsService.save(userMoneyDetails);
                        if (StringUtils.isNotBlank(userEntity.getOpenId())) {
                            ordersService.sendOrderMeg(orders, userEntity, information, 1, 5, 1);
                        }
                        if (orders.getOrderTakingUserId() != null) {
                            if (StringUtils.isNotBlank(riderUser.getShopOpenId())) {
                                ordersService.sendOrderMeg(orders, riderUser, information, 2, 6, 2);
                            }
                        }

                    }
                } else if ("1".equals(status)) {
                    AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
                    orders.setStartImg(startImg);
                    if (StringUtils.isNotBlank(userEntity.getOpenId())) {
                        ordersService.sendOrderMeg(orders, userEntity, information, 1, 15, 1);
                    }
                }
                orders.setState(status);
                baseMapper.updateById(orders);

            } else if ("0".equals(orders.getState())) {
                orders.setState("3");
            }
            baseMapper.updateById(orders);

            return Result.success();

        } else {
            return Result.error("订单不存在！");
        }
    }


    @Override
    public Result deleteOrder(Long id) {
        Orders orders = baseMapper.selectById(id);
        if (orders != null) {
            orders.setIsdelete((long) 1);
            baseMapper.update(orders, new QueryWrapper<Orders>().eq("orders_id", id));
            return Result.success();
        } else {
            return Result.error("订单不存在！");
        }
    }

    @Override
    public Result queryOrders(Long id) {
        Orders orders = baseMapper.selectById(id);
        UserCertification certification = certificationService.getOne(new QueryWrapper<UserCertification>().eq("user_id", orders.getOrderTakingUserId()));
        if (certification != null) {
            orders.setRideUserName(certification.getName());
            orders.setRidePhone(certification.getPhone());
            orders.setRideAvatar(certification.getAvatar());
        }
        AppointInformation information;
        if (orders.getOrdersType() == 1 || orders.getOrdersType() == 2) {
            information = informationService.selectByOrdersId(orders.getOrdersId());
        } else {
            information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
        }

        if (information.getPatientId() != null) {
            information.setPatientInfo(patientInfoService.getById(information.getPatientId()));
        }

        orders.setAppointInformation(information);
        SysEvaluate sysEvaluate = evaluateService.getOne(new QueryWrapper<SysEvaluate>().eq("indent_number", orders.getOrdersNo()));
        if (sysEvaluate != null) {
            orders.setEvaluate(sysEvaluate);
        }
        return Result.success().put("data", orders);
    }

    @Override
    public IPage<OrderAllResponse> queryOrdersAll(Long page, Long limit, Long type, String name, String userName, Long status, Long userId, String ordersNo, String startTime, String endTIme, Long hospitalId) {
        IPage<Orders> iPage = new Page<>(page, limit);
        String str = null;
        if (name != null && !(name.equals(""))) {
            str = "%" + name + "%";
        }
        IPage<OrderAllResponse> ordersAll = baseMapper.queryOrdersAll(iPage, type, str, userName, status, userId, ordersNo, startTime, endTIme, hospitalId);
        for (OrderAllResponse record : ordersAll.getRecords()) {
            AppointInformation information = informationService.selectByOrdersId(record.getOrdersId());
            if (information != null) {
                PatientInfo patientInfo = patientInfoService.getById(information.getPatientId());
                if (patientInfo != null) {
                    information.setPatientInfo(patientInfo);
                }
            }
            record.setAppointInformation(information);
            if (record.getCouponId() != null) {
                record.setCouponMoney(couponUserService.getById(record.getCouponId()).getMoney());
            }
        }

        return ordersAll;

    }

    @Override
    public ExcelData ordersListExcel(Long type, String name, Long status, Long userId, String ordersNo, String startTime, String endTIme) {
        if (StringUtils.isNotEmpty(name)) {
            name = "%" + name + "%";
        }
        List<OrderAllResponse> orderAllResponses = baseMapper.ordersListExcel(type, name, status, userId, ordersNo, startTime, endTIme);

        ExcelData data = new ExcelData();
        data.setName("订单列表");
        List<String> titles = new ArrayList();
        titles.add("编号");
        titles.add("接单用户");
        titles.add("下单用户");
        titles.add("下单用户电话");
        titles.add("就诊人姓名");
        titles.add("就诊人联系方式");
        titles.add("就诊人性别");
        titles.add("与下单用户关系");
        titles.add("订单编号");
        titles.add("订单类型");
        titles.add("商家佣金");
        titles.add("一级佣金");
        titles.add("二级佣金");
        titles.add("平台金额");
        titles.add("时长");
        titles.add("单位");
        titles.add("支付金额");
        titles.add("上门时间");
        titles.add("备注");
        titles.add("收货码");
        titles.add("订单状态");
        titles.add("创建时间");
        titles.add("是否为转单");
        data.setTitles(titles);
        List<List<Object>> rows = new ArrayList<>();
        for (OrderAllResponse orderAllResponse : orderAllResponses) {
            List<Object> row = new ArrayList<>();
            row.add(orderAllResponse.getOrdersId());
            row.add(orderAllResponse.getUserName());
            row.add(orderAllResponse.getOrdersUserName());
            UserEntity entity = userService.getById(orderAllResponse.getUserId());
            row.add(entity.getPhone());
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orderAllResponse.getOrdersId()));
            if (information != null) {
                PatientInfo patientInfo = patientInfoService.getById(information.getPatientId());
                if (patientInfo != null) {
                    row.add(patientInfo.getRealName());
                    row.add(patientInfo.getPhone());
                    if (patientInfo.getSex() == 1) {
                        row.add("男");
                    } else {
                        row.add("女");
                    }
                    row.add(patientInfo.getRelationship());
                } else {
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                }

            } else {
                row.add("");
                row.add("");
                row.add("");
                row.add("");
            }

            row.add(orderAllResponse.getOrdersNo());
            if (orderAllResponse.getOrdersType() == 1) {
                row.add("服务订单");
            } else {
                row.add("会员订单");
            }
            row.add(orderAllResponse.getRate() == null ? 0 : orderAllResponse.getRate());
            row.add(orderAllResponse.getZhiRate() == null ? 0 : orderAllResponse.getZhiRate());
            row.add(orderAllResponse.getFeiRate() == null ? 0 : orderAllResponse.getFeiRate());
            row.add(orderAllResponse.getPingRate() == null ? 0 : orderAllResponse.getPingRate());
            row.add(orderAllResponse.getOrderNumber() == null ? "" : orderAllResponse.getOrderNumber());
            row.add(orderAllResponse.getUnit() == null ? "" : orderAllResponse.getUnit());
            row.add(orderAllResponse.getPayMoney() == null ? "" : orderAllResponse.getPayMoney());
            row.add(orderAllResponse.getStartTime() == null ? "" : orderAllResponse.getStartTime());
            row.add(orderAllResponse.getRemarks() == null ? "" : orderAllResponse.getRemarks());
            row.add(orderAllResponse.getCode() == null ? "" : orderAllResponse.getCode());
            if (orderAllResponse.getState() == 0) {
                row.add("待支付");
            } else if (orderAllResponse.getState() == 1) {
                row.add("进行中");
            } else if (orderAllResponse.getState() == 2) {
                row.add("已完成");
            } else if (orderAllResponse.getState() == 3) {
                row.add("已退款");
            } else {
                row.add("未知");
            }
            row.add(orderAllResponse.getCreateTime() == null ? "" : orderAllResponse.getCreateTime());
            if (orderAllResponse.getIsTransfer() != null && orderAllResponse.getIsTransfer() == 1) {
                row.add("是");
            } else {
                row.add("否");
            }
            rows.add(row);
        }
        data.setRows(rows);
        return data;
    }


    @Override
    public IPage<Orders> selectMyTakeOrders(Integer page, Integer limit, Long userId, String state) {
        Page<Orders> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        QueryWrapper<Orders> wrapper = new QueryWrapper<Orders>().eq("order_taking_user_id", userId).ne("state", 0);
        if (StringUtils.isNotBlank(state)) {
            if (!"6".equals(state)) {
                wrapper.eq("state", state);
            } else {
                wrapper.in("state", 1, 5);
            }

        }
        wrapper.orderByDesc("create_time");
        return getOtherInfo(ordersDao.selectPage(pages, wrapper));
    }

    @Override
    public IPage<Orders> selectNowDayOrders(Integer page, Integer limit, Long userId) {
        Page<Orders> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return getOtherInfo(ordersDao.selectNowDayOrders(pages, userId));

    }

    private IPage<Orders> getOtherInfo(IPage<Orders> myOrder) {
        List<Orders> records = myOrder.getRecords();
        for (Orders order : records) {
            if (order != null && order.getOrdersId() != null) {
                UserEntity userEntity = userService.getById(order.getUserId());
                if (userEntity != null) {
                    order.setOrdersUserName(userEntity.getUserName());
                }
                AppointInformation information;
                if (order.getOrdersType() == 1 || order.getOrdersType() == 2) {
                    information = informationService.selectByOrdersId(order.getOrdersId());
                } else {
                    information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", order.getOrdersId()));
                }

                if (information != null && information.getPatientId() != null) {
                    information.setPatientInfo(patientInfoService.getById(information.getPatientId()));
                    order.setAppointInformation(information);
                }

                SysEvaluate sysEvaluate = evaluateService.getOne(new QueryWrapper<SysEvaluate>().eq("indent_number", order.getOrdersNo()));
                if (sysEvaluate != null) {
                    order.setEvaluate(sysEvaluate);
                }
            }

        }
        return myOrder.setRecords(records);
    }

    @Override
    public Result payMoney(Long ordersId) {
        CommonInfo one = commonInfoService.findOne(332);
        if (!"是".equals(one.getValue())) {
            return Result.error("零钱支付暂未开启");
        }
        Orders orders = baseMapper.selectById(ordersId);
        if (orders != null) {
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));

            UserMoney userMoney = userMoneyService.selectUserMoneyByUserId(orders.getUserId());
            if (userMoney.getMoney().doubleValue() >= orders.getPayMoney().doubleValue()) {
                UserEntity userEntity = userService.selectUserById(orders.getUserId());


                userMoneyService.updateMoney(2, orders.getUserId(), orders.getPayMoney());
                UserMoneyDetails userMoneyDetails = new UserMoneyDetails();
                userMoneyDetails.setMoney(orders.getPayMoney());
                userMoneyDetails.setUserId(orders.getUserId());
                userMoneyDetails.setContent("零钱支付订单");
                userMoneyDetails.setTitle("下单成功，订单号：" + orders.getOrdersNo());
                userMoneyDetails.setType(2);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                userMoneyDetails.setCreateTime(simpleDateFormat.format(new Date()));
                userMoneyDetailsService.save(userMoneyDetails);
                MessageInfo messageInfo = new MessageInfo();
                messageInfo.setContent("订单下单成功：" + orders.getOrdersNo());
                messageInfo.setTitle("订单通知");
                messageInfo.setState(String.valueOf(4));
                messageInfo.setUserName(userEntity.getUserName());
                messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
                messageInfo.setCreateAt(simpleDateFormat.format(new Date()));
                messageInfo.setIsSee("0");
                messageService.saveBody(messageInfo);
                if (StringUtil.isNotBlank(userEntity.getClientid())) {
                    userService.pushToSingle(messageInfo.getTitle(), messageInfo.getContent(), userEntity.getClientid());
                }


                if (orders.getOrderTakingUserId() != null) {
                    orders.setState("5");
                } else {
                    orders.setState("4");
                }
                orders.setIsRemind(0);
                orders.setPayWay(1);
                orders.setIsPay(1);
                baseMapper.updateById(orders);
                UserEntity rideUser = userService.getById(orders.getOrderTakingUserId());

                //用户小程序下单通知
                if (rideUser == null && StringUtils.isNotBlank(userEntity.getOpenId())) {
                    sendOrderMeg(orders, userEntity, information, 1, 1, 1);
                }
                if (rideUser != null && StringUtils.isNotBlank(userEntity.getOpenId())) {
                    sendOrderMeg(orders, userEntity, information, 1, 18, 1);
                }

                if (rideUser != null && StringUtils.isNotBlank(rideUser.getShopOpenId())) {
                    sendOrderMeg(orders, rideUser, information, 1, 11, 2);
                }
                if (rideUser == null) {
                    String value = commonInfoService.findOne(336).getValue();
                    if ("是".equals(value)) {
                        List<UserEntity> riderList = userService.getUserByCityRider(orders.getCity());
                        for (UserEntity entity : riderList) {
                            if (StringUtils.isNotBlank(entity.getShopOpenId())) {
                                ordersService.sendOrderMeg(orders, entity, information, 2, 17, 2);
                            }
                        }
                    }
                }

                return Result.success();
            } else {
                return Result.error("账户不足，请充值！");
            }
        } else {
            return Result.error("订单不存在，请刷新后重试！");
        }
    }

    /**
     * @param orders
     * @param userEntity
     * @param type       模板类型
     * @param status
     * @param userType   1用户 2接单人
     */

    @Override
    public void sendOrderMeg(Orders orders, UserEntity userEntity, AppointInformation information, Integer type, Integer status, Integer userType) {
        UserEntity riderEntity = userService.getById(orders.getOrderTakingUserId());
        CommonInfo tempId;
        if (userType == 1) {
            tempId = commonInfoService.findOne(308);
        } else {
            tempId = commonInfoService.findOne(312);
        }
        List<String> msgList = new ArrayList<>();
        msgList.add(information.getCity() + "" + information.getDistrict() + "" + information.getHospitalName());
        msgList.add(information.getServiceName());
        msgList.add(information.getHopeTime());
        switch (status) {
            //支付成功的下单用户提示信息
            case 1:
                msgList.add("待接单");
                msgList.add("订单下单成功");
                break;
            //下单用户的订单被接单提示信息
            case 2:
                msgList.add("订单被接单");
                msgList.add("师傅电话:" + riderEntity.getPhone());
                break;
            //下单用户订单被转单的提示信息
            case 3:
                msgList.add("订单已被转给其他师傅");
                msgList.add("师傅电话:" + riderEntity.getPhone());
                break;
            //被转单的人的提示信息
            case 4:
                msgList.add("转单成功");
                msgList.add("该订单已被转单给其他人");
                break;
            //用户取消订单的提示信息
            case 5:
                msgList.add("订单取消");
                msgList.add("订单已成功取消,支付金额已原路返回");
                break;
            //用户取消订单后接单人的提示信息
            case 6:
                msgList.add("订单已被取消");
                msgList.add("用户已取消订单,,点击查看详情");
                break;
            //管理员派单给陪护人后陪护人的提示信息
            case 7:
                msgList.add("管理员派单");
                msgList.add("收到来自管理员派发的订单");
                break;
            //管理员转单给其他陪护人的提示信息(原接单人)
            case 8:
                msgList.add("管理员转单");
                msgList.add("你的一条订单已被管理员转单");
                break;
            //管理员转单给其他陪护人的提示信息(被转单的人)
            case 9:
                msgList.add("管理员转单");
                msgList.add("你收到一条管理员转发的订单");
                break;
            //系统派单
            case 10:
                msgList.add("系统退单");
                msgList.add("你收到一条系统推送的订单");
                break;
            case 11:
                msgList.add("您有一条新订单");
                msgList.add("点击查看订单详情");
                break;
            case 12:
                msgList.add("订单已完成");
                msgList.add("订单已完成,有疑问可联系客服处理");
                break;
            case 13:
                msgList.add("系统派单");
                msgList.add("您有一条系统自动派单的订单");
                break;
            case 14:
                msgList.add("新订单提示");
                msgList.add("您有一条来自他人转发的订单");
                break;
            case 15:
                msgList.add("已开始服务");
                msgList.add("订单已开始服务,点击后查看详情");
                break;
            case 16:
                msgList.add("订单取消");
                msgList.add("师傅取消订单,订单已返回接单池");
                break;
            case 17:
                msgList.add("新订单提醒");
                msgList.add("点击查看详情");
                break;
            case 18:
                msgList.add("已接单");
                msgList.add("下单成功");
                break;
            default:
                break;
        }
        if (userType == 1) {
            SenInfoCheckUtil.sendMsg(userEntity.getOpenId(), tempId.getValue(), msgList, type, orders.getOrdersId());
        } else {
            SenInfoCheckUtil.sendRiderMsg(userEntity.getShopOpenId(), tempId.getValue(), msgList, type, orders.getOrdersId());
        }
    }

    @Override
    public HashMap<String, Object> incomeAnalysisOrders(String time, Integer flag) {
        HashMap<String, Object> hashMap = new HashMap<>();
        //全部订单数
        int allCount = ordersDao.getOrdersCount(time, flag, null);
        //待结算订单数
        int waitSettlementCount = ordersDao.getOrdersCount(time, flag, 1);
        //待支付订单数
        int waitPaymentCount = ordersDao.getOrdersCount(time, flag, 0);
        //退款订单数
        int refundCount = ordersDao.getOrdersCount(time, flag, 3);
        //已结算订单数
        int settlementCount = ordersDao.getOrdersCount(time, flag, 2);
        hashMap.put("allCount", allCount);
        hashMap.put("waitPaymentCount", waitPaymentCount);
        hashMap.put("waitSettlementCount", waitSettlementCount);
        hashMap.put("refundCount", refundCount);
        hashMap.put("settlementCount", settlementCount);
        return hashMap;
    }

    @Override
    public Integer getOrdersRemind(Long userId) {
        return baseMapper.getOrdersRemind(userId);
    }

    @Override
    public int updateOrdersIsRemind(Long userId) {
        return baseMapper.updateOrdersIsRemind(userId);
    }

    @Override
    public Result selectTeamOrdersList(Integer page, Integer limit, Long userId, Integer type, Integer status) {
        return Result.success().put("data", new PageUtils(baseMapper.selectTeamOrdersList(new Page<>(page, limit), userId, type, status)));
    }

    @Override
    public Result selectTeamUserList(Integer page, Integer limit, String invitationCode, Integer type, Long userId) {
        if (type == 1) {
            return Result.success().put("data", new PageUtils(baseMapper.selectTeamUserList(new Page<>(page, limit), invitationCode, type, userId)));
        } else {
            return Result.success().put("data", new PageUtils(baseMapper.selectTeamUserList2(new Page<>(page, limit), type, userId)));
        }
    }

    @Override
    public Double selectOrdersMoneyCountByUserId(Long userId, Integer type, String time) {
        return baseMapper.selectOrdersMoneyCountByUserId(userId, type, time);
    }

    @Override
    public Integer selectUserCountByInvitationCode(String invitationCode, Integer type) {
        return baseMapper.selectUserCountByInvitationCode(invitationCode, type);
    }

    @Override
    public Result selectNewestOrders() {
        //下单信息
        List<Map<String, Object>> maps = baseMapper.selectNewestOrders();
        return Result.success().put("data", maps);
    }

    @Override
    public Result selectOrdersCountAndMoney(Long userId) {
        return Result.success().put("data", baseMapper.selectOrdersCountAndMoney(userId));
    }

    @Override
    public IPage<Orders> selectMyOrder(Integer page, Integer limit, Orders orders) {

        Page<Orders> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return getOtherInfo(ordersDao.selectMyOrder(pages, orders));
    }

    @Override
    public Result distributionOrder(Long orderId, Long userId) {
        Orders orders = baseMapper.selectById(orderId);
        UserEntity riderUser = userService.getById(userId);
        UserEntity userEntity = userService.getById(orders.getUserId());
        if (riderUser == null) {
            return Result.error("用户不存在！");
        }
        if (riderUser.getIsAuthentication() == null) {
            return Result.error("该用户尚未实名认证，请实名认证后进行接单！");
        }
        if (riderUser.getIsSafetyMoney() == null || riderUser.getIsSafetyMoney() != 1) {
            return Result.error("该用户尚未缴纳保证金，请缴纳后进行接单！");
        }

        orders.setOrderTakingUserId(riderUser.getUserId());
        orders.setRateProportion(riderUser.getRate());
        orders.setIsTransfer(2);
        orders.setState("5");
        ordersDao.updateById(orders);
        if (StringUtils.isNotBlank(riderUser.getOpenId())) {
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
            sendOrderMeg(orders, riderUser, information, 2, 7, 2);
        }
        if (StringUtils.isNotBlank(userEntity.getOpenId())) {
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
            sendOrderMeg(orders, userEntity, information, 1, 2, 1);
        }

        return Result.success();
    }

    @Override
    public IPage<Orders> errandGetOrderList(Integer page, Integer limit, Double latitude, Double longitude, Integer createTime, Integer hopeTime, String hospitalName, Long userId) {
        Page<Orders> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        if (longitude == null || latitude == null || longitude == 0 || latitude == 0) {
            longitude = 121.47;
            latitude = 31.23;
        }
        CommonInfo one = commonInfoService.findOne(235);
        String url = "https://apis.map.qq.com/ws/geocoder/v1/";
        Map<String, String> maps = new HashMap<>();
        maps.put("location", latitude + "," + longitude);
        maps.put("key", one.getValue());
        String data = HttpClientUtil.doGet(url, maps);
        JSONObject jsonObject = JSON.parseObject(data);
        String status = jsonObject.getString("status");
        if ("0".equals(status)) {
            JSONObject result = jsonObject.getJSONObject("result");
            JSONObject adInfo = result.getJSONObject("ad_info");
            String city = (String) adInfo.get("city");

            IPage<Orders> ordersIPage = baseMapper.errandGetOrderList(pages, latitude, longitude, city, createTime, hopeTime, hospitalName, userId);
            for (Orders record : ordersIPage.getRecords()) {
                record.setAppointInformation(informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", record.getOrdersId())));
            }
            return ordersIPage;
        } else {
            return null;
        }

    }

    @Override
    public Result rideCancelOrder(Long userId, Long orderId) {
        String value = commonInfoService.findOne(344).getValue();
        String score = commonInfoService.findOne(345).getValue();
        if ("否".equals(value)) {
            return Result.error("当前系统设置不允许取消订单");
        }
        Orders orders = ordersService.getById(orderId);
        if (orders.getOrderTakingUserId().equals(userId)) {
            UserEntity userEntity = userService.getById(orders.getUserId());
            AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));

            if (StringUtils.isNotBlank(userEntity.getOpenId())) {
                ordersService.sendOrderMeg(orders, userEntity, information, 2, 16, 1);
            }

            UserEntity riderUserEntity = userService.getById(orders.getOrderTakingUserId());
            if (riderUserEntity != null) {
                if (StringUtils.isNotBlank(score)) {
                    Integer scoreNum = Integer.parseInt(score);

                    if (scoreNum >= riderUserEntity.getCreditScore()) {
                        scoreNum = riderUserEntity.getCreditScore();
                    }
                    recordService.updateUserCreditRecord(riderUserEntity, 2, scoreNum, "取消订单扣除");
                }
            }

            return Result.upStatus(ordersDao.rideCancelOrder(orderId));
        } else {
            return Result.error("你无权操作当前订单");
        }

    }

    @Override
    public IPage<HashMap<String, Object>> getProfitList(Integer page, Integer limit, String time, Integer flag) {
        Page<HashMap<String, Object>> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return ordersDao.getProfitList(pages, time, flag);
    }

    @Override
    public HashMap<String, Object> selectOrdersDate(Integer flag, String time) {
        HashMap<String, Object> hashMap = new HashMap<>();
        //订单状态 订单状态0待支付1进行中2已完成3已退款 4待接单 5待服务
        //数量统计
        Integer sumXCOrdersCount = baseMapper.selectOrdersCount(flag, time, null);
        Integer dzfXCOrdersCount = baseMapper.selectOrdersCount(flag, time, 0);
        Integer jxzXCOrdersCount = baseMapper.selectOrdersCount(flag, time, 1);
        Integer ywcXCOrdersCount = baseMapper.selectOrdersCount(flag, time, 2);
        Integer ytkXCOrdersCount = baseMapper.selectOrdersCount(flag, time, 3);
        Integer dqdXCOrdersCount = baseMapper.selectOrdersCount(flag, time, 4);
        Integer dfwXCOrdersCount = baseMapper.selectOrdersCount(flag, time, 5);


        hashMap.put("sumXCOrdersCount", sumXCOrdersCount);
        hashMap.put("dzfXCOrdersCount", dzfXCOrdersCount);
        hashMap.put("jxzXCOrdersCount", jxzXCOrdersCount);
        hashMap.put("ywcXCOrdersCount", ywcXCOrdersCount);
        hashMap.put("ytkXCOrdersCount", ytkXCOrdersCount);
        hashMap.put("dqdXCOrdersCount", dqdXCOrdersCount);
        hashMap.put("dfwXCOrdersCount", dfwXCOrdersCount);
        //金额统计
        BigDecimal sumXCOrdersMoney = baseMapper.selectOrdersMoney(flag, time, null);
        BigDecimal dzfXCOrdersMoney = baseMapper.selectOrdersMoney(flag, time, 0);
        BigDecimal jxzXCOrdersMoney = baseMapper.selectOrdersMoney(flag, time, 1);
        BigDecimal ywcXCOrdersMoney = baseMapper.selectOrdersMoney(flag, time, 2);
        BigDecimal ytkXCOrdersMoney = baseMapper.selectOrdersMoney(flag, time, 3);
        BigDecimal dqdXCOrdersMoney = baseMapper.selectOrdersMoney(flag, time, 4);
        BigDecimal dfwXCOrdersMoney = baseMapper.selectOrdersMoney(flag, time, 5);

        hashMap.put("sumXCOrdersMoney", sumXCOrdersMoney);
        hashMap.put("dzfXCOrdersMoney", dzfXCOrdersMoney);
        hashMap.put("jxzXCOrdersMoney", jxzXCOrdersMoney);
        hashMap.put("ywcXCOrdersMoney", ywcXCOrdersMoney);
        hashMap.put("ytkXCOrdersMoney", ytkXCOrdersMoney);
        hashMap.put("dqdXCOrdersMoney", dqdXCOrdersMoney);
        hashMap.put("dfwXCOrdersMoney", dfwXCOrdersMoney);

        return hashMap;


    }

    @Override
    public IPage<HashMap<String, Object>> getPingIncome(Integer flag, String time, Integer page, Integer limit) {
        Page<HashMap<String, Object>> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return baseMapper.getPingIncome(pages, flag, time);
    }

    @Override
    public HashMap<String, Object> orderStatistics(Integer flag, String time) {

        HashMap<String, Object> hashMap = new HashMap<>();
        //交易总金额
        BigDecimal allMoney = ordersDao.orderStatistics(flag, time, 1);
        //陪诊/陪护佣金金额
        BigDecimal rateMoney = ordersDao.orderStatistics(flag, time, 2);
        //推广员佣金金额
        BigDecimal zhiRateMoney = ordersDao.orderStatistics(flag, time, 3);
        //代理商佣金金额
        BigDecimal feiRateMoney = ordersDao.orderStatistics(flag, time, 4);
        //平台金额
        BigDecimal pingRateMoney = ordersDao.orderStatistics(flag, time, 5);
        hashMap.put("allMoney", allMoney);
        hashMap.put("rateMoney", rateMoney);
        hashMap.put("zhiRateMoney", zhiRateMoney);
        hashMap.put("feiRateMoney", feiRateMoney);
        hashMap.put("pingRateMoney", pingRateMoney);
        return hashMap;


    }

    @Override
    public BigDecimal selectShopRateMoney(Long userId) {

        return baseMapper.selectShopRateMoney(userId);

    }

    @Override
    public IPage<HashMap<String, Object>> getRateUserList(Integer page, Integer limit, Long userId) {
        Page<HashMap<String, Object>> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return baseMapper.getRateUserList(pages, userId);


    }

    @Override
    public IPage<HashMap<String, Object>> getUserList(Integer page, Integer limit, String invitationCode) {
        Page<HashMap<String, Object>> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return baseMapper.getUserList(pages, invitationCode);
    }
}
