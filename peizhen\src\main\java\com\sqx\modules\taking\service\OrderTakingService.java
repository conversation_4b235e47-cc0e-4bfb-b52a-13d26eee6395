package com.sqx.modules.taking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.taking.entity.OrderTaking;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

public interface OrderTakingService extends IService<OrderTaking> {
    /**
     *
     */
    Result queryTaking(String city,String longitude, String latitude, Long userId, String like, Integer condition, Long bannerid, Long isRecommend, String id, IPage iPage, Long sex, String by,Integer classify,String salesNum);

    /**
     * 查询乐享低价系列
     *
     * @return
     */
    Result queryLowTaking(Integer page,Integer limit,String city,String longitude, String latitude, Long userId, String like, Integer condition, Long bannerid, Long isRecommend, String id,  Long sex, String by,Integer classify,String salesNum);


    /**
     * 接单详情
     */
    Result queryTakingDetails(Long id, Long userId,String longitude,String latitude);

    Result queryTakingDetails(Long id,String longitude,String latitude);

    /**
     * 发布接单
     */
    Result insertOrderTaking(String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money,BigDecimal memberMoney, String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,String detailsImg,Integer authentication,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard);

    /**
     * 查看我的发布
     *
     * @return
     */
    Result selectMyRelease(Long userId, Long page, Long limit, String status);

    /**
     * 删除接单
     */
    Result deleteOrderTaking(Long id);

    /**
     * 查询所有接单
     */
    Result queryAllOrderTaking(Integer page,Integer limit,String city, String gameId, Long status, String userName, Long userId,Integer classify);

    /**
     * 审核接单
     */
    Result auditorOrderTaking(Long id, Integer status,String content);

    /**
     * 修改发布订单
     */
    Result updateTakingStatus(Long id, Integer status,String content);

    /**
     * 删除发布接单
     */
    Result deleteTaking(Long id);

    /**
     * 查询接单详情
     */
    Result queryTakingOrder(Long id);

    /**
     * 修改接单详情
     */
    Result updateTakingOrder(Long id, String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money, String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,Integer authentication,String detailsImg,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard);

    Result updateTakingOrders(Long id, String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money,BigDecimal memberMoney,BigDecimal oldMoney, String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,Integer authentication,String detailsImg,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard);

    /**
     * 查看我的接单
     */
    Result queryMyTakingOrder(Long userId,Long page, Long limit,Long status);

    Result selectShopData(@RequestAttribute Long userId,String startTime,String endTime);
}
