package com.sqx.modules.app.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description user_certification
 * @date 2021-08-13
 */
@Data
@ApiModel("user_certification")
public class UserCertification implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 用户实名认证id
     */
    @ApiModelProperty("用户实名认证id")
    private Long id;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String name;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String idNumber;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    @TableField(exist = false)
    private UserEntity userEntity;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createTime;
    /**
     * 正面
     */
    private String front;
    /**
     * 反面
     */
    private String back;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 说明
     */
    private String remek;

    /**
     * 修改时间
     */
    private String updateTime;


    private String phone;

    private String birth;

    private Integer sex;

    private Integer age;

    /**
     * 1陪诊认证 2陪护认证
     */
    private Integer authentication;
    /**
     * 护龄时长
     */
    private Integer workAge;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 县
     */
    private String district;
    /**
     * 其他资质
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String aptitude;
    /**
     * 头像
     */
    private String avatar;

    /**
     * 服务项目id
     */
    private String serviceIds;
    /**
     * 服务项目名称
     */
    @TableField(exist = false)
    private String serviceName;

    /**
     * 个人简介
     */
    private String details;

    public UserCertification() {
    }
}
