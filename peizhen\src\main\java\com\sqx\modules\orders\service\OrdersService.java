package com.sqx.modules.orders.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.response.OrderAllResponse;
import com.sqx.modules.utils.excel.ExcelData;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;


public interface OrdersService extends IService<Orders> {

    int selectMyOrdersCount(Long userId, String time);

    int selectTakeOrdersCount(Long userId, String time);


    /**
     * 生成订单
     *
     * @param userId
     * @param couponId
     * @param serviceType
     * @param type
     * @return
     */
    Result generateOrder(Long userId, Long couponId, Integer serviceType, Integer orderType, Long orderTakingUserId, AppointInformation information);

    Result updateOrdersAddress(Long ordersId, Long addressId);

    Result selectOrderList(Integer page, Integer limit, Integer createTime, Integer distance, String longitude, String latitude);

    Result insertMyOrders(Long ordersId, Long userId, String startImg);

    /**
     * 生成充值订单
     *
     * @param userId
     * @param money
     * @return
     */
    Result investOrder(Long userId, Double money, Integer classify);


    Result giveOrdersUser(UserEntity user, Long orderId);

    /**
     * 取消订单
     *
     * @param id
     * @param status
     * @return
     */
    Result cancelOrder(Long id, String status, String code, String endImg, String startImg, BigDecimal refundMoney);

    /**
     * 删除订单
     *
     * @param id
     * @param
     * @return
     */
    Result deleteOrder(Long id);

    /**
     * \
     * 查询订单详情
     *
     * @param id
     * @return
     */
    Result queryOrders(Long id);

    /**
     * 查看所有订单
     */
    IPage<OrderAllResponse> queryOrdersAll(Long page, Long limit, Long type, String name, String userName, Long status, Long userId, String ordersNo, String startTime, String endTIme, Long hospitalId);

    ExcelData ordersListExcel(Long type, String name, Long status, Long userId, String ordersNo, String startTime, String endTIme);

    IPage<Orders> selectMyTakeOrders(Integer page, Integer limit, Long userId, String state);

    IPage<Orders> selectNowDayOrders(Integer page, Integer limit, Long userId);

    Result payMoney(Long ordersId);


    void sendOrderMeg(Orders orders, UserEntity userEntity, AppointInformation information, Integer type, Integer status, Integer userType);

    HashMap<String, Object> incomeAnalysisOrders(String time, Integer flag);

    Integer getOrdersRemind(Long userId);

    int updateOrdersIsRemind(Long userId);

    Result selectTeamOrdersList(Integer page, Integer limit, Long userId, Integer type, Integer status);

    Result selectTeamUserList(Integer page, Integer limit, String invitationCode, Integer type, Long userId);

    Double selectOrdersMoneyCountByUserId(Long userId, Integer type, String time);

    Integer selectUserCountByInvitationCode(String invitationCode, Integer type);

    Result selectNewestOrders();

    Result selectOrdersCountAndMoney(Long userId);

    IPage<Orders> selectMyOrder(Integer page, Integer limit, Orders orders);


    Result distributionOrder(Long orderId, Long userId);

    IPage<Orders> errandGetOrderList(Integer page, Integer limit, Double latitude, Double longitude, Integer createTime, Integer hopeTime, String hospitalName, Long userId);

    Result rideCancelOrder(Long userId, Long orderId);

    IPage<HashMap<String,Object>> getProfitList(Integer page, Integer limit, String time, Integer flag);

    HashMap<String,Object> selectOrdersDate(Integer flag, String time);

    IPage<HashMap<String,Object>> getPingIncome(Integer flag, String time, Integer page, Integer limit);

    HashMap<String,Object> orderStatistics(Integer flag, String time);

    BigDecimal selectShopRateMoney(Long userId);

    IPage<HashMap<String,Object>> getRateUserList(Integer page, Integer limit, Long userId);

    IPage<HashMap<String, Object>> getUserList(Integer page, Integer limit, String invitationCode);

}
