(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["components/wm-poster/wm-posterorders"],{

/***/ 907:
/*!********************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/wm-poster/wm-posterorders.vue ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./wm-posterorders.vue?vue&type=template&id=78939f1a& */ 908);
/* harmony import */ var _wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wm-posterorders.vue?vue&type=script&lang=js& */ 910);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs




/* normalize component */

var component = Object(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__["render"],
  _wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "components/wm-poster/wm-posterorders.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 908:
/*!***************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/wm-poster/wm-posterorders.vue?vue&type=template&id=78939f1a& ***!
  \***************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wm-posterorders.vue?vue&type=template&id=78939f1a& */ 909);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_template_id_78939f1a___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 909:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/wm-poster/wm-posterorders.vue?vue&type=template&id=78939f1a& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 910:
/*!*********************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/wm-poster/wm-posterorders.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wm-posterorders.vue?vue&type=script&lang=js& */ 911);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_wm_posterorders_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 911:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/wm-poster/wm-posterorders.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 47));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 49));
//
//
//
//
//
//
//
//
//
//

var _this;
var _default = {
  name: 'wm-poster',
  props: {
    CanvasID: {
      //CanvasID 等同于 canvas-id
      Type: String,
      default: 'PosterCanvas'
    },
    imgSrc: {
      //展示图
      Type: String,
      default: ''
    },
    QrSrc: {
      //二维码
      Type: String,
      default: ''
    },
    Title: {
      //文本内容
      Type: String,
      default: ''
    },
    TitleColor: {
      //标题颜色
      Type: String,
      default: '#000000'
    },
    LineType: {
      //标题显示行数		（注超出2行显示会导致画布布局絮乱）
      Type: [String, Boolean],
      default: true
    },
    PriceTxt: {
      //价格值
      Type: String,
      default: ''
    },
    PriceColor: {
      //价格颜色
      Type: String,
      default: '#e31d1a'
    },
    OriginalTxt: {
      //原价值
      Type: String,
      default: ''
    },
    OriginalColor: {
      //默认颜色（如原价与扫描二维码颜色）
      Type: String,
      default: '#b8b8b8'
    },
    Width: {
      //画布宽度  (高度根据图片比例计算 单位upx)
      Type: String,
      default: 700
    },
    CanvasBg: {
      //canvas画布背景色
      Type: String,
      default: '#ffffff'
    },
    Referrer: {
      //推荐人信息
      Type: String,
      default: ''
    },
    ViewDetails: {
      //描述提示文字
      Type: String,
      default: '长按或扫描识别二维码'
    }
  },
  data: function data() {
    return {
      loading: false,
      tempFilePath: '',
      canvasW: 0,
      canvasH: 0,
      canvasImgSrc: '',
      ctx: null
    };
  },
  methods: {
    toSave: function toSave(url) {
      uni.getImageInfo({
        src: url,
        success: function success(image) {
          console.log('图片信息：', JSON.stringify(image));
          uni.saveImageToPhotosAlbum({
            filePath: image.path,
            success: function success() {
              console.log('save success');
              uni.showToast({
                title: '海报已保存相册',
                icon: 'success',
                duration: 2000
              });
            }
          });
        }
      });
    },
    OnCanvas: function OnCanvas() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var C_W, C_P, C_Q, _strlineW, _imgInfo, _QrCode, r, q, imgW, _strLastIndex, _strHeight, _num, i;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _this2.loading = true;
                // this.$queue.showLoading('海报生成中...');
                _this.ctx = uni.createCanvasContext(_this.CanvasID, _this2);
                C_W = uni.upx2px(_this.Width), C_P = uni.upx2px(30), C_Q = uni.upx2px(150); //二维码或太阳码宽高
                _strlineW = 0; //文本宽度
                _context.next = 6;
                return _this.getImageInfo({
                  imgSrc: _this.imgSrc
                });
              case 6:
                _imgInfo = _context.sent;
                _context.next = 9;
                return _this.getImageInfo({
                  imgSrc: _this.QrSrc
                });
              case 9:
                _QrCode = _context.sent;
                //二维码或太阳码
                r = [_imgInfo.width, _imgInfo.height];
                q = [_QrCode.width, _QrCode.height];
                imgW = C_W - C_P * 2 - 8;
                if (r[0] != imgW) {
                  r[1] = Math.floor(imgW / r[0] * r[1]);
                  r[0] = imgW;
                }
                if (q[0] != C_Q) {
                  q[1] = Math.floor(C_Q / q[0] * q[1]);
                  q[0] = C_Q;
                }
                _this.canvasW = C_W;
                _this.canvasH = r[1] + q[1] + 68;
                _this.ctx.setFillStyle(_this.CanvasBg); //canvas背景颜色
                _this.ctx.fillRect(0, 0, C_W, _this.canvasH); //canvas画布大小

                //添加图片展示
                _this.ctx.drawImage(_imgInfo.path, C_P, C_P, r[0], r[1]);
                //添加图片展示 end

                //设置文本

                _this.ctx.setFillStyle(_this.TitleColor); //设置标题文本颜色
                _strLastIndex = 0; //每次开始截取的字符串的索引
                _strHeight = r[1] + C_P * 2 + 10; //绘制字体距离canvas顶部的初始高度
                _num = 1;
                i = 0;
              case 25:
                if (!(i < _this.Title.length)) {
                  _context.next = 47;
                  break;
                }
                _strlineW += _this.ctx.measureText(_this.Title[i]).width;
                if (!(_strlineW > r[0])) {
                  _context.next = 43;
                  break;
                }
                if (!(_num == 2 && _this.LineType)) {
                  _context.next = 36;
                  break;
                }
                //文字换行数量大于二进行省略号处理
                _this.ctx.fillText(_this.Title.substring(_strLastIndex, i - 8) + '...', C_P, _strHeight);
                _strlineW = 0;
                _strLastIndex = i;
                _num++;
                return _context.abrupt("break", 47);
              case 36:
                _this.ctx.fillText(_this.Title.substring(_strLastIndex, i), C_P, _strHeight);
                _strlineW = 0;
                _strHeight += 20;
                _strLastIndex = i;
                _num++;
              case 41:
                _context.next = 44;
                break;
              case 43:
                if (i == _this.Title.length - 1) {
                  _this.ctx.fillText(_this.Title.substring(_strLastIndex, i + 1), C_P, _strHeight);
                  _strlineW = 0;
                }
              case 44:
                i++;
                _context.next = 25;
                break;
              case 47:
                //设置文本 end

                //添加二维码
                _strHeight += uni.upx2px(20);
                _this.ctx.drawImage(_QrCode.path, r[0] - q[0] + C_P, _strHeight, q[0], q[1]);
                //添加二维码 end

                //添加推荐人与描述
                _this.ctx.setFillStyle(_this.TitleColor);
                _this.ctx.setFontSize(uni.upx2px(30));
                _this.ctx.fillText(_this.Referrer, C_P, _strHeight + q[1] / 2);
                _this.ctx.setFillStyle(_this.OriginalColor);
                _this.ctx.setFontSize(uni.upx2px(24));
                _this.ctx.fillText(_this.ViewDetails, C_P, _strHeight + q[1] / 2 + 20);
                //添加推荐人与描述 end
                //延迟后渲染至canvas上
                setTimeout(function () {
                  _this.ctx.draw(true, function (ret) {
                    _this.getNewImage();
                  });
                }, 600);
              case 56:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    getImageInfo: function getImageInfo(_ref) {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var imgSrc;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                imgSrc = _ref.imgSrc;
                return _context2.abrupt("return", new Promise(function (resolve, errs) {
                  uni.getImageInfo({
                    src: imgSrc,
                    success: function success(image) {
                      resolve(image);
                    },
                    fail: function fail(err) {
                      errs(err);
                      _this.$queue.showToast('海报生成失败');
                      uni.hideLoading();
                    }
                  });
                }));
              case 2:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    getNewImage: function getNewImage() {
      uni.canvasToTempFilePath({
        canvasId: _this.CanvasID,
        quality: 1,
        complete: function complete(res) {
          _this.tempFilePath = res.tempFilePath;
          _this.$emit('success', res);
          _this.loading = false;
          _this.$queue.showToast('长按图片保存海报');
          uni.hideLoading();
        }
      }, this);
    }
  },
  mounted: function mounted() {
    _this = this;
    this.OnCanvas();
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ })

}]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/wm-poster/wm-posterorders.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/wm-poster/wm-posterorders-create-component',
    {
        'components/wm-poster/wm-posterorders-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(907))
        })
    },
    [['components/wm-poster/wm-posterorders-create-component']]
]);
