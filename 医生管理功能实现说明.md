# 医生管理功能实现说明

## 功能概述

本次实现了完整的医生管理功能，包括后端API接口和管理端页面，支持医生信息的增删改查操作。

## 实现的功能

### 1. 数据库设计

**表名**: `doctor`

**主要字段**:
- `doctor_id`: 医生主键ID
- `hospital_id`: 关联医院ID
- `department_id`: 关联科室ID
- `doctor_name`: 医生姓名
- `doctor_code`: 医生工号(唯一)
- `phone`: 联系电话
- `email`: 邮箱
- `gender`: 性别
- `birth_date`: 出生日期
- `id_card`: 身份证号
- `avatar`: 头像
- `title`: 职称
- `professional_level`: 专业技术等级
- `education`: 学历
- `specialty`: 专业特长
- `introduction`: 医生简介
- `tag`: 标签
- `remarks`: 备注
- `qualification_cert`: 执业资格证书图片
- `license_number`: 执业证书编号
- `practice_scope`: 执业范围
- `entry_date`: 入职日期
- `work_status`: 工作状态
- `sort`: 排序
- `is_enable`: 是否启用
- `create_time`: 创建时间
- `update_time`: 更新时间

### 2. 后端实现

#### 2.1 实体类
- `Doctor.java`: 医生实体类，包含所有字段定义和验证注解

#### 2.2 数据访问层
- `DoctorDao.java`: 数据访问接口
- `DoctorDao.xml`: MyBatis映射文件，包含分页查询和关联查询

#### 2.3 业务逻辑层
- `DoctorService.java`: 服务接口
- `DoctorServiceImpl.java`: 服务实现类，包含业务逻辑和数据验证

#### 2.4 控制器层
- `DoctorController.java`: REST API控制器

#### 2.5 API接口

**基础CRUD接口**:
- `GET /doctor/list` - 分页查询医生列表
- `GET /doctor/info/{doctorId}` - 根据ID查询医生信息
- `POST /doctor/save` - 新增医生
- `POST /doctor/update` - 修改医生
- `POST /doctor/delete` - 删除医生
- `POST /doctor/updateStatus` - 启用/禁用医生

**查询参数支持**:
- `doctorName`: 医生姓名模糊查询
- `hospitalId`: 医院ID筛选
- `departmentId`: 科室ID筛选
- `workStatus`: 工作状态筛选
- `isEnable`: 启用状态筛选

#### 2.6 辅助接口

为支持前端下拉选择，在现有控制器中添加了：
- `GET /admin/hospital/selectHospitalList` - 获取医院列表
- `GET /admin/department/selectDepartmentList` - 获取科室列表

### 3. 前端实现

#### 3.1 页面文件
- `doctorList.vue`: 医生列表页面
- `doctor-add-or-update.vue`: 医生新增/编辑页面

#### 3.2 功能特性

**列表页面功能**:
- 分页显示医生列表
- 多条件搜索筛选
- 批量删除操作
- 状态切换（启用/禁用）
- 新增/编辑/删除操作

**新增/编辑页面功能**:
- 表单验证
- 医院科室级联选择
- 图片上传（头像、证书）
- 丰富的字段输入支持

#### 3.3 路由配置
在 `router/index.js` 中添加了医生管理页面路由：
```javascript
{path: '/doctorList', component: _import('doctor/doctorList'), name: 'doctorList', meta: {title: '医生管理', isTab: true}}
```

### 4. 数据验证

#### 4.1 后端验证
- 医生姓名必填
- 医院和科室必选
- 科室必须属于选择的医院
- 医生工号唯一性验证
- 手机号格式验证
- 邮箱格式验证
- 身份证号格式验证

#### 4.2 前端验证
- 表单字段必填验证
- 格式验证（手机号、邮箱、身份证）
- 图片上传格式和大小限制

### 5. 测试数据

在数据库脚本中添加了2条测试数据，用于功能验证。

## 使用说明

### 1. 数据库初始化
执行 `peizhen/db/peizhen.sql` 中的医生表创建语句和测试数据。

### 2. 后端启动
确保Spring Boot应用正常启动，医生管理相关的API接口会自动注册。

### 3. 前端访问
在管理后台中访问医生管理页面，可以进行医生信息的管理操作。

## 技术特点

1. **完整的CRUD操作**: 支持医生信息的完整生命周期管理
2. **关联查询**: 支持医院、科室信息的关联显示
3. **数据验证**: 前后端双重验证确保数据质量
4. **用户友好**: 直观的界面设计和操作流程
5. **扩展性**: 预留了扩展字段，便于后续功能增强

## 注意事项

1. 医生工号必须唯一
2. 科室必须属于选择的医院
3. 删除医生前需要确认没有关联的业务数据
4. 图片上传需要配置文件存储服务

这个医生管理功能已经完整实现，可以直接投入使用。
