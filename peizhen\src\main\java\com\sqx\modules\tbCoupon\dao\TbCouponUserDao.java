package com.sqx.modules.tbCoupon.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.tbCoupon.entity.TbCouponUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Mapper
public interface TbCouponUserDao extends BaseMapper<TbCouponUser> {

    IPage<TbCouponUser> getMyCouponList(@Param("pages") Page<TbCouponUser> pages, @Param("couponUser") TbCouponUser couponUser);
}
