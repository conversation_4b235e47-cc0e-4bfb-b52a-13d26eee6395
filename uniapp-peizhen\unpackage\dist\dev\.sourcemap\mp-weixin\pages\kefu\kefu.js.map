{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/kefu/kefu.vue?3c50", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/kefu/kefu.vue?6933", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/kefu/kefu.vue?a74f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/kefu/kefu.vue?f42e", "uni-app:///pages/kefu/kefu.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/kefu/kefu.vue?22eb", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/kefu/kefu.vue?9c8a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "image", "isWeiXin", "weixin", "webviewStyles", "progress", "color", "SelKeFu", "onLoad", "console", "onPullDownRefresh", "uni", "methods", "copyHref", "success", "saveImg", "filePath", "that", "rests", "title", "mask", "duration", "icon", "window", "goChat", "extInfo", "url", "corpId", "fail", "goLoginInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwBvvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;UACAC;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;;IAOA;IACA;MACA;QACA;UACAC;UACA;QACA;MACA;IACA;IACA;IACA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;EACA;;EACAC;IACA;IACAC;MAAA;MACAF;QACAX;QACAc;UACA;QACA;MACA;IACA;IACAC;MACA;MACAJ;QACAK;QACAF;UACAG;QACA;MACA;IACA;IACAC;MACAP;QACAQ;QACAC;QACAC;QACAC;MACA;MACAC;IACA;IACA;IACAC;MACA;QAEA;QACA;UACA7B;YACA8B;cACAC;YACA;YACAC;YACAb;YACAc;cACAnB;YACA;UACA;QACA;UACAA;UACAE;YACAQ;UACA;QACA;MAmBA;QACA;QACA;UACAR;YACAe;UACA;QACA;UACA;QACA;MACA;IAEA;IACA;IACAG;MACAlB;QACAe;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAA0iC,CAAgB,q8BAAG,EAAC,C;;;;;;;;;;;ACA9jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/kefu/kefu.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/kefu/kefu.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./kefu.vue?vue&type=template&id=f9b472b4&\"\nvar renderjs\nimport script from \"./kefu.vue?vue&type=script&lang=js&\"\nexport * from \"./kefu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./kefu.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/kefu/kefu.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kefu.vue?vue&type=template&id=f9b472b4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kefu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kefu.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"height: 100vh;margin: 32upx;\">\r\n\t\t<view style=\"text-align: center;background: #FFFFFF;padding: 40upx;border-radius: 32upx;\">\r\n\t\t\t<view style=\"font-size: 38upx;color: #000\">添加客服微信咨询</view>\r\n\t\t\t<view style=\"font-size: 32upx;margin-top: 32upx;color: #000\">微信号：{{weixin}}</view>\r\n\t\t\t<view @click=\"copyHref\"\r\n\t\t\t\tstyle=\"background: #5E81F9;width:200upx;margin-top: 32upx;font-size: 30upx;margin-left: 36%;color: #FFFFFF;padding: 4upx 20upx;border-radius: 24upx;\">\r\n\t\t\t\t一键复制</view>\r\n\r\n\t\t\t<image @click=\"saveImg\" mode=\"aspectFit\" style=\"margin-top: 32upx\" :src=\"image\"></image>\r\n\t\t\t<view style=\"font-size: 28upx;color: #000;margin-top: 32upx\" v-if=\"isWeiXin\">\r\n\t\t\t\t{{ isWeiXin ? '长按识别上方二维码' : '' }}\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<!-- <view @click=\"goChat\"\r\n\t\t\t\tstyle=\"width:260upx;margin-top: 32upx;font-size: 30upx;margin-left: 28%;color: #5E81F9;padding: 4upx 20upx;border-radius: 24upx;\">\r\n\t\t\t\t联系在线客服</view> -->\r\n\t\t\t<view v-if=\"isWeiXin\" style=\"font-size: 24upx;color: #FFFFFF;margin-top: 80upx\" @click=\"rests\">无法识别？</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timage: 'https://game.shengqianxiong.com.cn/custom.jpg',\r\n\t\t\t\tisWeiXin: false,\r\n\t\t\t\tweixin: '710070994',\r\n\t\t\t\twebviewStyles: {\r\n\t\t\t\t\tprogress: {\r\n\t\t\t\t\t\tcolor: '#1A1929 '\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tSelKeFu: '2',\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.SelKeFu = this.$queue.getData('SelKeFu');\r\n\t\t\t// #ifdef H5\r\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\tthis.isWeiXin = true;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t//获取客服二维码\r\n\t\t\tthis.$Request.getT('/app/common/type/1').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\tconsole.log(res.data.value)\r\n\t\t\t\t\t\tthis.image = res.data.value;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\n\t\t\t//获取客服微信号\r\n\t\t\tthis.$Request.getT('/app/common/type/331').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\tthis.weixin = res.data.value;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tuni.stopPullDownRefresh(); // 停止刷新\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//邀请码复制\r\n\t\t\tcopyHref() {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: this.weixin,\r\n\t\t\t\t\tsuccess: r => {\r\n\t\t\t\t\t\tthis.$queue.showToast('复制成功');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsaveImg() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\tfilePath: that.image,\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tthat.$queue.showToast('保存成功');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\trests() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已刷新请再次长按识别',\r\n\t\t\t\t\tmask: false,\r\n\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\twindow.location.reload();\r\n\t\t\t},\r\n\t\t\t// 在线客服\r\n\t\t\tgoChat() {\r\n\t\t\t\tif (this.SelKeFu === '1') {\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tlet that = this\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\twx.openCustomerServiceChat({\r\n\t\t\t\t\t\t\textInfo: {\r\n\t\t\t\t\t\t\t\turl: that.$queue.getData('SelKeFuLink')\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcorpId: that.$queue.getData('SelKeFuAppId'),\r\n\t\t\t\t\t\t\tsuccess(res) {},\r\n\t\t\t\t\t\t\tfail(res) {\r\n\t\t\t\t\t\t\t\tconsole.error(res)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error(\"catchcatch\" + error)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请更新至微信最新版本'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\tlet url = this.$queue.getData('SelKeFuLink');\r\n\t\t\t\t\tif (url.indexOf('/pages/') !== -1 || url.indexOf('/my/') !== -1) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//#ifndef H5\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/index/webView?url=' + url\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t//#endif\r\n\t\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\t\twindow.location.href = url;\r\n\t\t\t\t\t\t//#endif\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet token = this.$queue.getData('token');\r\n\t\t\t\t\tif (token) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/my/setting/chat'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.goLoginInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\t//统一登录跳转\r\n\t\t\tgoLoginInfo() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/public/loginphone'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\t/* @import '../../static/css/index.css'; */\r\n\r\n\tpage {\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kefu.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./kefu.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447625799\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}