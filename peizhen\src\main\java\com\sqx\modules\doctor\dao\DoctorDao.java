package com.sqx.modules.doctor.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.doctor.entity.Doctor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 医生管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Mapper
public interface DoctorDao extends BaseMapper<Doctor> {

    /**
     * 分页查询医生列表（带关联查询）
     */
    IPage<Doctor> selectDoctorPage(@Param("page") Page<Doctor> page,
                                   @Param("doctorName") String doctorName,
                                   @Param("hospitalId") Long hospitalId,
                                   @Param("departmentId") Long departmentId,
                                   @Param("workStatus") Integer workStatus,
                                   @Param("isEnable") Integer isEnable);
}
