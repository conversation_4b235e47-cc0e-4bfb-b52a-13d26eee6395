package com.sqx.modules.rewardLevel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.DateUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.sqx.modules.message.dao.MessageInfoDao;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.rewardLevel.entity.RewardLevel;
import com.sqx.modules.rewardLevel.dao.RewardLevelDao;
import com.sqx.modules.rewardLevel.service.RewardLevelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RewardLevelServiceImpl extends ServiceImpl<RewardLevelDao, RewardLevel> implements RewardLevelService {
    @Autowired
    private UserService userService;
    @Autowired
    private OrdersService ordersService;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private MessageService messageService;

    @Override
    public Result saveLevel(RewardLevel rewardLevel) {
        QueryWrapper<RewardLevel> wrapper = new QueryWrapper<RewardLevel>().eq("order_count", rewardLevel.getOrderCount());
        if (rewardLevel.getRewardId() != null) {
            wrapper.ne("reward_id", rewardLevel.getRewardId());
        }
        RewardLevel level = baseMapper.selectOne(wrapper);
        if (level != null) {
            return Result.error("请勿添加重复分佣比例或完成订单数");
        }
        if (rewardLevel.getRewardId() != null) {
            return Result.upStatus(baseMapper.updateById(rewardLevel));
        } else {
            rewardLevel.setCreateTime(LocalDateTime.now());
            return Result.upStatus(baseMapper.insert(rewardLevel));
        }


    }

    @Override
    public IPage<RewardLevel> getLevelList(Integer page, Integer limit, RewardLevel rewardLevel) {
        Page<RewardLevel> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return baseMapper.selectPage(pages, new QueryWrapper<>(rewardLevel).orderByDesc("create_time"));
    }

    @Override
    public void checkOrderCount(Long orderTakingUserId) {
        String cycle = "";
            LocalDate now = LocalDate.now();
        //计算开始时间
        LocalDate startDay = LocalDate.now();
        //计算结束时间
        LocalDate endDay = LocalDate.now();
        //计算周期
        CommonInfo cycleInfo = commonInfoService.findOne(347);
        if (cycleInfo == null || StringUtils.isBlank(cycleInfo.getValue())) {
            return;
        }
        int value = Integer.parseInt(cycleInfo.getValue());
        QueryWrapper<Orders> wrapper = new QueryWrapper<Orders>().eq("order_taking_user_id", orderTakingUserId).eq("state", 2);
        //周
        if (value == 1) {
            startDay = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            endDay = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
            cycle = "周";
            //月
        } else if (value == 2) {
            startDay = now.with(TemporalAdjusters.firstDayOfMonth());
            endDay = now.with(TemporalAdjusters.lastDayOfMonth());
            cycle = "月";
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        wrapper.between("create_time", startDay.format(df), endDay.format(df));
        //获取当前计算周期内的订单数
        int count = ordersService.count(wrapper);
        RewardLevel rewardLevel = baseMapper.selectOne(new QueryWrapper<RewardLevel>().eq("order_count", count + 1).eq("is_enable", 1));
        if (rewardLevel != null) {
            UserEntity userEntity = new UserEntity();
            userEntity.setUserId(orderTakingUserId);
            userEntity.setRate(rewardLevel.getScale());
            userEntity.setRewardId(rewardLevel.getRewardId());
            userEntity.setLevelName(rewardLevel.getLevelName());
            userEntity.setIconImg(rewardLevel.getIconImg());
            userService.updateById(userEntity);

            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setContent("你在当前" + cycle + "内完成" + rewardLevel.getOrderCount() + "单目标达成,当前分佣比例为" + userEntity.getRate());
            messageInfo.setTitle("你在当前" + cycle + "内完成" + rewardLevel.getOrderCount() + "单目标达成,当前分佣比例为" + userEntity.getRate());
            messageInfo.setState(String.valueOf(5));
            messageInfo.setUserName(userEntity.getUserName());
            messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
            messageInfo.setCreateAt(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
            messageInfo.setIsSee("0");
            messageService.saveBody(messageInfo);
        }

    }
}
