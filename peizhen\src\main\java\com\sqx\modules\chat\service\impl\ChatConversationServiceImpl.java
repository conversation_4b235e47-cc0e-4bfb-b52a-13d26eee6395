package com.sqx.modules.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.chat.dao.ChatContentDao;
import com.sqx.modules.chat.dao.ChatConversationDao;
import com.sqx.modules.chat.entity.ChatContent;
import com.sqx.modules.chat.entity.ChatConversation;
import com.sqx.modules.chat.service.ChatContentService;
import com.sqx.modules.chat.service.ChatConversationService;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.locks.ReentrantReadWriteLock;


@Service
public class ChatConversationServiceImpl extends ServiceImpl<ChatConversationDao, ChatConversation> implements ChatConversationService {

    private ReentrantReadWriteLock reentrantReadWriteLock=new ReentrantReadWriteLock();



    @Override
    public PageUtils selectChatConversationPage(Integer page, Integer limit, Long userId,String nickName){
        return new PageUtils(baseMapper.selectChatConversationPage(new Page<>(page,limit),userId,nickName));
    }


    @Override
    public int insertChatConversation(ChatConversation chatConversation) {
        return baseMapper.insertChatConversation(chatConversation);
    }

    @Override
    public ChatConversation selectChatConversation(Long userId,Long focusedUserId){
        ChatConversation chatConversation = baseMapper.selectOne(new QueryWrapper<ChatConversation>().eq("user_id", userId).eq("focused_user_id", focusedUserId));
        if(chatConversation!=null){
            return chatConversation;
        }
        return baseMapper.selectOne(new QueryWrapper<ChatConversation>().eq("focused_user_id", userId).eq("user_id", focusedUserId));
    }


    @Override
    public Result insertChatConversations(ChatConversation chatConversation){
        reentrantReadWriteLock.writeLock().lock();
        try{
            ChatConversation chatConversation1 = selectChatConversation(chatConversation.getUserId(), chatConversation.getFocusedUserId());
            if(chatConversation1==null){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = sdf.format(new Date());
                chatConversation.setCreateTime(format);
                chatConversation.setUpdateTime(format);
                baseMapper.insertChatConversation(chatConversation);
                return Result.success().put("data",chatConversation);
            }
            return Result.success().put("data",chatConversation1);
        }catch (Exception e){
            e.printStackTrace();
            log.error("发起聊天出错"+e.getMessage(),e);
        }finally {
            reentrantReadWriteLock.writeLock().unlock();
        }
        return Result.error("系统繁忙，请稍后再试");
    }



}