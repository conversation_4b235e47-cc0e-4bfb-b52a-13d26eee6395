package com.sqx.modules.hospitalModular.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.sqx.modules.hospitalModular.service.HospitalModularService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * 服务类型管理-管理端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@RestController
@Api(value = "服务类型管理-管理端", tags = {"服务类型管理-管理端"})
@RequestMapping("/admin/hospitalModular/")
public class AdminHospitalModularController {
    @Autowired
    private HospitalModularService modularService;

    /**
     * 新增服务类型
     * @param hospitalModular
     * @return
     */
    @ApiOperation("新增服务类型")
    @PostMapping("addModular")
    public Result addModular(HospitalModular hospitalModular) {
        return modularService.addModular(hospitalModular);
    }

    /**
     * 修改服务类型
     * @param hospitalModular
     * @return
     */
    @ApiOperation("修改服务类型")
    @PostMapping("updateModular")
    public Result updateModular(HospitalModular hospitalModular) {
        return modularService.updateModular(hospitalModular);
    }

    /**
     * 删除服务类型
     * @param modularId
     * @return
     */
    @ApiOperation("删除服务类型")
    @GetMapping("deleteModular")
    public Result deleteModular(Long modularId) {
        return modularService.deleteModular(modularId);
    }

    /**
     * 获取服务类型详情
     * @param modularId
     * @return
     */
    @ApiOperation("获取服务类型详情")
    @GetMapping("getModularInfo")
    public Result getModularInfo(Long modularId) {
        return Result.success().put("data", modularService.getModularInfo(modularId));
    }

    /**
     * 获取服务类型列表
     * @param page
     * @param limit
     * @param hospitalModular
     * @return
     */
    @ApiOperation("获取服务类型列表")
    @GetMapping("getModularList")
    public Result getModularList(Integer page, Integer limit, HospitalModular hospitalModular) {
        return Result.success().put("data", modularService.getModularList(page, limit, hospitalModular));
    }
}

