<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.orders.dao.OrdersDao">
    <resultMap id="BaseResultMap" type="com.sqx.modules.orders.entity.Orders">
        <id column="orders_id" jdbcType="BIGINT" property="ordersId"/>
        <result column="orders_no" jdbcType="VARCHAR" property="ordersNo"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_taking_id" jdbcType="BIGINT" property="orderTakingId"/>
        <result column="pay_money" jdbcType="DECIMAL" property="payMoney"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="refund_content" jdbcType="VARCHAR" property="refundContent"/>
        <result column="orders_type" jdbcType="INTEGER" property="ordersType"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="order_number" jdbcType="INTEGER" property="orderNumber"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
        <result column="order_score" jdbcType="DOUBLE" property="orderScore"/>
        <result column="vip_details_id" jdbcType="BIGINT" property="vipDetailsId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="isdelete" jdbcType="INTEGER" property="isdelete"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="details_address" jdbcType="VARCHAR" property="detailsAddress"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="is_remind" jdbcType="INTEGER" property="isRemind"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="zhi_rate" jdbcType="DECIMAL" property="zhiRate"/>
        <result column="zhi_user_id" jdbcType="INTEGER" property="zhiUserId"/>
        <result column="fei_rate" jdbcType="DECIMAL" property="feiRate"/>
        <result column="fei_user_id" jdbcType="INTEGER" property="feiUserId"/>
        <result column="ping_rate" jdbcType="DECIMAL" property="pingRate"/>
        <result column="order_taking_user_id" jdbcType="INTEGER" property="orderTakingUserId"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="pay_way" jdbcType="INTEGER" property="payWay"/>
        <result column="car_no" jdbcType="VARCHAR" property="carNo"/>
        <result column="car_type" jdbcType="VARCHAR" property="carType"/>
        <result column="car_color" jdbcType="VARCHAR" property="carColor"/>
        <result column="car_name" jdbcType="VARCHAR" property="carName"/>
        <result column="car_phone" jdbcType="VARCHAR" property="carPhone"/>
        <result column="start_img" jdbcType="VARCHAR" property="startImg"/>
        <result column="end_img" jdbcType="VARCHAR" property="endImg"/>
        <result column="is_transfer" jdbcType="INTEGER" property="isTransfer"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="department_id" jdbcType="INTEGER" property="departmentId"/>
        <result column="service_id" jdbcType="INTEGER" property="serviceId"/>
        <result column="hospital_id" jdbcType="INTEGER" property="hospitalId"/>
        <association property="appointInformation"
                     javaType="com.sqx.modules.appointInformation.entity.AppointInformation">
            <id column="appoint_id" jdbcType="INTEGER" property="appointId"/>
            <result column="orders_id" jdbcType="INTEGER" property="ordersId"/>
            <result column="hospital_id" jdbcType="INTEGER" property="hospitalId"/>
            <result column="type" jdbcType="INTEGER" property="type"/>
            <result column="patient_id" jdbcType="INTEGER" property="patientId"/>
            <result column="hope_time" jdbcType="TIMESTAMP" property="hopeTime"/>
            <result column="department_id" jdbcType="INTEGER" property="departmentId"/>
            <result column="service_id" jdbcType="INTEGER" property="serviceId"/>
            <result column="bad_no" jdbcType="VARCHAR" property="badNo"/>
            <result column="symptom" jdbcType="INTEGER" property="symptom"/>
            <result column="self_ability" jdbcType="INTEGER" property="selfAbility"/>
            <result column="nursing_needs" jdbcType="INTEGER" property="nursingNeeds"/>
            <result column="service_time" jdbcType="INTEGER" property="serviceTime"/>
            <result column="phone" jdbcType="VARCHAR" property="phone"/>
            <result column="lat" jdbcType="DOUBLE" property="lat"/>
            <result column="lng" jdbcType="DOUBLE" property="lng"/>
            <result column="province" jdbcType="VARCHAR" property="province"/>
            <result column="city" jdbcType="VARCHAR" property="city"/>
            <result column="district" jdbcType="VARCHAR" property="district"/>
            <result column="detailsAddress" jdbcType="VARCHAR" property="detailsAddress"/>
        </association>
    </resultMap>

    <select id="selectOrderList" resultType="com.sqx.modules.orders.response.MyOrderResponse">
        SELECT
        o.orders_id,
        o.state,
        t.my_level as myLevel,
        t.homepage_img as homepageImg,
        o.province,
        o.city,
        o.district,
        o.details_address,
        o.update_time,
        o.order_taking_id,
        u.user_id,
        u.user_name,
        u.avatar,
        o.rate,
        o.zhi_rate as zhiRate,
        o.zhi_user_id as zhiUserId,
        o.fei_user_id as feiUserId,
        o.fei_rate as feiRate,
        o.ping_rate as pingRate,
        t.game_id as gameName,
        o.order_number,
        o.pay_money,
        t.classify,
        t.unit,
        o.code,
        o.car_no,
        o.car_type,
        o.car_color,
        o.car_name,
        o.car_phone,
        o.start_img,
        o.end_img,
        (st_distance (point (o.longitude, o.latitude),point(#{longitude},#{latitude}) ) *111195) as distance,
        t.id as orderTakingId
        FROM
        orders o
        LEFT JOIN order_taking t ON o.order_taking_id = t.id
        LEFT JOIN tb_user u ON u.user_id = o.order_taking_user_id
        WHERE
        o.orders_type = 1 and o.isdelete=0 and o.orders_id is not null and o.state=4
        <if test="createTime==1">
            order by o.create_time asc
            <if test="distance==1">
                order by o.distance asc
            </if>
            <if test="distance==2">
                order by o.distance desc
            </if>
        </if>
        <if test="createTime==2">
            order by o.create_time desc
            <if test="distance==1">
                ,o.distance asc
            </if>
            <if test="distance==2">
                ,o.distance desc
            </if>
        </if>
        <if test="createTime==0 and distance==1">
            order by o.distance asc
        </if>
        <if test="createTime==0 and distance==2">
            order by o.distance desc
        </if>
    </select>


    <select id="queryOrdersAll" resultType="com.sqx.modules.orders.response.OrderAllResponse">
        SELECT
        o.orders_id,
        o.orders_no,
        o.province,
        o.city,
        o.district,
        o.details_address,
        o.name,
        o.sf_zhi_user_id,
        o.sf_zhi_rate,
        o.phone,
        o.start_time,
        o.order_taking_id,
        o.pay_money,
        o.state,
        o.create_time,
        o.orders_type,
        o.remarks,
        o.order_number,
        o.vip_details_id,
        o.rate,
        o.zhi_rate as zhiRate,
        o.zhi_user_id as zhiUserId,
        o.fei_user_id as feiUserId,
        o.fei_rate as feiRate,
        o.ping_rate as pingRate,
        o.coupon_id,
        u.user_name,
        u.avatar,
        o.order_score,
        o.user_id,
        o.code,
        t.old_money as oldMoney,
        t.money,
        t.classify,
        t.unit,
        o.car_no,
        o.car_type,
        o.car_color,
        o.car_name,
        o.car_phone,

        t.my_level,
        o.start_img,
        o.end_img,
        o.is_transfer,
        ordersUser.user_name as ordersUserName,
        o.order_taking_user_id as orderTakingUserId,
        (select user_name from tb_user u2 where u2.user_id=o.zhi_user_id) as zhiUserName,
        (select user_name from tb_user u2 where u2.user_id=o.fei_user_id) as feiUserName,
        t.member_money as memberMoney
        FROM
        orders o
        LEFT JOIN order_taking t ON o.order_taking_id = t.id
        LEFT JOIN tb_user u ON u.user_id = o.order_taking_user_id
        left join tb_user ordersUser on ordersUser.user_id=o.user_id
        WHERE o.isdelete=0
        <if test="hospitalId!=null">
            and o.hospital_id=#{hospitalId}
        </if>
        <if test="type!=null">
            and o.orders_type=#{type}
        </if>
        <if test="status!=null">
            and o.state=#{status}
        </if>
        <if test="name!=null and name!=''">
            and (u.user_name like concat("%",#{name},"%") or u.phone like concat("%",#{name},"%") )
        </if>
        <if test="userId!=null">
            and o.user_id=#{userId}
        </if>
        <if test="ordersNo!=null and ordersNo!=''">
            and o.orders_no like concat("%",#{ordersNo},"%")
        </if>
        <if test="startTime!=null and startTime!=''">
            and date_format(o.create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(o.create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
        order by o.create_time desc
    </select>


    <select id="ordersListExcel" resultType="com.sqx.modules.orders.response.OrderAllResponse">
        SELECT
        o.orders_id,
        o.orders_no,
        o.province,
        o.city,
        o.district,
        o.details_address,
        o.name,
        o.phone,
        o.start_time,
        o.order_taking_id,
        o.pay_money,
        o.state,
        o.create_time,
        o.orders_type,
        o.remarks,
        o.order_number,
        o.vip_details_id,
        o.rate,
        o.zhi_rate as zhiRate,
        o.zhi_user_id as zhiUserId,
        o.fei_user_id as feiUserId,
        o.fei_rate as feiRate,
        o.ping_rate as pingRate,
        u.user_name,
        u.avatar,
        o.order_score,
        o.user_id,
        o.code,
        t.old_money as oldMoney,
        t.money,
        t.classify,
        t.unit,
        t.my_level,
        o.car_no,
        o.car_type,
        o.car_color,
        o.car_name,
        o.car_phone,
        o.start_img,
        o.end_img,
        o.is_transfer,
        ordersUser.user_name as ordersUserName,
        (select user_name from tb_user u2 where u2.user_id=o.zhi_user_id) as zhiUserName,
        (select user_name from tb_user u2 where u2.user_id=o.fei_user_id) as feiUserName,
        t.member_money as memberMoney
        FROM
        orders o
        LEFT JOIN order_taking t ON o.order_taking_id = t.id
        LEFT JOIN tb_user u ON u.user_id = o.order_taking_user_id
        left join tb_user ordersUser on ordersUser.user_id=o.user_id
        WHERE o.isdelete=0
        <if test="type!=null">
            and o.orders_type=#{type}
        </if>
        <if test="status!=null">
            and o.state=#{status}
        </if>
        <if test="name!=null and name!=''">
            and u.user_name like #{name}
        </if>
        <if test="userId!=null">
            and o.user_id=#{userId}
        </if>
        <if test="ordersNo!=null and ordersNo!=''">
            and o.orders_no like #{ordersNo}
        </if>
        <if test="startTime!=null and startTime!=''">
            and date_format(o.create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(o.create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
        order by o.create_time desc
    </select>

    <select id="selectMyTakeOrders" resultType="Map">
        select o.orders_id as ordersId,o.state,o.create_time as createTime,o.pay_money as payMoney,o.user_id as
        ordersUserId,ot.classify,ot.unit,
        u.user_name as userName,u.avatar,o.order_number as orderNumber,o.remarks,o.orders_no as ordersNo,ot.old_money as
        oldMoney,
        o.province,
        o.city,
        o.district,
        o.details_address as detailsAddress,
        o.name,
        o.phone,
        o.rate,
        o.code,
        o.car_no as carNo,
        o.car_type as carType,
        o.car_color as carColor,
        o.car_name as carName,
        o.car_phone as carPhone,
        o.start_img as startImg,
        o.end_img as endImg,
        o.zhi_rate as zhiRate,
        o.zhi_user_id as zhiUserId,
        o.fei_user_id as feiUserId,
        o.fei_rate as feiRate,
        o.ping_rate as pingRate,
        ot.my_level as myLevel,
        ot.homepage_img as homepageImg,
        o.start_time as startTime,
        ot.game_id as gameName,ot.id as orderTakingId
        from orders o
        left join order_taking ot on ot.id=o.order_taking_id
        left join tb_user u on u.user_id=o.user_id
        where o.order_taking_user_id=#{userId} and o.orders_type=1 and o.isdelete=0
        <if test="status!=null and status!=0">
            and o.state=#{status}
        </if>
        <if test="status==null or status==0">
            and o.state in (1,2,3,5)
        </if>
        order by o.create_time desc
    </select>


    <select id="selectOrdersCountAndMoney" resultType="Map">
        select sum(money) as money, count(*) as counts
        from user_money_details
        where title like '订单完成：%'
          and user_id = #{userId}
          and type = 1
          and date_format(create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
    </select>


    <select id="selectMyOrdersCount" resultType="Integer">
        select count(*)
        from orders
        where user_id = #{userId}
          and date_format(create_time, '%Y-%m') = date_format(#{time}, '%Y-%m')
          and `state` = 2
    </select>

    <select id="selectTakeOrdersCount" resultType="Integer">
        select count(*)
        from orders
        where order_taking_user_id = #{userId}
          and date_format(create_time, '%Y-%m') = date_format(#{time}, '%Y-%m')
          and `state` = 2
    </select>

    <select id="countOrdersByCreateTime" resultType="Integer">
        select count(*) from orders where state in (1,2,3,4)
        <if test="flag!=null and flag==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>

    <select id="sumOrdersByCreateTime" resultType="Double">
        select sum(pay_money) from orders where `state` in (1,2,3,4)
        <if test="flag!=null and flag==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(create_time,'%Y')=date_format(#{time},'%Y')

        </if>
    </select>


    <select id="incomeAnalysisOrders" resultType="Map">
        select o.orders_id as orderId,u.user_name as userName,u.user_id as userId,t.classify,t.unit,
        pay_money as payMoney,u.create_time as createTime,t.old_money as money,o.code
        from orders o
        left join tb_user u on u.user_id=o.user_id
        left join order_taking t on t.id=o.order_taking_id
        where o.state=2 and t.id is not null
        <if test="flag!=null and flag==1">
            and date_format(o.create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(o.create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(o.create_time,'%Y')=date_format(#{time},'%Y')
        </if>
        order by o.create_time desc
    </select>

    <select id="selectOrdersMoneyByUserId" resultType="java.math.BigDecimal">
        select ifnull(sum(money),0.00) from user_money_details where `type`=1 and (title like '%订单完成%' or title like
        '%接单完成%') and user_id =#{userId}
        <if test="startTime!=null and startTime!=''">
            and date_format(create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>

    <select id="selectOrdersCountByUserId" resultType="Integer">
        select count(*) from orders where order_taking_user_id=#{userId} and
        state in (1,2,3)
        <if test="startTime!=null and startTime!=''">
            and date_format(create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>

    <select id="selectOrderScoreByUserId" resultType="java.math.BigDecimal">
        select ifnull(avg(score), 0)
        from taking_commnt
        where order_taking_id in (select order_taking_id from orders where order_taking_user_id = #{userId})
    </select>

    <select id="selectOrdersRefundMoneyByUserId" resultType="java.math.BigDecimal">
        select ifnull(sum(t.old_money),0.00) from orders o
        left join order_taking t on t.id=o.order_taking_id
        where o.order_taking_user_id=#{userId} and o.state=3
        <if test="startTime!=null and startTime!=''">
            and date_format(o.create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(o.create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>

    <select id="selectOrdersRefundCountByUserId" resultType="Integer">
        select count(*) from orders where order_taking_user_id=#{userId} and
        state=#{status}
        <if test="startTime!=null and startTime!=''">
            and date_format(create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>

    <select id="getOrdersRemind" resultType="Integer">
        select count(*)
        from orders
        where order_taking_user_id = #{userId}
          and is_remind = 0
    </select>

    <update id="updateOrdersIsRemind">
        update orders
        set is_remind=1
        where order_taking_user_id = #{userId}
    </update>
    <update id="rideCancelOrder">
        update orders
        set state="4",
            order_taking_user_id=null
        where orders_id = #{orderId}
    </update>
    <select id="settlement" resultType="java.math.BigDecimal">
        select ifnull(sum(pay_money),0) from orders where state in (1,4,5)
        <if test="flag!=null and flag==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>


    <select id="selectTeamOrdersList" resultType="Map">
        select o.orders_id as ordersId,o.state,o.create_time as createTime,o.pay_money as payMoney,o.user_id as
        ordersUserId,ot.classify,ot.unit,o.code,
        u.user_name as userName,u.avatar,o.order_number as orderNumber,o.remarks,o.orders_no as ordersNo,ot.old_money as
        oldMoney,
        o.car_no as carNo,
        o.car_type as carType,
        o.car_color as carColor,
        o.car_name as carName,
        o.car_phone as carPhone,
        o.start_img as startImg,
        o.end_img as endImg,
        o.coupon_id as couponId,
        o.coupon_money as couponMoney,
        g.game_name as gameName,g.game_img as gameImg,ot.id as orderTakingId,o.zhi_rate as zhiRate,o.fei_rate as feiRate
        from orders o
        left join order_taking ot on ot.id=o.order_taking_id
        left join game g on g.id=ot.game_id
        left join tb_user u on u.user_id=o.user_id
        where o.orders_type=1
        <if test="status!=null and status!=0">
            and o.state=#{status}
        </if>
        <if test="type==1">
            and o.zhi_user_id=#{userId}
        </if>
        <if test="type==2">
            and o.fei_user_id=#{userId}
        </if>
        order by o.create_time desc
    </select>

    <select id="selectOrdersMoneyCountByUserId" resultType="Double">
        select
        <if test="type==1">
            ifnull(sum(zhi_rate),0.00)
        </if>
        <if test="type==2">
            ifnull(sum(fei_rate),0.00)
        </if>
        from orders
        where state = 2
        <if test="type==1">
            and zhi_user_id=#{userId}
        </if>
        <if test="type==2">
            and fei_user_id=#{userId}
        </if>
        <if test="time!=null">
            and end_time like concat('%',#{time},'%')
        </if>
    </select>

    <select id="selectUserCountByInvitationCode" resultType="Integer">
        select count(*) from tb_user
        where
        <if test="type==1">
            inviter_code=#{invitationCode}
        </if>
        <if test="type==2">
            inviter_code in (select invitation_code from tb_user where inviter_code=#{invitationCode})
        </if>
    </select>

    <select id="selectTeamUserList" resultType="Map">
        select * from (
        select u.user_id as userId,u.user_name as userName,u.avatar,u.create_time as createTime,
        <if test="type==1">
            (select ifnull(sum(o.zhi_rate),0.00) from orders o where
            o.user_id = u.user_id and o.state=2 and zhi_user_id=#{userId} ) as money
        </if>
        <if test="type==2">
            (select ifnull(sum(o.fei_rate),0.00) from orders o where
            o.user_id = u.user_id and o.state=2 and fei_user_id=#{userId} ) as money
        </if>
        from tb_user u
        where
        <if test="type==1">
            inviter_code=#{invitationCode}
        </if>
        <if test="type==2">
            inviter_code in (select invitation_code from tb_user where inviter_code=#{invitationCode})
        </if>
        ) a order by money desc
    </select>

    <select id="selectTeamUserList2" resultType="Map">
        select *
        from (
                 select u.user_id     AS userId,
                        u.user_name   AS userName,
                        u.avatar,
                        u.create_time AS createTime,
                        o.money
                 from (
                          SELECT ifnull(sum(fei_rate), 0.00) as money, user_id
                          FROM orders
                          WHERE state = 2
                            and fei_user_id = #{userId}
                          GROUP BY user_id) o
                          left join tb_user u on u.user_id = o.user_id
             ) a
        order by money desc
    </select>
    <select id="selectNewestOrders" resultType="Map">
        select o.orders_id   as ordersId,
               u.user_name   as takingUserName,
               u1.avatar,
               u1.user_name  as userName,
               o.create_time as createTime
        from orders o
                 left join tb_user u on u.user_id = o.order_taking_user_id
                 left join tb_user u1 on u1.user_id = o.user_id
        where o.state in (1, 2)
        order by o.create_time desc limit 5
    </select>

    <select id="selectMyOrder" resultType="com.sqx.modules.orders.entity.Orders">
        SELECT
        o.*,
        u.user_name AS ride_user_name,
        u.phone AS ride_phone,
        u.avatar AS ride_avatar,
        (select if(count(*)>0,1,0) from sys_evaluate e where e.indent_number = o.orders_no) as commentCount
        <if test="orders.longitude!=null and orders.latitude!=null">
            ,(st_distance (point (o.longitude,o.latitude),point(#{orders.longitude},#{orders.latitude}) ) *111195) AS
            distance
        </if>
        FROM
        orders o
        left join tb_user u
        on u.user_id = o.order_taking_user_id
        WHERE
        o.isdelete=0 and o.orders_id is not null
        <if test="orders.state!=null and orders.state!=''">
            and o.state=#{orders.state}
        </if>
        <if test="orders.ordersType!=null">
            and o.orders_type=#{orders.ordersType}
        </if>
        <if test="orders.userId!=null">
            AND o.user_id = #{orders.userId}
        </if>
        <if test="orders.longitude!=null and orders.latitude!=null">
            order by distance asc
        </if>
        <if test="orders.longitude==null and orders.latitude==null">
            order by o.create_time desc
        </if>
    </select>
    <select id="selectNowDayOrders" resultType="com.sqx.modules.orders.entity.Orders">
        SELECT o.*
        FROM orders o,
             appoint_information a
        WHERE o.orders_id = a.orders_id
          AND DATE (a.hope_time) = CURDATE()
          AND o.state IN (1
            , 5)
          and o.order_taking_user_id = #{userId}
        ORDER BY a.hope_time DESC
    </select>
    <select id="getUserNowHasOrder" resultType="java.lang.Integer">
        SELECT count(0)
        FROM orders o,
             appoint_information a
        WHERE o.orders_id = a.orders_id
          AND DATE (a.hope_time) = #{hopeTime}
          AND o.state IN (1
            , 5)
          and o.order_taking_user_id = #{orderTakingUserId}
    </select>
    <select id="getAllMoney" resultType="java.math.BigDecimal">
        SELECT ifnull(SUM(pay_money),0) FROM `orders` WHERE 1=1
        <if test="state!=null">
            and `state` = #{state}
        </if>
        <if test="flag!=null and flag==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>
    <select id="orderCount" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from orders
        where 1 = 1

    </select>
    <select id="getOrdersCount" resultType="java.lang.Integer">
        select ifnull(count(*),0) from orders where 1=1
        <if test="type==0">
            and `state` = 0
        </if>
        <if test="type==1">
            and `state` in (1, 4, 5)
        </if>
        <if test="type==2">
            and `state` = 2
        </if>
        <if test="type==3">
            and `state` = 3
        </if>
        <if test="flag!=null and flag==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>
    <select id="getOrderLast" resultType="com.sqx.modules.orders.entity.Orders">
        select *
        from orders o
        where o.state = 3
          and date_format(o.create_time, '%Y-%m-%d') &gt;= #{time}


    </select>
    <select id="errandGetOrderList" resultType="com.sqx.modules.orders.entity.Orders">
        SELECT *
        FROM (SELECT o.*,
        (st_distance(POINT(o.longitude, o.latitude),
        POINT(#{longitude}, #{latitude})) * 111195) AS
        distance,
        a.hope_time
        FROM orders o,
        appoint_information a
        WHERE o.orders_id = a.orders_id
        AND o.state = 4
        and o.order_taking_user_id is null
        <if test="city!=null and city!=''">
            and (o.city like concat("%",#{city},"%") or o.province like concat("%",#{city},"%"))
        </if>

        <if test="hospitalName!=null and hospitalName!=''">
            AND a.hospital_name like concat("%",#{hospitalName},"%")
        </if>
        ) t1
        order by
        <if test="createTime!=null ">
            <if test="createTime==1">
                create_time asc,
            </if>
            <if test="createTime==2">
                create_time desc,
            </if>
        </if>
        <if test="hopeTime!=null ">
            <if test="hopeTime==1">
                hope_time asc,
            </if>
            <if test="hopeTime==2">
                hope_time desc,
            </if>
        </if>
        create_time desc
    </select>
    <select id="getProfitList" resultType="java.util.HashMap">
        SELECT
        CASE
        WHEN @score = t.money
        THEN @rank
        WHEN @score := t.money
        THEN @rank := @rank + 1
        END AS rank,
        t.*
        FROM
        (SELECT
        u.user_id,
        u.user_name,
        u.avatar,
        SUM(s.rate) AS money
        FROM
        orders s,
        tb_user u
        WHERE u.user_id = s.order_taking_user_id
        AND s.state = 2
        <if test="flag!=null and flag==1">
            and date_format(s.create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(s.create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(s.create_time,'%Y')=date_format(#{time},'%Y')
        </if>
        GROUP BY u.user_id
        ORDER BY money DESC
        ) AS t,
        (SELECT
        @rank := 0,
        @score := NULL) r
    </select>
    <select id="selectOrdersCount" resultType="java.lang.Integer">
        select count(0) from orders o where 1=1
        <if test="state!=null">
            and o.state = #{state}
        </if>
        <if test="flag!=null and flag==1">
            and date_format(o.create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(o.create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(o.create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>
    <select id="selectOrdersMoney" resultType="java.math.BigDecimal">
        select ifnull(sum(pay_money),0) from orders o where 1=1
        <if test="state!=null">
            and o.state = #{state}
        </if>
        <if test="flag!=null and flag==1">
            and date_format(o.create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(o.create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(o.create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>
    <select id="getPingIncome" resultType="java.util.HashMap">
        SELECT o.orders_id AS ordersId,
        (SELECT user_name FROM tb_user t WHERE t.user_id = o.user_id) AS userName,
        o.pay_money AS payMoney,
        o.rate,
        o.user_id as userId,
        o.ping_rate AS pingRate,
        o.create_time AS createTime
        FROM orders o
        WHERE o.state = 2
        <if test="flag!=null and flag==1">
            and date_format(o.create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(o.create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(o.create_time,'%Y')=date_format(#{time},'%Y')
        </if>
        ORDER BY o.create_time DESC
    </select>
    <select id="orderStatistics" resultType="java.math.BigDecimal">
        select
        <if test="type==1">
            ifnull(sum(pay_money),0)
        </if>
        <if test="type==2">
            ifnull(sum(rate),0)
        </if>
        <if test="type==3">
            ifnull(sum(zhi_rate),0)
        </if>
        <if test="type==4">
            ifnull(sum(fei_rate),0)
        </if>
        <if test="type==5">
            ifnull(sum(ping_rate),0)
        </if>
        from orders o where state = 2
        <if test="flag!=null and flag==1">
            and date_format(o.create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(o.create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(o.create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>
    <select id="selectShopRateMoney" resultType="java.math.BigDecimal">
        select sum(sf_zhi_rate)
        from orders
        where state = 2
          and sf_zhi_user_id = #{userId}
    </select>
    <select id="getRateUserList" resultType="java.util.HashMap">
        SELECT u.user_id       as userId,
               u.`avatar`,
               u.`user_name`   as userName,
               uc.`name`,
               o.`sf_zhi_rate` as sfZhiRate,
               o.`create_time` as createTime
        FROM orders o,
             tb_user u,
             user_certification uc
        WHERE o.`order_taking_user_id` = u.user_id
          AND uc.`user_id` = u.`user_id`
          AND o.state = 2
          AND o.`sf_zhi_user_id` = #{userId}
        order by o.create_time desc

    </select>
    <select id="getUserList" resultType="java.util.HashMap">
        select u.user_id     as userId,
               u.`avatar`,
               u.`user_name` as userName,
               u.create_time as createTime
        from tb_user u
        where u.inviter_code = #{invitationCode}
          and (u.is_authentication is not null and u.is_authentication = 1)
        order by create_time desc
    </select>

</mapper>