{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/mescroll-uni/me-tabs/me-tabs.vue?9389", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/mescroll-uni/me-tabs/me-tabs.vue?4eca", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/mescroll-uni/me-tabs/me-tabs.vue?1e1f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/mescroll-uni/me-tabs/me-tabs.vue?48c6", "uni-app:///components/mescroll-uni/me-tabs/me-tabs.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/mescroll-uni/me-tabs/me-tabs.vue?5d91", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/mescroll-uni/me-tabs/me-tabs.vue?5c94"], "names": ["props", "tabs", "type", "default", "<PERSON><PERSON><PERSON>", "value", "fixed", "tabWidth", "height", "top", "data", "viewId", "scrollLeft", "windowWidth", "windowTop", "computed", "isScroll", "tabHeightPx", "tabHeightVal", "tabWidthPx", "tabWidthVal", "lineLeft", "topFixed", "<PERSON><PERSON><PERSON><PERSON>", "watch", "created", "mounted", "methods", "getTabName", "tabClick", "scrollCenter", "rect", "tabLeft", "diff", "initWarpRect", "setTimeout", "query", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCqBzwB;EACAA;IACAC;MAAA;MACAC;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACAF;MACAC;IACA;IACAE;MAAA;MACAH;MACAC;IACA;IACAG;IAAA;IACAC;IAAA;IACAC;MAAA;MACAN;MACAC;IACA;IACAM;MAAA;MACAP;MACAC;IACA;EACA;EACAO;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAvB;MACA;MACA;IACA;IACAI;MACA;IACA;EACA;EACAoB;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;cAAA;gBAEAC;gBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAOA;IACAC;MAAA;MACA;QACAC;UAAA;UACA;UAEAC;;UAEAA;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAo5C,CAAgB,8tCAAG,EAAC,C;;;;;;;;;;;ACAx6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/mescroll-uni/me-tabs/me-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./me-tabs.vue?vue&type=template&id=27b8914b&\"\nvar renderjs\nimport script from \"./me-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./me-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./me-tabs.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/mescroll-uni/me-tabs/me-tabs.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./me-tabs.vue?vue&type=template&id=27b8914b&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tabs.length\n  var l0 = g0\n    ? _vm.__map(_vm.tabs, function (tab, i) {\n        var $orig = _vm.__get_orig(tab)\n        var m0 = _vm.getTabName(tab)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./me-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./me-tabs.vue?vue&type=script&lang=js&\"", "<!-- tab组件: <me-tabs v-model=\"tabIndex\" :tabs=\"tabs\" @change=\"tabChange\"></me-tabs> -->\n<template>\n\t<view class=\"me-tabs\" :class=\"{'tabs-fixed': fixed}\" :style=\"{height: tabHeightVal, top:topFixed, 'margin-top':topMargin}\">\n\t\t<scroll-view v-if=\"tabs.length\" :id=\"viewId\" :scroll-left=\"scrollLeft\" scroll-x scroll-with-animation :scroll-animation-duration=\"300\">\n\t\t\t<view class=\"tabs-item\" :class=\"{'tabs-flex':!isScroll, 'tabs-scroll':isScroll}\">\n\t\t\t\t<!-- tab -->\n\t\t\t\t<view class=\"tab-item\" style=\"width: 120rpx;height:100rpx;margin-top: 25rpx;line-height: 30upx;z-index: 9;\" v-for=\"(tab, i) in tabs\" :class=\"{'active': value===i}\" :key=\"i\" @click=\"tabClick(i)\">\n\t\t\t\t\t{{getTabName(tab)}}\n\t\t\t\t\t<!-- {{tab.gameName}} -->\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<image v-if=\"value == i\" src=\"../../../static/images/index/wx.png\" style=\"width: 55upx;height: 16upx;margin-top: -5upx;\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 下划线 -->\n\t\t\t\t<!-- <view class=\"tabs-line\" :style=\"{left:lineLeft}\"></view> -->\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tprops:{\n\t\t\ttabs: { // 支持格式: ['全部', '待付款'] 或 [{name:'全部'}, {name:'待付款'}]\n\t\t\t\ttype: Array,\n\t\t\t\tdefault(){\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t},\n\t\t\tnameKey: { // 取name的字段\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'name'\n\t\t\t},\n\t\t\tvalue: { // 当前显示的下标 (使用v-model语法糖: 1.props需为value; 2.需回调input事件)\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tfixed: Boolean, // 是否悬浮,默认false\n\t\t\ttabWidth: Number, // 每个tab的宽度,默认不设置值,为flex平均分配; 如果指定宽度,则不使用flex,每个tab居左,超过则水平滑动(单位默认rpx)\n\t\t\theight: { // 高度,单位rpx\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 100\n\t\t\t},\n\t\t\ttop: { // 顶部偏移的距离,默认单位rpx (当fixed=true时,已加上windowTop)\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tviewId: 'id_' + Math.random().toString(36).substr(2,16),\n\t\t\t\tscrollLeft: 0,\n\t\t\t\twindowWidth: 0,\n\t\t\t\twindowTop: 0\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tisScroll(){\n\t\t\t\treturn this.tabWidth && this.tabs.length // 指定了tabWidth的宽度,则支持水平滑动\n\t\t\t},\n\t\t\ttabHeightPx(){\n\t\t\t\treturn uni.upx2px(this.height)\n\t\t\t},\n\t\t\ttabHeightVal(){\n\t\t\t\treturn this.tabHeightPx+'px'\n\t\t\t},\n\t\t\ttabWidthPx(){\n\t\t\t\treturn uni.upx2px(this.tabWidth)\n\t\t\t},\n\t\t\ttabWidthVal(){\n\t\t\t\treturn this.isScroll ? this.tabWidthPx+'px' : ''\n\t\t\t},\n\t\t\tlineLeft() {\n\t\t\t\tif (this.isScroll) {\n\t\t\t\t\treturn this.tabWidthPx * this.value + this.tabWidthPx/2 + 'px' // 需转为px (用rpx的话iOS真机显示有误差)\n\t\t\t\t} else{\n\t\t\t\t\treturn 100/this.tabs.length*(this.value + 1) - 100/(this.tabs.length*2) + '%'\n\t\t\t\t}\n\t\t\t},\n\t\t\ttopFixed(){\n\t\t\t\treturn this.fixed ? this.windowTop + uni.upx2px(this.top) + 'px' : 0\n\t\t\t},\n\t\t\ttopMargin(){\n\t\t\t\treturn this.fixed ? 0 : this.top + 'rpx'\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\ttabs() {\n\t\t\t\tthis.warpWidth = null; // 重新计算容器宽度\n\t\t\t\tthis.scrollCenter(); // 水平滚动到中间\n\t\t\t},\n\t\t\tvalue() {\n\t\t\t\tthis.scrollCenter(); // 水平滚动到中间\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tlet sys = uni.getSystemInfoSync();\n\t\t\tthis.windowWidth = sys.windowWidth\n\t\t\tthis.windowTop = sys.windowTop\n\t\t},\n\t\tmounted() {\n\t\t\tthis.scrollCenter() // 滚动到当前下标\n\t\t},\n\t\tmethods: {\n\t\t\tgetTabName(tab){\n\t\t\t\treturn typeof tab === \"object\" ? tab[this.nameKey] : tab\n\t\t\t},\n\t\t\ttabClick(i){\n\t\t\t\tif(this.value!=i){\n\t\t\t\t\tthis.$emit(\"input\",i);\n\t\t\t\t\tthis.$emit(\"change\",i);\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync scrollCenter(){\n\t\t\t\tif(!this.isScroll) return;\n\t\t\t\tif(!this.warpWidth){ // tabs容器的宽度\n\t\t\t\t\tlet rect = await this.initWarpRect()\n\t\t\t\t\tthis.warpWidth = rect ? rect.width : this.windowWidth; // 某些情况下取不到宽度,暂时取屏幕宽度\n\t\t\t\t}\n\t\t\t\tlet tabLeft = this.tabWidthPx * this.value + this.tabWidthPx/2; // 当前tab中心点到左边的距离\n\t\t\t\tlet diff = tabLeft - this.warpWidth/2 // 如果超过tabs容器的一半,则滚动差值\n\t\t\t\tthis.scrollLeft = diff;\n\t\t\t\t// #ifdef MP-TOUTIAO\n\t\t\t\tthis.scrollTimer && clearTimeout(this.scrollTimer)\n\t\t\t\tthis.scrollTimer = setTimeout(()=>{ // 字节跳动小程序,需延时再次设置scrollLeft,否则tab切换跨度较大时不生效\n\t\t\t\t\tthis.scrollLeft = Math.ceil(diff)\n\t\t\t\t},400)\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tinitWarpRect(){\n\t\t\t\treturn new Promise(resolve=>{\n\t\t\t\t\tsetTimeout(()=>{ // 延时确保dom已渲染, 不使用$nextclick\n\t\t\t\t\t\tlet query = uni.createSelectorQuery();\n\t\t\t\t\t\t// #ifndef MP-ALIPAY\n\t\t\t\t\t\tquery = query.in(this) // 支付宝小程序不支持in(this),而字节跳动小程序必须写in(this), 否则都取不到值\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\tquery.select('#'+this.viewId).boundingClientRect(data => {\n\t\t\t\t\t\t\tresolve(data)\n\t\t\t\t\t\t}).exec();\n\t\t\t\t\t},20)\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.me-tabs{\n\t\tposition: relative;\n\t\tfont-size: 28rpx;\n\t\t// background-color: #1F1F21;\n\t\tcolor: #1E1F31;\n\t\tbox-sizing: border-box;\n\t\toverflow-y: hidden;\n\t\t// padding: 0 10px ;\n\t\t&.tabs-fixed{\n\t\t\tz-index: 9;\n\t\t\tposition: fixed;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t}\n\t\t\n\t\t.tabs-item{\n\t\t\tposition: relative;\n\t\t\twhite-space: nowrap;\n\t\t\t// padding-bottom: 30rpx; // 撑开高度,再配合me-tabs的overflow-y: hidden,以达到隐藏滚动条的目的\n\t\t\tbox-sizing: border-box;\n\t\t\t\n\t\t\t.tab-item{\n\t\t\t\ttext-align: center;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tmargin: 0 15px;\n\t\t\t\t&.active{\n\t\t\t\t\t// font-weight: bold;\n\t\t\t\t\tcolor:#1E1F31;\n\t\t\t\t\t// font-size: 32upx;\n\t\t\t\t\t\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 平分的方式显示item\n\t\t.tabs-flex{\n\t\t\tdisplay: flex;\n\t\t\t.tab-item{\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\t\t// 居左显示item,支持水平滑动\n\t\t.tabs-scroll{\n\t\t\t.tab-item{\n\t\t\t\tdisplay: inline-block;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// 选中tab的线\n\t\t.tabs-line{\n\t\t\tz-index: 1;\n\t\t\tposition: absolute;\n\t\t\tbottom: 30rpx; // 至少与.tabs-item的padding-bottom一致,才能保证在底部边缘\n\t\t\twidth: 50rpx;\n\t\t\theight: 6rpx;\n\t\t\ttransform: translateX(-50%);\n\t\t\tborder-radius: 4rpx;\n\t\t\ttransition: left .3s;\n\t\t\tbackground: #1789FD;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./me-tabs.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./me-tabs.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627928\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}