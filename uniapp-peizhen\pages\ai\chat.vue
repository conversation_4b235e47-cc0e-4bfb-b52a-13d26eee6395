<template>
	<view class="chat-container">
		<!-- 顶部工具栏 -->
		<view class="chat-header">
			<text class="conversation-title">{{ conversationTitle }}</text>
		</view>

		<!-- 消息列表 -->
		<scroll-view
			class="message-list"
			scroll-y
			:scroll-top="scrollTop"
			:scroll-into-view="scrollIntoView"
			:scroll-with-animation="true"
			@scrolltoupper="loadMoreHistory"
		>
			<!-- 加载更多历史消息 -->
			<view v-if="hasMoreHistory" class="load-more-history">
				<view v-if="loadingHistory" class="loading-indicator">
					<u-loading :show="true" size="20"></u-loading>
					<text>加载中...</text>
				</view>
				<text v-else @click="loadMoreHistory">点击加载更多历史消息</text>
			</view>

			<!-- 消息项 -->
			<view v-for="(message, index) in messageList" :key="message.id" :id="'msg-' + message.id" class="message-item">
				<!-- 用户消息 -->
				<view v-if="message.role === 'user'" class="message-wrapper user-message">
					<view class="message-content user-content" @longpress="showMessageActions(message)">
						<text class="message-text">{{ message.content }}</text>
					</view>
					<view class="message-avatar">
						<u-avatar :src="userAvatar" size="32"></u-avatar>
					</view>
				</view>

				<!-- AI消息 -->
				<view v-else class="message-wrapper ai-message">
					<view class="message-avatar">
						<u-avatar src="/static/ai-avatar.png" size="32"></u-avatar>
					</view>
					<view class="message-content ai-content" @longpress="showMessageActions(message)">
						<view class="ai-header">
							<text class="ai-name">{{ currentModelName }}</text>
							<text class="message-time">{{ formatTime(message.createTime) }}</text>
						</view>
						<view class="message-text-container">
							<text class="message-text">{{ message.content }}</text>
							<!-- 流式输出光标 -->
							<text v-if="message.isStreaming" class="stream-cursor">|</text>
						</view>
						<!-- 消息状态 -->
						<view v-if="message.status" class="message-status">
							<view v-if="message.status === 'sending'" class="sending-indicator">
								<u-loading :show="true" size="14"></u-loading>
								<text>发送中...</text>
							</view>
							<text v-else-if="message.status === 'error'" class="error-text" @click="resendMessage(message)">发送失败，点击重试</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 正在输入提示 -->
			<!-- <view v-if="aiTyping" class="message-wrapper ai-message">
				<view class="message-avatar">
					<u-avatar src="/static/ai-avatar.png" size="32"></u-avatar>
				</view>
				<view class="message-content ai-content">
					<view class="typing-indicator">
						<u-loading :show="true" size="16"></u-loading>
						<text>AI正在思考中...</text>
					</view>
				</view>
			</view> -->

			<!-- 底部锚点，用于滚动到底部 -->
			<view id="bottom-anchor" style="height: 1px;"></view>
		</scroll-view>

		<!-- 输入区域 -->
		<view class="input-area">
			<view class="input-wrapper">
				<!-- 左侧菜单按钮 -->
				<view class="menu-btn" @click="showInputMenu">
					<u-icon name="plus" size="22" color="#666"></u-icon>
				</view>

				<!-- 输入框 -->
				<textarea
					v-model="inputMessage"
					placeholder="输入消息..."
					class="message-input"
					maxlength="1000"
					:disabled="sending"
					@confirm="sendMessage"
					auto-height
				></textarea>

				<!-- 发送按钮 -->
				<view
					class="send-btn"
					:class="{ 'send-btn-active': inputMessage.trim() && !sending }"
					@click="sendMessage"
				>
					<u-icon
						v-if="!sending"
						name="arrow-right"
						size="24"
						:color="inputMessage.trim() ? '#fff' : '#ccc'"
					></u-icon>
					<u-loading v-else color="#fff" size="24" mode="circle"></u-loading>
				</view>
			</view>
		</view>

		<!-- 更多操作菜单 -->
		<u-action-sheet :list="moreActionList" v-model="showMoreActionSheet" @click="handleMoreAction"></u-action-sheet>

		<!-- 消息操作菜单 -->
		<u-action-sheet :list="messageActionList" v-model="showMessageActionSheet" @click="handleMessageAction"></u-action-sheet>

		<!-- 模型切换弹窗 -->
		<u-popup v-model="showModelSelector" mode="bottom" border-radius="20">
			<view class="model-selector">
				<view class="selector-header">
					<text class="selector-title">切换AI模型</text>
					<u-icon name="close" @click="showModelSelector = false"></u-icon>
				</view>
				<view class="model-options">
					<view 
						v-for="model in availableModels" 
						:key="model.modelCode"
						class="model-option"
						:class="{ active: currentModelCode === model.modelCode }"
						@click="changeModel(model)"
					>
						<text class="model-name">{{ model.modelName }}</text>
						<u-icon 
							:name="currentModelCode === model.modelCode ? 'checkmark-circle-fill' : 'circle'" 
							:color="currentModelCode === model.modelCode ? '#0175FE' : '#ccc'"
						></u-icon>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				conversationId: '',
				conversationTitle: 'AI对话',
				currentModelCode: '',
				currentModelName: '',
				messageList: [],
				inputMessage: '',
				sending: false,
				aiTyping: false,
				scrollTop: 0,
				scrollIntoView: '', // 微信小程序滚动到指定元素
				userAvatar: '',
				
				// 历史消息加载
				historyPage: 1,
				historyLimit: 20,
				hasMoreHistory: true,
				loadingHistory: false,
				
				// 模型相关
				availableModels: [],
				showModelSelector: false,
				
				// 更多操作
				showMoreActionSheet: false,
				moreActionList: [
					{ text: '编辑标题', value: 'editTitle' },
					{ text: '清空对话', value: 'clearChat' },
					{ text: '删除对话', value: 'deleteChat', color: '#f56c6c' }
				],

				// 消息操作
				showMessageActionSheet: false,
				currentMessage: null,
				messageActionList: [
					{ text: '复制消息', value: 'copy' },
					{ text: '重新发送', value: 'resend' }
				],

				// 流式输出相关
				streamTaskId: '',
				streamPollingTimer: null,
				streamScrollTimer: null, // 流式滚动定时器
				streamCurrentIndex: 0,
				streamMessage: null // 当前正在流式输出的消息对象
			}
		},
		onLoad(options) {
			this.conversationId = options.conversationId
			this.currentModelCode = options.modelCode || 'deepseek-chat'

			// 获取用户头像
			this.userAvatar = this.$queue.getData('avatar') || '/static/default-avatar.png'

			// 如果没有conversationId，添加欢迎消息
			if (!this.conversationId) {
				this.messageList = [
					{
						id: 'welcome_' + Date.now(),
						role: 'assistant',
						content: '你好！我是AI助手，有什么可以帮助你的吗？',
						createTime: new Date().toISOString()
					}
				]
				this.conversationId = 'temp_' + Date.now()
			}

			this.initChat()
		},

		onReady() {
			// 页面渲染完成后再次尝试滚动
			setTimeout(() => {
				console.log('页面渲染完成，尝试滚动到底部')
				this.scrollToLatestMessage()
			}, 1000)
		},

		onShow() {
			// 页面显示时也尝试滚动到底部
			setTimeout(() => {
				console.log('页面显示，尝试滚动到底部')
				this.scrollToLatestMessage()
			}, 500)
		},

		onUnload() {
			// 清理流式轮询定时器
			this.stopStreamPolling()

			// 如果有正在进行的流式任务，尝试取消
			if (this.streamTaskId) {
				this.$Request.post(`/app/ai/chat/stream/${this.streamTaskId}/cancel`).catch(error => {
					console.log('取消流式任务失败:', error)
				})
			}

			// 页面卸载时保存对话状态
			this.saveConversationState()
		},
		methods: {
			// 初始化聊天
			initChat() {
				this.loadAvailableModels()
				this.loadConversationDetail()
				this.loadChatHistory()

				// 延迟滚动，确保数据加载完成后再滚动到底部
				setTimeout(() => {
					console.log('初始化完成，开始滚动到底部')
					this.scrollToLatestMessage()
				}, 800)
			},

			// 加载可用模型
			loadAvailableModels() {
				this.$Request.get('/app/ai/chat/models').then(res => {
					console.log('模型列表:', res)
					if (res.code === 0) {
						this.availableModels = res.data || []
						this.updateCurrentModelName()
					} else {
						console.error('加载模型失败:', res.msg)
					}
				}).catch(error => {
					console.error('加载模型失败:', error)
				})
			},

			// 加载对话详情
			loadConversationDetail() {
				if (!this.conversationId || String(this.conversationId).startsWith('temp_')) {
					// 临时对话，不需要加载详情
					return
				}

				this.$Request.get(`/app/ai/chat/conversation/${this.conversationId}`).then(res => {
					console.log('对话详情:', res)
					if (res.code === 0) {
						const detail = res.data
						this.conversationTitle = detail.title || 'AI对话'
						this.currentModelCode = detail.modelCode || this.currentModelCode
						this.updateCurrentModelName()
					} else {
						console.error('加载对话详情失败:', res.msg)
					}
				}).catch(error => {
					console.error('加载对话详情失败:', error)
				})
			},

			// 加载聊天历史
			loadChatHistory(loadMore = false) {
				if (this.loadingHistory) return
				if (!this.conversationId || String(this.conversationId).startsWith('temp_')) {
					// 临时对话，不需要加载历史
					this.loadingHistory = false
					return
				}

				this.loadingHistory = true

				// 首次加载时先从缓存获取
				if (!loadMore && this.$aiStore) {
					const cachedMessages = this.$aiStore.getChatHistory(this.conversationId)
					if (cachedMessages.length > 0) {
						this.messageList = [...this.messageList, ...cachedMessages]
						console.log('缓存消息加载完成，消息数量:', this.messageList.length)
						this.$nextTick(() => {
							setTimeout(() => {
								this.scrollToLatestMessage()
							}, 500)
						})
					}
				}

				let data = {
					page: loadMore ? this.historyPage : 1,
					limit: this.historyLimit
				}

				this.$Request.get(`/app/ai/chat/conversation/${this.conversationId}/history`, data).then(res => {
					console.log('聊天历史:', res)
					if (res.code === 0) {
						const newMessages = res.data.list || []

						if (loadMore) {
							// 加载更多历史消息，插入到列表前面
							this.messageList.unshift(...newMessages.reverse())
						} else {
							// 首次加载，合并到现有消息
							if (newMessages.length > 0) {
								this.messageList = [...this.messageList, ...newMessages]

								// 首次加载完成后滚动到底部
								console.log('历史消息加载完成，消息数量:', this.messageList.length)
								this.$nextTick(() => {
									setTimeout(() => {
										this.scrollToLatestMessage()
									}, 500)
								})
							}
							// 缓存消息历史
							if (this.$aiStore) {
								this.$aiStore.setChatHistory(this.conversationId, newMessages)
							}
						}

						// 检查是否还有更多历史消息
						this.hasMoreHistory = newMessages.length >= this.historyLimit
						if (loadMore && newMessages.length > 0) {
							this.historyPage++
						}
					} else {
						console.error('加载聊天历史失败:', res.msg)
					}
				}).catch(error => {
					console.error('加载聊天历史失败:', error)
					// 网络错误时使用缓存数据
					if (!loadMore && this.messageList.length <= 1 && this.$aiStore) {
						const cachedMessages = this.$aiStore.getChatHistory(this.conversationId)
						if (cachedMessages.length > 0) {
							this.messageList = [...this.messageList, ...cachedMessages]
							this.$queue.showToast('网络异常，显示缓存消息')

							// 缓存数据加载后滚动到底部
							this.$nextTick(() => {
								setTimeout(() => {
									this.scrollToLatestMessage()
								}, 100)
							})
						}
					}
				}).finally(() => {
					this.loadingHistory = false
				})
			},

			// 加载更多历史消息
			loadMoreHistory() {
				if (this.hasMoreHistory && !this.loadingHistory) {
					this.loadChatHistory(true)
				}
			},

			// 发送消息
			sendMessage() {
				const message = this.inputMessage.trim()
				if (!message || this.sending) {
					console.log('消息为空或正在发送中，忽略发送请求')
					return
				}

				console.log('发送消息，使用流式模式')

				// 添加用户消息到列表
				const userMessage = {
					id: Date.now(),
					role: 'user',
					content: message,
					createTime: new Date().toISOString()
				}
				this.messageList.push(userMessage)

				// 清空输入框
				this.inputMessage = ''
				this.scrollToBottom()

				// 显示AI正在输入
				this.aiTyping = true
				this.sending = true

				// 如果是临时对话，先创建对话
				if (!this.conversationId || String(this.conversationId).startsWith('temp_')) {
					this.createConversationAndSend(message, userMessage)
				} else {
					// 使用流式发送模式
					console.log('使用流式发送模式')
					this.sendMessageStream(message, userMessage)
				}
			},

			// 创建对话并发送消息
			createConversationAndSend(message, userMessage) {
				let data = {
					modelCode: this.currentModelCode,
					title: message.length > 20 ? message.substring(0, 20) + '...' : message
				}

				this.$Request.post('/app/ai/chat/conversation/create', data).then(res => {
					console.log('创建对话:', res)
					if (res.code === 0) {
						this.conversationId = res.data.id
						this.conversationTitle = data.title
						// 创建成功后使用流式发送消息
						this.sendMessageStream(message, userMessage)
					} else {
						this.handleSendError(res.msg || '创建对话失败')
					}
				}).catch(error => {
					console.error('创建对话失败:', error)
					this.handleSendError('创建对话失败，请重试')
				})
			},



			// 处理发送错误
			handleSendError(errorMsg) {
				const errorMessage = {
					id: Date.now() + 1,
					role: 'assistant',
					content: errorMsg,
					createTime: new Date().toISOString(),
					status: 'error'
				}
				this.messageList.push(errorMessage)
				this.aiTyping = false
				this.sending = false
				this.scrollToBottom()
			},

			// 流式发送消息
			sendMessageStream(message, userMessage) {
				console.log('开始流式发送消息:', message)
				let data = {
					message: message,
					modelCode: this.currentModelCode
				}

				this.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/send-stream`, data).then(res => {
					console.log('启动流式发送响应:', res)
					if (res.code === 0) {
						this.streamTaskId = res.data.taskId
						this.streamCurrentIndex = 0
						console.log('获得流式任务ID:', this.streamTaskId)

						// 创建流式消息对象
						this.streamMessage = {
							id: Date.now() + 1,
							role: 'assistant',
							content: '',
							createTime: new Date().toISOString(),
							isStreaming: true
						}
						this.messageList.push(this.streamMessage)
						console.log('创建流式消息对象，开始轮询')

						// 立即滚动到底部显示新消息
						this.$nextTick(() => {
							this.scrollToLatestMessage()
						})

						// 开始轮询获取流式内容
						this.startStreamPolling()

						// 缓存用户消息
						if (this.$aiStore) {
							this.$aiStore.addMessageToHistory(this.conversationId, userMessage)
						}
					} else {
						console.error('启动流式发送失败:', res.msg)
						this.handleSendError(res.msg || '启动流式发送失败')
					}
				}).catch(error => {
					console.error('启动流式发送请求失败:', error)
					this.handleSendError('启动流式发送失败，请重试')
				})
			},

			// 开始流式轮询
			startStreamPolling() {
				if (this.streamPollingTimer) {
					clearInterval(this.streamPollingTimer)
				}
				if (this.streamScrollTimer) {
					clearInterval(this.streamScrollTimer)
				}

				console.log('开始流式轮询，任务ID:', this.streamTaskId)
				this.streamPollingTimer = setInterval(() => {
					this.pollStreamChunks()
				}, 300) // 每300ms轮询一次

				// 额外的滚动定时器，确保实时滚动到底部
				this.streamScrollTimer = setInterval(() => {
					if (this.streamMessage && this.streamMessage.isStreaming) {
						this.scrollToLatestMessage()
					}
				}, 500) // 每500ms滚动一次（微信小程序滚动频率不宜过高）
			},

			// 轮询获取流式分块
			pollStreamChunks() {
				if (!this.streamTaskId) {
					console.log('没有流式任务ID，停止轮询')
					return
				}

				let data = { fromIndex: this.streamCurrentIndex }
				console.log(`轮询流式分块，任务ID: ${this.streamTaskId}, 当前索引: ${this.streamCurrentIndex}`)

				this.$Request.get(`/app/ai/chat/stream/${this.streamTaskId}/chunks`, data).then(res => {
					console.log('获取流式分块响应:', res)
					if (res.code === 0) {
						const chunks = res.data.chunks || []
						console.log(`收到 ${chunks.length} 个新分块`)

						// 处理新的分块
						chunks.forEach(chunk => {
							console.log(`处理分块，内容长度: ${chunk.chunkIndex}, 是否完成: ${chunk.isLast}`)

							// 更新流式消息内容（总是更新，不比较索引）
							if (this.streamMessage) {
								this.streamMessage.content = chunk.accumulatedContent
								console.log('更新消息内容:', chunk.accumulatedContent.length, '字符')

								// 实时滚动到最新消息
								this.$nextTick(() => {
									this.scrollToLatestMessage()
								})
							}
							this.streamCurrentIndex = chunk.chunkIndex

							// 检查是否完成
							if (chunk.isLast) {
								console.log('流式输出完成')
								this.finishStreamOutput(chunk)
							}
						})
					} else {
						console.error('获取流式分块失败:', res.msg)
						this.stopStreamPolling()
						this.handleStreamError('获取流式内容失败')
					}
				}).catch(error => {
					console.error('轮询流式分块请求失败:', error)
					this.stopStreamPolling()
					this.handleStreamError('网络错误，流式输出中断')
				})
			},

			// 完成流式输出
			finishStreamOutput(lastChunk) {
				this.stopStreamPolling()

				if (this.streamMessage) {
					// 标记流式输出完成
					this.streamMessage.isStreaming = false
					this.streamMessage.content = lastChunk.accumulatedContent

					// 缓存AI消息
					if (this.$aiStore) {
						this.$aiStore.addMessageToHistory(this.conversationId, this.streamMessage)
					}
				}

				this.aiTyping = false
				this.sending = false
				this.streamTaskId = ''
				this.streamMessage = null
				this.scrollToBottom()
			},

			// 处理流式错误
			handleStreamError(errorMsg) {
				this.stopStreamPolling()

				if (this.streamMessage) {
					this.streamMessage.content = errorMsg
					this.streamMessage.status = 'error'
					this.streamMessage.isStreaming = false
				}

				this.aiTyping = false
				this.sending = false
				this.streamTaskId = ''
				this.streamMessage = null
			},

			// 停止流式轮询
			stopStreamPolling() {
				if (this.streamPollingTimer) {
					clearInterval(this.streamPollingTimer)
					this.streamPollingTimer = null
				}
				if (this.streamScrollTimer) {
					clearInterval(this.streamScrollTimer)
					this.streamScrollTimer = null
				}
			},



			// 更新对话标题
			updateConversationTitle(firstMessage) {
				if (!this.conversationId || String(this.conversationId).startsWith('temp_')) {
					return
				}

				// 使用第一条消息的前20个字符作为标题
				const title = firstMessage.length > 20 ? firstMessage.substring(0, 20) + '...' : firstMessage
				let data = { title: title }

				this.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/title`, data).then(res => {
					console.log('更新标题:', res)
					if (res.code === 0) {
						this.conversationTitle = title
					} else {
						console.error('更新对话标题失败:', res.msg)
					}
				}).catch(error => {
					console.error('更新对话标题失败:', error)
				})
			},

			// 切换模型
			switchModel() {
				this.showModelSelector = true
			},

			// 更改模型
			changeModel(model) {
				if (model.modelCode === this.currentModelCode) {
					this.showModelSelector = false
					return
				}

				if (!this.conversationId || String(this.conversationId).startsWith('temp_')) {
					// 临时对话，直接切换
					this.currentModelCode = model.modelCode
					this.updateCurrentModelName()
					this.$queue.showToast(`已切换到 ${model.modelName}`)
					this.showModelSelector = false
					return
				}

				let data = { modelCode: model.modelCode }

				this.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/switch-model`, data).then(res => {
					console.log('切换模型:', res)
					if (res.code === 0) {
						this.currentModelCode = model.modelCode
						this.updateCurrentModelName()
						this.$queue.showToast(`已切换到 ${model.modelName}`)
					} else {
						this.$queue.showToast(res.msg || '切换模型失败')
					}
				}).catch(error => {
					console.error('切换模型失败:', error)
					this.$queue.showToast('切换模型失败，请重试')
				}).finally(() => {
					this.showModelSelector = false
				})
			},

			// 更新当前模型名称
			updateCurrentModelName() {
				const model = this.availableModels.find(m => m.modelCode === this.currentModelCode)
				this.currentModelName = model ? model.modelName : this.currentModelCode
			},

			// 显示输入菜单
			showInputMenu() {
				// 更新菜单列表，添加切换模型选项
				this.moreActionList = [
					{ text: '切换模型', value: 'switchModel' },
					{ text: '编辑标题', value: 'editTitle' },
					{ text: '清空对话', value: 'clearChat' },
					{ text: '删除对话', value: 'deleteChat', color: '#f56c6c' }
				]
				this.showMoreActionSheet = true
			},

			// 显示更多操作（保持兼容性）
			showMoreActions() {
				this.showInputMenu()
			},

			// 处理更多操作
			handleMoreAction(index) {
				const action = this.moreActionList[index]
				switch (action.value) {
					case 'switchModel':
						this.switchModel()
						break
					case 'editTitle':
						this.editTitle()
						break
					case 'clearChat':
						this.clearChat()
						break
					case 'deleteChat':
						this.deleteChat()
						break
				}
			},

			// 编辑标题
			editTitle() {
				uni.showModal({
					title: '编辑标题',
					editable: true,
					placeholderText: '请输入对话标题',
					success: (res) => {
						if (res.confirm && res.content.trim()) {
							if (!this.conversationId || String(this.conversationId).startsWith('temp_')) {
								// 临时对话，直接更新标题
								this.conversationTitle = res.content.trim()
								this.$queue.showToast('标题更新成功')
								return
							}

							let data = { title: res.content.trim() }
							this.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/title`, data).then(result => {
								console.log('更新标题:', result)
								if (result.code === 0) {
									this.conversationTitle = res.content.trim()
									this.$queue.showToast('标题更新成功')
								} else {
									this.$queue.showToast(result.msg || '更新失败')
								}
							}).catch(error => {
								console.error('更新标题失败:', error)
								this.$queue.showToast('更新失败，请重试')
							})
						}
					}
				})
			},

			// 清空对话
			clearChat() {
				uni.showModal({
					title: '确认清空',
					content: '清空后消息记录将无法恢复，确定要清空对话吗？',
					success: (res) => {
						if (res.confirm) {
							this.messageList = []
							this.$queue.showToast('对话已清空')
						}
					}
				})
			},

			// 删除对话
			deleteChat() {
				uni.showModal({
					title: '确认删除',
					content: '删除后无法恢复，确定要删除这个对话吗？',
					success: (res) => {
						if (res.confirm) {
							if (!this.conversationId || String(this.conversationId).startsWith('temp_')) {
								// 临时对话，直接返回
								this.$queue.showToast('删除成功')
								setTimeout(() => {
									uni.navigateBack()
								}, 1000)
								return
							}

							this.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/delete`).then(result => {
								console.log('删除对话:', result)
								if (result.code === 0) {
									this.$queue.showToast('删除成功')
									setTimeout(() => {
										uni.navigateBack()
									}, 1000)
								} else {
									this.$queue.showToast(result.msg || '删除失败')
								}
							}).catch(error => {
								console.error('删除对话失败:', error)
								this.$queue.showToast('删除失败，请重试')
							})
						}
					}
				})
			},

			// 滚动到底部
			scrollToBottom() {
				this.$nextTick(() => {
					// 微信小程序使用scroll-into-view滚动到底部锚点
					this.scrollIntoView = 'bottom-anchor'
					console.log('滚动到底部锚点')

					// 清空scrollIntoView，避免影响后续滚动
					setTimeout(() => {
						this.scrollIntoView = ''
					}, 300)
				})
			},

			// 滚动到最新消息
			scrollToLatestMessage() {
				if (this.messageList.length > 0) {
					const latestMessage = this.messageList[this.messageList.length - 1]
					console.log('滚动到最新消息:', latestMessage.id, '消息总数:', this.messageList.length)

					this.$nextTick(() => {
						// 先尝试使用scroll-into-view
						this.scrollIntoView = 'msg-' + latestMessage.id

						// 同时使用scroll-top作为备选方案
						setTimeout(() => {
							this.scrollTop = 999999
							this.scrollIntoView = ''
						}, 200)

						// 再次尝试滚动到底部锚点
						setTimeout(() => {
							this.scrollIntoView = 'bottom-anchor'
						}, 400)

						// 最终清空
						setTimeout(() => {
							this.scrollIntoView = ''
						}, 600)
					})
				} else {
					console.log('没有消息，滚动到底部')
					this.scrollToBottom()
				}
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return ''

				// 处理iOS兼容性：将 "yyyy-MM-dd HH:mm:ss" 格式转换为 "yyyy/MM/dd HH:mm:ss"
				let processedTimeStr = timeStr
				if (typeof timeStr === 'string') {
					// 如果是 "2025-08-04 17:10:46" 格式，转换为 "2025/08/04 17:10:46"
					processedTimeStr = timeStr.replace(/-/g, '/')
				}

				const time = new Date(processedTimeStr)

				// 检查日期是否有效
				if (isNaN(time.getTime())) {
					console.warn('无效的时间格式:', timeStr)
					return ''
				}

				return time.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit'
				})
			},



			// 显示消息操作菜单
			showMessageActions(message) {
				this.currentMessage = message
				// 根据消息类型动态设置操作选项
				if (message.role === 'user') {
					this.messageActionList = [
						{ text: '复制消息', value: 'copy' },
						{ text: '重新发送', value: 'resend' }
					]
				} else {
					this.messageActionList = [
						{ text: '复制消息', value: 'copy' }
					]
				}
				this.showMessageActionSheet = true
			},

			// 处理消息操作
			handleMessageAction(index) {
				const action = this.messageActionList[index]
				switch (action.value) {
					case 'copy':
						this.copyMessage()
						break
					case 'resend':
						this.resendMessage(this.currentMessage)
						break
				}
			},

			// 复制消息
			copyMessage() {
				if (this.currentMessage) {
					uni.setClipboardData({
						data: this.currentMessage.content,
						success: () => {
							this.$queue.showToast('消息已复制')
						},
						fail: () => {
							this.$queue.showToast('复制失败')
						}
					})
				}
			},

			// 重新发送消息
			resendMessage(message) {
				if (message.role !== 'user') return

				// 重新发送用户消息
				this.inputMessage = message.content
				this.sendMessage()
			},

			// 保存对话状态
			saveConversationState() {
				// 可以在这里保存一些本地状态
				uni.setStorageSync(`chat_${this.conversationId}`, {
					scrollPosition: this.scrollTop,
					lastViewTime: new Date().toISOString()
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.chat-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background: #f5f5f5;
		position: relative;
	}

	.chat-header {
		background: #0175FE;
		color: white;
		padding: 30rpx;
		text-align: center;
		position: sticky;
		top: 0;
		z-index: 100;
	}

	.conversation-title {
		font-size: 32rpx;
		font-weight: 500;
		color: white;
	}

	.message-list {
		flex: 1;
		padding: 20rpx;
		padding-bottom: 120rpx; /* 为底部输入框留出空间 */
		overflow-y: auto;
		height: calc(100vh - 200rpx); /* 减去头部和输入框的高度 */
	}

	.load-more-history {
		text-align: center;
		padding: 20rpx;
		color: #666;
		font-size: 28rpx;
	}

	.message-item {
		margin-bottom: 30rpx;
		width: 100%;
	}

	.message-wrapper {
		display: flex;
		align-items: flex-start;
		gap: 20rpx;
		width: 100%;
		margin-bottom: 10rpx;
	}

	.user-message {
		flex-direction: row-reverse;
	}

	.ai-message {
		flex-direction: row;
	}

	.message-avatar {
		flex-shrink: 0;
	}

	.message-content {
		max-width: 70%;
		padding: 20rpx 24rpx;
		border-radius: 16rpx;
		position: relative;
	}

	.user-content {
		background: #0175FE;
		color: white;
		border-bottom-right-radius: 6rpx;
		margin-left: auto;
	}

	.ai-content {
		background: white;
		color: #333;
		border-bottom-left-radius: 6rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		margin-right: auto;
	}

	.ai-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12rpx;
		padding-bottom: 8rpx;
		border-bottom: 1rpx solid #eee;
	}

	.ai-name {
		font-size: 24rpx;
		color: #0175FE;
		font-weight: 500;
	}

	.message-time {
		font-size: 22rpx;
		color: #999;
	}

	.message-text {
		font-size: 30rpx;
		line-height: 1.6;
		word-wrap: break-word;
	}

	.message-status {
		margin-top: 12rpx;
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.error-text {
		color: #f56c6c;
		font-size: 24rpx;
	}

	.typing-indicator {
		display: flex;
		align-items: center;
		gap: 12rpx;
		color: #666;
		font-size: 28rpx;
	}

	.input-area {
		background: white;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #eee;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		/* #ifdef H5 */
		padding-bottom: env(safe-area-inset-bottom);
		/* #endif */
	}

	.input-wrapper {
		display: flex;
		align-items: flex-end;
		gap: 16rpx;
		min-height: 80rpx;
	}

	.menu-btn {
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		background: #f8f9fa;
		border: 1rpx solid #e9ecef;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		flex-shrink: 0;
	}

	.menu-btn:active {
		background: #e9ecef;
		transform: scale(0.95);
	}

	.message-input {
		flex: 1;
		background: #f8f9fa;
		border-radius: 36rpx;
		padding: 20rpx 25rpx;
		border: 1rpx solid #e9ecef;
		font-size: 30rpx;
		line-height: 1.4;
		min-height: 72rpx;
		max-height: 200rpx;
		resize: none;
		outline: none;
		transition: all 0.3s ease;
	}

	.message-input:focus {
		border-color: #0175FE;
		background: #fff;
		box-shadow: 0 0 0 6rpx rgba(1, 117, 254, 0.1);
	}

	.send-btn {
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		background: #e9ecef;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		flex-shrink: 0;
		cursor: pointer;
	}

	.send-btn-active {
		background: linear-gradient(135deg, #0175FE 0%, #0056d3 100%);
		box-shadow: 0 4rpx 12rpx rgba(1, 117, 254, 0.3);
		transform: scale(1.05);
	}

	.send-btn:active {
		transform: scale(0.95);
	}

	.send-btn-active:active {
		transform: scale(1.0);
	}

	.model-selector {
		background: white;
		border-radius: 20rpx 20rpx 0 0;
		padding: 40rpx 30rpx;
		max-height: 60vh;
	}

	.selector-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #eee;
	}

	.selector-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}

	.model-options {
		max-height: 400rpx;
		overflow-y: auto;
	}

	.model-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 20rpx;
		border-radius: 12rpx;
		margin-bottom: 16rpx;
		background: #f8f9fa;
		transition: all 0.3s ease;

		&.active {
			background: #e6f3ff;
			border: 1rpx solid #0175FE;
		}
	}

	.model-name {
		font-size: 30rpx;
		color: #333;
	}

	.loading-indicator {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
		color: #666;
		font-size: 28rpx;
	}

	.sending-indicator {
		display: flex;
		align-items: center;
		gap: 8rpx;
		color: #999;
		font-size: 24rpx;
	}

	.typing-indicator {
		display: flex;
		align-items: center;
		gap: 12rpx;
		color: #666;
		font-size: 28rpx;
	}

	.message-text-container {
		display: inline;
		position: relative;
	}

	.stream-cursor {
		color: #0175FE;
		font-weight: bold;
		animation: blink 1s infinite;
		margin-left: 2rpx;
	}

	@keyframes blink {
		0%, 50% { opacity: 1; }
		51%, 100% { opacity: 0; }
	}
</style>
