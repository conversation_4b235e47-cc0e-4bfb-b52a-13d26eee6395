package com.sqx.modules.healthRecord.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.Result;
import com.sqx.modules.healthRecord.entity.HealthStatusRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 健康状况记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface HealthStatusRecordService extends IService<HealthStatusRecord> {

    /**
     * 保存健康记录（新增或修改）
     * @param healthRecord 健康记录对象
     * @return 操作结果
     */
    Result saveHealthRecord(HealthStatusRecord healthRecord);

    /**
     * 分页查询健康记录列表
     * @param page 页码
     * @param limit 每页数量
     * @param healthRecord 查询条件
     * @return 分页结果
     */
    IPage<HealthStatusRecord> getHealthRecordList(Integer page, Integer limit, HealthStatusRecord healthRecord);

    /**
     * 根据记录ID获取健康记录详情
     * @param recordId 记录ID
     * @return 健康记录详情
     */
    HealthStatusRecord getHealthRecordInfo(Long recordId);

    /**
     * 根据患者ID分页查询健康记录
     * @param page 页码
     * @param limit 每页数量
     * @param patientId 患者ID
     * @return 分页结果
     */
    IPage<HealthStatusRecord> getHealthRecordsByPatientId(Integer page, Integer limit, Long patientId);

    /**
     * 根据用户ID分页查询健康记录
     * @param page 页码
     * @param limit 每页数量
     * @param userId 用户ID
     * @return 分页结果
     */
    IPage<HealthStatusRecord> getHealthRecordsByUserId(Integer page, Integer limit, Long userId);

    /**
     * 删除健康记录（逻辑删除）
     * @param recordId 记录ID
     * @param operatorId 操作人ID
     * @return 操作结果
     */
    Result deleteHealthRecord(Long recordId, Long operatorId);

    /**
     * 批量删除健康记录（逻辑删除）
     * @param recordIds 记录ID数组
     * @param operatorId 操作人ID
     * @return 操作结果
     */
    Result batchDeleteHealthRecords(Long[] recordIds, Long operatorId);
}
