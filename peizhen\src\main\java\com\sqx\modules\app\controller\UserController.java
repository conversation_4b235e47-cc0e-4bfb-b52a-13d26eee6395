package com.sqx.modules.app.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.DateUtils;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.dao.UserMoneyDao;
import com.sqx.modules.app.entity.UserCertification;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.entity.UserMoney;
import com.sqx.modules.app.entity.UserMoneyDetails;
import com.sqx.modules.app.response.HomeMessageResponse;
import com.sqx.modules.app.response.UserMessageResponse;
import com.sqx.modules.app.service.UserCertificationService;
import com.sqx.modules.app.service.UserMoneyDetailsService;
import com.sqx.modules.app.service.UserMoneyService;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.creditRecord.service.CreditRecordService;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.pay.entity.CashOut;
import com.sqx.modules.pay.service.PayDetailsService;
import com.sqx.modules.rewardLevel.entity.RewardLevel;
import com.sqx.modules.rewardLevel.service.RewardLevelService;
import com.sqx.modules.utils.EasyPoi.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/30
 */
@RestController
@Api(value = "用户管理", tags = {"用户管理"})
@RequestMapping(value = "/user")
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private UserMoneyDetailsService userMoneyDetailsService;
    @Autowired
    private UserMoneyService userMoneyService;
    @Autowired
    private PayDetailsService payDetailsService;
    @Autowired
    private OrdersService ordersService;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private RewardLevelService levelService;
    @Autowired
    private CreditRecordService recordService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private UserCertificationService certificationService;

    @RequestMapping(value = "/{userId}", method = RequestMethod.GET)
    @ApiOperation("获取用户详细信息")
    @ResponseBody
    public Result selectUserById(@ApiParam("用户id") @PathVariable Long userId) {
        Map<String, Object> map = new HashMap<>();
        UserEntity userEntity = userService.queryByUserId(userId);
        if (userEntity==null){
            return Result.error("用户不存在");
        }
        if (userEntity.getRewardId() == null) {
            CommonInfo info = commonInfoService.findOne(339);
            if (info != null) {
                RewardLevel rewardLevel = levelService.getById(info.getValue());
                if (rewardLevel != null) {
                    userEntity.setRewardId(rewardLevel.getRewardId());
                    userEntity.setLevelName(rewardLevel.getLevelName());
                    userEntity.setIconImg(rewardLevel.getIconImg());
                    userService.updateById(userEntity);
                }

            }
        }
        UserMoney userMoney = userMoneyService.selectUserMoneyByUserId(userId);
        Double money = 0.0;
        BigDecimal safetyMoney = BigDecimal.ZERO;
        if (userMoney != null) {
            money = userMoney.getMoney().doubleValue();
            safetyMoney = userMoney.getSafetyMoney();
        }
        //查询用户钱包
        Double m = money;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = simpleDateFormat.format(new Date());
        //查询本月充值
        Double consume = payDetailsService.instantselectSumPay(date, userId);
        //查询本月提现
        Double income = userMoneyDetailsService.monthIncome(date, userId);
        //查询邀请人数
        int count = userService.queryInviterCount(userEntity.getInvitationCode());
        //本月接单
        int takeOrdersCount = ordersService.selectTakeOrdersCount(userId, date);
        //本月订单
        int myOrdersCount = ordersService.selectMyOrdersCount(userId, date);
        map.put("userEntity", userEntity);
        map.put("money", m);
        map.put("safetyMoney", safetyMoney);
        map.put("consume", consume);
        map.put("income", income);
        map.put("count", count);
        map.put("takeOrdersCount", takeOrdersCount);
        map.put("myOrdersCount", myOrdersCount);
        return Result.success().put("data", map);
    }

    @PostMapping("/updateSafetyMoney")
    @ApiOperation("修改")
    public Result updateSafetyMoney(Long userId, Integer type, BigDecimal money) {
        userMoneyService.updateSafetyMoney(type, userId, money);
        UserMoneyDetails userMoneyDetails = new UserMoneyDetails();
        userMoneyDetails.setClassify(4);
        userMoneyDetails.setUserId(userId);
        if (type == 1) {
            userMoneyDetails.setTitle("[保证金]增加保证金");
            userMoneyDetails.setContent("系统增加保证金");
        } else {
            userMoneyDetails.setTitle("[保证金]减少保证金");
            userMoneyDetails.setContent("系统减少保证金");
        }
        userMoneyDetails.setType(type);
        userMoneyDetails.setMoney(money);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userMoneyDetails.setCreateTime(sdf.format(new Date()));
        userMoneyDetailsService.save(userMoneyDetails);
        UserMoney userMoney = userMoneyService.selectUserMoneyByUserId(userId);
        if (userMoney.getSafetyMoney().doubleValue() > 0) {
            UserEntity userEntity = userService.selectUserById(userId);
            userEntity.setIsSafetyMoney(1);
            userService.updateById(userEntity);
        } else {
            UserEntity userEntity = userService.selectUserById(userId);
            userEntity.setIsSafetyMoney(2);
            userService.updateById(userEntity);
        }
        return Result.success();
    }

    @GetMapping("selectUserByInvitationCode")
    @ApiOperation("根据邀请码查询用户")
    public Result selectUserByInvitationCode(String invitationCode) {
        return Result.success().put("data", userService.getOne(new QueryWrapper<UserEntity>().eq("invitation_code", invitationCode)));
    }

    @RequestMapping(value = "/addCannotMoney/{userId}/{money}/{type}", method = RequestMethod.POST)
    @ApiOperation("修改金额")
    @ResponseBody
    public Result addCannotMoney(@PathVariable("userId") Long userId, @PathVariable("money") Double money, @PathVariable("type") Integer type) {
        if (type == 1) {
            userMoneyService.updateMoney(1, userId, BigDecimal.valueOf(money));
            UserMoneyDetails userMoneyDetails = new UserMoneyDetails();
            userMoneyDetails.setMoney(BigDecimal.valueOf(money));
            userMoneyDetails.setUserId(userId);
            userMoneyDetails.setContent("管理端充值：" + money);
            userMoneyDetails.setTitle("管理端充值金额");
            userMoneyDetails.setType(1);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            userMoneyDetails.setCreateTime(simpleDateFormat.format(new Date()));
            userMoneyDetailsService.save(userMoneyDetails);
        } else {
            userMoneyService.updateMoney(2, userId, BigDecimal.valueOf(money));
            UserMoneyDetails userMoneyDetails = new UserMoneyDetails();
            userMoneyDetails.setMoney(BigDecimal.valueOf(money));
            userMoneyDetails.setUserId(userId);
            userMoneyDetails.setContent("管理端减少：" + money);
            userMoneyDetails.setTitle("管理端减少金额");
            userMoneyDetails.setType(2);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            userMoneyDetails.setCreateTime(simpleDateFormat.format(new Date()));
            userMoneyDetailsService.save(userMoneyDetails);
        }
        return Result.success();
    }


    @RequestMapping(value = "/selectUserList", method = RequestMethod.GET)
    @ApiOperation("查询所有用户列表")
    @ResponseBody
    public Result selectUserList(Integer page, Integer limit,
                                 String phone,
                                 Integer sex,
                                 String platform,
                                 String sysPhone,
                                 Integer status,
                                 Integer isAuthentication, Integer isPromotion, Integer isAgent, String userName, String invitationCode,
                                 String inviterCode, String startTime, String endTime) {
        return Result.success().put("data", userService.selectUserPage(page, limit, phone, sex, platform, sysPhone, status, isAuthentication, isPromotion, isAgent, userName, invitationCode, inviterCode, startTime, endTime));
    }


    @RequestMapping(value = "/deleteUserByUserId/{userId}", method = RequestMethod.POST)
    @ApiOperation("删除用户")
    @ResponseBody
    public Result deleteUserByUserId(@PathVariable("userId") Long userId) {
        userService.removeById(userId);
        certificationService.remove(new QueryWrapper<UserCertification>().eq("user_id", userId));
        return Result.success();
    }

    @RequestMapping(value = "/updateUserByUserId", method = RequestMethod.POST)
    @ApiOperation("修改用户")
    @ResponseBody
    public Result updateUserByUserId(@RequestBody UserEntity userEntity) {
        UserEntity entity = userService.getById(userEntity.getUserId());
        if (StringUtils.isNotBlank(userEntity.getPhone())) {
            int count = userService.count(new QueryWrapper<UserEntity>().eq("phone", userEntity.getPhone()).ne("user_id", userEntity.getUserId()));
            if (count >= 1) {
                return Result.error("当前手机号已存在");
            }
        }
        if (userEntity.getIsAgent() != null && userEntity.getIsAgent() != 1) {
            userEntity.setProvince(null);
            userEntity.setCity(null);
            userEntity.setDistrict(null);
            userService.cancelArea(userEntity);
        }
        userService.updateById(userEntity);
        return Result.success();
    }

    /**
     * @param userId 用户id
     * @param type   1:增加 2减少
     * @param score  操作的分数
     * @return
     */
    @ApiOperation("修改用户信用分")
    @PostMapping("updateUserCredit")
    public Result updateUserCredit(Long userId, Integer type, Integer score, String remark) {
        if (score <= 0) {
            return Result.error("请填入大于0的数");
        }
        UserEntity userEntity = userService.getById(userId);
        String text;
        if (type == 1) {
            text = "增加";
            if (userEntity.getCreditScore() + score > 10) {
                return Result.error("最多增加" + (10 - userEntity.getCreditScore()) + "分");
            }
            recordService.updateUserCreditRecord(userEntity, 1, score, remark);
        } else {
            text = "扣除";
            if (userEntity.getCreditScore() < score) {
                return Result.error("当前用户最多扣除" + userEntity.getCreditScore() + "分");
            }
            recordService.updateUserCreditRecord(userEntity, 2, score, remark);
        }
        MessageInfo riderMessageInfo = new MessageInfo();
        riderMessageInfo.setContent("管理员" + text + "您" + score + "信用分,当前信用分" + userEntity.getCreditScore());
        riderMessageInfo.setTitle("信用分变动通知");
        riderMessageInfo.setState(String.valueOf(5));
        riderMessageInfo.setUserName(userEntity.getUserName());
        riderMessageInfo.setUserId(String.valueOf(userEntity.getUserId()));
        riderMessageInfo.setCreateAt(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        riderMessageInfo.setIsSee("0");
        messageService.save(riderMessageInfo);
        return messageService.save(riderMessageInfo) ? Result.success() : Result.error();
    }

    @ResponseBody
    @PostMapping(value = "/updateAgent")
    @ApiOperation("修改用户代理")
    public Result updateAgent(@RequestBody UserEntity userEntity) {
        int count = 0;
        //代理省级单位
        if (StringUtils.isNotBlank(userEntity.getProvince()) && StringUtils.isBlank(userEntity.getCity()) && StringUtils.isBlank(userEntity.getDistrict())) {
            count += userService.count(new QueryWrapper<UserEntity>().eq("province", userEntity.getProvince()).ne("user_id", userEntity.getUserId()));
        }
        //代理市级单位
        if (StringUtils.isNotBlank(userEntity.getProvince()) && StringUtils.isNotBlank(userEntity.getCity()) && StringUtils.isBlank(userEntity.getDistrict())) {
            count += userService.count(new QueryWrapper<UserEntity>().eq("province", userEntity.getProvince()).eq("city", userEntity.getCity()).ne("user_id", userEntity.getUserId()));
        }
        //代理区/县级单位
        if (StringUtils.isNotBlank(userEntity.getProvince()) && StringUtils.isNotBlank(userEntity.getCity()) && StringUtils.isNotBlank(userEntity.getDistrict())) {
            count += userService.count(new QueryWrapper<UserEntity>().eq("province", userEntity.getProvince()).eq("city", userEntity.getCity()).eq("district", userEntity.getDistrict()));
            count += userService.count(new QueryWrapper<UserEntity>().eq("province", userEntity.getProvince()).eq("city", userEntity.getCity()).isNull("district"));
            count += userService.count(new QueryWrapper<UserEntity>().eq("province", userEntity.getProvince()).isNull("city").isNull("district").ne("user_id", userEntity.getUserId()));
        }
        if (count > 0) {
            return Result.error("当前区域已有代理");
        }
        if (StringUtils.isBlank(userEntity.getProvince())) {
            userEntity.setProvince(null);
        }
        if (StringUtils.isBlank(userEntity.getCity())) {
            userEntity.setCity(null);
        }
        if (StringUtils.isBlank(userEntity.getDistrict())) {
            userEntity.setDistrict(null);
        }
        return Result.upStatus(userService.cancelArea(userEntity));

    }

    @RequestMapping(value = "/updateUserStatusByUserId", method = RequestMethod.GET)
    @ApiOperation("禁用或启用用户")
    @ResponseBody
    public Result updateUserByUserId(Long userId) {
        UserEntity byId = userService.getById(userId);
        if (byId.getStatus().equals(1)) {
            byId.setStatus(2);
        } else {
            byId.setStatus(1);
        }
        userService.updateById(byId);
        return Result.success();
    }


    /**
     * 获取openid
     *
     * @param code 微信code
     * @return openid
     */
    @GetMapping("/openId/{code:.+}/{userId}")
    @ApiOperation("根据code获取openid")
    public Result getOpenid(@PathVariable("code") String code, @PathVariable("userId") Long userId) {
        return userService.getOpenId(code, userId);
    }

    /**
     * 信息分析
     *
     * @return
     */
    @GetMapping("/homeMessage")
    @ApiOperation("信息分析")
    public Result homeMessage() {
        HomeMessageResponse homeMessageResponse = new HomeMessageResponse();
        //   0查总   1查天  2查月  3查年
        //设置总用户人数
        homeMessageResponse.setTotalUsers(userService.queryUserCount(0, null, null, null));
        //设置今日新增
        homeMessageResponse.setNewToday(userService.queryUserCount(1, null, null, null));
        //设置本月新增
        homeMessageResponse.setNewMonth(userService.queryUserCount(2, null, null, null));
        //设置本年新增
        homeMessageResponse.setNewYear(userService.queryUserCount(3, null, null, null));

        //设置总收入
        homeMessageResponse.setTotalRevenue(userService.queryPayMoney(0));
        //设置今日收入
        homeMessageResponse.setTodayRevenue(userService.queryPayMoney(1));
        //设置本月收入
        homeMessageResponse.setMonthRevenue(userService.queryPayMoney(2));
        //设置本年收入
        homeMessageResponse.setYearRevenue(userService.queryPayMoney(3));

        return Result.success().put("data", homeMessageResponse);
    }

    /**
     * 接单分析
     *
     * @return
     */
    @GetMapping("/takingOrdersMessage")
    @ApiOperation("接单分析")
    public Result takingOrdersMessage(Long page, Long limit, String date, Long type) {
        Page<Map<String, Object>> iPage = new Page<>(page, limit);
        return userService.takingOrdersMessage(iPage, type, date);
    }

    /**
     * 用户分析
     */
    /**
     * 用户分析
     */
    @GetMapping("/userMessage")
    @ApiOperation("用户分析")
    public Result userMessage(String date, Integer flag) {
        int sumUserCount = userService.queryUserCount(flag, date, null, null);
        int h5Count = userService.queryUserCount(flag, date, "H5", null);
        int appCount = userService.queryUserCount(flag, date, "app", null);
        int wxCount = userService.queryUserCount(flag, date, "小程序", null);
        int memberCount = userService.userMessage(date, flag);
        int userCount = sumUserCount - memberCount;
        int sumAuthUserCount = userService.queryUserCount(flag, date, null, 1);
        int h5AuthCount = userService.queryUserCount(flag, date, "H5", 1);
        int appAuthCount = userService.queryUserCount(flag, date, "app", 1);
        int wxAuthCount = userService.queryUserCount(flag, date, "小程序", 1);
        Map<String, Integer> result = new HashMap<>();
        result.put("sumUserCount", sumUserCount);
        result.put("h5Count", h5Count);
        result.put("appCount", appCount);
        result.put("wxCount", wxCount);
        result.put("memberCount", memberCount);
        result.put("userCount", userCount);
        result.put("sumAuthUserCount", sumAuthUserCount);
        result.put("h5AuthCount", h5AuthCount);
        result.put("appAuthCount", appAuthCount);
        result.put("wxAuthCount", wxAuthCount);
        return Result.success().put("data", result);
    }

    @ApiOperation("取消师傅实名认证")
    @PostMapping("updateCertification")
    public Result updateCertification(Long userId) {
        return userService.updateCertification(userId);
    }


    @GetMapping("/userExportExcel")
    public void userListExcel(String phone,
                              Integer sex,
                              String platform,
                              String sysPhone,
                              Integer status,
                              Integer isAuthentication, Integer isPromotion, Integer isAgent, String userName, String invitationCode,
                              String inviterCode, String startTime, String endTime, HttpServletResponse response, String search) throws IOException {
        List<UserEntity> list = userService.selectUserList(phone, sex, platform, sysPhone, status, isAuthentication, isPromotion, isAgent, userName, invitationCode, inviterCode, startTime, endTime, search);
        ExcelUtils.exportExcel(list, "用户统计表", "用户统计Sheet", UserEntity.class, "用户统计表", response);
    }

    @GetMapping(value = "/getNursingListV5")
    @ApiOperation("获取护工列表")
    @ResponseBody
    public Result getNursingListV5(Integer page, Integer limit, Integer sex, Integer workMin, Integer workMax, Integer ageMin, Integer ageMax, String city, Integer authentication, Integer orderCount, Integer finalScore, String realName, String phone) {
        return Result.success().put("data", userService.getNursingListV5(page, limit, sex, workMin, workMax, ageMin, ageMax, city, authentication, orderCount, finalScore, realName, phone));
    }
}