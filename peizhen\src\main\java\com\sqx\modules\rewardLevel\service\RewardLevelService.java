package com.sqx.modules.rewardLevel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.Result;
import com.sqx.modules.rewardLevel.entity.RewardLevel;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RewardLevelService extends IService<RewardLevel> {

    Result saveLevel(RewardLevel rewardLevel);

    IPage<RewardLevel> getLevelList(Integer page, Integer limit, RewardLevel rewardLevel);

    void checkOrderCount(Long orderTakingUserId);
}
