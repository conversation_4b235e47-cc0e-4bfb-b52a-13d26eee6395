{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-navbar/u-navbar.vue?9d87", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-navbar/u-navbar.vue?fb44", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-navbar/u-navbar.vue?d45a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-navbar/u-navbar.vue?7415", "uni-app:///uview-ui/components/u-navbar/u-navbar.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-navbar/u-navbar.vue?4ead", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-navbar/u-navbar.vue?1919"], "names": ["menuButtonInfo", "name", "props", "height", "type", "default", "backIconColor", "backIconName", "backIconSize", "backText", "backTextStyle", "color", "title", "titleWidth", "titleColor", "titleBold", "titleSize", "isBack", "background", "isFixed", "immersive", "borderBottom", "zIndex", "customBack", "data", "statusBarHeight", "computed", "navbarInnerStyle", "style", "navbarStyle", "Object", "titleStyle", "navbarHeight", "created", "methods", "goBack", "uni"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoC1wB;AACA;AACA;AACA;;AAEAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,gBAuBA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;QACA;UACAM;QACA;MACA;IACA;IACA;IACAC;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;QACA;UACAa;QACA;MACA;IACA;IACA;IACAC;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;EACA;EACAmB;IACA;MACAxB;MACAyB;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;MACA;;MAEA;MACAA;MAEA;IACA;IACA;IACAC;MACA;MACAD;MACA;MACAE;MACA;IACA;IACA;IACAC;MACA;;MAMA;MACA;MACAH;MACAA,iHACA;MAEAA;MACA;IACA;IACA;IACAI;MAKA;MACA;MACA;MACA;MACA;IAEA;EACA;EACAC;EACAC;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;QACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7OA;AAAA;AAAA;AAAA;AAA66C,CAAgB,uvCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-navbar/u-navbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-navbar.vue?vue&type=template&id=1194bf80&scoped=true&\"\nvar renderjs\nimport script from \"./u-navbar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-navbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-navbar.vue?vue&type=style&index=0&id=1194bf80&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1194bf80\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-navbar/u-navbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=template&id=1194bf80&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.navbarStyle])\n  var s1 = _vm.__get_style([_vm.navbarInnerStyle])\n  var s2 =\n    _vm.isBack && _vm.backText ? _vm.__get_style([_vm.backTextStyle]) : null\n  var s3 = _vm.title ? _vm.__get_style([_vm.titleStyle]) : null\n  var m0 = _vm.isFixed && !_vm.immersive ? Number(_vm.navbarHeight) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"\">\n\t\t<view class=\"u-navbar\" :style=\"[navbarStyle]\" :class=\"{ 'u-navbar-fixed': isFixed, 'u-border-bottom': borderBottom }\">\n\t\t\t<view class=\"u-status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\t\t\t<view class=\"u-navbar-inner\" :style=\"[navbarInnerStyle]\">\n\t\t\t\t<view class=\"u-back-wrap\" v-if=\"isBack\" @tap=\"goBack\">\n\t\t\t\t\t<view class=\"u-icon-wrap\">\n\t\t\t\t\t\t<u-icon :name=\"backIconName\" :color=\"backIconColor\" :size=\"backIconSize\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-icon-wrap u-back-text u-line-1\" v-if=\"backText\" :style=\"[backTextStyle]\">{{ backText }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-navbar-content-title\" v-if=\"title\" :style=\"[titleStyle]\">\n\t\t\t\t\t<view\n\t\t\t\t\t    class=\"u-title u-line-1\"\n\t\t\t\t\t    :style=\"{\n\t\t\t\t\t\t\tcolor: titleColor,\n\t\t\t\t\t\t\tfontSize: titleSize + 'rpx',\n\t\t\t\t\t\t\tfontWeight: titleBold ? 'bold' : 'normal'\n\t\t\t\t\t\t}\">\n\t\t\t\t\t\t{{ title }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-slot-content\">\n\t\t\t\t\t<slot></slot>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-slot-right\">\n\t\t\t\t\t<slot name=\"right\"></slot>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 解决fixed定位后导航栏塌陷的问题 -->\n\t\t<view class=\"u-navbar-placeholder\" v-if=\"isFixed && !immersive\" :style=\"{ width: '100%', height: Number(navbarHeight) + statusBarHeight + 'px' }\"></view>\n\t</view>\n</template>\n\n<script>\n\t// 获取系统状态栏的高度\n\tlet systemInfo = uni.getSystemInfoSync();\n\tlet menuButtonInfo = {};\n\t// 如果是小程序，获取右上角胶囊的尺寸信息，避免导航栏右侧内容与胶囊重叠(支付宝小程序非本API，尚未兼容)\n\t// #ifdef MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ\n\tmenuButtonInfo = uni.getMenuButtonBoundingClientRect();\n\t// #endif\n\t/**\n\t * navbar 自定义导航栏\n\t * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uniapp自带的导航栏。\n\t * @tutorial https://www.uviewui.com/components/navbar.html\n\t * @property {String Number} height 导航栏高度(不包括状态栏高度在内，内部自动加上)，注意这里的单位是px（默认44）\n\t * @property {String} back-icon-color 左边返回图标的颜色（默认#606266）\n\t * @property {String} back-icon-name 左边返回图标的名称，只能为uView自带的图标（默认arrow-left）\n\t * @property {String Number} back-icon-size 左边返回图标的大小，单位rpx（默认30）\n\t * @property {String} back-text 返回图标右边的辅助提示文字\n\t * @property {Object} back-text-style 返回图标右边的辅助提示文字的样式，对象形式（默认{ color: '#606266' }）\n\t * @property {String} title 导航栏标题，如设置为空字符，将会隐藏标题占位区域\n\t * @property {String Number} title-width 导航栏标题的最大宽度，内容超出会以省略号隐藏，单位rpx（默认250）\n\t * @property {String} title-color 标题的颜色（默认#606266）\n\t * @property {String Number} title-size 导航栏标题字体大小，单位rpx（默认32）\n\t * @property {Function} custom-back 自定义返回逻辑方法\n\t * @property {String Number} z-index 固定在顶部时的z-index值（默认980）\n\t * @property {Boolean} is-back 是否显示导航栏左边返回图标和辅助文字（默认true）\n\t * @property {Object} background 导航栏背景设置，见官网说明（默认{ background: '#ffffff' }）\n\t * @property {Boolean} is-fixed 导航栏是否固定在顶部（默认true）\n\t * @property {Boolean} immersive 沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效（默认false）\n\t * @property {Boolean} border-bottom 导航栏底部是否显示下边框，如定义了较深的背景颜色，可取消此值（默认true）\n\t * @example <u-navbar back-text=\"返回\" title=\"剑未配妥，出门已是江湖\"></u-navbar>\n\t */\n\texport default {\n\t\tname: \"u-navbar\",\n\t\tprops: {\n\t\t\t// 导航栏高度，单位px，非rpx\n\t\t\theight: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 返回箭头的颜色\n\t\t\tbackIconColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#606266'\n\t\t\t},\n\t\t\t// 左边返回的图标\n\t\t\tbackIconName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'nav-back'\n\t\t\t},\n\t\t\t// 左边返回图标的大小，rpx\n\t\t\tbackIconSize: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: '44'\n\t\t\t},\n\t\t\t// 返回的文字提示\n\t\t\tbackText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 返回的文字的 样式\n\t\t\tbackTextStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tcolor: '#606266'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 导航栏标题\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 标题的宽度，如果需要自定义右侧内容，且右侧内容很多时，可能需要减少这个宽度，单位rpx\n\t\t\ttitleWidth: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: '250'\n\t\t\t},\n\t\t\t// 标题的颜色\n\t\t\ttitleColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#606266'\n\t\t\t},\n\t\t\t// 标题字体是否加粗\n\t\t\ttitleBold: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 标题的字体大小\n\t\t\ttitleSize: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 32\n\t\t\t},\n\t\t\tisBack: {\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 对象形式，因为用户可能定义一个纯色，或者线性渐变的颜色\n\t\t\tbackground: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tbackground: '#ffffff'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 导航栏是否固定在顶部\n\t\t\tisFixed: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 是否沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效\n\t\t\timmersive: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 是否显示导航栏的下边框\n\t\t\tborderBottom: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tzIndex: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 自定义返回逻辑\n\t\t\tcustomBack: {\n\t\t\t\ttype: Function,\n\t\t\t\tdefault: null\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmenuButtonInfo: menuButtonInfo,\n\t\t\t\tstatusBarHeight: systemInfo.statusBarHeight\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t// 导航栏内部盒子的样式\n\t\t\tnavbarInnerStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\t// 导航栏宽度，如果在小程序下，导航栏宽度为胶囊的左边到屏幕左边的距离\n\t\t\t\tstyle.height = this.navbarHeight + 'px';\n\t\t\t\t// // 如果是各家小程序，导航栏内部的宽度需要减少右边胶囊的宽度\n\t\t\t\t// #ifdef MP\n\t\t\t\tlet rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left;\n\t\t\t\tstyle.marginRight = rightButtonWidth + 'px';\n\t\t\t\t// #endif\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\t// 整个导航栏的样式\n\t\t\tnavbarStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\tstyle.zIndex = this.zIndex ? this.zIndex : this.$u.zIndex.navbar;\n\t\t\t\t// 合并用户传递的背景色对象\n\t\t\t\tObject.assign(style, this.background);\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\t// 导航中间的标题的样式\n\t\t\ttitleStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\t// #ifndef MP\n\t\t\t\tstyle.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';\n\t\t\t\tstyle.right = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP\n\t\t\t\t// 此处是为了让标题显示区域即使在小程序有右侧胶囊的情况下也能处于屏幕的中间，是通过绝对定位实现的\n\t\t\t\tlet rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left;\n\t\t\t\tstyle.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';\n\t\t\t\tstyle.right = rightButtonWidth - (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + rightButtonWidth +\n\t\t\t\t\t'px';\n\t\t\t\t// #endif\n\t\t\t\tstyle.width = uni.upx2px(this.titleWidth) + 'px';\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\t// 转换字符数值为真正的数值\n\t\t\tnavbarHeight() {\n\t\t\t\t// #ifdef APP-PLUS || H5\n\t\t\t\treturn this.height ? this.height : 44;\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP\n\t\t\t\t// 小程序特别处理，让导航栏高度 = 胶囊高度 + 两倍胶囊顶部与状态栏底部的距离之差(相当于同时获得了导航栏底部与胶囊底部的距离)\n\t\t\t\t// 此方法有缺陷，暂不用(会导致少了几个px)，采用直接固定值的方式\n\t\t\t\t// return menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;//导航高度\n\t\t\t\tlet height = systemInfo.platform == 'ios' ? 44 : 48;\n\t\t\t\treturn this.height ? this.height : height;\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tcreated() {},\n\t\tmethods: {\n\t\t\tgoBack() {\n\t\t\t\t// 如果自定义了点击返回按钮的函数，则执行，否则执行返回逻辑\n\t\t\t\tif (typeof this.customBack === 'function') {\n\t\t\t\t\t// 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this\n\t\t\t\t\t// 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文\n\t\t\t\t\tthis.customBack.bind(this.$u.$parent.call(this))();\n\t\t\t\t} else {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t@import \"../../libs/css/style.components.scss\";\n\n\t.u-navbar {\n\t\twidth: 100%;\n\t}\n\n\t.u-navbar-fixed {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\ttop: 0;\n\t\tz-index: 991;\n\t}\n\n\t.u-status-bar {\n\t\twidth: 100%;\n\t}\n\n\t.u-navbar-inner {\n\t\t@include vue-flex;\n\t\tjustify-content: space-between;\n\t\tposition: relative;\n\t\talign-items: center;\n\t}\n\n\t.u-back-wrap {\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tflex: 1;\n\t\tflex-grow: 0;\n\t\tpadding: 14rpx 14rpx 14rpx 24rpx;\n\t}\n\n\t.u-back-text {\n\t\tpadding-left: 4rpx;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.u-navbar-content-title {\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex: 1;\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 60rpx;\n\t\ttext-align: center;\n\t\tflex-shrink: 0;\n\t}\n\n\t.u-navbar-centent-slot {\n\t\tflex: 1;\n\t}\n\n\t.u-title {\n\t\tline-height: 60rpx;\n\t\tfont-size: 32rpx;\n\t\tflex: 1;\n\t}\n\n\t.u-navbar-right {\n\t\tflex: 1;\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-end;\n\t}\n\n\t.u-slot-content {\n\t\tflex: 1;\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=style&index=0&id=1194bf80&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=style&index=0&id=1194bf80&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627515\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}