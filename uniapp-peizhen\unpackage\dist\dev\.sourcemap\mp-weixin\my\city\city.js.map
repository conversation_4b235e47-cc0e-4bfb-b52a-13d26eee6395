{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/city/city.vue?74a7", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/city/city.vue?65b9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/city/city.vue?37eb", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/city/city.vue?0ca5", "uni-app:///my/city/city.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/city/city.vue?a446", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/city/city.vue?a41e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "latitude", "longitude", "city", "scrollTop", "indexList", "onLoad", "methods", "cityClick", "uni", "icon", "title", "mask", "setTimeout", "success", "beforePage", "getCityList", "scroll", "getMap", "type", "that", "fail", "console", "selectCity"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiCvvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,2GACA;IAEA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAH;MACAI;QACA;QACA;QACAJ;UACAK;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAT;QACAU;QAAA;QACAL;UACA;UACAM;UACAA;UACAX;UACAA;;UAGA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAW;QAkBA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACAD;UACA;UACAb;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChJA;AAAA;AAAA;AAAA;AAAs3C,CAAgB,2tCAAG,EAAC,C;;;;;;;;;;;ACA14C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/city/city.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/city/city.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./city.vue?vue&type=template&id=becd390c&\"\nvar renderjs\nimport script from \"./city.vue?vue&type=script&lang=js&\"\nexport * from \"./city.vue?vue&type=script&lang=js&\"\nimport style0 from \"./city.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/city/city.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city.vue?vue&type=template&id=becd390c&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uIndexList: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-index-list/u-index-list\" */ \"@/uview-ui/components/u-index-list/u-index-list.vue\"\n      )\n    },\n    uIndexAnchor: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-index-anchor/u-index-anchor\" */ \"@/uview-ui/components/u-index-anchor/u-index-anchor.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- 定位城市 -->\r\n\t\t<view class=\"city flex justify-center\">\r\n\t\t\t<view class=\"city-box flex justify-between align-center\">\r\n\t\t\t\t<view class=\"\" @click=\"cityClick(city)\">\r\n\t\t\t\t\t{{city}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"\" @click=\"getMap\">\r\n\t\t\t\t\t<u-icon name=\"map\" color=\"#000000\" size=\"26\" style=\"margin-right: 10rpx;\"></u-icon>重新定位\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 城市列表选择 -->\r\n\t\t<view class=\"cityList flex justify-center\">\r\n\t\t\t<view class=\"cityList-box\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\" style=\"width: 100%;height: 100%;\" @scroll=\"scroll\">\r\n\t\t\t\t\t<u-index-list :scrollTop=\"scrollTop\" offset-top=\"200\">\r\n\t\t\t\t\t\t<view v-for=\"(item, index) in indexList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<u-index-anchor :index=\"item.letter\" />\r\n\t\t\t\t\t\t\t<view class=\"list-cell\" v-for=\"(ite,ind) in item.city\" :key=\"ind\" @click=\"cityClick(ite)\">\r\n\t\t\t\t\t\t\t\t{{ite}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</u-index-list>\r\n\t\t\t\t</scroll-view>\r\n\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlatitude: '',\r\n\t\t\t\tlongitude: '',\r\n\t\t\t\tcity: '',\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\tindexList: [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\",\r\n\t\t\t\t\t\"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\"\r\n\t\t\t\t],\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t//获取定位\r\n\t\t\tthis.getMap()\r\n\t\t\t//获取城市列表\r\n\t\t\tthis.getCityList()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//修改当前城市\r\n\t\t\tcityClick(city) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '修改成功',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t})\r\n\t\t\t\tuni.setStorageSync('city', city)\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tlet pages = getCurrentPages(); // 当前页面\r\n\t\t\t\t\tlet beforePage = pages[pages.length - 2]; // 上一页\r\n\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\tbeforePage.onShow(); // 执行上一页的onShow方法\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\t//获取城市数据\r\n\t\t\tgetCityList() {\r\n\t\t\t\tthis.$Request.get('/app/hospital/getCityList').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.indexList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//同步滑动的高度\r\n\t\t\tscroll(e) {\r\n\t\t\t\tthis.scrollTop = e.detail.scrollTop;\r\n\t\t\t},\r\n\t\t\t//获取定位\r\n\t\t\tgetMap() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02', //wgs84  gcj02\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t// console.log(res, '地理位置')\r\n\t\t\t\t\t\tthat.latitude = res.latitude\r\n\t\t\t\t\t\tthat.longitude = res.longitude\r\n\t\t\t\t\t\tuni.setStorageSync('latitude', res.latitude)\r\n\t\t\t\t\t\tuni.setStorageSync('longitude', res.longitude)\r\n\r\n\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t\t// \turl: 'https://apis.map.qq.com/ws/geocoder/v1/?location=' + that.latitude +\r\n\t\t\t\t\t\t// \t\t',' + that.longitude + '&key=O2PBZ-6J3CX-GWK44-TXGQL-QKC2T-2UBP6',\r\n\t\t\t\t\t\t// \tsuccess(re) {\r\n\t\t\t\t\t\t// \t\tconsole.log(re)\r\n\t\t\t\t\t\t// \t\tif (re.statusCode === 200) {\r\n\t\t\t\t\t\t// \t\t\tlet citydata = re.data.result.address_component.city\r\n\t\t\t\t\t\t// \t\t\t// console.log(\"获取城市名称成功\", citydata)\r\n\t\t\t\t\t\t// \t\t\tthat.city = citydata ? citydata : '未知'\r\n\t\t\t\t\t\t// \t\t\tuni.setStorageSync('city', citydata)\r\n\t\t\t\t\t\t// \t\t} else {\r\n\t\t\t\t\t\t// \t\t\tconsole.log(\"获取信息失败，请重试！\")\r\n\t\t\t\t\t\t// \t\t}\r\n\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t// });\r\n\t\t\t\t\t\tthat.selectCity(that.longitude, that.latitude);\r\n\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\tconsole.log(res, '*****************')\r\n\t\t\t\t\t\tif (res.address) {\r\n\t\t\t\t\t\t\tthat.city = res.address.city\r\n\t\t\t\t\t\t\tuni.setStorageSync('city', res.address.city)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.selectCity(that.longitude, that.latitude);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\tthat.selectCity(that.longitude, that.latitude);\r\n\t\t\t\t\t\t// #endif\r\n\r\n\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tconsole.log(err, '获取地址失败')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tselectCity(longitude, latitude) {\r\n\t\t\t\tthis.$Request.get('/app/Login/selectCity?lat=' + latitude + '&lng=' + longitude).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tconsole.log(res, '+++++++++++++++++++')\r\n\t\t\t\t\t\tthis.city = res.data.result ? res.data.result.ad_info.city : '未知'\r\n\t\t\t\t\t\tuni.setStorageSync('city', this.city)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #F7F7F7;\r\n\t\toverflow-y: hidden;\r\n\t}\r\n\r\n\t.city {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tposition: fixed;\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 108rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef H5 */\r\n\t\ttop: 20rpx;\r\n\r\n\t\t/* #endif */\r\n\t\t.city-box {\r\n\t\t\twidth: 710rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.list-cell {\r\n\t\tdisplay: flex;\r\n\t\tbox-sizing: border-box;\r\n\t\twidth: 100%;\r\n\t\tpadding: 10px 24rpx;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #323233;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 24px;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.cityList {\r\n\t\tposition: fixed;\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 208rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef H5 */\r\n\t\ttop: 100rpx;\r\n\t\t/* #endif */\r\n\t\twidth: 100%;\r\n\t\theight: calc(100vh - 228rpx);\r\n\t\tbackground-color: #ffffff;\r\n\r\n\t\t.cityList-box {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627310\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}