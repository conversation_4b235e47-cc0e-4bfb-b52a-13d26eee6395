@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.models-container.data-v-1516d4f0 {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.header.data-v-1516d4f0 {
  background: #fff;
  padding: 40rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}
.title.data-v-1516d4f0 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}
.subtitle.data-v-1516d4f0 {
  font-size: 28rpx;
  color: #666;
}
.models-list.data-v-1516d4f0 {
  padding: 20rpx;
}
.model-item.data-v-1516d4f0 {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.model-item.active.data-v-1516d4f0 {
  border: 2rpx solid #0175FE;
  box-shadow: 0 4rpx 16rpx rgba(1, 117, 254, 0.2);
}
.model-info.data-v-1516d4f0 {
  flex: 1;
}
.model-header.data-v-1516d4f0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.model-name.data-v-1516d4f0 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.model-description.data-v-1516d4f0 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}
.model-params.data-v-1516d4f0 {
  display: flex;
  gap: 30rpx;
}
.param-item.data-v-1516d4f0 {
  font-size: 24rpx;
  color: #999;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.model-select.data-v-1516d4f0 {
  margin-left: 20rpx;
}
.bottom-actions.data-v-1516d4f0 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  z-index: 100;
}
.loading-overlay.data-v-1516d4f0 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.loading-content.data-v-1516d4f0 {
  background: #fff;
  padding: 40rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  font-size: 28rpx;
  color: #666;
}

