{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/index.vue?70b8", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/index.vue?3fd4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/index.vue?a0f4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/index.vue?96f4", "uni-app:///pages/index/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/index.vue?077d", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/index.vue?94b3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "onShareAppMessage", "path", "invitationCode", "title", "imageUrl", "onShareTimeline", "data", "popushongbao", "status", "iconType", "loadText", "loadmore", "loading", "nomore", "tui<PERSON><PERSON>", "tuiImage", "background", "backgroundColor", "swiperList", "gridData", "classType", "myId", "page", "limit", "hospitalList", "city", "first", "couponNum", "couponId", "newYouhui", "pages", "showModal", "arr", "privacyContractName", "onLoad", "success", "console", "fail", "complete", "that", "uni", "type", "onShow", "methods", "goAiChat", "url", "content", "goApiTest", "openPrivacyContract", "exitMiniProgram", "handleAgreePrivacyAuthorization", "GetQuan", "num", "icon", "setTimeout", "<PERSON><PERSON><PERSON>", "selectCity", "getClassfly", "gethospitalList", "getBannerList", "classify", "get<PERSON><PERSON>", "openMsg", "withSubscriptions", "confirmText", "cancelText", "tmplIds", "goSelectCity", "goNavs", "goNavSwiper", "appId", "envVersion", "goNav", "selectKeFu", "phoneNumber", "extInfo", "corpId", "bindTo", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC2HxvB;EACAC;IACAC;EACA;EACAC;IACA;MACAC,8CACAC;MAAA;MACAC;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;MACAJ;MAAA;MACAE;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAb;MACAc;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAEA;MACAzC;QACA0C;UACAC;UACA;YACA;YACAA;YACA;UACA;QACA;QACAC;QACAC;MACA;IACA;MACA;IAAA;IAIA;IACAC;IACAA;IACAA;IACAC;MACAC;MAAA;MACAN;QACAC;QACAG;QACAA;QACAC;QACAA;;QAGA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAD;MAsBA;MACAF;QACAD;MACA;IACA;IACAG;IACA;MACAA;QAAA;QACA;UACA;YACA;YACAA;YACAA;YACA;UACA;QACA;MACA;;MACA;QAAA;QACA;UACAA;YACA;cACA;gBACAA;gBACAA;cACA;gBACAA;cACA;YACA;UACA;QACA;MACA;IAEA;;IAEA;IACA;IACA;MACAA;IACA;IAEA;MACA;MACAA;IACA;IAEA;MACAA;MACAA;QAAA;QACA;UACAA;QACA;MACA;MACAA;QAAA;QACA;UACAA;QACA;MACA;IACA;EACA;EAEAG;IACA;IACA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACAH;QAAA;QACA;UACA;YACAA;UACA;QACA;MACA;MACA;QACA;MACA;IACA;EAGA;EACAI;IACA;IACAC;MACA;MACA;QACAJ;UACAK;QACA;MACA;QACAL;UACArC;UACA2C;UACAX;YACA;cACAK;gBACAK;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAE;MACAP;QACAK;MACA;IACA;IACA;IACAG;MACA;MACAvD;QACA4C;UACAE;QACA;MACA;IACA;IACA;IACAU;MACA;MACAxD;IACA;IACA;IACAyD;MACA;IACA;IACAC;MAAA;MACA;QACAvB;QACAwB;MACA;MACA;QACA;UACA;UACA;UACAZ;YACArC;YACAkD;UACA;UACAC;YACAd;cACAK;YACA;UACA;QACA;MACA;IACA;IACAU;MACAf;QACAK;MACA;IACA;IACAW;MAAA;MACA;QACA;UACA;UACAhB;UACA;UACA;QACA;MACA;IACA;IACA;IACAiB;MAAA;MACA;QACAhB;MACA;MACA;QACA;UACA;QACA;MAEA;IACA;IACA;IACAiB;MAAA;MACA;QACApC;QACAC;QACAE;MACA;MACA;QACA;UACA;UACA;YACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;;QACAe;MAEA;IACA;IACA;IACAmB;MAAA;MACA;QACAC;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAD;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAE;MACA1B;MACA;MACAI;QACAuB;QAAA;QACA5B;UACAC;UACA;UACA;YACAI;YACAA;cAAA;cACAL;gBACAC;cACA;YACA;UACA;YAAA;YACAA;YACAI;YACAA;cACArC;cACA2C;cACAkB;cACAC;cACA9B;gBACA;kBACA1C;oBACAyE;oBACA/B;sBACAC,gCACA;sBACA;sBACA;wBACAA;wBACA;sBACA;oBACA;;oBACAC;sBACAD;oBACA;kBACA;kBACA;kBACAA;kBACAG;gBACA;kBACAH;kBACA;kBACAG;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACA4B;MACA3B;QACAK;MACA;IACA;IACA;IACAuB;MACA;QACA5B;UACAK;QACA;QACA;MACA;MACAT;MACA;QACAA;QACA3C;UACAyE;UACA/B;YACAC;YACA;YACA;cACAA;YACA;UACA;UACAC;YACAD;UACA;QACA;MACA;MACA;QACAI;UACAK;QACA;MACA;QACAL;UACArC;UACA2C;UACAX;YACA;cACAC;cACAI;gBACAK;cACA;YACA;cACAT;YACA;UACA;QACA;MACA;IAEA;IACA;IACAiC;MACA;QACAjC;QACA3C;UACAyE;UACA/B;YACAC;YACA;YACA;cACAA;YACA;UACA;UACAC;YACAD;UACA;QACA;MACA;MACA;QACA;QACA;QACAI;UACA8B;UACArE;UACAsE;UACApC;YACA;YACAC;UACA;QACA;QACA;MACA;MACA;MACA;QACAI;UACAK;QACA;MACA;QAEAL;UACAK;QACA;MAKA;QACA;MACA;IACA;IACA2B;MACApC;MACA;QACAA;QACA3C;UACAyE;UACA/B;YACAC;YACA;YACA;cACAA;YACA;UACA;UACAC;YACAD;UACA;QACA;MACA;MACA;MACA;QACA;UACAI;YACAK;UACA;QACA;UACAL;YACArC;YACA2C;YACAX;cACA;gBACAC;gBACAI;kBACAK;gBACA;cACA;gBACAT;cACA;YACA;UACA;QACA;MACA;QAEAI;UACAK;QACA;MAKA;QACA;MACA;IACA;IACA;IACA4B;MACA;MACA;QAAA;QACAjC;UACAkC;QACA;MACA;QAAA;QACA;QAEAjF;UACAkF;YACA9B;UACA;UACA+B;UACAzC;QACA;MAUA;QAAA;QACAK;UACAK;QACA;MACA;IACA;IACAgC;MACA;QACAzC;QACA3C;UACAyE;UACA/B;YACAC;YACA;YACA;cACAA;YACA;UACA;UACAC;YACAD;UACA;QACA;MACA;MACA;QACA;QACA;QACAI;UACA8B;UACArE;UACAsE;UACApC;YACA;YACAC;UACA;QACA;QACA;MACA;MACA;MAEA;QACA;MAEA;QAEAI;UACAK;QACA;MAKA;QACA;UACAL;YACAK;UACA;QACA;UACAL;YACArC;YACA2C;YACAX;cACA;gBACAC;gBACAI;kBACAK;gBACA;cACA;gBACAT;cACA;YACA;UACA;QACA;MAEA;IACA;EACA;EACA0C;IACA;MACA;MACA;IACA;EAEA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9xBA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-navbar/u-navbar\" */ \"@/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uGrid: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-grid/u-grid\" */ \"@/uview-ui/components/u-grid/u-grid.vue\"\n      )\n    },\n    uGridItem: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-grid-item/u-grid-item\" */ \"@/uview-ui/components/u-grid-item/u-grid-item.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hospitalList.length\n  var g1 = _vm.hospitalList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<u-navbar :is-back=\"false\" title=\"首页\" :border-bottom=\"false\" title-color=\"#ffffff\" :background=\"background\">\r\n\t\t\t<view class=\"slot-wrap flex align-center\" @click=\"goSelectCity\">\r\n\t\t\t\t<u-icon name=\"map-fill\" style=\"margin-left: 20rpx;\" color=\"#ffffff\" size=\"36\"></u-icon>\r\n\t\t\t\t<text style=\"margin-left: 10rpx;color: #F7F7F7;font-size: 24rpx;\">{{city}}</text>\r\n\t\t\t</view>\r\n\t\t</u-navbar>\r\n\t\t<!-- 轮播图 -->\r\n\t\t<view class=\"swiper flex justify-center align-end\">\r\n\t\t\t<view class=\"swiper-box\">\r\n\t\t\t\t<swiper :indicator-dots=\"false\" :autoplay=\"true\" style=\"width: 100%;height: 100%;border-radius: 16rpx;\"\r\n\t\t\t\t\t:interval=\"3000\" :duration=\"1000\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item,index) in swiperList\" :key=\"index\"\r\n\t\t\t\t\t\t@click=\"goNavSwiper(item.url,item.name,item.describes)\">\r\n\t\t\t\t\t\t<image :src=\"item.imageUrl\" style=\"width: 100%;height: 100%;border-radius: 16rpx;\" mode=\"\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 金刚区tabs -->\r\n\t\t<!-- <view class=\"tabs flex justify-center\">\r\n\t\t\t<view class=\"tabs-box\">\r\n\t\t\t\t<view class=\"tabs-box-item flex justify-between align-center\">\r\n\t\t\t\t\t<view class=\"tabs-box-item-c flex justify-center flex-wrap\" @click=\"gotoorder()\"\r\n\t\t\t\t\t\tv-for=\"(item,index) in tabs\" :key=\"index\">\r\n\t\t\t\t\t\t<image :src=\"item.img\" style=\"width: 84rpx;height: 84rpx;border-radius: 50%;\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"tabs-box-item-c-t\">\r\n\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<view class=\"margin\" style=\"border-radius: 24upx;\">\r\n\t\t\t<u-grid :col=\"5\" :border=\"false\">\r\n\t\t\t\t<u-grid-item bg-color=\"#f5f5f5\" v-for=\"(item,index) in gridData\" :key='index'\r\n\t\t\t\t\t@click=\"bindTo(item.url,item.name,item.describes)\">\r\n\t\t\t\t\t<image :src=\"item.imageUrl\" style=\"width: 92rpx;height: 92rpx;border-radius: 92rpx;\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view style=\"margin-top: 14rpx;\">{{item.name}}</view>\r\n\t\t\t\t</u-grid-item>\r\n\t\t\t\t<!-- AI助手入口 -->\r\n\t\t\t\t<u-grid-item bg-color=\"#f5f5f5\" @click=\"goAiChat()\">\r\n\t\t\t\t\t<image src=\"../../static/images/my/ai-chat.png\" style=\"width: 92rpx;height: 92rpx;border-radius: 92rpx;\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view style=\"margin-top: 14rpx;\">AI助手</view>\r\n\t\t\t\t</u-grid-item>\r\n\t\t\t\t<!-- API测试入口 -->\r\n\t\t\t\t<u-grid-item bg-color=\"#f5f5f5\" @click=\"goApiTest()\">\r\n\t\t\t\t\t<image src=\"../../static/images/my/ai-chat.png\" style=\"width: 92rpx;height: 92rpx;border-radius: 92rpx;\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view style=\"margin-top: 14rpx;\">API测试</view>\r\n\t\t\t\t</u-grid-item>\r\n\t\t\t</u-grid>\r\n\t\t</view>\r\n\t\t<!-- 分类 -->\r\n\t\t<view class=\"types flex justify-center\">\r\n\t\t\t<view class=\"types-box flex flex-wrap justify-between \">\r\n\t\t\t\t<view v-for=\"(item,index) in classType\" :key=\"index\" class=\"types-box-l margin-bottom-sm\"\r\n\t\t\t\t\t@click=\"goNav(item.tags,item.modularId)\">\r\n\t\t\t\t\t<image :src=\"item.image\" mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 医院列表 -->\r\n\t\t<view class=\"hospital flex justify-center\" v-if=\"hospitalList.length>0\">\r\n\t\t\t<view class=\"hospital-box\">\r\n\t\t\t\t<view class=\"hospital-box-item flex justify-center\"\r\n\t\t\t\t\t@click=\"goNavs('/my/yxpeizhen/yxpeizhen?hospitalName='+item.hospitalName+'&hospitalId='+item.hospitalId,2)\"\r\n\t\t\t\t\tv-for=\"(item,index) in hospitalList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"hospital-box-item-c flex align-center justify-between\">\r\n\t\t\t\t\t\t<view class=\"hospital-box-item-c-l\">\r\n\t\t\t\t\t\t\t<image :src=\"item.hospitalImg?item.hospitalImg:'../../static/logo.png'\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"hospital-box-item-c-r flex flex-wrap align-center\">\r\n\t\t\t\t\t\t\t<view class=\"hospital-box-item-c-r-title\">{{item.hospitalName}}</view>\r\n\t\t\t\t\t\t\t<view class=\"hospital-box-item-c-r-info flex align-center\">\r\n\t\t\t\t\t\t\t\t<text>{{item.hospitalLevel}}</text>|<span>{{item.hospitalType}}</span>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"hospital-box-item-c-r-jj\">{{item.hospitalDetails}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"tips\">\r\n\t\t\t没有我要去的医院？<text @click=\"goNavs('/my/customServer/customServer')\">点这儿里</text>\r\n\t\t</view>\r\n\t\t<!-- 新人红包 -->\r\n\t\t<u-popup v-model=\"popushongbao\" mode=\"center\">\r\n\t\t\t<view>\r\n\t\t\t\t<image @tap=\"GetQuan\"\r\n\t\t\t\t\tsrc=\"https://songshui.xianmaxiong.com/file/uploadPath/2022/11/28/737f1212e0e0c39fbaccc5b9091a2d73.png\"\r\n\t\t\t\t\tstyle=\"width: 564upx;height:618upx \"></image>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t<!-- <u-loadmore v-if=\"hospitalList.length>0\" :status=\"status\" :icon-type=\"iconType\" :load-text=\"loadText\" /> -->\r\n\t\t<empty v-if=\"hospitalList.length<=0\" />\r\n\t\t<!-- 悬浮客服按钮 -->\r\n\t\t<image src=\"../../static/images/kefu.png\" @click=\"selectKeFu()\" class=\"kefu\" mode=\"\"></image>\r\n\t\t<uni-popup ref=\"popusAuthorization\" type=\"center\" :maskClick=\"false\">\r\n\t\t\t<view class=\"contentview\">\r\n\t\t\t\t<view class=\"title\">隐私保护指引</view>\r\n\t\t\t\t<view class=\"des\" @click=\"openPrivacyContract\">\r\n\t\t\t\t\t在使用当前小程序服务之前，请仔细阅读<text\r\n\t\t\t\t\t\tstyle=\"color: #5074FF;\">{{privacyContractName}}</text>。如你同意{{privacyContractName}}，请点击“同意”开始使用。\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btns\">\r\n\t\t\t\t\t<button class=\"item reject\" @click=\"exitMiniProgram\">拒绝</button>\r\n\t\t\t\t\t<button id=\"agree-btn\" class=\"item agree\" open-type=\"agreePrivacyAuthorization\"\r\n\t\t\t\t\t\t@agreeprivacyauthorization=\"handleAgreePrivacyAuthorization\">同意</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport empty from '@/components/empty.vue'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tempty,\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/pages/index/index?invitation=' + this\r\n\t\t\t\t\t.invitationCode, //这是为了传参   onload(data){let id=data.id;} \r\n\t\t\t\ttitle: this.tuiName,\r\n\t\t\t\timageUrl: this.tuiImage\r\n\t\t\t}\r\n\t\t},\r\n\t\t/*\r\n\t\t * uniapp微信小程序分享页面到微信朋友圈\r\n\t\t */\r\n\t\tonShareTimeline(res) {\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/pages/index/index?invitation=' + this.invitationCode, //这是为了传参   onload(data){let id=data.id;} \r\n\t\t\t\ttitle: this.tuiName,\r\n\t\t\t\timageUrl: this.tuiImage\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpopushongbao: false,\r\n\t\t\t\tstatus: 'loadmore',\r\n\t\t\t\ticonType: 'flower',\r\n\t\t\t\tloadText: {\r\n\t\t\t\t\tloadmore: '轻轻上拉',\r\n\t\t\t\t\tloading: '努力加载中',\r\n\t\t\t\t\tnomore: '实在没有了'\r\n\t\t\t\t},\r\n\t\t\t\ttuiName: '',\r\n\t\t\t\ttuiImage: '',\r\n\t\t\t\tinvitationCode: '',\r\n\t\t\t\tbackground: {\r\n\t\t\t\t\tbackgroundColor: '#468EF8',\r\n\t\t\t\t},\r\n\t\t\t\tswiperList: [],\r\n\t\t\t\tgridData: [],\r\n\t\t\t\tclassType: [],\r\n\t\t\t\tmyId: '',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\thospitalList: [],\r\n\t\t\t\tcity: '请选择城市',\r\n\r\n\t\t\t\tfirst: true,\r\n\t\t\t\tcouponNum: '',\r\n\t\t\t\tcouponId: '',\r\n\t\t\t\tnewYouhui: 0,\r\n\t\t\t\tpages: 1,\r\n\t\t\t\tshowModal: true,\r\n\t\t\t\tarr: [],\r\n\t\t\t\tprivacyContractName: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\ttry {\r\n\t\t\t\twx.getPrivacySetting({\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tconsole.log(\"是否需要授权：\", res.needAuthorization, \"隐私协议的名称为：\", res.privacyContractName)\r\n\t\t\t\t\t\tif (res.needAuthorization) {\r\n\t\t\t\t\t\t\tthis.privacyContractName = res.privacyContractName;\r\n\t\t\t\t\t\t\tconsole.log(111111)\r\n\t\t\t\t\t\t\tthis.$refs.popusAuthorization.open();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {},\r\n\t\t\t\t\tcomplete: () => {},\r\n\t\t\t\t})\r\n\t\t\t} catch (e) {\r\n\t\t\t\t//TODO handle the exception\r\n\t\t\t}\r\n\r\n\t\t\t// #endif\r\n\t\t\tlet that = this\r\n\t\t\tthat.getClassfly()\r\n\t\t\tthat.getBannerList()\r\n\t\t\tthat.getGrid()\r\n\t\t\tuni.getLocation({\r\n\t\t\t\ttype: 'gcj02', //wgs84  gcj02\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tconsole.log(res, '地理位置')\r\n\t\t\t\t\tthat.latitude = res.latitude\r\n\t\t\t\t\tthat.longitude = res.longitude\r\n\t\t\t\t\tuni.setStorageSync('latitude', res.latitude)\r\n\t\t\t\t\tuni.setStorageSync('longitude', res.longitude)\r\n\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t// \turl: 'https://apis.map.qq.com/ws/geocoder/v1/?location=' + that.latitude +\r\n\t\t\t\t\t// \t\t',' + that.longitude + '&key=O2PBZ-6J3CX-GWK44-TXGQL-QKC2T-2UBP6',\r\n\t\t\t\t\t// \tsuccess(re) {\r\n\t\t\t\t\t// \t\tconsole.log(re)\r\n\t\t\t\t\t// \t\tif (re.statusCode === 200) {\r\n\t\t\t\t\t// \t\t\tlet citydata = re.data.result.address_component.city\r\n\t\t\t\t\t// \t\t\tconsole.log(\"获取城市名称成功\", citydata)\r\n\t\t\t\t\t// \t\t\tthat.city = citydata ? citydata : '未知'\r\n\t\t\t\t\t// \t\t\tuni.setStorageSync('city', citydata)\r\n\t\t\t\t\t// \t\t\tthat.page = 1\r\n\t\t\t\t\t// \t\t\tthat.gethospitalList()\r\n\t\t\t\t\t// \t\t} else {\r\n\t\t\t\t\t// \t\t\tconsole.log(\"获取信息失败，请重试！\")\r\n\t\t\t\t\t// \t\t}\r\n\t\t\t\t\t// \t}\r\n\t\t\t\t\t// });\r\n\t\t\t\t\tthat.selectCity(that.longitude, that.latitude);\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\r\n\t\t\t\t\tif (res.address) {\r\n\t\t\t\t\t\tthat.city = res.address.city\r\n\t\t\t\t\t\tuni.setStorageSync('city', res.address.city)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.selectCity(that.longitude, that.latitude);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthat.page = 1\r\n\t\t\t\t\tthat.gethospitalList()\r\n\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthat.selectCity(that.longitude, that.latitude);\r\n\t\t\t\t\t// #endif\r\n\r\n\r\n\t\t\t\t},\r\n\t\t\t\tfail: function() {\r\n\t\t\t\t\tconsole.log('获取地址失败')\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tthat.myId = uni.getStorageSync('userId')\r\n\t\t\tif (that.first && that.myId) {\r\n\t\t\t\tthat.$Request.getT('/app/common/type/309').then(res => { //新人领取优惠劵Id\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\t\tlet coupon = res.data.value.split(',')\r\n\t\t\t\t\t\t\tthat.couponId = coupon[0]\r\n\t\t\t\t\t\t\tthat.couponNum = coupon[1]\r\n\t\t\t\t\t\t\t// console.log(that.couponId,that.couponNum)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.$Request.get('/app/common/type/310').then(res => { //是否开启新人优惠券配置  0不开启 1开启\r\n\t\t\t\t\tif (res.code == 0 && res.data.value == 1) {\r\n\t\t\t\t\t\tthat.$Request.get(\"/app/user/selectUserById\").then(res => {\r\n\t\t\t\t\t\t\tif (res.code == 0 && res.data) {\r\n\t\t\t\t\t\t\t\tif (res.data.isNewPeople == 1) {\r\n\t\t\t\t\t\t\t\t\tthat.popushongbao = true\r\n\t\t\t\t\t\t\t\t\tthat.first = false\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthat.first = true\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t}\r\n\r\n\t\t\t// 分享\r\n\t\t\t// 获取邀请码保存到本地\r\n\t\t\tif (e.invitation) {\r\n\t\t\t\tthat.$queue.setData('inviterCode', e.invitation);\r\n\t\t\t}\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (e.scene) {\r\n\t\t\t\tconst scene = decodeURIComponent(e.scene);\r\n\t\t\t\tthat.$queue.setData('inviterCode', scene.split(',')[0]);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tif (this.myId) {\r\n\t\t\t\tthat.invitationCode = uni.getStorageSync('invitationCode')\r\n\t\t\t\tthat.$Request.getT('/app/common/type/276').then(res => { //分享标题 276\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.tuiName = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthat.$Request.getT('/app/common/type/277').then(res => { //分享图 277\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.tuiImage = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonShow() {\r\n\t\t\tlet that = this\r\n\t\t\tthis.city = uni.getStorageSync('city')\r\n\t\t\tif (this.city) {\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis.gethospitalList()\r\n\t\t\t}\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t//订阅\r\n\t\t\tif (that.myId) {\r\n\t\t\t\tthat.$Request.getT('/app/common/type/308').then(res => { //订单变更通知 308\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tif (res.data && res.data.value) {\r\n\t\t\t\t\t\t\tthat.arr.push(res.data.value)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tif (this.showModal) {\r\n\t\t\t\t\tthis.openMsg()\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 跳转AI对话\r\n\t\t\tgoAiChat() {\r\n\t\t\t\tlet userId = uni.getStorageSync('userId');\r\n\t\t\t\tif (userId) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/ai/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跳转API测试页面\r\n\t\t\tgoApiTest() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/ai/api-test'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 打开隐私协议页面\r\n\t\t\topenPrivacyContract() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\twx.openPrivacyContract({\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tthat.$queue.showToast('遇到错误无法打开！');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 拒绝隐私协议\r\n\t\t\texitMiniProgram() {\r\n\t\t\t\t// 直接退出小程序\r\n\t\t\t\twx.exitMiniProgram()\r\n\t\t\t},\r\n\t\t\t// 同意隐私协议\r\n\t\t\thandleAgreePrivacyAuthorization() {\r\n\t\t\t\tthis.$refs.popusAuthorization.close();\r\n\t\t\t},\r\n\t\t\tGetQuan() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tcouponId: this.couponId,\r\n\t\t\t\t\tnum: this.couponNum\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get('/app/couponUser/receiveEnvelope', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.first = false\r\n\t\t\t\t\t\tthis.popushongbao = false\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '领取成功',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(d => {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/my/youhuijuan/myList'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgotoorder() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/index/game/orderDet'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tselectCity(longitude, latitude) {\r\n\t\t\t\tthis.$Request.get('/app/Login/selectCity?lat=' + latitude + '&lng=' + longitude).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.city = res.data.result ? res.data.result.ad_info.city : '未知'\r\n\t\t\t\t\t\tuni.setStorageSync('city', this.city)\r\n\t\t\t\t\t\tthis.page = 1\r\n\t\t\t\t\t\tthis.gethospitalList()\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//获取分类\r\n\t\t\tgetClassfly() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: 1\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get('/app/hospitalModular/getModularList', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.classType = res.data.records\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//获取陪诊医院列表\r\n\t\t\tgethospitalList() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tlimit: this.limit,\r\n\t\t\t\t\tcity: this.city\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get('/app/hospital/getHospitalList', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.pages = res.data.pages\r\n\t\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\t\tthis.hospitalList = res.data.records\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.hospitalList = [...this.hospitalList, ...res.data.records]\r\n\t\t\t\t\t\t\t// for (let i = 0; i < this.orderList.length; i++) {\r\n\t\t\t\t\t\t\t// \tthis.orderList[i].gameName = this.orderList[i].gameName.split(\",\");\r\n\t\t\t\t\t\t\t// }\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//获取轮播图\r\n\t\t\tgetBannerList() {\r\n\t\t\t\tthis.$Request.get(\"/app/banner/selectBannerList\", {\r\n\t\t\t\t\tclassify: 1\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.swiperList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 获取金刚区分类\r\n\t\t\tgetGrid() {\r\n\t\t\t\tthis.$Request.get(\"/app/banner/selectBannerList\", {\r\n\t\t\t\t\tclassify: 2\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.gridData = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 开启订阅消息\r\n\t\t\topenMsg() {\r\n\t\t\t\tconsole.log('订阅消息')\r\n\t\t\t\tvar that = this\r\n\t\t\t\tuni.getSetting({\r\n\t\t\t\t\twithSubscriptions: true, //是否获取用户订阅消息的订阅状态，默认false不返回\r\n\t\t\t\t\tsuccess(ret) {\r\n\t\t\t\t\t\tconsole.log(ret.subscriptionsSetting, '------------------')\r\n\t\t\t\t\t\t// if (ret.subscriptionsSetting.itemSettings && Object.keys(ret.subscriptionsSetting.itemSettings).length == 2) {\r\n\t\t\t\t\t\tif (ret.subscriptionsSetting.itemSettings) {\r\n\t\t\t\t\t\t\tuni.setStorageSync('sendMsg', true)\r\n\t\t\t\t\t\t\tuni.openSetting({ // 打开设置页 \r\n\t\t\t\t\t\t\t\tsuccess(rea) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(rea.authSetting)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息\r\n\t\t\t\t\t\t\tconsole.log(99999)\r\n\t\t\t\t\t\t\tuni.setStorageSync('sendMsg', false)\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\tcontent: '为了更好的体验,请绑定消息推送',\r\n\t\t\t\t\t\t\t\tconfirmText: '确定',\r\n\t\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\twx.requestSubscribeMessage({\r\n\t\t\t\t\t\t\t\t\t\t\ttmplIds: that.arr,\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess(re) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(JSON.stringify(re),\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'++++++++++++++')\r\n\t\t\t\t\t\t\t\t\t\t\t\tvar datas = JSON.stringify(re);\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (datas.indexOf(\"accept\") != -1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(re)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// uni.setStorageSync('sendMsg', true)\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t// uni.setStorageSync('sendMsg', true)\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('确认')\r\n\t\t\t\t\t\t\t\t\t\tthat.showModal = false\r\n\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('取消')\r\n\t\t\t\t\t\t\t\t\t\t// uni.setStorageSync('sendMsg', false)\r\n\t\t\t\t\t\t\t\t\t\tthat.showModal = true\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 选择城市\r\n\t\t\tgoSelectCity() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/city/city'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 跳转\r\n\t\t\tgoNavs(url, type) {\r\n\t\t\t\tif (type == 2) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(url, '1111112333')\r\n\t\t\t\tif (uni.getStorageSync('sendMsg')) {\r\n\t\t\t\t\tconsole.log('授权+1')\r\n\t\t\t\t\twx.requestSubscribeMessage({\r\n\t\t\t\t\t\ttmplIds: this.arr,\r\n\t\t\t\t\t\tsuccess(re) {\r\n\t\t\t\t\t\t\tconsole.log(JSON.stringify(re), 111111111111)\r\n\t\t\t\t\t\t\tvar datas = JSON.stringify(re);\r\n\t\t\t\t\t\t\tif (datas.indexOf(\"accept\") != -1) {\r\n\t\t\t\t\t\t\t\tconsole.log(re)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (uni.getStorageSync('userId')) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\t//轮播图跳转\r\n\t\t\tgoNavSwiper(url, name, describes) {\r\n\t\t\t\tif (uni.getStorageSync('sendMsg')) {\r\n\t\t\t\t\tconsole.log('授权+1')\r\n\t\t\t\t\twx.requestSubscribeMessage({\r\n\t\t\t\t\t\ttmplIds: this.arr,\r\n\t\t\t\t\t\tsuccess(re) {\r\n\t\t\t\t\t\t\tconsole.log(JSON.stringify(re), 111111111111)\r\n\t\t\t\t\t\t\tvar datas = JSON.stringify(re);\r\n\t\t\t\t\t\t\tif (datas.indexOf(\"accept\") != -1) {\r\n\t\t\t\t\t\t\t\tconsole.log(re)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (describes && describes === '跳转小程序') {\r\n\t\t\t\t\tlet appid = url.substring(0, url.indexOf(','))\r\n\t\t\t\t\tlet usls = url.substring(url.indexOf(',') + 1)\r\n\t\t\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\t\t\tappId: appid,\r\n\t\t\t\t\t\tpath: usls,\r\n\t\t\t\t\t\tenvVersion: 'release',\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t// 打开成功\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 判断是否是应用内链接\r\n\t\t\t\tif (url.indexOf('/pages/') !== -1 || url.indexOf('/my/') !== -1 || url.indexOf('/main/') !== -1) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (url.indexOf('http') !== -1) {\r\n\t\t\t\t\t//#ifndef H5\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/webView/webView?url=' + url\r\n\t\t\t\t\t})\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\twindow.location.href = url;\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t} else if (url.indexOf('客服') !== -1) {\r\n\t\t\t\t\tthis.selectKeFu()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoNav(url, modularId) {\r\n\t\t\t\tconsole.log(url, '1111112333')\r\n\t\t\t\tif (uni.getStorageSync('sendMsg')) {\r\n\t\t\t\t\tconsole.log('授权+1')\r\n\t\t\t\t\twx.requestSubscribeMessage({\r\n\t\t\t\t\t\ttmplIds: this.arr,\r\n\t\t\t\t\t\tsuccess(re) {\r\n\t\t\t\t\t\t\tconsole.log(JSON.stringify(re), 111111111111)\r\n\t\t\t\t\t\t\tvar datas = JSON.stringify(re);\r\n\t\t\t\t\t\t\tif (datas.indexOf(\"accept\") != -1) {\r\n\t\t\t\t\t\t\t\tconsole.log(re)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// 判断是否是应用内链接\r\n\t\t\t\tif (url.indexOf('/pages/') !== -1 || url.indexOf('/my/') !== -1 || url.indexOf('/main/') !== -1) {\r\n\t\t\t\t\tif (uni.getStorageSync('userId')) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: url + '?modularId=' + modularId\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (url.indexOf('http') !== -1) {\r\n\t\t\t\t\t//#ifndef H5\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/webView/webView?url=' + url\r\n\t\t\t\t\t})\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\twindow.location.href = url;\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t} else if (url.indexOf('客服') !== -1) {\r\n\t\t\t\t\tthis.selectKeFu()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//跳转客服\r\n\t\t\tselectKeFu() {\r\n\t\t\t\tlet SelKeFu = this.$queue.getData('SelKeFu');\r\n\t\t\t\tif (SelKeFu + '' == '1') { //手机号\r\n\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t\tphoneNumber: uni.getStorageSync('kefuPhone')\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (SelKeFu + '' == '2') { //企业微信\r\n\t\t\t\t\tlet that = this\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\twx.openCustomerServiceChat({\r\n\t\t\t\t\t\textInfo: {\r\n\t\t\t\t\t\t\turl: that.$queue.getData('kefu')\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tcorpId: that.$queue.getData('kefuAppId'),\r\n\t\t\t\t\t\tsuccess(res) {}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\twindow.location.href = that.$queue.getData('kefu');\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP\r\n\t\t\t\t\tlet kefu = that.$queue.getData('kefu')\r\n\t\t\t\t\tconsole.log(kefu)\r\n\t\t\t\t\tplus.runtime.openURL(kefu, function(res) {});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t} else { //客服二维码页面\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/kefu/kefu'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbindTo(url, name, describes) {\r\n\t\t\t\tif (uni.getStorageSync('sendMsg')) {\r\n\t\t\t\t\tconsole.log('授权+1')\r\n\t\t\t\t\twx.requestSubscribeMessage({\r\n\t\t\t\t\t\ttmplIds: this.arr,\r\n\t\t\t\t\t\tsuccess(re) {\r\n\t\t\t\t\t\t\tconsole.log(JSON.stringify(re), 111111111111)\r\n\t\t\t\t\t\t\tvar datas = JSON.stringify(re);\r\n\t\t\t\t\t\t\tif (datas.indexOf(\"accept\") != -1) {\r\n\t\t\t\t\t\t\t\tconsole.log(re)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (describes && describes === '跳转小程序') {\r\n\t\t\t\t\tlet appid = url.substring(0, url.indexOf(','))\r\n\t\t\t\t\tlet usls = url.substring(url.indexOf(',') + 1)\r\n\t\t\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\t\t\tappId: appid,\r\n\t\t\t\t\t\tpath: usls,\r\n\t\t\t\t\t\tenvVersion: 'release',\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t// 打开成功\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet userId = uni.getStorageSync('userId')\r\n\r\n\t\t\t\tif (url.indexOf('客服') !== -1) {\r\n\t\t\t\t\tthis.selectKeFu()\r\n\r\n\t\t\t\t} else if (url.indexOf('http') !== -1) {\r\n\t\t\t\t\t//#ifndef H5\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/webView/webView?url=' + url\r\n\t\t\t\t\t})\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\twindow.location.href = url;\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (userId) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tif (this.page < this.pages) {\r\n\t\t\t\tthis.page = this.page + 1;\r\n\t\t\t\tthis.gethospitalList()\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.gethospitalList()\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.u-mode-center-box {\r\n\t\tbackground-color: '' !important;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n\r\n\t.tips {\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\r\n\t\ttext {\r\n\t\t\tcolor: #5074FF;\r\n\t\t}\r\n\t}\r\n\r\n\t.kefu {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: fixed;\r\n\t\tbottom: 200rpx;\r\n\t\tright: 40rpx;\r\n\t}\r\n\r\n\t.content {\r\n\t\tpadding-bottom: 50rpx;\r\n\t}\r\n\r\n\t.swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 320rpx;\r\n\t\tbackground-image: linear-gradient(to bottom, #468EF8, #ffffff);\r\n\r\n\t\t.swiper-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 300rpx;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.tabs {\r\n\t\twidth: 100%;\r\n\t\theight: 130rpx;\r\n\t\tmargin-top: 20rpx;\r\n\r\n\t\t.tabs-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\r\n\t\t\t.tabs-box-item-c-t {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.types {\r\n\t\twidth: 100%;\r\n\t\t// height: 165rpx;\r\n\t\tmargin-top: 30rpx;\r\n\r\n\t\t.types-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\r\n\t\t\t.types-box-l {\r\n\t\t\t\twidth: 333rpx;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 165rpx;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.types-box-r {\r\n\t\t\t\twidth: 333rpx;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.hospital {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\t// margin-top: 20rpx;\r\n\r\n\t\t.hospital-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: auto;\r\n\r\n\t\t\t.hospital-box-item {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 220rpx;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tbackground-color: #ffffff;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.hospital-box-item-c {\r\n\t\t\t\twidth: calc(686rpx - 60rpx);\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.hospital-box-item-c-l {\r\n\t\t\t\twidth: 200rpx;\r\n\t\t\t\theight: 160rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.hospital-box-item-c-r {\r\n\t\t\t\twidth: 406rpx;\r\n\t\t\t\theight: 160rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.hospital-box-item-c-r-title {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tcolor: #1E1F31;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.hospital-box-item-c-r-info {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: #FF574E;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tspan {\r\n\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.hospital-box-item-c-r-jj {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.contentview {\r\n\t\twidth: 632rpx;\r\n\t\tpadding: 48rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 16rpx;\r\n\t}\r\n\r\n\t.contentview .title {\r\n\t\ttext-align: center;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.contentview .des {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-top: 40rpx;\r\n\t\ttext-align: justify;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t.contentview .des .link {\r\n\t\tcolor: #5074FF;\r\n\t\ttext-decoration: underline;\r\n\t}\r\n\r\n\tbutton::after {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.btns {\r\n\t\tmargin-top: 48rpx;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.btns .item {\r\n\t\tjustify-content: space-between;\r\n\t\twidth: 244rpx;\r\n\t\theight: 80rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.btns .reject {\r\n\t\tbackground: #f4f4f5;\r\n\t\tcolor: #909399;\r\n\t}\r\n\r\n\t.btns .agree {\r\n\t\tbackground: #5074FF;\r\n\t\tcolor: #fff;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627372\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}