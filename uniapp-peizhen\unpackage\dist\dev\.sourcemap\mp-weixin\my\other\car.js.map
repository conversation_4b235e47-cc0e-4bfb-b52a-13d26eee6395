{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/car.vue?435d", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/car.vue?e7ab", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/car.vue?11e2", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/car.vue?4bb9", "uni-app:///my/other/car.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/car.vue?39c4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/car.vue?31bf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "dataList", "page", "limit", "type", "isSelect", "onLoad", "uni", "title", "onShow", "methods", "getcar", "console", "gotoAddCar", "url", "<PERSON><PERSON><PERSON>", "dete", "patientId", "icon", "getcarlist", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACc;;;AAGhE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAkuB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC+CtvB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;MACA;QACA;QACA;QACA;QACA;QACAL;QACAA;MACA;QACAK;QACAL;QACA;QACAA;MACA;IACA;IACA;IACAM;MACAN;QACAO;MACA;IACA;IACA;IACAC;MACAR;QACAO;MACA;IACA;IACA;IACAE;MAAA;MACAT;QACAC;MACA;MACA;QACAS;MACA;MACA;QACAV;QACA;UACAA;YACAC;YACAU;UACA;UACA;UACA;QACA;UACAN;QACA;MACA;IACA;IACAO;MAAA;MAEA;QACAjB;QACAC;MACA;MACA;QACAI;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACAK;QACA;QACAL;MACA;IACA;EACA;EACAa;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAAq1C,CAAgB,2rCAAG,EAAC,C;;;;;;;;;;;ACAz2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/other/car.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/other/car.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./car.vue?vue&type=template&id=68e26940&\"\nvar renderjs\nimport script from \"./car.vue?vue&type=script&lang=js&\"\nexport * from \"./car.vue?vue&type=script&lang=js&\"\nimport style0 from \"./car.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/other/car.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=template&id=68e26940&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dataList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 200rpx;\">\r\n\t\t<!-- 车辆列表 -->\r\n\t\t<view class=\"carList\">\r\n\t\t\t<view class=\"carList-box\">\r\n\t\t\t\t<view class=\"carList-box-item\" v-for=\"(item,index) in dataList\" :key=\"index\" @click.stop=\"getcar(item)\">\r\n\t\t\t\t\t<view class=\"carList-box-item-r\">\r\n\t\t\t\t\t\t<view class=\"carList-box-item-r-name\">{{item.realName}} {{item.phone}}</view>\r\n\t\t\t\t\t\t<view class=\"margin-tb-xs\">{{item.idNumber}}</view>\r\n\t\t\t\t\t\t<view class=\"carList-box-item-r-label\">\r\n\t\t\t\t\t\t\t<view v-if=\"item.sex==1\">男</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.sex==2\">女</view>\r\n\t\t\t\t\t\t\t<view class=\"padding-lr-xs\">|</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.isUnderAge==1\">已满十八岁</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.isUnderAge==2\">未满十八岁</view>\r\n\t\t\t\t\t\t\t<view class=\"padding-lr-xs\">|</view>\r\n\t\t\t\t\t\t\t<view>{{item.relationship}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"margin-tb-xs\" style=\"color: #999999;\" v-if=\"item.emergencyPhone\">\r\n\t\t\t\t\t\t\t紧急联系人：{{item.emergencyPhone}}</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"flex align-end justify-end margin-right-sm \">\r\n\t\t\t\t\t\t<view class=\"flex align-center text-right\">\r\n\t\t\t\t\t\t\t<view @click.stop=\"bianji(item.patientId)\">\r\n\t\t\t\t\t\t\t\t<image src=\"../static/bianji.png\" style=\"width: 35upx;height: 35upx;\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"isSelect!='ok'\" class=\"margin-left\" @click.stop=\"dete(item.patientId)\">\r\n\t\t\t\t\t\t\t\t<image src=\"../static/dete.png\" style=\"width: 35upx;height: 38upx;\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 添加车辆 -->\r\n\t\t<view class=\"addCar\">\r\n\t\t\t<view class=\"addCar-box\" @click=\"gotoAddCar()\">\r\n\t\t\t\t添加就诊人\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<empty v-if=\"dataList.length == 0\"></empty>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport empty from '../../components/empty.vue'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tempty\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdataList: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\ttype: '',\r\n\t\t\t\tisSelect: 'no'\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中....'\r\n\t\t\t})\r\n\t\t\tif (option.type) {\r\n\t\t\t\tthis.type = option.type\r\n\t\t\t}\r\n\t\t\tif (option.isSelect) {\r\n\t\t\t\tthis.isSelect = option.isSelect\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getcarlist()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetcar(e) {\r\n\t\t\t\tconsole.log(this.type)\r\n\t\t\t\tif (this.type == 1) {\r\n\t\t\t\t\t// let obj = {\r\n\t\t\t\t\t// \trealName:e.realName,\r\n\t\t\t\t\t// \tpatientId:e.patientId\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tuni.$emit('updateData', e)\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t} else if (this.type != 4) {\r\n\t\t\t\t\tconsole.log('走着里了？')\r\n\t\t\t\t\tuni.$emit('jiuzhenlist', e)\r\n\t\t\t\t\t// uni.setStorageSync(\"jiuzhenlist\", e)\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//添加车辆\r\n\t\t\tgotoAddCar() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: './addCar'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//重新编辑\r\n\t\t\tbianji(patientId) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/other/addCar?patientId=' + patientId\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//删除\r\n\t\t\tdete(patientId) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在删除'\r\n\t\t\t\t})\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpatientId: patientId\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.getT(\"/app/patientInfo/deletePatient\", data).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\t\tthis.getcarlist()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(res.msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetcarlist() {\r\n\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tlimit: this.limit\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/patientInfo/getPatientList\", data).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\t\tthis.dataList = res.data.records\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.dataList = [...this.dataList, ...res.data.records]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(res.msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.page = this.page + 1;\r\n\t\t\tthis.getcarlist()\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.getcarlist()\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\">\r\n\tpage {\r\n\t\tbackground: #F5F5F5;\r\n\t}\r\n\r\n\t.carList {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\r\n\r\n\t\t.carList-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: auto;\r\n\r\n\t\t\t.carList-box-item {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tpadding: 20upx;\r\n\t\t\t}\r\n\r\n\t\t\t.carList-box-item-r {\r\n\t\t\t\tmargin-left: 17rpx;\r\n\r\n\t\t\t\t.carList-box-item-r-name {\r\n\t\t\t\t\tcolor: #1E1F31;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.carList-box-item-r-label {\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.addCar {\r\n\t\twidth: 100%;\r\n\t\theight: 150rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: #F5F5F5;\r\n\t\tz-index: 999;\r\n\r\n\t\t.addCar-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 88rpx;\r\n\t\t\tborder-radius: 44rpx;\r\n\t\t\tbackground-color: #0175FE;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./car.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621957\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}