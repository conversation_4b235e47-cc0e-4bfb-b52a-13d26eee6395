package com.sqx.modules.tbCoupon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.pay.entity.PayDetails;
import com.sqx.modules.tbCoupon.entity.TbCoupon;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public interface TbCouponService extends IService<TbCoupon> {

    Result addCoupon(TbCoupon tbCoupon);

    IPage<TbCoupon> getCouponPageList(Integer page, Integer limit, TbCoupon tbCoupon);

    Result deleteCoupon(Long couponId);

    Result updateCoupon(TbCoupon tbCoupon);


}
