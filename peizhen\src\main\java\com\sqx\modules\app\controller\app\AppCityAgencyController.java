package com.sqx.modules.app.controller.app;


import com.sqx.common.utils.DateUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.entity.CityAgency;
import com.sqx.modules.app.service.CityAgencyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/19
 */
@Slf4j
@RestController
@Api(value = "代理申请", tags = {"代理申请"})
@RequestMapping(value = "/app/cityAgency")
public class AppCityAgencyController {

    @Autowired
    private CityAgencyService cityAgencyService;

    @Login
    @GetMapping("/selectCityAgencyList")
    @ApiOperation("查询城市代理")
    public Result selectCityAgencyList(Integer page,Integer limit,String userName,String phone,Integer classify){
        return cityAgencyService.selectCityAgencyList(page, limit, userName, phone, classify);
    }

    @Login
    @PostMapping("/insertCityAgency")
    @ApiOperation("添加城市代理")
    public Result insertCityAgency(@RequestBody CityAgency cityAgency,@RequestAttribute Long userId){
        cityAgency.setUserId(userId);
        cityAgency.setCreateTime(DateUtils.format(new Date()));
        cityAgencyService.save(cityAgency);
        return Result.success();
    }

    @Login
    @PostMapping("/updateCityAgency")
    @ApiOperation("修改城市代理")
    public Result updateCityAgency(@RequestBody CityAgency cityAgency){
        cityAgencyService.updateById(cityAgency);
        return Result.success();
    }

    @Login
    @PostMapping("/deleteCityAgency")
    @ApiOperation("删除城市代理")
    public Result deleteCityAgency(Long id){
        cityAgencyService.removeById(id);
        return Result.success();
    }




}