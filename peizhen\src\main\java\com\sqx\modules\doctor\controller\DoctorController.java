package com.sqx.modules.doctor.controller;

import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.doctor.entity.Doctor;
import com.sqx.modules.doctor.service.DoctorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 医生管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@RestController
@RequestMapping("/doctor")
@Api(value = "医生管理", tags = {"医生管理"})
public class DoctorController {

    @Autowired
    private DoctorService doctorService;

    /**
     * 分页查询医生列表
     */
    @GetMapping("/list")
    @ApiOperation("分页查询医生列表")
    public Result list(@RequestParam(defaultValue = "1") Integer page,
                      @RequestParam(defaultValue = "10") Integer limit,
                      @RequestParam(required = false) String doctorName,
                      @RequestParam(required = false) Long hospitalId,
                      @RequestParam(required = false) Long departmentId,
                      @RequestParam(required = false) Integer workStatus,
                      @RequestParam(required = false) Integer isEnable) {
        PageUtils pageUtils = doctorService.queryPage(page, limit, doctorName, 
                                                     hospitalId, departmentId, workStatus, isEnable);
        return Result.success().put("page", pageUtils);
    }

    /**
     * 根据ID查询医生信息
     */
    @GetMapping("/info/{doctorId}")
    @ApiOperation("根据ID查询医生信息")
    public Result info(@PathVariable("doctorId") Long doctorId) {
        Doctor doctor = doctorService.getById(doctorId);
        return Result.success().put("doctor", doctor);
    }

    /**
     * 新增医生
     */
    @PostMapping("/save")
    @ApiOperation("新增医生")
    public Result save(@RequestBody Doctor doctor) {
        return doctorService.addDoctor(doctor);
    }

    /**
     * 修改医生
     */
    @PostMapping("/update")
    @ApiOperation("修改医生")
    public Result update(@RequestBody Doctor doctor) {
        return doctorService.updateDoctor(doctor);
    }

    /**
     * 删除医生
     */
    @PostMapping("/delete")
    @ApiOperation("删除医生")
    public Result delete(@RequestBody Long[] doctorIds) {
        return doctorService.deleteDoctor(doctorIds);
    }

    /**
     * 启用/禁用医生
     */
    @PostMapping("/updateStatus")
    @ApiOperation("启用/禁用医生")
    public Result updateStatus(@RequestParam Long doctorId, @RequestParam Integer isEnable) {
        Doctor doctor = new Doctor();
        doctor.setDoctorId(doctorId);
        doctor.setIsEnable(isEnable);
        boolean result = doctorService.updateById(doctor);
        return result ? Result.success() : Result.error("操作失败");
    }
}
