package com.sqx.modules.taking.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.taking.entity.OrderTaking;
import com.sqx.modules.taking.service.GameService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("app/appGame")
@Api("app游戏分类")
public class AppGameController {

    private GameService gameService;

    @GetMapping("/queryGameName")
    @ApiOperation("查询首页游戏分类")
    public Result queryGameName() {
        return gameService.queryGameName();
    }
}
