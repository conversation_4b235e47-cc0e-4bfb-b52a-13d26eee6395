package com.sqx.modules.creditRecord.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.creditRecord.entity.CreditRecord;
import com.sqx.modules.creditRecord.service.CreditRecordService;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 信用分记录表
 *
 * @since 2023-05-17
 */
@RestController
@RequestMapping("/app/creditRecord/")
public class AppCreditRecordController {

    @Autowired
    private CreditRecordService recordService;

    /**
     * 获取信用分记录
     *
     * @param page
     * @param limit
     * @param creditRecord
     * @return
     */
    @Login
    @GetMapping("getCreditRecordList")
    @ApiModelProperty("获取信用分记录")
    public Result getCreditRecordList(@RequestAttribute("userId")Long userId, Integer page, Integer limit, CreditRecord creditRecord) {
        creditRecord.setRiderUserId(userId);
        return Result.success().put("data", recordService.getCreditRecordList(page, limit, creditRecord));
    }
}

