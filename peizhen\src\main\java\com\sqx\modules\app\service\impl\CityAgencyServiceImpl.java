package com.sqx.modules.app.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.dao.CityAgencyDao;
import com.sqx.modules.app.entity.CityAgency;
import com.sqx.modules.app.service.CityAgencyService;
import org.springframework.stereotype.Service;

/**
 * 商户
 */
@Service
public class CityAgencyServiceImpl extends ServiceImpl<CityAgencyDao, CityAgency> implements CityAgencyService {


    @Override
    public Result selectCityAgencyList(Integer page,Integer limit,String userName,String phone,Integer classify){
        Page<CityAgency> pages=new Page<>(page,limit);
        return Result.success().put("data",new PageUtils(baseMapper.selectCityAgencyList(pages,userName,phone,classify)));
    }


}
