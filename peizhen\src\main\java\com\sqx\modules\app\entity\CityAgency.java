package com.sqx.modules.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * @description city_agency
 * <AUTHOR>
 * @date 2021-07-19
 */
@Data
public class CityAgency implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 代理id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 意向代理城市
     */
    private String city;


    /**
     * 姓名
     */
    private String userName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 1 职位招聘 2招商加盟
     */
    private Integer classify;

    /**
     * 创建时间
     */
    private String createTime;

    public CityAgency() {}
}
