package com.sqx.modules.riderLocation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sqx.modules.app.entity.UserEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel("rider_location")
public class RiderLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)

    @ApiModelProperty("主键id")
    private Long id;


    @ApiModelProperty("骑手id")
    private Long userId;


    @ApiModelProperty("骑手当前经度")
    private Double lng;


    @ApiModelProperty("骑手当前纬度")
    private Double lat;

    @ApiModelProperty("骑手当前省")
    private String province;

    @ApiModelProperty("骑手当前市")
    private String city;

    @ApiModelProperty("骑手当前详细地址")
    private String addressDetails;

    @ApiModelProperty("骑手当前区")
    private String district;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    @TableField(exist = false)
    /**
     * 骑手昵称
     */
    @ApiModelProperty("骑手昵称")
    private String userName;
    @TableField(exist = false)
    /**
     * 骑手手机号
     */
    @ApiModelProperty("骑手手机号")
    private String phone;
    @TableField(exist = false)
    private UserEntity userEntity;

    /**
     * 骑手当前订单数
     */
    @TableField(exist = false)
    private int orderCount;
    public RiderLocation() {
    }
}