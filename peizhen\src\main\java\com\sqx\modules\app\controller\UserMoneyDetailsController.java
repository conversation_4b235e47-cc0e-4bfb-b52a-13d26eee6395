package com.sqx.modules.app.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.service.UserMoneyDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/Details")
@AllArgsConstructor
@Api("钱包明细")
public class UserMoneyDetailsController {
    private UserMoneyDetailsService userMoneyDetailsService;

    @ApiOperation("钱包明细")
    @GetMapping("/queryUserMoneyDetails")
    public Result queryUserMoneyDetails(Integer page, Integer limit, Long userId, Integer classify, Integer type) {

        return userMoneyDetailsService.queryUserMoneyDetails(page, limit, userId, classify, type);
    }

    @ApiOperation("获取推广收益排行榜")
    @GetMapping("/getRankingList")
    public Result getRankingList(Integer page, Integer limit) {
        return Result.success().put("data", userMoneyDetailsService.getRankingList(page, limit,10));
    }
    @ApiOperation("获取代理商收益排行榜")
    @GetMapping("/getAgentList")
    public Result getAgentList(Integer page, Integer limit) {
        return Result.success().put("data", userMoneyDetailsService.getRankingList(page, limit,20));
    }
}
