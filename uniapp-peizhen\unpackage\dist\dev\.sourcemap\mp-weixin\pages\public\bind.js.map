{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/bind.vue?e657", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/bind.vue?b2f0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/bind.vue?214f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/bind.vue?866b", "uni-app:///pages/public/bind.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/bind.vue?aaa0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/bind.vue?642d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "listCell", "data", "mobile", "code", "logining", "sending", "sendTime", "count", "methods", "inputChange", "navBack", "uni", "countDown", "setTimeout", "sendMsg", "showCancel", "title", "content", "<PERSON><PERSON><PERSON><PERSON>", "phone", "openId", "inviterCode", "avatar", "userName", "msg", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCuBvvB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;IACA;IACAC;MACAC;IACA;IACAC;MACA,IACAL,QACA,KADAA;MAEA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACAM;MACA;IACA;IACAC;MAAA;MACA,IACAZ,SACA,KADAA;MAEA;QACA;MACA;QACA;MACA;QACA;QACA;UACA;YACA;YACA;YACA;YACAS;UACA;YACAA;YACAA;cACAI;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA,IACAhB,SAEA,KAFAA;QACAC,OACA,KADAA;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;UACAgB;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;UACA;YACA;YACA;YACA;YACA;YACA,oEACA;YACA;YACAX;cACAF;gBACAc;cACA;YACA;UACA;YACAd;cACAI;cACAC;cACAC;YACA;UACA;UACAN;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAs3C,CAAgB,2tCAAG,EAAC,C;;;;;;;;;;;ACA14C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/public/bind.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/public/bind.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bind.vue?vue&type=template&id=558812ba&\"\nvar renderjs\nimport script from \"./bind.vue?vue&type=script&lang=js&\"\nexport * from \"./bind.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bind.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/public/bind.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind.vue?vue&type=template&id=558812ba&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"cu-form-group\"\r\n\t\t\tstyle=\"margin: 30upx;border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\r\n\t\t\t<view class=\"title\">手机号</view>\r\n\t\t\t<input type=\"number\" :value=\"mobile\" placeholder=\"请输入手机号\" maxlength=\"11\" data-key=\"mobile\"\r\n\t\t\t\t@input=\"inputChange\" />\r\n\t\t</view>\r\n\t\t<view class=\"cu-form-group\"\r\n\t\t\tstyle=\"margin: 30upx;border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\r\n\t\t\t<text class=\"title\">验证码</text>\r\n\t\t\t<input type=\"number\" :value=\"code\" placeholder=\"请输入验证码\" maxlength=\"6\" data-key=\"code\" @input=\"inputChange\"\r\n\t\t\t\t@confirm=\"toLogin\" />\r\n\t\t\t<button class=\"send-msg\" @click=\"sendMsg\" :disabled=\"sending\">{{ sendTime }}</button>\r\n\t\t</view>\r\n\r\n\t\t<button class=\"confirm-btn\" @click=\"toLogin\" :disabled=\"logining\">立即绑定</button>\r\n\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport listCell from '@/components/com-input';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tlistCell\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmobile: '',\r\n\t\t\t\tcode: '',\r\n\t\t\t\tlogining: false,\r\n\t\t\t\tsending: false,\r\n\t\t\t\tsendTime: '获取验证码',\r\n\t\t\t\tcount: 60,\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\tinputChange(e) {\r\n\t\t\t\tconst key = e.currentTarget.dataset.key;\r\n\t\t\t\tthis[key] = e.detail.value;\r\n\t\t\t},\r\n\t\t\tnavBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\tcountDown() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcount\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (count === 1) {\r\n\t\t\t\t\tthis.count = 60;\r\n\t\t\t\t\tthis.sending = false;\r\n\t\t\t\t\tthis.sendTime = '获取验证码'\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.count = count - 1;\r\n\t\t\t\t\tthis.sending = true;\r\n\t\t\t\t\tthis.sendTime = count - 1 + '秒后重新获取';\r\n\t\t\t\t\tsetTimeout(this.countDown.bind(this), 1000);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsendMsg() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tmobile\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (!mobile) {\r\n\t\t\t\t\tthis.$queue.showToast(\"请输入手机号\");\r\n\t\t\t\t} else if (mobile.length !== 11) {\r\n\t\t\t\t\tthis.$queue.showToast(\"请输入正确的手机号\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$queue.showLoading(\"正在发送验证码...\");\r\n\t\t\t\t\tthis.$Request.getT('/app/Login/sendMsg/' + mobile + '/gzg').then(res => {\r\n\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\tthis.sending = true;\r\n\t\t\t\t\t\t\tthis.$queue.showToast('验证码发送成功请注意查收');\r\n\t\t\t\t\t\t\tthis.countDown();\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\ttitle: '短信发送失败',\r\n\t\t\t\t\t\t\t\tcontent: res.msg ? res.msg : '请一分钟后再获取验证码'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoLogin() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tmobile,\r\n\t\t\t\t\tcode\r\n\t\t\t\t} = this;\r\n\t\t\t\tlet userId = this.$queue.getData(\"userId\");\r\n\t\t\t\tif (!mobile) {\r\n\t\t\t\t\tthis.$queue.showToast(\"请输入手机号\");\r\n\t\t\t\t} else if (mobile.length !== 11) {\r\n\t\t\t\t\tthis.$queue.showToast(\"请输入正确的手机号\");\r\n\t\t\t\t} else if (!code) {\r\n\t\t\t\t\tthis.$queue.showToast(\"请输入验证码\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$queue.showLoading(\"正在绑定中...\");\r\n\t\t\t\t\tlet openId = this.$queue.getData('openid') ? this.$queue.getData('openid') : '';\r\n\t\t\t\t\tlet openidnickname = this.$queue.getData('openidnickname') ? this.$queue.getData('openidnickname') : '';\r\n\t\t\t\t\tlet openidheadimgurl = this.$queue.getData('openidheadimgurl') ? this.$queue.getData('openidheadimgurl') : '';\r\n\t\t\t\t\tlet invitation = this.$queue.getData('inviterCode') ? this.$queue.getData('inviterCode') : '';\r\n\t\t\t\t\tthis.$Request.post(`/app/Login/registerCode`, {\r\n\t\t\t\t\t\tphone: mobile,\r\n\t\t\t\t\t\topenId: openId,\r\n\t\t\t\t\t\tinviterCode: invitation,\r\n\t\t\t\t\t\tavatar: openidheadimgurl,\r\n\t\t\t\t\t\tuserName: openidnickname,\r\n\t\t\t\t\t\tmsg: code\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\tthis.$queue.setData(\"token\", res.token);\r\n\t\t\t\t\t\t\tthis.$queue.setData('userId', res.user.userId);\r\n\t\t\t\t\t\t\tthis.$queue.setData('userName', res.user.userName);\r\n\t\t\t\t\t\t\tthis.$queue.setData('phone', res.user.phone);\r\n\t\t\t\t\t\t\tthis.$queue.setData('avatar', res.user.avatar ? res.user.avatar :\r\n\t\t\t\t\t\t\t\t'../../static/logo.png');\r\n\t\t\t\t\t\t\tthis.$queue.showToast('绑定成功');\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\ttitle: '绑定失败',\r\n\t\t\t\t\t\t\t\tcontent: res.msg,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\r\n\t}\r\n</script>\r\n\r\n<style lang='scss'>\r\n\t.send-msg {\r\n\t\tborder-radius: 30px;\r\n\t\tcolor: white;\r\n\t\theight: 30px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 30px;\r\n\t\tbackground: #557EFD;\r\n\t}\r\n\r\n\t.container {\r\n\t\ttop: 0;\r\n\t\tpadding-top: 32upx;\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #ffffff;\r\n\t}\r\n\r\n\r\n\r\n\r\n\t.confirm-btn {\r\n\t\twidth: 600upx;\r\n\t\theight: 80upx;\r\n\t\tline-height: 80upx;\r\n\t\tborder-radius: 60upx;\r\n\t\tmargin-top: 32upx;\r\n\t\tbackground: #557EFD;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32upx;\r\n\r\n\t\t&:after {\r\n\t\t\tborder-radius: 60px;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bind.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627787\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}