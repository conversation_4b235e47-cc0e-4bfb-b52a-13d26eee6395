{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?d30f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?5e82", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?edd6", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?7af3", "uni-app:///pages/index/game/orderDet.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?cb34", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?92f6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgRemarks", "isPay", "exclusiveTypeShow", "exclusiveType", "serviceshow", "showxy", "content", "loading", "customStyle", "width", "color", "background", "border", "checked", "serviceId", "order", "price", "show", "params", "year", "month", "day", "hour", "minute", "second", "timestamp", "hopeTime", "hopeTimes", "startHour", "showPay", "openWay", "openLists", "modularId", "shows", "hospitalId", "hospitalIsTrue", "hospitalName", "hospitalList", "orderTakingUserId", "orderTakingUserName", "<PERSON><PERSON><PERSON><PERSON>", "patientId", "patientName", "emergencyPhone", "departmentId", "departmentName", "phone", "remarks", "serviceNum", "czSel", "show<PERSON><PERSON><PERSON><PERSON><PERSON>", "youhuiList", "couponId", "couponName", "<PERSON><PERSON><PERSON><PERSON>", "page", "limit", "totalprice", "serviceType", "hospitalInfo", "reportType", "reportTypeList", "addressId", "address", "exclusiveTypeList", "drugsType", "drugsName", "drugsTypeshow", "drugsTypeshowList", "onLoad", "uni", "image", "text", "id", "destryed", "onShow", "that", "methods", "uploudImg", "sourceType", "sizeType", "count", "success", "url", "filePath", "name", "closeImg", "getPeopList", "hospitalInfock", "getyhqt", "title", "icon", "gotoNav", "<PERSON><PERSON><PERSON>", "getdrugsType", "type", "click", "getzxgs", "res", "item", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmtype", "getreportType", "checkboxChange", "console", "getxy", "<PERSON><PERSON><PERSON><PERSON>", "gotohospital", "getyiyuanInfo", "getyy", "valChange", "qingkong", "youhuiPay", "getMyList", "status", "totojzr", "selectWay", "getNowTime", "te<PERSON><PERSON><PERSON>", "date", "dateCompare", "arr", "arr1", "arr2", "d1", "d2", "s", "statusChange", "getDet", "duration", "getyiyuan", "city", "selConfirm", "<PERSON><PERSON><PERSON><PERSON>", "openPay", "pays", "orderType", "complete", "orderId", "classify", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "setTimeout", "fail", "ordersId", "pay", "callPay", "document", "onBridgeReady", "WeixinJSBridge", "setPayment", "orderInfo", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,aAAa,yQAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2lB1wB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;MACA;MAEA;MACA;IAEA;MACA;MACA;IACA;IACA;IACAC;IAEA;IACA;IAmBA;MACAC;MACAC;MACAC;IACA;MACAF;MACAC;MACAC;IACA;IACA;EAkCA;EACAC;IACA;EAAA,CACA;EACAC;IACA;IACAL;MACA;MACAM;MACAA;IACA;IACAN;MACA;MACAM;MACAA;MACA;QACAA;MACA;QACAA;MACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACAN;MACAM;MACAA;IACA;IACA;IACAN;MACAM;MACAA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAR;QACAS;QACAC;QACAC;QACAC;UACA;UACA;YACAN;YACAN;cAAA;cACAa;cAAA;cACA;cACAC;cACAC;cACAH;gBACAN;gBACAN;cACA;YACA;UACA;QACA;MACA;IACA;IACAgB;MACA;IACA;IACA;IACAC;MACAjB;QACAa;MACA;IACA;IACAK;MACAlB;QACAa;MACA;IACA;IACAM;MACA;QACAnB;UACAoB;UACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MACAtB;QACAa;MAEA;IACA;IACAU;MACA;IACA;IACA;IACAC;MAAA;MAEA;QACAC;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAF;MACA;MACA;QACA;UACAG;YACAC;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA9B;QACAa;MACA;IACA;IACAkB;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAP;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAQ;MACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACApC;QACAa;MACA;IACA;IACA;IACAwB;MACArC;QACAa;MACA;IACA;IACA;IACAyB;MAAA;MACA;QACA1E;MACA;MACA;QACA;UACA;QACA;UACAoC;YACAoB;YACAC;UACA;QACA;MACA;IACA;IACAkB;MACAL;MACA;QACAlC;UACAoB;UACAC;QACA;MACA;QACA;MACA;IACA;IACAmB;MACA;MACA;QACAxC;UACAoB;UACAC;QACA;QACA;MACA;QACA;UACA;QACA;UACA;UACA;YACA;UACA;YACA;UACA;QAEA;MACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACAhG;MACA;QACAA;MACA;MACAwF;MACA;MACA;QACA;QACA;UACA;QACA;UACA;QACA;QAEAA;MACA;QACAlC;UACAoB;UACAC;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;IAEA;IACAsB;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA7C;QACAa;MACA;IACA;IACAiC;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;MACA;MACAC;MACA;QACAnG;QACAC;QACAC;QACAC;QACAC;MACAJ;MACAC;MACA;MACA;MACA;IACA;IACA;IACAmG;MACA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;QACAH;QACAG;MACA;MACA;QACAH;QACAC;QACAC;QACAE;MACA;QACAJ;QACAI;MACA;MAEAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACAvB;MACA;MACA;MACA;MACA;MACA;MACA;QAAA;QACA;QACA;MACA;QACAlC;UACAoB;UACAC;QACA;QACA;MACA;IACA;IACA;IACAqC;MAAA;MACA;QACAlH;MACA;QACA;UACA;UACA;YACA;UACA;UACA;UACA;UACA;QACA;UACAwD;YACAoB;YACAuC;YACAtC;UACA;QACA;MACA;IACA;IACAuC;MAAA;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;UACA;UACA;YACA;YACApI;YACAA;YACA0H;UACA;UACA;QACA;MACA;IACA;IACAW;MACA5B;MACA;MACA;MACA;IACA;IACA6B;MACA;QACA/D;UACAa;QACA;MACA;QACAb;UACAoB;UACAC;QACA;MACA;IAEA;IACA2C;MACA;QACAhE;UACAoB;UACAC;UACAsC;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;MACA;MACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;MACA;MACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;MACA;MACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;MAEA;MACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;QACA;UACA3D;YACAoB;YACAC;YACAsC;UACA;UACA;QACA;MAEA;MACA;QACA3D;UACAoB;UACAC;UACAsC;QACA;QACA;MACA;MACA;IACA;IACAM;MAAA;MACA;QACA;MACA;MACA;MACA;MACA3D;QACA9D;QACAiF;QACA7D;QACAO;QACAf;QACAkB;QACAG;QACAD;QACAE;QACAI;QACAU;QACAF;QACAzD;QACA8D;QACAC;QACAR;QACA8E;QACAlG;QACAtC;MACA;QACA;UACA4E;UACAN;YACAoB;YACApF;YACAmI;cACA;gBACA;gBACAnE;kBACAoB;gBACA;gBACA;kBAAA;;kBAEAd;oBACA8D;oBACAC;kBACA;oBACA;sBACArE;wBACAsE;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACA/D;0BACAZ;0BACAA;4BACAoB;0BACA;0BACAd;0BACAsE;4BACA5E;8BACAa;4BACA;0BAEA;0BACAb,sBACA;wBACA;wBACA6E;0BACA7E;0BACAM;0BACAA,sBACA;wBACA;sBACA;oBACA;sBACAN;wBACAoB;wBACAC;sBACA;oBACA;kBACA;gBA2CA;gBAAA,CAqCA;kBAAA;kBACAf;oBACAwE;kBACA;oBACA;sBACA9E;wBACAoB;sBACA;sBACAd;sBACAsE;wBACA5E;0BACAa;wBACA;sBAEA;sBACAb;oBACA;sBACAM;sBACAN;wBACAoB;wBACAC;sBACA;oBACA;kBACA;gBACA;cAEA;gBACArB;gBACAA;kBACAoB;kBACAC;gBACA;gBACAf;gBACAsE;kBACA5E;oBACAa;kBACA;gBAEA;cACA;YACA;UACA;QACA;UACAb;YACAoB;YACAC;UACA;UACA;QACA;MAEA;IACA;IACA0D;MACA;MACAzE;IAEA;IAEA0E;MACA;QACA;UACAC;QACA;UACAA;UACAA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACAC,sBACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;QAAA;QACA;MACA,GACA;QACA;UACA;UACA;UACAnF;UACAA;YACAoB;UACA;UACA;UACAwD;YACA5E;cACAa;YACA;UAEA;UACAb;QACA;UACA;UACAA;QACA;QACAmF;MACA,EACA;IACA;IACAC;MACA;MACApF;QACAsE;QACAe;QAAA;QACAzE;UACAZ;UACAA;YACAoB;UACA;UACAd;UACAsE;YACA5E;cACAa;YACA;UAEA;UACAb;QACA;QACA6E;UACAvE;UACAN;UACAkC;QACA;MACA;IACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAoD;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5pDA;AAAA;AAAA;AAAA;AAAmkC,CAAgB,y8BAAG,EAAC,C;;;;;;;;;;;ACAvlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/game/orderDet.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/game/orderDet.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderDet.vue?vue&type=template&id=482a6b7c&\"\nvar renderjs\nimport script from \"./orderDet.vue?vue&type=script&lang=js&\"\nexport * from \"./orderDet.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderDet.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/game/orderDet.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=template&id=482a6b7c&\"", "var components\ntry {\n  components = {\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-checkbox/u-checkbox\" */ \"@/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-button/u-button\" */ \"@/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-picker/u-picker\" */ \"@/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uSelect: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-select/u-select\" */ \"@/uview-ui/components/u-select/u-select.vue\"\n      )\n    },\n    uActionSheet: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-action-sheet/u-action-sheet\" */ \"@/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.order.tags && _vm.order.tags.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.order.tags, function (ite, ind) {\n        var $orig = _vm.__get_orig(ite)\n        var g1 = _vm.order.tags.length\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  var g2 = _vm.serviceType == 1 ? _vm.imgRemarks.length : null\n  var g3 =\n    _vm.serviceType == 2 || _vm.serviceType == 3 || _vm.serviceType == 4\n      ? _vm.imgRemarks.length\n      : null\n  var g4 = _vm.serviceType == 5 ? _vm.imgRemarks.length : null\n  var g5 = _vm.serviceType == 6 ? _vm.imgRemarks.length : null\n  var g6 = _vm.serviceType == 7 ? _vm.imgRemarks.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.serviceshow = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.exclusiveTypeShow = true\n    }\n    _vm.e6 = function ($event) {\n      _vm.exclusiveTypeShow = true\n    }\n    _vm.e7 = function ($event) {\n      _vm.drugsTypeshow = true\n    }\n    _vm.e8 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e9 = function ($event) {\n      _vm.showxy = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 140px;\">\r\n\t\t<view class=\"padding\">\r\n\t\t\t<view class=\"types flex align-center\" @click=\"gotoNav(order)\">\r\n\t\t\t\t<image :src=\"order.img?order.img:'../../../static/logo.png'\"\r\n\t\t\t\t\tstyle=\"width: 85rpx;height: 85rpx;margin-left: 44rpx;\" mode=\"aspectFill\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<view class=\"types-r\">\r\n\t\t\t\t\t<view class=\"types-r-t\">{{order.serviceName}}</view>\r\n\t\t\t\t\t<view class=\"types-r-b flex align-center\" v-if=\"order.tags && order.tags.length>0\">\r\n\t\t\t\t\t\t<view v-for=\"(ite,ind) in order.tags\" :key=\"ind\">\r\n\t\t\t\t\t\t\t{{ite}} <text v-if=\"ind!=order.tags.length-1\" class=\"padding-lr-xs\">|</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"padding bg margin-top radius u-skeleton-fillet\">\r\n\t\t\t\t<view class=\"flex align-center justify-between padding-tb-xs\" v-if=\"!hospitalIsTrue\" @click=\"getyy()\">\r\n\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 200upx;font-weight: bold;\">\r\n\t\t\t\t\t\t<text v-if=\"!hospitalName\">医院</text>\r\n\t\t\t\t\t\t<text @click.stop=\"hospitalInfock(hospitalId)\" style=\"color: rgb(78, 134, 248);\"\r\n\t\t\t\t\t\t\tv-if=\"hospitalName\">医院简介</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t<!-- <view v-if=\"hospitalName\">{{hospitalName}}</view>\r\n\t\t\t\t\t\t\t<view v-else style=\"color:#999999;\">请选择医院</view> -->\r\n\t\t\t\t\t\t\t<u-input v-model=\"hospitalName\" @click=\"getyy()\" type=\"text\" :disabled=\"true\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请选择医院\" placeholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex align-center justify-between padding-tb-xs\" v-else @click=\"gotohospital()\">\r\n\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 200upx;font-weight: bold;\">{{hospitalName}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t<view style=\"color:#4e86f8;\">医院简介</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 指定陪诊人 -->\r\n\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t<view class=\"flex align-center justify-between padding-tb-xs\" @click=\"getPeopList()\">\r\n\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 200upx;font-weight: bold;\">\r\n\t\t\t\t\t\t<text>指定陪诊人</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t<!-- <view v-if=\"hospitalName\">{{hospitalName}}</view>\r\n\t\t\t\t\t\t\t<view v-else style=\"color:#999999;\">请选择医院</view> -->\r\n\t\t\t\t\t\t\t<u-input v-model=\"orderTakingUserName\" @click=\"getPeopList()\" type=\"text\" :disabled=\"true\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请选择陪诊员(选填)\" placeholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">就诊人</view>\r\n\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t<u-input v-model=\"patientName\" :clearable=\"false\" type=\"text\" @click=\"totojzr()\"\r\n\t\t\t\t\t\t\t\t:disabled=\"true\" placeholder=\"请选择就诊人\"\r\n\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"emergencyPhone\">\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">紧急联系人</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"emergencyPhone\" :clearable=\"false\" type=\"text\" :disabled=\"true\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"请选择紧急联系人\" placeholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\r\n\t\t\t\t<block v-if=\"serviceType==1\">\r\n\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">期望就诊时间</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"hopeTime\" @click=\"show = true\" :disabled=\"true\" :clearable=\"false\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择就诊时间\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"openKeshi\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">科室</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs padding-tb-xs\">\r\n\t\t\t\t\t\t\t\t<!-- <view v-if=\"departmentName\">{{departmentName}}</view>\r\n\t\t\t\t\t\t\t\t<view v-else style=\"color:#999999;\">请选择科室</view> -->\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"departmentName\" :clearable=\"false\" :disabled=\"true\" @click=\"openKeshi\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择科室\" placeholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">接送地址</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"address\" :clearable=\"false\" type=\"text\" @click=\"gotoAddress()\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"true\" placeholder=\"请选择接送地址\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"getyhqt\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">优惠劵</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between padding-tb-sm\">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">{{-couponName?-couponName:'立即使用'}}</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30 flex align-center\"\r\n\t\t\t\t\t\t\tstyle=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">\r\n\t\t\t\t\t\t\t资料上传\r\n\t\t\t\t\t\t\t<text style=\"font-size: 20rpx;\">(就诊卡、病例等)</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"img_remarks flex flex-wrap\">\r\n\t\t\t\t\t\t\t<view class=\"img_remarks-item\" v-for=\"(item,index) in imgRemarks\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"close-circle\" @click=\"closeImg(index)\" class=\"close-circle-close\"\r\n\t\t\t\t\t\t\t\t\tcolor=\"red\" size=\"38\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view @click=\"uploudImg()\" class=\"img_remarks-uploud\" v-if=\"imgRemarks.length<9\">\r\n\t\t\t\t\t\t\t\t<u-icon class=\"img_remarks-uploud-icon\" name=\"camera\" color=\"grey\" size=\"48\"></u-icon>\r\n\t\t\t\t\t\t\t\t<view class=\"img_remarks-uploud-txt\">\r\n\t\t\t\t\t\t\t\t\t图片上传\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">备注说明</view>\r\n\t\t\t\t\t\t<textarea v-model=\"remarks\" class=\"text-white text-df flex-sub padding-tb-sm \"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入其他服务需求...\" style=\"height: 200upx;\"></textarea>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"serviceType==2 || serviceType==3 || serviceType==4\">\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">期望就诊时间</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"hopeTime\" @click=\"show = true\" :disabled=\"true\" :clearable=\"false\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择就诊时间\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"openKeshi\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">科室</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs padding-tb-xs\">\r\n\t\t\t\t\t\t\t\t<!-- <view v-if=\"departmentName\">{{departmentName}}</view>\r\n\t\t\t\t\t\t\t\t<view v-else style=\"color:#999999;\">请选择科室</view> -->\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"departmentName\" :clearable=\"false\" :disabled=\"true\" @click=\"openKeshi\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择科室\" placeholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"getyhqt\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">优惠劵</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between padding-tb-sm\">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">{{-couponName?-couponName:'立即使用'}}</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30 flex align-center\"\r\n\t\t\t\t\t\t\tstyle=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">\r\n\t\t\t\t\t\t\t资料上传\r\n\t\t\t\t\t\t\t<text style=\"font-size: 20rpx;\">(就诊卡、病例等)</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"img_remarks flex flex-wrap\">\r\n\t\t\t\t\t\t\t<view class=\"img_remarks-item\" v-for=\"(item,index) in imgRemarks\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"close-circle\" @click=\"closeImg(index)\" class=\"close-circle-close\"\r\n\t\t\t\t\t\t\t\t\tcolor=\"red\" size=\"38\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view @click=\"uploudImg()\" class=\"img_remarks-uploud\" v-if=\"imgRemarks.length<9\">\r\n\t\t\t\t\t\t\t\t<u-icon class=\"img_remarks-uploud-icon\" name=\"camera\" color=\"grey\" size=\"48\"></u-icon>\r\n\t\t\t\t\t\t\t\t<view class=\"img_remarks-uploud-txt\">\r\n\t\t\t\t\t\t\t\t\t图片上传\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">备注说明</view>\r\n\t\t\t\t\t\t<textarea v-model=\"remarks\" class=\"text-white text-df flex-sub padding-tb-sm \"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入其他服务需求...\" style=\"height: 200upx;\"></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"serviceType==5\">\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">期望处理时间</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"hopeTime\" @click=\"show = true\" :disabled=\"true\" :clearable=\"false\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择期望处理时间\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">收件信息</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"address\" :clearable=\"false\" type=\"text\" @click=\"gotoAddress()\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"true\" placeholder=\"请选择收件信息\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">报告信息</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"reportType\" :clearable=\"false\" type=\"text\" @click=\"serviceshow = true\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"true\" placeholder=\"请选择报告信息\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"getyhqt\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">优惠劵</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between padding-tb-sm\">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">{{-couponName?-couponName:'立即使用'}}</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30 flex align-center\"\r\n\t\t\t\t\t\t\tstyle=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">\r\n\t\t\t\t\t\t\t资料上传\r\n\t\t\t\t\t\t\t<text style=\"font-size: 20rpx;\">(就诊卡、病例等)</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"img_remarks flex flex-wrap\">\r\n\t\t\t\t\t\t\t<view class=\"img_remarks-item\" v-for=\"(item,index) in imgRemarks\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"close-circle\" @click=\"closeImg(index)\" class=\"close-circle-close\"\r\n\t\t\t\t\t\t\t\t\tcolor=\"red\" size=\"38\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view @click=\"uploudImg()\" class=\"img_remarks-uploud\" v-if=\"imgRemarks.length<9\">\r\n\t\t\t\t\t\t\t\t<u-icon class=\"img_remarks-uploud-icon\" name=\"camera\" color=\"grey\" size=\"48\"></u-icon>\r\n\t\t\t\t\t\t\t\t<view class=\"img_remarks-uploud-txt\">\r\n\t\t\t\t\t\t\t\t\t图片上传\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">备注说明</view>\r\n\t\t\t\t\t\t<textarea v-model=\"remarks\" class=\"text-white text-df flex-sub padding-tb-sm \"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入其他服务需求...\" style=\"height: 200upx;\"></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"serviceType==6\">\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">期望就诊时间</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"hopeTime\" @click=\"show = true\" :disabled=\"true\" :clearable=\"false\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择就诊时间\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"openKeshi\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">科室</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs padding-tb-xs\">\r\n\t\t\t\t\t\t\t\t<!-- <view v-if=\"departmentName\">{{departmentName}}</view>\r\n\t\t\t\t\t\t\t\t<view v-else style=\"color:#999999;\">请选择科室</view> -->\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"departmentName\" :clearable=\"false\" :disabled=\"true\" @click=\"openKeshi\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择科室\" placeholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"exclusiveTypeShow = true\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">专享归属</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs padding-tb-xs\">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"exclusiveType\" :clearable=\"false\" :disabled=\"true\"\r\n\t\t\t\t\t\t\t\t\t@click=\"exclusiveTypeShow = true\" type=\"text\" placeholder=\"请选择专享归属\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"getyhqt\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">优惠劵</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between padding-tb-sm\">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">{{-couponName?-couponName:'立即使用'}}</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30 flex align-center\"\r\n\t\t\t\t\t\t\tstyle=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">\r\n\t\t\t\t\t\t\t资料上传\r\n\t\t\t\t\t\t\t<text style=\"font-size: 20rpx;\">(就诊卡、病例等)</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"img_remarks flex flex-wrap\">\r\n\t\t\t\t\t\t\t<view class=\"img_remarks-item\" v-for=\"(item,index) in imgRemarks\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"close-circle\" @click=\"closeImg(index)\" class=\"close-circle-close\"\r\n\t\t\t\t\t\t\t\t\tcolor=\"red\" size=\"38\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view @click=\"uploudImg()\" class=\"img_remarks-uploud\" v-if=\"imgRemarks.length<9\">\r\n\t\t\t\t\t\t\t\t<u-icon class=\"img_remarks-uploud-icon\" name=\"camera\" color=\"grey\" size=\"48\"></u-icon>\r\n\t\t\t\t\t\t\t\t<view class=\"img_remarks-uploud-txt\">\r\n\t\t\t\t\t\t\t\t\t图片上传\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">备注说明</view>\r\n\t\t\t\t\t\t<textarea v-model=\"remarks\" class=\"text-white text-df flex-sub padding-tb-sm \"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入其他服务需求...\" style=\"height: 200upx;\"></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-if=\"serviceType==7\">\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">药物类型</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"drugsType\" :clearable=\"false\" type=\"text\"\r\n\t\t\t\t\t\t\t\t\t@click=\"drugsTypeshow = true\" :disabled=\"true\" placeholder=\"请选择药物类型\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">药物名称</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"drugsName\" :clearable=\"false\" type=\"text\" placeholder=\"请准确输入药物名称\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">期望处理时间</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"hopeTime\" @click=\"show = true\" :disabled=\"true\" :clearable=\"false\"\r\n\t\t\t\t\t\t\t\t\ttype=\"text\" placeholder=\"请选择期望处理时间\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">收件信息</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs \">\r\n\t\t\t\t\t\t\t\t<u-input v-model=\"address\" :clearable=\"false\" type=\"text\" @click=\"gotoAddress()\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"true\" placeholder=\"请选择请选择收件信息\"\r\n\t\t\t\t\t\t\t\t\tplaceholder-style=\"color:#999999;font-size:13px;\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between \" @click=\"getyhqt\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;width: 240upx;font-weight: bold;\">优惠劵</view>\r\n\t\t\t\t\t\t<view class=\"flex justify-between padding-tb-sm\">\r\n\t\t\t\t\t\t\t<view class=\"margin-right-xs\">{{-couponName?-couponName:'立即使用'}}</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30 flex align-center\"\r\n\t\t\t\t\t\t\tstyle=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">\r\n\t\t\t\t\t\t\t资料上传\r\n\t\t\t\t\t\t\t<text style=\"font-size: 20rpx;\">(就诊卡、病例等)</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"img_remarks flex flex-wrap\">\r\n\t\t\t\t\t\t\t<view class=\"img_remarks-item\" v-for=\"(item,index) in imgRemarks\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<u-icon name=\"close-circle\" @click=\"closeImg(index)\" class=\"close-circle-close\"\r\n\t\t\t\t\t\t\t\t\tcolor=\"red\" size=\"38\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view @click=\"uploudImg()\" class=\"img_remarks-uploud\" v-if=\"imgRemarks.length<9\">\r\n\t\t\t\t\t\t\t\t<u-icon class=\"img_remarks-uploud-icon\" name=\"camera\" color=\"grey\" size=\"48\"></u-icon>\r\n\t\t\t\t\t\t\t\t<view class=\"img_remarks-uploud-txt\">\r\n\t\t\t\t\t\t\t\t\t选择图片\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<view class=\"text-30\" style=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">备注说明</view>\r\n\t\t\t\t\t\t<textarea v-model=\"remarks\" class=\"text-white text-df flex-sub padding-tb-sm \"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入其他服务需求...\" style=\"height: 200upx;\"></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<!-- 注意事项 -->\r\n\t\t<view class=\"zz flex justify-center\">\r\n\t\t\t<view class=\"zz-box\">\r\n\t\t\t\t<view class=\"zz-box-title\">\r\n\t\t\t\t\t<u-icon name=\"error-circle-fill\" color=\"#ffa722\" style=\"margin-right: 10rpx;\" size=\"32\"></u-icon>\r\n\t\t\t\t\t注意事项\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"zz-box-con\">\r\n\t\t\t\t\t{{order.mattersThing}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"flex justify-center\"\r\n\t\t\tstyle=\"position: fixed;bottom: 0;background-color: #FFFFFF;width: 100%;padding-top: 30rpx;padding-bottom: 20rpx;\">\r\n\t\t\t<view class=\"\" style=\"width: 686rpx;\">\r\n\t\t\t\t<view class=\"flex justify-center\" style=\"width: 100%;margin-bottom: 20rpx;\">\r\n\r\n\t\t\t\t\t<u-checkbox label-size=\"26rpx\" v-model=\"checked\" @change=\"checkboxChange\" shape=\"circle\">\r\n\t\t\t\t\t\t我已认真阅读预约相关<text @click.stop=\"gotoxieyi()\" style=\"color: #4e86f8;\">《服务条款同意书》</text></u-checkbox>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\" flex justify-between\" style=\"width: 100%;\">\r\n\t\t\t\t\t<view style=\"color: #FF2D01;\">\r\n\t\t\t\t\t\t实付：<text style=\"font-size: 38rpx;font-weight: bold;\">￥</text>\r\n\t\t\t\t\t\t<text style=\"font-size: 52upx;margin-top: 8upx;font-weight: bold;\">{{totalprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<u-button :custom-style=\"customStyle\" @click=\"openPay\" shape=\"circle\" :hair-line=\"false\">立即支付\r\n\t\t\t\t\t\t</u-button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 协议弹窗 -->\r\n\t\t<u-popup v-model=\"showxy\" mode=\"center\" border-radius=\"24\" width=\"80%\" height=\"60%\">\r\n\t\t\t<view class=\"\" style=\"width: 100%;height: 100%;background-color: #FFFFFF;position: relative;\">\r\n\t\t\t\t<view class=\"flex justify-center\" style=\"width: 100%;height: 85%;\">\r\n\t\t\t\t\t<view class=\"\" style=\"width: 90%;height: 95%;margin-top: 5%;\">\r\n\t\t\t\t\t\t<scroll-view scroll-y=\"true\" style=\"width: 100%;height: 100%;background-color: #FFFFFF;\">\r\n\t\t\t\t\t\t\t<view v-html=\"content\"></view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex justify-center align-center\"\r\n\t\t\t\t\tstyle=\"width: 100%;position: absolute;bottom: 0;background-color: #FFFFFF;height: 15%;\">\r\n\t\t\t\t\t<view @click=\"showxy=false\" class=\"flex justify-center align-center\"\r\n\t\t\t\t\t\tstyle=\"width: 90%;height: 70rpx;border-radius: 40rpx;background-color: #4e86f8;color: #FFFFFF;\">\r\n\t\t\t\t\t\t确认关闭\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t<u-picker v-model=\"show\" mode=\"time\" :mask-close-able=\"false\" :params=\"params\" @confirm=\"statusChange\">\r\n\t\t</u-picker>\r\n\r\n\t\t<u-select v-model=\"shows\" :list=\"hospitalList\" @confirm=\"selConfirm\"></u-select>\r\n\r\n\t\t<u-popup v-model=\"showPay\" mode=\"bottom\" border-radius=\"14\" :closeable=\"true\">\r\n\t\t\t<view\r\n\t\t\t\tstyle=\"width: 100%;text-align: center;font-size:38rpx;font-weight: bold;margin-top:15rpx;margin-bottom: 80rpx;\">\r\n\t\t\t\t选择支付方式\r\n\t\t\t</view>\r\n\t\t\t<view style=\"display: flex;\" v-for=\"(item,index) in openLists\" :key='index'>\r\n\t\t\t\t<view style=\"width: 100%;display: flex;height: 100upx;align-items: center;padding: 20upx 30rpx;\"\r\n\t\t\t\t\tv-if=\"item.text === '零钱' && czSel != '否'\">\r\n\t\t\t\t\t<view style=\"display: flex;width:80%;align-items: center;\">\r\n\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\r\n\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 20%;display: flex;justify-content: flex-end;\">\r\n\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item.id)'>\r\n\t\t\t\t\t\t\t<label class=\"tui-radio\">\r\n\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"width: 100%;display: flex;height: 100upx;align-items: center;padding: 20upx 30rpx;\"\r\n\t\t\t\t\tv-if=\"item.text != '零钱'\">\r\n\t\t\t\t\t<view style=\"display: flex;width:80%;align-items: center;\">\r\n\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\r\n\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 20%;display: flex;justify-content: flex-end;\">\r\n\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item.id)'>\r\n\t\t\t\t\t\t\t<label class=\"tui-radio\">\r\n\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view\r\n\t\t\t\tstyle=\"width: 690rpx;height: 80rpx;background:#1789FD;color:#FFFFFF;text-align: center;line-height: 80rpx;border-radius: 50rpx;margin: 30rpx;\"\r\n\t\t\t\t@click=\"pay()\">确认支付</view>\r\n\t\t</u-popup>\r\n\r\n\t\t<u-popup v-model=\"showYouhuijuan\" mode=\"bottom\" border-radius=\"14\" height=\"900rpx\" :closeable=\"true\">\r\n\t\t\t<view style=\"padding-bottom: 30rpx;\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tstyle=\"width: 100%;text-align: center;font-size:38rpx;font-weight: bold;padding-top:15rpx;padding-bottom:40rpx;\">\r\n\t\t\t\t\t优惠劵\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"margin-right text-right\" @click=\"qingkong\">\r\n\t\t\t\t\t<view class=\"noyouhui\">不使用优惠劵</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"listbox\" v-for=\"(item,ind) in youhuiList\" :key=\"ind\">\r\n\t\t\t\t\t<view class=\"flex align-start justify-between padding-lr\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view style=\"color: #000000;font-size: 30upx;\">{{item.couponName}}</view>\r\n\t\t\t\t\t\t\t<view style=\"color: #999999;font-size: 24upx;margin-top: 10upx;\" v-if=\"item.expirationTime\">\r\n\t\t\t\t\t\t\t\t有效期至{{item.expirationTime}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view style=\"color: #999999;font-size: 24upx;margin-top: 10upx;\" v-else>永久有效\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"color: #FD3C44;font-size: 30upx;\">¥<text\r\n\t\t\t\t\t\t\t\tstyle=\"font-size: 48upx;font-weight: bold;\">{{item.money}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 100%;border-top:1rpx dashed #E6E6E6;margin: 30upx 0upx;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between padding-lr\">\r\n\t\t\t\t\t\t<view v-if=\"item.minMoney\">满{{item.minMoney}}元可用</view>\r\n\t\t\t\t\t\t<view v-else>无门槛使用</view>\r\n\t\t\t\t\t\t<view class=\"btn\" @click=\"youhuiPay(item)\">立即使用</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t<!-- 报告类型 -->\r\n\t\t<u-select v-model=\"serviceshow\" :list=\"reportTypeList\" value-name=\"value\" label-name=\"value\"\r\n\t\t\t@confirm=\"confirmtype\"></u-select>\r\n\t\t<!-- 归属专享 -->\r\n\t\t<u-action-sheet :list=\"exclusiveTypeList\" v-model=\"exclusiveTypeShow\" @click=\"click\"></u-action-sheet>\r\n\t\t<!-- 药物类型 -->\r\n\t\t<u-select v-model=\"drugsTypeshow\" :list=\"drugsTypeshowList\" value-name=\"value\" label-name=\"value\"\r\n\t\t\t@confirm=\"confirmywu\"></u-select>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport configdata from '../../../common/config.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timgRemarks: [], //图片备注\r\n\t\t\t\tisPay: true, //防抖\r\n\t\t\t\texclusiveTypeShow: false,\r\n\t\t\t\texclusiveType: '',\r\n\t\t\t\tserviceshow: false,\r\n\t\t\t\tshowxy: false,\r\n\t\t\t\tcontent: '',\r\n\t\t\t\tloading: true, // 是否显示骨架屏组件\r\n\t\t\t\tcustomStyle: {\r\n\t\t\t\t\twidth: '340upx',\r\n\t\t\t\t\tcolor: '#FFFFFF',\r\n\t\t\t\t\tbackground: \"#468EF8\",\r\n\t\t\t\t\tborder: 0\r\n\t\t\t\t},\r\n\t\t\t\tchecked: false,\r\n\t\t\t\tserviceId: '',\r\n\t\t\t\torder: {},\r\n\t\t\t\tprice: '',\r\n\t\t\t\tshow: false,\r\n\t\t\t\tparams: {\r\n\t\t\t\t\tyear: true,\r\n\t\t\t\t\tmonth: true,\r\n\t\t\t\t\tday: true,\r\n\t\t\t\t\thour: true,\r\n\t\t\t\t\tminute: false,\r\n\t\t\t\t\tsecond: false,\r\n\t\t\t\t\ttimestamp: false\r\n\t\t\t\t},\r\n\t\t\t\thopeTime: '',\r\n\t\t\t\thopeTimes: '',\r\n\t\t\t\tstartHour: '',\r\n\t\t\t\tshowPay: false,\r\n\t\t\t\topenWay: 0,\r\n\t\t\t\topenLists: [],\r\n\r\n\t\t\t\tmodularId: '',\r\n\t\t\t\tshows: false,\r\n\t\t\t\thospitalId: '', //医院id\r\n\t\t\t\thospitalIsTrue: false,\r\n\t\t\t\thospitalName: '', //医院名称\r\n\t\t\t\thospitalList: [], //医院列表\r\n\t\t\t\torderTakingUserId: '', //护工id\r\n\t\t\t\torderTakingUserName: '', //护工名称\r\n\t\t\t\tjiuzhenlist: [],\r\n\t\t\t\tpatientId: '', //就诊人id\r\n\t\t\t\tpatientName: '', //就诊人名称\r\n\t\t\t\temergencyPhone: '', //紧急联系人\r\n\t\t\t\tdepartmentId: '', //科室id\r\n\t\t\t\tdepartmentName: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tremarks: '',\r\n\t\t\t\tserviceNum: 1, //服务天数\r\n\t\t\t\tczSel: '否',\r\n\t\t\t\tshowYouhuijuan: false,\r\n\t\t\t\tyouhuiList: [],\r\n\t\t\t\tcouponId: '',\r\n\t\t\t\tcouponName: '',\r\n\t\t\t\tyouhuiMoney: 0,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\ttotalprice: 0,\r\n\t\t\t\tserviceType: '',\r\n\t\t\t\thospitalInfo: {},\r\n\t\t\t\treportType: '', //报告类型\r\n\t\t\t\treportTypeList: [], //报告类型数组\r\n\t\t\t\taddressId: '', //地址id\r\n\t\t\t\taddress: '', //地址位置\r\n\t\t\t\texclusiveTypeList: [],\r\n\t\t\t\tdrugsType: '', //药物类型\r\n\t\t\t\tdrugsName: '', //药物名称\r\n\t\t\t\tdrugsTypeshow: false,\r\n\t\t\t\tdrugsTypeshowList: [],\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tif (option.orderTakingUserName && option.orderTakingUserId) {\r\n\t\t\t\tthis.orderTakingUserName = option.orderTakingUserName\r\n\t\t\t\tthis.orderTakingUserId = option.orderTakingUserId\r\n\t\t\t}\r\n\t\t\tthis.czSel = this.$queue.getData('czSel');\r\n\t\t\tthis.serviceType = option.serviceType\r\n\t\t\tif (this.serviceType == 5) {\r\n\t\t\t\tthis.getreportType()\r\n\t\t\t}\r\n\t\t\tif (this.serviceType == 6) {\r\n\t\t\t\tthis.getzxgs()\r\n\t\t\t}\r\n\t\t\tif (this.serviceType == 7) {\r\n\t\t\t\tthis.getdrugsType()\r\n\t\t\t}\r\n\t\t\tif (option.hospitalId) {\r\n\t\t\t\tthis.hospitalName = option.hospitalName\r\n\t\t\t\tthis.hospitalId = option.hospitalId\r\n\r\n\t\t\t\tthis.hospitalIsTrue = true\r\n\t\t\t\tthis.getyiyuanInfo() //获取医院详情\r\n\r\n\t\t\t} else {\r\n\t\t\t\tthis.getyiyuan()\r\n\t\t\t\tthis.hospitalIsTrue = false\r\n\t\t\t}\r\n\t\t\tthis.serviceId = option.serviceId\r\n\t\t\tuni.removeStorageSync('jiuzhenlist')\r\n\r\n\t\t\tthis.getDet()\r\n\t\t\tthis.getMyList();\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.openLists = [{\r\n\t\t\t\timage: '/static/images/zhifubao.png',\r\n\t\t\t\ttext: '支付宝',\r\n\t\t\t\tid: 1\r\n\t\t\t}, {\r\n\t\t\t\timage: '/static/images/icon_weixin.png',\r\n\t\t\t\ttext: '微信',\r\n\t\t\t\tid: 2\r\n\t\t\t}, {\r\n\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\ttext: '零钱',\r\n\t\t\t\tid: 3\r\n\t\t\t}];\r\n\t\t\tthis.openWay = 1;\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.openLists = [{\r\n\t\t\t\timage: '/static/images/icon_weixin.png',\r\n\t\t\t\ttext: '微信',\r\n\t\t\t\tid: 2\r\n\t\t\t}, {\r\n\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\ttext: '零钱',\r\n\t\t\t\tid: 3\r\n\t\t\t}];\r\n\t\t\tthis.openWay = 2;\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef H5\r\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\tthis.openLists = [{\r\n\t\t\t\t\timage: '/static/images/zhifubao.png',\r\n\t\t\t\t\ttext: '支付宝',\r\n\t\t\t\t\tid: 1\r\n\t\t\t\t}, {\r\n\t\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\t\ttext: '零钱',\r\n\t\t\t\t\tid: 3\r\n\t\t\t\t}, {\r\n\t\t\t\t\timage: '/static/images/icon_weixin.png',\r\n\t\t\t\t\ttext: '微信',\r\n\t\t\t\t\tid: 2\r\n\t\t\t\t}];\r\n\t\t\t\tthis.openWay = 1;\r\n\t\t\t} else {\r\n\t\t\t\tthis.openLists = [{\r\n\t\t\t\t\timage: '/static/images/zhifubao.png',\r\n\t\t\t\t\ttext: '支付宝',\r\n\t\t\t\t\tid: 1\r\n\t\t\t\t}, {\r\n\t\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\t\ttext: '零钱',\r\n\t\t\t\t\tid: 3\r\n\t\t\t\t}];\r\n\t\t\t\tthis.openWay = 1;\r\n\t\t\t}\r\n\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdestryed() {\r\n\t\t\t// uni.removeStorageSync('jiuzhenlist')\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tlet that = this\r\n\t\t\tuni.$on('peizhenPeople', (data) => {\r\n\t\t\t\t// console.log(data, 'sssssssss')\r\n\t\t\t\tthat.orderTakingUserId = data.orderTakingUserId;\r\n\t\t\t\tthat.orderTakingUserName = data.orderTakingUserName;\r\n\t\t\t})\r\n\t\t\tuni.$on('jiuzhenlist', (data) => {\r\n\t\t\t\t// console.log(data, 'sssssssss')\r\n\t\t\t\tthat.patientId = data.patientId\r\n\t\t\t\tthat.patientName = data.realName\r\n\t\t\t\tif (data.emergencyPhone) {\r\n\t\t\t\t\tthat.emergencyPhone = data.emergencyPhone\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.emergencyPhone = ''\r\n\t\t\t\t}\r\n\r\n\t\t\t})\r\n\t\t\t// this.jiuzhenlist = uni.getStorageSync('jiuzhenlist')\r\n\t\t\t// this.patientId = this.jiuzhenlist.patientId\r\n\t\t\t// this.patientName = this.jiuzhenlist.realName\r\n\t\t\t// if(this.jiuzhenlist.emergencyPhone){\r\n\t\t\t// \tthis.emergencyPhone = this.jiuzhenlist.emergencyPhone\r\n\t\t\t// }\r\n\t\t\tuni.$on('xzks', (data) => {\r\n\t\t\t\tthat.departmentId = data.departmentId\r\n\t\t\t\tthat.departmentName = data.departmentName\r\n\t\t\t})\r\n\t\t\tthis.getxy();\r\n\t\t\tuni.$on('address', (data) => {\r\n\t\t\t\tthat.addressId = data.addressId\r\n\t\t\t\tthat.address = data.province + '' + data.city + '' + data.district + ' ' + data.address\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tuploudImg() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\t//选择图片\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tsourceType: ['camera', 'album'],\r\n\t\t\t\t\tsizeType: 'compressed',\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t//循环上传图片\r\n\t\t\t\t\t\tfor (let i = 0; i < res.tempFilePaths.length; i++) {\r\n\t\t\t\t\t\t\tthat.$queue.showLoading(\"上传中...\");\r\n\t\t\t\t\t\t\tuni.uploadFile({ // 上传接口\r\n\t\t\t\t\t\t\t\turl: configdata.APIHOST1 + '/alioss/upload', //真实的接口地址\r\n\t\t\t\t\t\t\t\t// url: 'https://peizhensf.xianmaxiong.com/sqx_fast/alioss/upload',\r\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePaths[i],\r\n\t\t\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\t\t\t\tthat.imgRemarks.push(JSON.parse(uploadFileRes.data).data)\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcloseImg(index) {\r\n\t\t\t\tthis.imgRemarks.splice(index, 1)\r\n\t\t\t},\r\n\t\t\t//选择护工\r\n\t\t\tgetPeopList() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/peizhenPeople/peizhenPeople'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thospitalInfock(hospitalId) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/hospital/hospital?hospitalId=' + hospitalId\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetyhqt() {\r\n\t\t\t\tif (this.youhuiList.length == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '暂无可用优惠券',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.showYouhuijuan = true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgotoNav(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/yxpeizhen/info?data=' + encodeURIComponent(JSON.stringify(item)) + '&type=1',\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tconfirmywu(e) {\r\n\t\t\t\tthis.drugsType = e[0].value\r\n\t\t\t},\r\n\t\t\t//获取药物类型\r\n\t\t\tgetdrugsType() {\r\n\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '药物类型'\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.drugsTypeshowList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclick(index) {\r\n\t\t\t\tthis.exclusiveType = this.exclusiveTypeList[index].text\r\n\t\t\t},\r\n\t\t\t//专享归属\r\n\t\t\tgetzxgs() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '专享归属'\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tres.data.map(item => {\r\n\t\t\t\t\t\t\titem.text = item.value\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.exclusiveTypeList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//去地址管理\r\n\t\t\tgotoAddress() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/address/address?type=1'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tconfirmtype(e) {\r\n\t\t\t\tthis.reportType = e[0].value\r\n\t\t\t},\r\n\t\t\t//获取报告类型\r\n\t\t\tgetreportType() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '报告类型'\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.reportTypeList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcheckboxChange() {\r\n\t\t\t\tconsole.log(this.checked)\r\n\t\t\t\tif (this.checked == false) {\r\n\t\t\t\t\tthis.showxy = true\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.showxy = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//协议\r\n\t\t\tgetxy() {\r\n\t\t\t\tthis.$Request.get('/app/common/type/313').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.content = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//服务协议\r\n\t\t\tgotoxieyi() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/xieyi/xieyi?type=1'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//医院详情页面\r\n\t\t\tgotohospital() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/hospital/hospital?hospitalId=' + this.hospitalId\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//医院详情\r\n\t\t\tgetyiyuanInfo() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\thospitalId: this.hospitalId\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.getT('/app/hospital/getHospitalInfo', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.hospitalInfo = res.data\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\ticon: 'error'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetyy() {\r\n\t\t\t\tconsole.log(this.hospitalList)\r\n\t\t\t\tif (this.hospitalList.length == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '该城市暂无入驻医院，请选择其他城市',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.shows = true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvalChange(e) {\r\n\t\t\t\t// console.log('当前值为: ' + e.value)\r\n\t\t\t\tif (e.value == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '服务天数不能低于1天',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!this.couponId) {\r\n\t\t\t\t\t\tthis.totalprice = parseFloat(this.price * e.value).toFixed(2)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tlet totalprice = parseFloat(this.price * e.value).toFixed(2)\r\n\t\t\t\t\t\tif (parseFloat(totalprice) > parseFloat(this.couponName)) {\r\n\t\t\t\t\t\t\tthis.totalprice = parseFloat(totalprice - this.couponName).toFixed(2)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.totalprice = 0.01\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tqingkong() {\r\n\t\t\t\tthis.couponId = ''\r\n\t\t\t\tthis.couponName = ''\r\n\t\t\t\t// alert(this.price+'-'+this.serviceNum)\r\n\t\t\t\tthis.totalprice = parseFloat(this.price * this.serviceNum).toFixed(2)\r\n\t\t\t\tthis.showYouhuijuan = false\r\n\t\t\t},\r\n\t\t\tyouhuiPay(e) {\r\n\t\t\t\tlet price\r\n\t\t\t\tif (this.serviceNum != 0) {\r\n\t\t\t\t\tprice = this.price * this.serviceNum\r\n\t\t\t\t} else {\r\n\t\t\t\t\tprice = this.price\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(price, price >= e.minMoney)\r\n\t\t\t\t// return\r\n\t\t\t\tif (Number(price) >= Number(e.minMoney)) {\r\n\t\t\t\t\t// let totalprice = parseFloat(this.order.money * this.number).toFixed(0)\r\n\t\t\t\t\tif (parseFloat(price) > parseFloat(e.money)) {\r\n\t\t\t\t\t\tthis.totalprice = parseFloat(price - e.money).toFixed(2)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.totalprice = 0.01\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log(this.totalprice, parseFloat(price - e.money))\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '使用优惠劵，下单金额必须大于' + e.minMoney + '元',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.youhuiMoney = e.money\r\n\t\t\t\tthis.openWay = 3\r\n\t\t\t\tthis.couponId = e.id\r\n\t\t\t\tthis.couponName = e.money\r\n\t\t\t\tthis.showYouhuijuan = false\r\n\r\n\t\t\t},\r\n\t\t\tgetMyList() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tstatus: 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.getT('/app/couponUser/getMyCouponList', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.youhuiList = res.data.records\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//就诊人\r\n\t\t\ttotojzr() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/other/car?isSelect=ok'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tselectWay: function(id) {\r\n\t\t\t\tthis.openWay = id;\r\n\t\t\t},\r\n\t\t\t//获取当前系统时间\r\n\t\t\tgetNowTime(tempminit) {\r\n\t\t\t\tif (!tempminit) {\r\n\t\t\t\t\ttempminit = 0;\r\n\t\t\t\t}\r\n\t\t\t\tvar date = new Date();\r\n\t\t\t\tdate.setMinutes(date.getMinutes() - tempminit);\r\n\t\t\t\tvar year = date.getFullYear(),\r\n\t\t\t\t\tmonth = date.getMonth() + 1,\r\n\t\t\t\t\tday = date.getDate(),\r\n\t\t\t\t\thour = date.getHours() < 10 ? \"0\" + date.getHours() : date.getHours(),\r\n\t\t\t\t\tminute = date.getMinutes() < 10 ? \"0\" + date.getMinutes() : date.getMinutes(),\r\n\t\t\t\t\tsecond = date.getSeconds() < 10 ? \"0\" + date.getSeconds() : date.getSeconds();\r\n\t\t\t\tmonth >= 1 && month <= 9 ? (month = \"0\" + month) : \"\";\r\n\t\t\t\tday >= 0 && day <= 9 ? (day = \"0\" + day) : \"\";\r\n\t\t\t\tvar timer = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;\r\n\t\t\t\t/* console.log(timer); */\r\n\t\t\t\treturn timer;\r\n\t\t\t},\r\n\t\t\t//比较时间大小\r\n\t\t\tdateCompare(startStr, endStr) {\r\n\t\t\t\tvar d1, d2, s, arr, arr1, arr2;\r\n\t\t\t\tif (startStr.length > 10) {\r\n\t\t\t\t\tarr = startStr.split(\" \");\r\n\t\t\t\t\tarr1 = arr[0].split(\"-\");\r\n\t\t\t\t\tarr2 = arr[1].split(\":\");\r\n\t\t\t\t\td1 = new Date(arr1[0], arr1[1] - 1, arr1[2], arr2[0], arr2[1], arr2[2]);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tarr = startStr.split(\"-\");\r\n\t\t\t\t\td1 = new Date(arr[0], arr[1], arr[2]);\r\n\t\t\t\t}\r\n\t\t\t\tif (endStr.length > 10) {\r\n\t\t\t\t\tarr = endStr.split(\" \");\r\n\t\t\t\t\tarr1 = arr[0].split(\"-\");\r\n\t\t\t\t\tarr2 = arr[1].split(\":\");\r\n\t\t\t\t\td2 = new Date(arr1[0], arr1[1] - 1, arr1[2], arr2[0], arr2[1], arr2[2]);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tarr = endStr.split(\"-\");\r\n\t\t\t\t\td2 = new Date(arr[0], arr[1], arr[2]);\r\n\t\t\t\t}\r\n\r\n\t\t\t\ts = d2 - d1;\r\n\t\t\t\tif (s < 0) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\treturn true;\r\n\t\t\t},\r\n\t\t\tstatusChange(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\t//选中的时间\r\n\t\t\t\tlet hopeTimes = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + ':00:00'\r\n\t\t\t\t//获取当前时间\r\n\t\t\t\tlet tadayDate = this.getNowTime()\r\n\t\t\t\tlet flag = this.dateCompare(tadayDate, hopeTimes)\r\n\t\t\t\tif (flag == true) { //开始时间小于当前时间\r\n\t\t\t\t\tthis.hopeTimes = hopeTimes\r\n\t\t\t\t\tthis.hopeTime = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + '时'\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '期望就诊时间必须大于当前时间',\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 详情\r\n\t\t\tgetDet() {\r\n\t\t\t\tthis.$Request.get(\"/app/hospitalEmploy/getHospitalEmployInfo\", {\r\n\t\t\t\t\tserviceId: this.serviceId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.order = res.data\r\n\t\t\t\t\t\tif (this.order.tags) {\r\n\t\t\t\t\t\t\tthis.order.tags = res.data.tags.split(',')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.price = res.data.money\r\n\t\t\t\t\t\tthis.totalprice = (res.data.money).toFixed(2)\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\tduration: 1000,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetyiyuan() { //获取医院列表\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tcity: uni.getStorageSync('city')\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get('/app/hospital/getHospitalList', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tlet dictist = res.data.records\r\n\t\t\t\t\t\tvar arr = []\r\n\t\t\t\t\t\tfor (let i = 0; i < dictist.length; i++) {\r\n\t\t\t\t\t\t\tvar data = {}\r\n\t\t\t\t\t\t\tdata.label = dictist[i].hospitalName\r\n\t\t\t\t\t\t\tdata.value = dictist[i].hospitalId\r\n\t\t\t\t\t\t\tarr.push(data)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.hospitalList = arr\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tselConfirm(e) {\r\n\t\t\t\tconsole.log(e, '--------', e[0].label)\r\n\t\t\t\t// this.relationship = e[0].label\r\n\t\t\t\tthis.hospitalId = e[0].value\r\n\t\t\t\tthis.hospitalName = e[0].label\r\n\t\t\t},\r\n\t\t\topenKeshi() {\r\n\t\t\t\tif (this.hospitalId) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/my/keshi/index?hospitalId=' + this.hospitalId\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择医院',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\topenPay() {\r\n\t\t\t\tif (!this.hospitalName) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择医院',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// if(!this.orderTakingUserName){\r\n\t\t\t\t// \tuni.showToast({\r\n\t\t\t\t// \t\ttitle: '请选择陪诊员',\r\n\t\t\t\t// \t\ticon: 'none',\r\n\t\t\t\t// \t\tduration: 1000\r\n\t\t\t\t// \t})\r\n\t\t\t\t// \treturn\r\n\t\t\t\t// }\r\n\t\t\t\t// console.log(this.serviceType,'this.serviceTypethis.serviceType')\r\n\t\t\t\tif (this.serviceType == 1) {\r\n\t\t\t\t\tif (!this.patientName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择就诊人',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.hopeTime) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择期望就诊时间',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.departmentName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择科室',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.addressId) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择接送地址',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.serviceType == 2 || this.serviceType == 3 || this.serviceType == 4) {\r\n\t\t\t\t\tif (!this.patientName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择就诊人',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.hopeTime) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择期望就诊时间',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.departmentName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择科室',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.serviceType == 5) {\r\n\t\t\t\t\tif (!this.patientName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择就诊人',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.hopeTime) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择期望处理时间',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.addressId) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择收件信息',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.reportType) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择报告类型',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.serviceType == 6) {\r\n\t\t\t\t\tif (!this.patientName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择就诊人',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.hopeTime) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择期望就诊时间',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.departmentName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择科室',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.exclusiveType) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择专享归属',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t\tif (this.serviceType == 7) {\r\n\t\t\t\t\tif (!this.patientName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择就诊人',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.drugsType) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择药物类型',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.drugsName) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请准确输入药物名称',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.hopeTime) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择期望处理时间',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.addressId) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择收件信息',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t\tif (this.checked == false) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请勾选服务条款同意书',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.showPay = true;\r\n\t\t\t},\r\n\t\t\tpays() {\r\n\t\t\t\tif (this.isPay == false) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthis.isPay = false\r\n\t\t\t\tthat.$Request.get(\"/app/orders/generateOrder\", {\r\n\t\t\t\t\tserviceId: that.serviceId,\r\n\t\t\t\t\ttype: 1,\r\n\t\t\t\t\thospitalId: that.hospitalId,\r\n\t\t\t\t\tpatientId: that.patientId,\r\n\t\t\t\t\thopeTime: that.hopeTimes,\r\n\t\t\t\t\tdepartmentId: that.departmentId,\r\n\t\t\t\t\tremarks: that.remarks,\r\n\t\t\t\t\tphone: that.phone,\r\n\t\t\t\t\tserviceNum: that.serviceNum,\r\n\t\t\t\t\tcouponId: that.couponId,\r\n\t\t\t\t\taddressId: that.addressId,\r\n\t\t\t\t\treportType: that.reportType,\r\n\t\t\t\t\texclusiveType: that.exclusiveType,\r\n\t\t\t\t\tdrugsType: that.drugsType,\r\n\t\t\t\t\tdrugsName: that.drugsName,\r\n\t\t\t\t\tserviceType: that.serviceType,\r\n\t\t\t\t\torderType: 1,\r\n\t\t\t\t\torderTakingUserId: that.orderTakingUserId,\r\n\t\t\t\t\timgRemarks: that.imgRemarks.length > 0 ? that.imgRemarks.join(',') : ''\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.showPay = false;\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '付款提示',\r\n\t\t\t\t\t\t\tcontent: '确认支付' + that.totalprice + '元吗?',\r\n\t\t\t\t\t\t\tcomplete: function(re) {\r\n\t\t\t\t\t\t\t\tif (re.confirm) {\r\n\t\t\t\t\t\t\t\t\tlet classify = 1;\r\n\t\t\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tif (that.openWay == 2) { //微信\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 3\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttimeStamp: red.data.timestamp,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tnonceStr: red.data.noncestr,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpackage: red.data.package,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsignType: red.data.signType,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpaySign: red.data.sign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: function(redd) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'carlist')\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.$queue.showToast(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'支付失败');\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\t\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(ua)\r\n\t\t\t\t\t\t\t\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassify: 4\r\n\t\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat.callPay(red.data);\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 1\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(JSON.stringify(red))\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.setPayment('wxpay', JSON.stringify(red\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.data));\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\t\t} else if (that.openWay == 1) { //支付宝\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 2\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst div = document.createElement('div')\r\n\t\t\t\t\t\t\t\t\t\t\t\tdiv.innerHTML = red.data //此处form就是后台返回接收到的数据\r\n\t\t\t\t\t\t\t\t\t\t\t\tdocument.body.appendChild(div)\r\n\t\t\t\t\t\t\t\t\t\t\t\tdocument.forms[0].submit()\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 1\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.setPayment('alipay', red.data);\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t} else if (that.openWay == 3) { //零钱\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/orders/payMoney\", {\r\n\t\t\t\t\t\t\t\t\t\t\tordersId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('EditAddress')\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '已取消',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.isPay = true\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tpay() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.pays()\r\n\r\n\t\t\t},\r\n\r\n\t\t\tcallPay: function(response) {\r\n\t\t\t\tif (typeof WeixinJSBridge === \"undefined\") {\r\n\t\t\t\t\tif (document.addEventListener) {\r\n\t\t\t\t\t\tdocument.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);\r\n\t\t\t\t\t} else if (document.attachEvent) {\r\n\t\t\t\t\t\tdocument.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));\r\n\t\t\t\t\t\tdocument.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.onBridgeReady(response);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonBridgeReady: function(response) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!response.package) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tWeixinJSBridge.invoke(\r\n\t\t\t\t\t'getBrandWCPayRequest', {\r\n\t\t\t\t\t\t\"appId\": response.appid, //公众号名称，由商户传入\r\n\t\t\t\t\t\t\"timeStamp\": response.timestamp, //时间戳，自1970年以来的秒数\r\n\t\t\t\t\t\t\"nonceStr\": response.noncestr, //随机串\r\n\t\t\t\t\t\t\"package\": response.package,\r\n\t\t\t\t\t\t\"signType\": response.signType, //微信签名方式：\r\n\t\t\t\t\t\t\"paySign\": response.sign //微信签名\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\tif (res.err_msg === \"get_brand_wcpay_request:ok\") {\r\n\t\t\t\t\t\t\t// 使用以上方式判断前端返回,微信团队郑重提示：\r\n\t\t\t\t\t\t\t//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.isPay = true\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\tuni.removeStorageSync('carlist')\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.isPay = true\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tWeixinJSBridge.log(response.err_msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t\tsetPayment(name, order) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\tprovider: name,\r\n\t\t\t\t\torderInfo: order, //微信、支付宝订单数据\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\tuni.removeStorageSync('carlist')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tconsole.log(err, 12)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\t// computed: {\r\n\t\t// \tprice() {\r\n\t\t// \t\tlet price = this.isVip ? this.order.memberMoney : this.order.money\r\n\t\t// \t\tconsole.log(price * 1 * this.value)\r\n\t\t// \t\treturn (price * this.value).toFixed(2)\r\n\t\t// \t}\r\n\t\t// }\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.page = this.page + 1;\r\n\t\t\t// this.getWaterlist()\r\n\t\t\tthis.getMyList()\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.page = 1;\r\n\t\t\t// this.getWaterlist()\r\n\t\t\tthis.getMyList()\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\ttextarea::-webkit-input-placeholder {\r\n\t\tcolor: red;\r\n\r\n\t}\r\n\r\n\t.img_remarks {\r\n\t\twidth: 100%;\r\n\t\t/* height: 100rpx; */\r\n\t\t/* background-color: red; */\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.img_remarks-uploud {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tborder: 1px dashed grey;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.img_remarks-item {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tposition: relative;\r\n\r\n\t}\r\n\r\n\t.close-circle-close {\r\n\t\tposition: absolute;\r\n\t\tright: -10rpx;\r\n\t\ttop: -10rpx;\r\n\r\n\t}\r\n\r\n\t.img_remarks-item>image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.img_remarks-uploud-icon {\r\n\t\tmargin-top: 25%;\r\n\t}\r\n\r\n\t.img_remarks-uploud-txt {\r\n\t\tcolor: grey;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.zz {\r\n\t\twidth: 100%;\r\n\t\t/* height: 200rpx; */\r\n\r\n\t}\r\n\r\n\t.zz-box {\r\n\t\twidth: 686rpx;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.zz-box-title {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tpadding-left: 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t}\r\n\r\n\t.zz-box-con {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding-left: 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999999;\r\n\t\tletter-spacing: 2px;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n\r\n\t.types {\r\n\t\twidth: 100%;\r\n\t\theight: 192rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 16rpx;\r\n\t}\r\n\r\n\t.types-r {\r\n\t\tmargin-left: 30rpx;\r\n\t}\r\n\r\n\t.types-r-t {\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.types-r-b {\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.bg {\r\n\t\tbackground-color: #FFFFFF;\r\n\t}\r\n\r\n\t.u-input {\r\n\t\ttext-align: right !important;\r\n\t}\r\n\r\n\t.listbox {\r\n\t\t/* background: #FFFFFF; */\r\n\t\tbackground: #F5F5F5;\r\n\t\tmargin: 30rpx 30rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 30rpx 0;\r\n\t}\r\n\r\n\t.btn {\r\n\t\tcolor: #FD3C44;\r\n\t\tborder: 1rpx solid #FD3C44;\r\n\t\tborder-radius: 55upx;\r\n\t\tpadding: 8upx 25upx;\r\n\t}\r\n\r\n\t.noyouhui {\r\n\t\tcolor: #FD3C44;\r\n\t\tborder: 1rpx solid #FD3C44;\r\n\t\tborder-radius: 15upx;\r\n\t\tdisplay: inline-block;\r\n\t\tpadding: 5rpx 20rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447626965\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}