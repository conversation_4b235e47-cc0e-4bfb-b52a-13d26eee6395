package com.sqx.modules.ai.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sqx.modules.ai.entity.AiStreamMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * AI流式消息DAO
 */
@Mapper
public interface AiStreamMessageDao extends BaseMapper<AiStreamMessage> {
    
    /**
     * 根据任务ID获取所有分块（按序号排序）
     */
    @Select("SELECT * FROM ai_stream_message WHERE task_id = #{taskId} ORDER BY chunk_index ASC")
    List<AiStreamMessage> selectByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据任务ID获取最新的分块
     */
    @Select("SELECT * FROM ai_stream_message WHERE task_id = #{taskId} ORDER BY chunk_index DESC LIMIT 1")
    AiStreamMessage selectLatestByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据任务ID和起始分块序号获取增量分块
     */
    @Select("SELECT * FROM ai_stream_message WHERE task_id = #{taskId} AND chunk_index > #{fromIndex} ORDER BY chunk_index ASC")
    List<AiStreamMessage> selectIncrementalChunks(@Param("taskId") String taskId, @Param("fromIndex") Integer fromIndex);
    
    /**
     * 更新任务状态
     */
    @Update("UPDATE ai_stream_message SET status = #{status}, error_message = #{errorMessage}, update_time = #{updateTime} WHERE task_id = #{taskId}")
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") String status, 
                        @Param("errorMessage") String errorMessage, @Param("updateTime") String updateTime);
    
    /**
     * 删除指定时间之前的流式消息（清理过期数据）
     */
    @Update("DELETE FROM ai_stream_message WHERE create_time < #{beforeTime}")
    int deleteBeforeTime(@Param("beforeTime") String beforeTime);
}
