package com.sqx.modules.tbCoupon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.pay.entity.PayClassify;
import com.sqx.modules.pay.entity.PayDetails;
import com.sqx.modules.pay.service.PayClassifyService;
import com.sqx.modules.tbCoupon.dao.TbCouponUserDao;
import com.sqx.modules.tbCoupon.entity.TbCoupon;
import com.sqx.modules.tbCoupon.entity.TbCouponUser;
import com.sqx.modules.tbCoupon.service.TbCouponService;
import com.sqx.modules.tbCoupon.service.TbCouponUserService;
import jodd.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
public class TbCouponUserServiceImpl extends ServiceImpl<TbCouponUserDao, TbCouponUser> implements TbCouponUserService {
    @Autowired
    private TbCouponService tbCouponService;
    @Autowired
    private TbCouponUserDao couponUserDao;
    @Autowired
    private UserService userService;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private PayClassifyService classifyService;
    @Autowired
    private MessageService messageService;
    private final ReentrantReadWriteLock reentrantReadWriteLock = new ReentrantReadWriteLock(true);
    @Override
    public IPage<TbCouponUser> getMyCouponList(Integer page, Integer limit, TbCouponUser couponUser) {

        Page<TbCouponUser> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return couponUserDao.getMyCouponList(pages, couponUser);
    }

    @Override
    public Result receiveEnvelope(Long userId, Long couponId, Integer num) {
        UserEntity userEntity = userService.getById(userId);
        if (userEntity.getIsNewPeople() != 1) {
            return Result.error("你不是新人，暂时无法领取");
        }
        CommonInfo commonInfo = commonInfoService.findOne(310);
        if (commonInfo != null && commonInfo.getValue().equals("0")) {
            return Result.error("新人优惠券暂未开启");
        }
        TbCoupon tbCoupon = tbCouponService.getById(couponId);
        TbCouponUser couponUser = new TbCouponUser();
        for (int i = 0; i < tbCoupon.getCouponNum() * num; i++) {
            giveCoupon(tbCoupon, userId);
        }
        userEntity.setIsNewPeople(0);
        userService.updateById(userEntity);
        return Result.success();
    }

    @Override
    public Result receiveActivity(Long userId, Long couponId) {
        reentrantReadWriteLock.readLock().lock();
        try {
            TbCoupon tbCoupon = tbCouponService.getById(couponId);
            if (tbCoupon.getCouponNum()>0){
                if (tbCoupon.getIsEnable() != 1) {
                    return Result.error("当前优惠券未启用");
                }
                Integer num = couponUserDao.selectCount(new QueryWrapper<TbCouponUser>().eq("user_id", userId).eq("coupon_id", tbCoupon.getCouponId()));
                if (tbCoupon.getMaxReceive() != 0) {
                    if (tbCoupon.getMaxReceive() <= num) {
                        return Result.error("您已领取过!");
                    }
                }
                giveCoupon(tbCoupon, userId);
                tbCoupon.setCouponNum(tbCoupon.getCouponNum() - 1);
                tbCouponService.updateById(tbCoupon);
                return Result.success();
            }else {
                return Result.error("当前优惠券已被领完!");
            }
        }catch (Exception e){
            e.printStackTrace();
        } finally {
            reentrantReadWriteLock.readLock().unlock();
        }
        return Result.error("领取异常,请重试!");
    }


    @Override
    public void paymentCallback(PayDetails payDetails) {
        PayClassify payClassify = classifyService.getById(payDetails.getParentId());
        if (payClassify.getCouponId() != null) {
            TbCoupon tbCoupon = tbCouponService.getById(payClassify.getCouponId());
            for (int i = 0; i < payClassify.getGiveNum() * tbCoupon.getCouponNum(); i++) {
                giveCoupon(tbCoupon, payDetails.getUserId());
            }
            UserEntity userEntity = userService.selectUserById(payDetails.getUserId());

            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setContent("充值金额" + payDetails.getMoney() + "元;赠送优惠券【" + tbCoupon.getCouponName() + "】共" + payClassify.getGiveNum() + "张");
            messageInfo.setTitle("充值赠礼");
            messageInfo.setState(String.valueOf(5));
            messageInfo.setUserName(userEntity.getUserName());
            messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
            messageInfo.setCreateAt(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            messageInfo.setIsSee("0");
            messageService.saveBody(messageInfo);
        }

    }

    /**
     * 赠送优惠券计算
     *
     * @param tbCoupon
     * @param userId
     */
    @Override
    public void giveCoupon(TbCoupon tbCoupon, Long userId) {
        TbCouponUser couponUser = new TbCouponUser();
        //copy对象
        BeanUtils.copyProperties(tbCoupon, couponUser);
        couponUser.setUserId(userId);
        couponUser.setCreateTime(new Date());
        //是否是永久有效
        if (tbCoupon.getValidDays() != null && tbCoupon.getValidDays() != 0) {
            //如果不是永久
            Calendar instance = Calendar.getInstance();
            instance.setTime(new Date());
            instance.add(Calendar.DATE, tbCoupon.getValidDays());
            couponUser.setExpirationTime(instance.getTime());
            couponUser.setValidDays(tbCoupon.getValidDays());
        } else {
            couponUser.setValidDays(0);
        }
        couponUser.setStatus(0);
        couponUserDao.insert(couponUser);
    }

    @Override
    public Result giveUserCoupon(String userIds, Long couponId, Integer num) {
        TbCoupon tbCoupon = tbCouponService.getById(couponId);
        String[] split = userIds.split(",");
        for (String userId : split) {
            for (int i = 0; i < num; i++) {
                giveCoupon(tbCoupon, Long.valueOf(userId));
            }
        }
        return Result.success();
    }


}
