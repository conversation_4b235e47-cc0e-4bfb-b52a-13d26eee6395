{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedbackIndex/feedbackIndex.vue?7dfb", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedbackIndex/feedbackIndex.vue?7e65", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedbackIndex/feedbackIndex.vue?2200", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedbackIndex/feedbackIndex.vue?bae8", "uni-app:///my/feedbackIndex/feedbackIndex.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedbackIndex/feedbackIndex.vue?b36a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedbackIndex/feedbackIndex.vue?bbad"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "helpClassifyList", "onLoad", "methods", "openList", "item", "getlist", "types", "uni", "title", "icon", "onClick", "url", "Changekefu", "phoneNumber", "extInfo", "corpId", "success", "toFeedback", "fail", "complete", "content", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA4uB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsChwB;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;QAEA;UACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IACAC;MACAH;QACAI;MACA;IAEA;IACAC;MACA;MACA;QAAA;QACAL;UACAM;QACA;MACA;QAAA;QACA;QAEAnB;UACAoB;YACAH;UACA;UACAI;UACAC;QACA;MAUA;QAAA;QACAT;UACAI;QACA;MACA;IACA;IAEAM;MACA;QACAV;UACAI;UACAK;UACAE;UACAC;QACA;MACA;QACAZ;UACAC;UACAY;UACAJ;YACA;cACAK;cACAd;gBACAI;cACA;YACA;cACAU;YACA;UACA;QACA;MACA;IAGA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AAAmjC,CAAgB,88BAAG,EAAC,C;;;;;;;;;;;ACAvkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/feedbackIndex/feedbackIndex.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/feedbackIndex/feedbackIndex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./feedbackIndex.vue?vue&type=template&id=29cb4536&\"\nvar renderjs\nimport script from \"./feedbackIndex.vue?vue&type=script&lang=js&\"\nexport * from \"./feedbackIndex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./feedbackIndex.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/feedbackIndex/feedbackIndex.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedbackIndex.vue?vue&type=template&id=29cb4536&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.helpClassifyList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.parentId == 0 ? item.helpWordList.length : null\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedbackIndex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedbackIndex.vue?vue&type=script&lang=js&\"", "<!-- 帮助反馈页面 -->\n<template>\n\t<view>\n\t\t<!-- <view class=\"text-top\">常见问题</view> -->\n\n\t\t<view class=\"bg-list\">\n\t\t\t<view v-for=\"(item,index) in helpClassifyList\" :key=\"index\" :title=\"item.helpClassifyName\"\n\t\t\t\tclass=\"list-title padding-bottom\">\n\t\t\t\t<view class=\"flex align-center justify-between\" @click.stop=\"openList(item)\">\n\t\t\t\t\t<view class=\"text-title\">{{item.helpClassifyName}}</view>\n\t\t\t\t\t<view @click.stop=\"openList(item)\" >\n\t\t\t\t\t\t<image src=\"../static/up.png\" style=\"width: 21rpx;height: 15rpx;\" v-if=\"item.parentId==0\"></image>\n\t\t\t\t\t\t<image src=\"../static/dowm.png\" style=\"width: 21rpx;height: 15rpx;\" v-else></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-for=\"(problemItem,problemIndex) in item.helpWordList\" :key=\"problemIndex\" class=\"list-question\"\n\t\t\t\t\thover-class=\"hover\" @click=\"onClick(problemItem)\" v-if=\"item.parentId==0\">\n\t\t\t\t\t<view class=\"text-item\">{{problemItem.helpWordTitle}}</view>\n\t\t\t\t\t<view class=\"line\" v-if=\"problemIndex!=item.helpWordList.length-1\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"bg-box\">\n\t\t\t<view class=\"bg-white-box\">\n\t\t\t\t<image src=\"../../static/images/icon-letter.png\" class=\"image\"></image>\n\t\t\t\t<view class=\"text-feedback\" hover-class=\"hover\" @click=\"Changekefu\">联系客服</view>\n\n\t\t\t\t<view class=\"vertical-line\"></view>\n\n\t\t\t\t<image src=\"../../static/images/icon-edit.png\" class=\"image\"></image>\n\t\t\t\t<view class=\"text-feedback\" hover-class=\"hover\" @click=\"toFeedback\">我要反馈</view>\n\t\t\t</view>\n\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\thelpClassifyList: []\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getlist()\n\t\t},\n\t\tmethods: {\n\t\t\topenList(item) {\n\t\t\t\tvar oldhelpClassifyList=item\n\t\t\t\tif(oldhelpClassifyList.parentId==1){\n\t\t\t\t\titem.parentId=0\n\t\t\t\t}else{\n\t\t\t\t\titem.parentId=1\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetlist() {\n\t\t\t\tlet data={\n\t\t\t\t\ttypes:1\n\t\t\t\t}\n\t\t\t\tthis.$Request.get(\"/app/helpWord/selectHelpList\",data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.helpClassifyList = res.data\n\t\t\t\t\t\tfor (var i = 0; i < this.helpClassifyList.length; i++) {\n\t\t\t\t\t\t\tthis.helpClassifyList[i].parentId = 1\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// this.helpClassifyList.isTrue = false\n\t\t\t\t\t\t\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tonClick(item) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/my/helpDetail/helpDetail?title=' + item.helpWordTitle + '&helpWordId=' + item.helpWordId,\n\t\t\t\t})\n\n\t\t\t},\n\t\tChangekefu() {\n\t\t\tlet SelKeFu = this.$queue.getData('SelKeFu');\n\t\t\tif (SelKeFu + '' == '1') { //手机号\n\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\tphoneNumber: uni.getStorageSync('kefuPhone')\n\t\t\t\t});\n\t\t\t} else if (SelKeFu + '' == '2') { //企业微信\n\t\t\t\tlet that = this\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\twx.openCustomerServiceChat({\n\t\t\t\t\textInfo: {\n\t\t\t\t\t\turl: that.$queue.getData('kefu')\n\t\t\t\t\t},\n\t\t\t\t\tcorpId: that.$queue.getData('kefuAppId'),\n\t\t\t\t\tsuccess(res) {}\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\twindow.location.href = that.$queue.getData('kefu');\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP\n\t\t\t\tlet kefu = that.$queue.getData('kefu')\n\t\t\t\tconsole.log(kefu)\n\t\t\t\tplus.runtime.openURL(kefu, function(res) {});\n\t\t\t\t// #endif\n\t\t\t} else { //客服二维码页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/kefu/kefu'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\t\n\t\t\ttoFeedback() {\n\t\t\t\tif(uni.getStorageSync('userId')){\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/my/feedback/index',\n\t\t\t\t\t\tsuccess: res => {},\n\t\t\t\t\t\tfail: () => {},\n\t\t\t\t\t\tcomplete: () => {}\n\t\t\t\t\t});\n\t\t\t\t}else{\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground-color: #FFFFFF;\n\t\theight: 100%;\n\t}\n\n\n\n\n\t.bg-box {\n\t\tbackground-color: #FFFFFF;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t}\n\n\t.bg-list {\n\t\tmargin-bottom: 100rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tpadding: 30rpx\n\t}\n\n\t.bg-white-box {\n\t\tbackground-color: #F7F7F7;\n\t\tmargin: 30rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 10rpx;\n\t\tborder-radius: 20rpx;\n\t\tcolor: #000;\n\t\tfont-size: 32rpx;\n\t}\n\n\t.vertical-line {\n\t\theight: 20rpx;\n\t\tbackground-color: #cecece;\n\t\twidth: 2rpx;\n\t\tmargin-left: 30rpx;\n\t\tmargin-right: 30rpx;\n\t}\n\n\t.line {\n\t\twidth: 100%;\n\t\theight: 1rpx;\n\t\tbackground-color: #d3d3d3;\n\t}\n\n\n\n\t.text-title {\n\t\tcolor: #000;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.text-item {\n\t\tcolor: #999999;\n\t\tfont-size: 28rpx;\n\t\tpadding: 24rpx 0rpx;\n\t}\n\n\t.list-title {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.list-question {\n\t\tcolor: #000;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.hover {\n\t\tbackground-color: #ffffff;\n\t\topacity: 0.6;\n\t}\n\n\t.image {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.text-feedback {\n\t\tpadding: 20rpx;\n\t\t/* color: #000000; */\n\t}\n\n\t.text-top {\n\t\tmargin: 30rpx;\n\t\tcolor: #000;\n\t\tfont-size: 34rpx;\n\t}\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedbackIndex.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedbackIndex.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447625724\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}