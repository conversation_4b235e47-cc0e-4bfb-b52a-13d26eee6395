package com.sqx.modules.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Query;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.dao.AiConversationDao;
import com.sqx.modules.ai.dao.AiMessageDao;
import com.sqx.modules.ai.entity.AiConversation;
import com.sqx.modules.ai.entity.AiMessage;
import com.sqx.modules.ai.entity.AiModelConfig;
import com.sqx.modules.ai.service.AiConversationService;
import com.sqx.modules.ai.service.AiModelConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * AI对话会话服务实现
 */
@Slf4j
@Service
public class AiConversationServiceImpl extends ServiceImpl<AiConversationDao, AiConversation> implements AiConversationService {

    @Autowired
    private AiModelConfigService modelConfigService;
    
    @Autowired
    private AiMessageDao messageDao;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Result createConversation(Long userId, String modelCode, String title) {
        try {
            // 验证模型是否存在且启用
            AiModelConfig modelConfig = modelConfigService.getByModelCode(modelCode);
            if (modelConfig == null) {
                return Result.error("指定的AI模型不存在或未启用");
            }

            // 创建对话会话
            AiConversation conversation = new AiConversation();
            conversation.setUserId(userId);
            conversation.setTitle(StringUtils.isNotBlank(title) ? title : "新对话");
            conversation.setModelId(modelConfig.getId());
            conversation.setModelCode(modelCode);
            conversation.setMessageCount(0);
            conversation.setTotalTokens(0);
            conversation.setStatus(1);
            conversation.setCreateTime(dateFormat.format(new Date()));
            conversation.setUpdateTime(dateFormat.format(new Date()));

            this.save(conversation);
            
            // 设置模型配置信息
            conversation.setModelConfig(modelConfig);
            
            return Result.success().put("data", conversation);
        } catch (Exception e) {
            return Result.error("创建对话失败: " + e.getMessage());
        }
    }

    @Override
    public Result getUserConversations(Long userId, Integer page, Integer limit) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("limit", limit);
            IPage<AiConversation> pageInfo = new Query<AiConversation>().getPage(params);
            IPage<AiConversation> result = baseMapper.selectUserConversations(pageInfo, userId);
            return Result.success().put("data", new PageUtils(result));
        } catch (Exception e) {
            return Result.error("获取对话列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result getConversationDetail(Long conversationId, Long userId) {
            AiConversation conversation = this.getOne(
                new QueryWrapper<AiConversation>()
                    .eq("id", conversationId)
                    .eq(userId!=null ,"user_id", userId)
                    .eq("status", 1)
            );
            
            if (conversation == null) {
                return Result.error("对话不存在或无权限访问");
            }

            // 获取模型配置
            AiModelConfig modelConfig = modelConfigService.getById(conversation.getModelId());
            conversation.setModelConfig(modelConfig);

            // 获取最近的消息列表
            conversation.setMessages(messageDao.selectConversationMessages(conversationId, 50));

            return Result.success().put("data", conversation);
    }

    @Override
    public Result updateConversationTitle(Long conversationId, Long userId, String title) {
        try {
            AiConversation conversation = this.getOne(
                new QueryWrapper<AiConversation>()
                    .eq("id", conversationId)
                    .eq("user_id", userId)
                    .eq("status", 1)
            );
            
            if (conversation == null) {
                return Result.error("对话不存在或无权限访问");
            }

            conversation.setTitle(title);
            conversation.setUpdateTime(dateFormat.format(new Date()));
            this.updateById(conversation);

            return Result.success("标题更新成功");
        } catch (Exception e) {
            return Result.error("更新标题失败: " + e.getMessage());
        }
    }

    @Override
    public Result deleteConversation(Long conversationId, Long userId) {
        try {
            AiConversation conversation = this.getOne(
                new QueryWrapper<AiConversation>()
                    .eq("id", conversationId)
                    .eq("user_id", userId)
                    .eq("status", 1)
            );
            
            if (conversation == null) {
                return Result.error("对话不存在或无权限访问");
            }

            // 软删除
            conversation.setStatus(0);
            conversation.setUpdateTime(dateFormat.format(new Date()));
            this.updateById(conversation);

            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @Override
    public Result switchModel(Long conversationId, Long userId, String modelCode) {
        try {
            AiConversation conversation = this.getOne(
                new QueryWrapper<AiConversation>()
                    .eq("id", conversationId)
                    .eq("user_id", userId)
                    .eq("status", 1)
            );
            
            if (conversation == null) {
                return Result.error("对话不存在或无权限访问");
            }

            // 验证新模型
            AiModelConfig modelConfig = modelConfigService.getByModelCode(modelCode);
            if (modelConfig == null) {
                return Result.error("指定的AI模型不存在或未启用");
            }

            conversation.setModelId(modelConfig.getId());
            conversation.setModelCode(modelCode);
            conversation.setUpdateTime(dateFormat.format(new Date()));
            this.updateById(conversation);

            return Result.success("模型切换成功");
        } catch (Exception e) {
            return Result.error("切换模型失败: " + e.getMessage());
        }
    }

    @Override
    public void updateConversationStats(Long conversationId) {
        try {
            AiMessage stats = messageDao.selectConversationStats(conversationId);
            if (stats != null) {
                baseMapper.updateConversationStats(conversationId, 
                    stats.getPromptTokens(), // 这里复用字段存储消息数量
                    stats.getCompletionTokens()); // 这里复用字段存储总token数
            }
        } catch (Exception e) {
            log.error("更新对话统计失败", e);
        }
    }

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String title = (String) params.get("title");
        String modelCode = (String) params.get("modelCode");
        String userSearch = (String) params.get("userId"); // 改为字符串，支持用户ID、用户名、手机号搜索

        IPage<AiConversation> pageInfo = new Query<AiConversation>().getPage(params);
        IPage<AiConversation> page = baseMapper.selectConversationListWithUser(pageInfo, title, modelCode, userSearch);

        return new PageUtils(page);
    }
}
