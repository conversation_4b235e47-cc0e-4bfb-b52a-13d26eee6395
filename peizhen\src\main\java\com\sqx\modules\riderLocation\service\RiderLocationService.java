package com.sqx.modules.riderLocation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.riderLocation.entity.RiderLocation;

import java.util.HashMap;

public interface RiderLocationService extends IService<RiderLocation> {

    Result riderLocation(Long userId, Double lng, Double lat, String province, String city, String district, String addressDetails);

    Result selectRiderLocation(Long riderUserId, Double lng, Double lat);


    Long getDistanceNearRide(String longitude, String latitude, Double cashDeposit, Double distance);

    IPage<RiderLocation> selectAllRiderLocation(Integer page,Integer limit,RiderLocation riderLocation);

    HashMap<String,Object> getMapOrderData(String riderPhone, String hospitalName, String city, String ordersNo);
}
