<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.creditRecord.dao.CreditRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sqx.modules.creditRecord.entity.CreditRecord">
        <id column="record_id" property="recordId" />
        <result column="sys_user_name" property="sysUserName" />
        <result column="sys_user_id" property="sysUserId" />
        <result column="type" property="type" />
        <result column="num" property="num" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
    </resultMap>

</mapper>
