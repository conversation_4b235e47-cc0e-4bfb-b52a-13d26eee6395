<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.common.dao.CommonInfoDao">


    <select id="findByCondition" resultType="com.sqx.modules.common.entity.CommonInfo">
        select * from common_info where condition_from=#{condition}
    </select>

    <select id="findOne" resultType="com.sqx.modules.common.entity.CommonInfo">
        select * from common_info where `type`=#{type}
    </select>



</mapper>