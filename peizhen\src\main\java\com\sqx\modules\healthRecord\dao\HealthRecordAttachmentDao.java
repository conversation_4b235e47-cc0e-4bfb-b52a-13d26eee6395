package com.sqx.modules.healthRecord.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sqx.modules.healthRecord.entity.HealthRecordAttachment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 健康记录附件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface HealthRecordAttachmentDao extends BaseMapper<HealthRecordAttachment> {

    /**
     * 根据健康记录ID查询附件列表
     * @param recordId 健康记录ID
     * @return 附件列表
     */
    List<HealthRecordAttachment> getAttachmentsByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据健康记录ID删除所有附件（逻辑删除）
     * @param recordId 健康记录ID
     * @param operatorId 操作人ID
     * @return 影响行数
     */
    int deleteAttachmentsByRecordId(@Param("recordId") Long recordId, @Param("operatorId") Long operatorId);
}
