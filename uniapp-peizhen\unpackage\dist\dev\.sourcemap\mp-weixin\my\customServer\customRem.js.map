{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customRem.vue?8165", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customRem.vue?70e4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customRem.vue?ece5", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customRem.vue?f194", "uni-app:///my/customServer/customRem.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customRem.vue?82e2", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customRem.vue?7838"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "count", "value", "userId", "userInfo", "back", "typeInfo", "onLoad", "methods", "getUserInfo", "uni", "title", "icon", "goBack", "content", "success", "console", "url", "encodeURIComponent", "stringify"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAwuB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyD5vB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAN;MACA;MACA;QACA;UACA;QACA;UACAO;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAH;UACAC;UACAG;UACAC;YACA;cACAC;cACAN;gBACAO;cACA;YACA;cACAD;YACA;UACA;QACA;MACA;QACA;UAAA;UACA;UACA;UACA;UACA;UACAN;YACAO,yEACAC,wBACAC;UACA;QAGA;UACAT;YACAC;YACAC;UACA;QACA;MACA;;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3IA;AAAA;AAAA;AAAA;AAA23C,CAAgB,guCAAG,EAAC,C;;;;;;;;;;;ACA/4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/customServer/customRem.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/customServer/customRem.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./customRem.vue?vue&type=template&id=56d982d3&\"\nvar renderjs\nimport script from \"./customRem.vue?vue&type=script&lang=js&\"\nexport * from \"./customRem.vue?vue&type=script&lang=js&\"\nimport style0 from \"./customRem.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/customServer/customRem.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customRem.vue?vue&type=template&id=56d982d3&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uRate: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-rate/u-rate\" */ \"@/uview-ui/components/u-rate/u-rate.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customRem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customRem.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"info flex justify-center align-center\">\r\n\t\t\t<view class=\"info-box\">\r\n\t\t\t\t<view class=\"info-box-avatar flex \">\r\n\t\t\t\t\t<image :src=\"userInfo.avatar?userInfo.avatar:'../../static/logo.png'\" mode=\"\"></image>\r\n\t\t\t\t\t<view class=\"info-box-info\">\r\n\t\t\t\t\t\t<view class=\"info-box-info-name flex align-center\">\r\n\t\t\t\t\t\t\t{{userInfo.name}}\r\n\t\t\t\t\t\t\t<image v-if=\"userInfo.userEntity && userInfo.userEntity.iconImg\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 30rpx;height: 30rpx;margin-left: 20rpx;border-radius: 0;\"\r\n\t\t\t\t\t\t\t\t:src=\"userInfo.userEntity.iconImg\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t<text v-if=\"userInfo.age\"\r\n\t\t\t\t\t\t\t\tstyle=\"margin-left: 10rpx;font-weight: 400;font-size: 28rpx;\">{{userInfo.age}}岁</text>\r\n\t\t\t\t\t\t\t<u-icon v-if=\"userInfo.sex===1\" style=\"margin-left: 10rpx;\" name=\"man\" color=\"#ffffff\"\r\n\t\t\t\t\t\t\t\tsize=\"32\"></u-icon>\r\n\t\t\t\t\t\t\t<u-icon v-else style=\"margin-left: 10rpx;\" name=\"woman\" color=\"pink\" size=\"32\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-box-info-address\">\r\n\t\t\t\t\t\t\t{{userInfo.province?userInfo.province:''}} {{userInfo.city?userInfo.city:''}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-box-info-orderNum\">\r\n\t\t\t\t\t\t\t接单量：{{userInfo.userEntity && userInfo.userEntity.orderCount?userInfo.userEntity.orderCount:0}}单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-box-info-fuwu flex align-center\">\r\n\t\t\t\t\t\t\t服务评价：\r\n\t\t\t\t\t\t\t<u-rate :count=\"count\" v-model=\"value\" active-color=\"#FFAA01\" inactive-color=\"#FFAA01\"\r\n\t\t\t\t\t\t\t\tstyle=\"margin-left: -5rpx;\"></u-rate>{{userInfo.userEntity && userInfo.userEntity.finalScore?userInfo.userEntity.finalScore:'5.0'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 服务 -->\r\n\t\t<view class=\"fuwu flex justify-center align-center\">\r\n\t\t\t<view class=\"fuwu-box\">\r\n\t\t\t\t<view class=\"fuwu-box-title\">\r\n\t\t\t\t\t服务项目\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fuwu-box-fuwu\">\r\n\t\t\t\t\t{{userInfo.serviceName?userInfo.serviceName:'暂无'}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fuwu-box-title\">\r\n\t\t\t\t\t自我介绍\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fuwu-box-jieshao\">\r\n\t\t\t\t\t{{userInfo.details?userInfo.details:'暂无'}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"changeTa flex justify-center align-center\" @click=\"goBack(userInfo)\">\r\n\t\t\t立即下单\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcount: 1,\r\n\t\t\t\tvalue: 1,\r\n\t\t\t\tuserId: '',\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tback: false,\r\n\t\t\t\ttypeInfo: {}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.userId = e.userId\r\n\t\t\tif (e.back) {\r\n\t\t\t\tthis.back = e.back\r\n\t\t\t} else {\r\n\t\t\t\tthis.back = false\r\n\t\t\t}\r\n\t\t\tif (e.info) {\r\n\t\t\t\tthis.typeInfo = JSON.parse(decodeURIComponent(e.info))\r\n\t\t\t} else {\r\n\t\t\t\tthis.typeInfo = {}\r\n\t\t\t}\r\n\t\t\tthis.getUserInfo()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tuserId: this.userId\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.getT('/app/userCertification/getNursingInfo', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.userInfo = res.data\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 携带参数跳转\r\n\t\t\tgoBack(item) {\r\n\t\t\t\tif (!uni.getStorageSync('token')) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (item.userEntity.workStatus == 0) { //不忙碌\r\n\t\t\t\t\t\t// let objInfo = {\r\n\t\t\t\t\t\t// \torderTakingUserName: item.realName,\r\n\t\t\t\t\t\t// \torderTakingUserId: item.userId\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/my/customServer/customPeihu?hguserId=' + item.userId + '&info=' +\r\n\t\t\t\t\t\t\t\tencodeURIComponent(JSON\r\n\t\t\t\t\t\t\t\t\t.stringify(this.typeInfo))\r\n\t\t\t\t\t\t})\r\n\r\n\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择空闲状态的护理员',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// uni.navigateBack()\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #ffffff;\r\n\t}\r\n\r\n\t// .468EF8\r\n\t.info {\r\n\t\twidth: 100%;\r\n\t\theight: 300rpx;\r\n\t\tbackground-color: #468EF8;\r\n\r\n\t\t.info-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\t\t\t// background-color: red;\r\n\t\t}\r\n\r\n\t\t.info-box-avatar {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 150rpx;\r\n\t\t\t\theight: 150rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\r\n\t\t\t.info-box-info {\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\r\n\t\t\t\t.info-box-info-name {\r\n\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-box-info-address {\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-box-info-orderNum {\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-box-info-fuwu {\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.fuwu {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 40rpx 40rpx 0 0;\r\n\t\tmargin-top: -80rpx;\r\n\r\n\t\t.fuwu-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\r\n\t\t.fuwu-box-title {\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\tfont-size: 36rpx;\r\n\t\t}\r\n\r\n\t\t.fuwu-box-fuwu {\r\n\t\t\tbackground-color: #F5F5F5;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t}\r\n\r\n\t\t.fuwu-box-jieshao {\r\n\t\t\t// padding: 20rpx;\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.changeTa {\r\n\t\twidth: 686rpx;\r\n\t\theight: 80rpx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 100rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, 0%);\r\n\t\tz-index: 99;\r\n\t\tborder-radius: 40rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tbackground-color: #468EF8;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customRem.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customRem.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627350\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}