package com.sqx.modules.ai.controller.app;

import com.sqx.common.utils.Result;
import com.sqx.modules.ai.service.AiChatService;
import com.sqx.modules.ai.service.AiConversationService;
import com.sqx.modules.ai.service.AiModelConfigService;
import com.sqx.modules.ai.service.AiStreamChatService;
import com.sqx.modules.app.annotation.Login;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * APP端AI聊天Controller
 */
@RestController
@RequestMapping("/app/ai/chat")
@Api(value = "APP端AI聊天", tags = {"APP端AI聊天"})
public class AppAiChatController {

    @Autowired
    private AiChatService chatService;

    @Autowired
    private AiConversationService conversationService;

    @Autowired
    private AiModelConfigService modelConfigService;

    @Autowired
    private AiStreamChatService streamChatService;

    /**
     * 获取可用的AI模型列表
     */
    @Login
    @GetMapping("/models")
    @ApiOperation("获取可用的AI模型列表")
    public Result getModels() {
        return modelConfigService.getEnabledModels();
    }

    /**
     * 创建新的对话会话
     */
    @Login
    @PostMapping("/conversation/create")
    @ApiOperation("创建新的对话会话")
    public Result createConversation(@RequestAttribute("userId") Long userId,
                                   @RequestParam(required = false, defaultValue = "deepseek-chat") String modelCode,
                                   @RequestParam(required = false) String title) {
        return conversationService.createConversation(userId, modelCode, title);
    }

    /**
     * 获取用户的对话会话列表
     */
    @Login
    @GetMapping("/conversation/list")
    @ApiOperation("获取用户的对话会话列表")
    public Result getConversations(@RequestAttribute("userId") Long userId,
                                 @RequestParam(defaultValue = "1") Integer page,
                                 @RequestParam(defaultValue = "20") Integer limit) {
        return conversationService.getUserConversations(userId, page, limit);
    }

    /**
     * 获取对话会话详情
     */
    @Login
    @GetMapping("/conversation/{id}")
    @ApiOperation("获取对话会话详情")
    public Result getConversationDetail(@RequestAttribute("userId") Long userId,
                                      @PathVariable("id") Long id) {
        return conversationService.getConversationDetail(id, userId);
    }

    /**
     * 更新对话标题
     */
    @Login
    @PostMapping("/conversation/{id}/title")
    @ApiOperation("更新对话标题")
    public Result updateTitle(@RequestAttribute("userId") Long userId,
                            @PathVariable("id") Long id,
                            @RequestParam String title) {
        return conversationService.updateConversationTitle(id, userId, title);
    }

    /**
     * 删除对话会话
     */
    @Login
    @PostMapping("/conversation/{id}/delete")
    @ApiOperation("删除对话会话")
    public Result deleteConversation(@RequestAttribute("userId") Long userId,
                                   @PathVariable("id") Long id) {
        return conversationService.deleteConversation(id, userId);
    }

    /**
     * 切换对话使用的模型
     */
    @Login
    @PostMapping("/conversation/{id}/switch-model")
    @ApiOperation("切换对话使用的模型")
    public Result switchModel(@RequestAttribute("userId") Long userId,
                            @PathVariable("id") Long id,
                            @RequestParam String modelCode) {
        return conversationService.switchModel(id, userId, modelCode);
    }

    /**
     * 发送消息并获取AI回复
     */
    @Login
    @PostMapping("/conversation/{id}/send")
    @ApiOperation("发送消息并获取AI回复")
    public Result sendMessage(@RequestAttribute("userId") Long userId,
                            @PathVariable("id") Long id,
                            @RequestParam String message,
                            @RequestParam(required = false) String modelCode) {
        return chatService.sendMessage(userId, id, message, modelCode);
    }

    /**
     * 获取对话历史
     */
    @Login
    @GetMapping("/conversation/{id}/history")
    @ApiOperation("获取对话历史")
    public Result getHistory(@RequestAttribute("userId") Long userId,
                           @PathVariable("id") Long id,
                           @RequestParam(defaultValue = "1") Integer page,
                           @RequestParam(defaultValue = "50") Integer limit) {
        return chatService.getConversationHistory(id, userId, page, limit);
    }

    // ==================== 流式聊天接口 ====================

    /**
     * 发送消息并启动流式响应
     */
    @Login
    @PostMapping("/conversation/{id}/send-stream")
    @ApiOperation("发送消息并启动流式响应")
    public Result sendMessageStream(@RequestAttribute("userId") Long userId,
                                  @PathVariable("id") Long id,
                                  @RequestParam String message,
                                  @RequestParam(required = false) String modelCode) {
        return streamChatService.sendMessageStream(userId, id, message, modelCode);
    }

    /**
     * 获取流式消息的增量内容
     */
    @Login
    @GetMapping("/stream/{taskId}/chunks")
    @ApiOperation("获取流式消息的增量内容")
    public Result getStreamChunks(@RequestAttribute("userId") Long userId,
                                @PathVariable("taskId") String taskId,
                                @RequestParam(defaultValue = "0") Integer fromIndex) {
        return streamChatService.getStreamChunks(userId, taskId, fromIndex);
    }

    /**
     * 获取流式任务状态
     */
    @Login
    @GetMapping("/stream/{taskId}/status")
    @ApiOperation("获取流式任务状态")
    public Result getStreamStatus(@RequestAttribute("userId") Long userId,
                                @PathVariable("taskId") String taskId) {
        return streamChatService.getStreamStatus(userId, taskId);
    }

    /**
     * 取消流式任务
     */
    @Login
    @PostMapping("/stream/{taskId}/cancel")
    @ApiOperation("取消流式任务")
    public Result cancelStreamTask(@RequestAttribute("userId") Long userId,
                                 @PathVariable("taskId") String taskId) {
        return streamChatService.cancelStreamTask(userId, taskId);
    }

    /**
     * 清理已完成的流式任务
     */
    @Login
    @PostMapping("/stream/{taskId}/cleanup")
    @ApiOperation("清理已完成的流式任务")
    public Result cleanupStreamTask(@RequestAttribute("userId") Long userId,
                                  @PathVariable("taskId") String taskId) {
        return streamChatService.cleanupTask(userId, taskId);
    }
}
