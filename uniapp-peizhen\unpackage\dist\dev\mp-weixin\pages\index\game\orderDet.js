(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/index/game/orderDet"],{

/***/ 92:
/*!******************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/main.js?{"page":"pages%2Findex%2Fgame%2ForderDet"} ***!
  \******************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _orderDet = _interopRequireDefault(__webpack_require__(/*! ./pages/index/game/orderDet.vue */ 93));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_orderDet.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 93:
/*!*********************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./orderDet.vue?vue&type=template&id=482a6b7c& */ 94);
/* harmony import */ var _orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./orderDet.vue?vue&type=script&lang=js& */ 96);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _orderDet_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./orderDet.vue?vue&type=style&index=0&lang=css& */ 98);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__["render"],
  _orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/index/game/orderDet.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 94:
/*!****************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?vue&type=template&id=482a6b7c& ***!
  \****************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=template&id=482a6b7c& */ 95);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_template_id_482a6b7c___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 95:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?vue&type=template&id=482a6b7c& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uInput: function () {
      return Promise.all(/*! import() | uview-ui/components/u-input/u-input */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uview-ui/components/u-input/u-input")]).then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-input/u-input.vue */ 803))
    },
    uIcon: function () {
      return __webpack_require__.e(/*! import() | uview-ui/components/u-icon/u-icon */ "uview-ui/components/u-icon/u-icon").then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-icon/u-icon.vue */ 719))
    },
    uCheckbox: function () {
      return __webpack_require__.e(/*! import() | uview-ui/components/u-checkbox/u-checkbox */ "uview-ui/components/u-checkbox/u-checkbox").then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-checkbox/u-checkbox.vue */ 811))
    },
    uButton: function () {
      return __webpack_require__.e(/*! import() | uview-ui/components/u-button/u-button */ "uview-ui/components/u-button/u-button").then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-button/u-button.vue */ 818))
    },
    uPopup: function () {
      return __webpack_require__.e(/*! import() | uview-ui/components/u-popup/u-popup */ "uview-ui/components/u-popup/u-popup").then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-popup/u-popup.vue */ 740))
    },
    uPicker: function () {
      return Promise.all(/*! import() | uview-ui/components/u-picker/u-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uview-ui/components/u-picker/u-picker")]).then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-picker/u-picker.vue */ 825))
    },
    uSelect: function () {
      return __webpack_require__.e(/*! import() | uview-ui/components/u-select/u-select */ "uview-ui/components/u-select/u-select").then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-select/u-select.vue */ 835))
    },
    uActionSheet: function () {
      return __webpack_require__.e(/*! import() | uview-ui/components/u-action-sheet/u-action-sheet */ "uview-ui/components/u-action-sheet/u-action-sheet").then(__webpack_require__.bind(null, /*! @/uview-ui/components/u-action-sheet/u-action-sheet.vue */ 842))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.order.tags && _vm.order.tags.length > 0
  var l0 = g0
    ? _vm.__map(_vm.order.tags, function (ite, ind) {
        var $orig = _vm.__get_orig(ite)
        var g1 = _vm.order.tags.length
        return {
          $orig: $orig,
          g1: g1,
        }
      })
    : null
  var g2 = _vm.serviceType == 1 ? _vm.imgRemarks.length : null
  var g3 =
    _vm.serviceType == 2 || _vm.serviceType == 3 || _vm.serviceType == 4
      ? _vm.imgRemarks.length
      : null
  var g4 = _vm.serviceType == 5 ? _vm.imgRemarks.length : null
  var g5 = _vm.serviceType == 6 ? _vm.imgRemarks.length : null
  var g6 = _vm.serviceType == 7 ? _vm.imgRemarks.length : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.show = true
    }
    _vm.e1 = function ($event) {
      _vm.show = true
    }
    _vm.e2 = function ($event) {
      _vm.show = true
    }
    _vm.e3 = function ($event) {
      _vm.serviceshow = true
    }
    _vm.e4 = function ($event) {
      _vm.show = true
    }
    _vm.e5 = function ($event) {
      _vm.exclusiveTypeShow = true
    }
    _vm.e6 = function ($event) {
      _vm.exclusiveTypeShow = true
    }
    _vm.e7 = function ($event) {
      _vm.drugsTypeshow = true
    }
    _vm.e8 = function ($event) {
      _vm.show = true
    }
    _vm.e9 = function ($event) {
      _vm.showxy = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 96:
/*!**********************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=script&lang=js& */ 97);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 97:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _config = _interopRequireDefault(__webpack_require__(/*! ../../../common/config.js */ 34));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      imgRemarks: [],
      //图片备注
      isPay: true,
      //防抖
      exclusiveTypeShow: false,
      exclusiveType: '',
      serviceshow: false,
      showxy: false,
      content: '',
      loading: true,
      // 是否显示骨架屏组件
      customStyle: {
        width: '340upx',
        color: '#FFFFFF',
        background: "#468EF8",
        border: 0
      },
      checked: false,
      serviceId: '',
      order: {},
      price: '',
      show: false,
      params: {
        year: true,
        month: true,
        day: true,
        hour: true,
        minute: false,
        second: false,
        timestamp: false
      },
      hopeTime: '',
      hopeTimes: '',
      startHour: '',
      showPay: false,
      openWay: 0,
      openLists: [],
      modularId: '',
      shows: false,
      hospitalId: '',
      //医院id
      hospitalIsTrue: false,
      hospitalName: '',
      //医院名称
      hospitalList: [],
      //医院列表
      orderTakingUserId: '',
      //护工id
      orderTakingUserName: '',
      //护工名称
      jiuzhenlist: [],
      patientId: '',
      //就诊人id
      patientName: '',
      //就诊人名称
      emergencyPhone: '',
      //紧急联系人
      departmentId: '',
      //科室id
      departmentName: '',
      phone: '',
      remarks: '',
      serviceNum: 1,
      //服务天数
      czSel: '否',
      showYouhuijuan: false,
      youhuiList: [],
      couponId: '',
      couponName: '',
      youhuiMoney: 0,
      page: 1,
      limit: 10,
      totalprice: 0,
      serviceType: '',
      hospitalInfo: {},
      reportType: '',
      //报告类型
      reportTypeList: [],
      //报告类型数组
      addressId: '',
      //地址id
      address: '',
      //地址位置
      exclusiveTypeList: [],
      drugsType: '',
      //药物类型
      drugsName: '',
      //药物名称
      drugsTypeshow: false,
      drugsTypeshowList: []
    };
  },
  onLoad: function onLoad(option) {
    if (option.orderTakingUserName && option.orderTakingUserId) {
      this.orderTakingUserName = option.orderTakingUserName;
      this.orderTakingUserId = option.orderTakingUserId;
    }
    this.czSel = this.$queue.getData('czSel');
    this.serviceType = option.serviceType;
    if (this.serviceType == 5) {
      this.getreportType();
    }
    if (this.serviceType == 6) {
      this.getzxgs();
    }
    if (this.serviceType == 7) {
      this.getdrugsType();
    }
    if (option.hospitalId) {
      this.hospitalName = option.hospitalName;
      this.hospitalId = option.hospitalId;
      this.hospitalIsTrue = true;
      this.getyiyuanInfo(); //获取医院详情
    } else {
      this.getyiyuan();
      this.hospitalIsTrue = false;
    }
    this.serviceId = option.serviceId;
    uni.removeStorageSync('jiuzhenlist');
    this.getDet();
    this.getMyList();
    this.openLists = [{
      image: '/static/images/icon_weixin.png',
      text: '微信',
      id: 2
    }, {
      image: '/static/images/lingqian.png',
      text: '零钱',
      id: 3
    }];
    this.openWay = 2;
  },
  destryed: function destryed() {
    // uni.removeStorageSync('jiuzhenlist')
  },
  onShow: function onShow() {
    var that = this;
    uni.$on('peizhenPeople', function (data) {
      // console.log(data, 'sssssssss')
      that.orderTakingUserId = data.orderTakingUserId;
      that.orderTakingUserName = data.orderTakingUserName;
    });
    uni.$on('jiuzhenlist', function (data) {
      // console.log(data, 'sssssssss')
      that.patientId = data.patientId;
      that.patientName = data.realName;
      if (data.emergencyPhone) {
        that.emergencyPhone = data.emergencyPhone;
      } else {
        that.emergencyPhone = '';
      }
    });
    // this.jiuzhenlist = uni.getStorageSync('jiuzhenlist')
    // this.patientId = this.jiuzhenlist.patientId
    // this.patientName = this.jiuzhenlist.realName
    // if(this.jiuzhenlist.emergencyPhone){
    // 	this.emergencyPhone = this.jiuzhenlist.emergencyPhone
    // }
    uni.$on('xzks', function (data) {
      that.departmentId = data.departmentId;
      that.departmentName = data.departmentName;
    });
    this.getxy();
    uni.$on('address', function (data) {
      that.addressId = data.addressId;
      that.address = data.province + '' + data.city + '' + data.district + ' ' + data.address;
    });
  },
  methods: {
    uploudImg: function uploudImg() {
      var that = this;
      //选择图片
      uni.chooseImage({
        sourceType: ['camera', 'album'],
        sizeType: 'compressed',
        count: 1,
        success: function success(res) {
          //循环上传图片
          for (var i = 0; i < res.tempFilePaths.length; i++) {
            that.$queue.showLoading("上传中...");
            uni.uploadFile({
              // 上传接口
              url: _config.default.APIHOST1 + '/alioss/upload',
              //真实的接口地址
              // url: 'https://peizhensf.xianmaxiong.com/sqx_fast/alioss/upload',
              filePath: res.tempFilePaths[i],
              name: 'file',
              success: function success(uploadFileRes) {
                that.imgRemarks.push(JSON.parse(uploadFileRes.data).data);
                uni.hideLoading();
              }
            });
          }
        }
      });
    },
    closeImg: function closeImg(index) {
      this.imgRemarks.splice(index, 1);
    },
    //选择护工
    getPeopList: function getPeopList() {
      uni.navigateTo({
        url: '/my/peizhenPeople/peizhenPeople'
      });
    },
    hospitalInfock: function hospitalInfock(hospitalId) {
      uni.navigateTo({
        url: '/my/hospital/hospital?hospitalId=' + hospitalId
      });
    },
    getyhqt: function getyhqt() {
      if (this.youhuiList.length == 0) {
        uni.showToast({
          title: '暂无可用优惠券',
          icon: 'none'
        });
      } else {
        this.showYouhuijuan = true;
      }
    },
    gotoNav: function gotoNav(item) {
      uni.navigateTo({
        url: '/my/yxpeizhen/info?data=' + encodeURIComponent(JSON.stringify(item)) + '&type=1'
      });
    },
    confirmywu: function confirmywu(e) {
      this.drugsType = e[0].value;
    },
    //获取药物类型
    getdrugsType: function getdrugsType() {
      var _this = this;
      var data = {
        type: '药物类型'
      };
      this.$Request.get("/app/dict/selectDictList", data).then(function (res) {
        if (res.code == 0) {
          _this.drugsTypeshowList = res.data;
        }
      });
    },
    click: function click(index) {
      this.exclusiveType = this.exclusiveTypeList[index].text;
    },
    //专享归属
    getzxgs: function getzxgs() {
      var _this2 = this;
      var data = {
        type: '专享归属'
      };
      this.$Request.get("/app/dict/selectDictList", data).then(function (res) {
        if (res.code == 0) {
          res.data.map(function (item) {
            item.text = item.value;
          });
          _this2.exclusiveTypeList = res.data;
        }
      });
    },
    //去地址管理
    gotoAddress: function gotoAddress() {
      uni.navigateTo({
        url: '/my/address/address?type=1'
      });
    },
    confirmtype: function confirmtype(e) {
      this.reportType = e[0].value;
    },
    //获取报告类型
    getreportType: function getreportType() {
      var _this3 = this;
      var data = {
        type: '报告类型'
      };
      this.$Request.get("/app/dict/selectDictList", data).then(function (res) {
        if (res.code == 0) {
          _this3.reportTypeList = res.data;
        }
      });
    },
    checkboxChange: function checkboxChange() {
      console.log(this.checked);
      if (this.checked == false) {
        this.showxy = true;
      } else {
        this.showxy = false;
      }
    },
    //协议
    getxy: function getxy() {
      var _this4 = this;
      this.$Request.get('/app/common/type/313').then(function (res) {
        if (res.code == 0) {
          _this4.content = res.data.value;
        }
      });
    },
    //服务协议
    gotoxieyi: function gotoxieyi() {
      uni.navigateTo({
        url: '/my/xieyi/xieyi?type=1'
      });
    },
    //医院详情页面
    gotohospital: function gotohospital() {
      uni.navigateTo({
        url: '/my/hospital/hospital?hospitalId=' + this.hospitalId
      });
    },
    //医院详情
    getyiyuanInfo: function getyiyuanInfo() {
      var _this5 = this;
      var data = {
        hospitalId: this.hospitalId
      };
      this.$Request.getT('/app/hospital/getHospitalInfo', data).then(function (res) {
        if (res.code == 0) {
          _this5.hospitalInfo = res.data;
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'error'
          });
        }
      });
    },
    getyy: function getyy() {
      console.log(this.hospitalList);
      if (this.hospitalList.length == 0) {
        uni.showToast({
          title: '该城市暂无入驻医院，请选择其他城市',
          icon: 'none'
        });
      } else {
        this.shows = true;
      }
    },
    valChange: function valChange(e) {
      // console.log('当前值为: ' + e.value)
      if (e.value == 0) {
        uni.showToast({
          title: '服务天数不能低于1天',
          icon: 'none'
        });
        return;
      } else {
        if (!this.couponId) {
          this.totalprice = parseFloat(this.price * e.value).toFixed(2);
        } else {
          var totalprice = parseFloat(this.price * e.value).toFixed(2);
          if (parseFloat(totalprice) > parseFloat(this.couponName)) {
            this.totalprice = parseFloat(totalprice - this.couponName).toFixed(2);
          } else {
            this.totalprice = 0.01;
          }
        }
      }
    },
    qingkong: function qingkong() {
      this.couponId = '';
      this.couponName = '';
      // alert(this.price+'-'+this.serviceNum)
      this.totalprice = parseFloat(this.price * this.serviceNum).toFixed(2);
      this.showYouhuijuan = false;
    },
    youhuiPay: function youhuiPay(e) {
      var price;
      if (this.serviceNum != 0) {
        price = this.price * this.serviceNum;
      } else {
        price = this.price;
      }
      console.log(price, price >= e.minMoney);
      // return
      if (Number(price) >= Number(e.minMoney)) {
        // let totalprice = parseFloat(this.order.money * this.number).toFixed(0)
        if (parseFloat(price) > parseFloat(e.money)) {
          this.totalprice = parseFloat(price - e.money).toFixed(2);
        } else {
          this.totalprice = 0.01;
        }
        console.log(this.totalprice, parseFloat(price - e.money));
      } else {
        uni.showToast({
          title: '使用优惠劵，下单金额必须大于' + e.minMoney + '元',
          icon: 'none'
        });
        return;
      }
      this.youhuiMoney = e.money;
      this.openWay = 3;
      this.couponId = e.id;
      this.couponName = e.money;
      this.showYouhuijuan = false;
    },
    getMyList: function getMyList() {
      var _this6 = this;
      var data = {
        status: 0
      };
      this.$Request.getT('/app/couponUser/getMyCouponList', data).then(function (res) {
        if (res.code == 0) {
          _this6.youhuiList = res.data.records;
        }
      });
    },
    //就诊人
    totojzr: function totojzr() {
      uni.navigateTo({
        url: '/my/other/car?isSelect=ok'
      });
    },
    selectWay: function selectWay(id) {
      this.openWay = id;
    },
    //获取当前系统时间
    getNowTime: function getNowTime(tempminit) {
      if (!tempminit) {
        tempminit = 0;
      }
      var date = new Date();
      date.setMinutes(date.getMinutes() - tempminit);
      var year = date.getFullYear(),
        month = date.getMonth() + 1,
        day = date.getDate(),
        hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours(),
        minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes(),
        second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      month >= 1 && month <= 9 ? month = "0" + month : "";
      day >= 0 && day <= 9 ? day = "0" + day : "";
      var timer = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
      /* console.log(timer); */
      return timer;
    },
    //比较时间大小
    dateCompare: function dateCompare(startStr, endStr) {
      var d1, d2, s, arr, arr1, arr2;
      if (startStr.length > 10) {
        arr = startStr.split(" ");
        arr1 = arr[0].split("-");
        arr2 = arr[1].split(":");
        d1 = new Date(arr1[0], arr1[1] - 1, arr1[2], arr2[0], arr2[1], arr2[2]);
      } else {
        arr = startStr.split("-");
        d1 = new Date(arr[0], arr[1], arr[2]);
      }
      if (endStr.length > 10) {
        arr = endStr.split(" ");
        arr1 = arr[0].split("-");
        arr2 = arr[1].split(":");
        d2 = new Date(arr1[0], arr1[1] - 1, arr1[2], arr2[0], arr2[1], arr2[2]);
      } else {
        arr = endStr.split("-");
        d2 = new Date(arr[0], arr[1], arr[2]);
      }
      s = d2 - d1;
      if (s < 0) {
        return false;
      }
      return true;
    },
    statusChange: function statusChange(e) {
      console.log(e);
      //选中的时间
      var hopeTimes = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + ':00:00';
      //获取当前时间
      var tadayDate = this.getNowTime();
      var flag = this.dateCompare(tadayDate, hopeTimes);
      if (flag == true) {
        //开始时间小于当前时间
        this.hopeTimes = hopeTimes;
        this.hopeTime = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + '时';
      } else {
        uni.showToast({
          title: '期望就诊时间必须大于当前时间',
          icon: "none"
        });
        return;
      }
    },
    // 详情
    getDet: function getDet() {
      var _this7 = this;
      this.$Request.get("/app/hospitalEmploy/getHospitalEmployInfo", {
        serviceId: this.serviceId
      }).then(function (res) {
        if (res.code == 0) {
          _this7.order = res.data;
          if (_this7.order.tags) {
            _this7.order.tags = res.data.tags.split(',');
          }
          _this7.price = res.data.money;
          _this7.totalprice = res.data.money.toFixed(2);
          _this7.loading = false;
        } else {
          uni.showToast({
            title: res.msg,
            duration: 1000,
            icon: 'none'
          });
        }
      });
    },
    getyiyuan: function getyiyuan() {
      var _this8 = this;
      //获取医院列表
      var data = {
        city: uni.getStorageSync('city')
      };
      this.$Request.get('/app/hospital/getHospitalList', data).then(function (res) {
        if (res.code == 0) {
          var dictist = res.data.records;
          var arr = [];
          for (var i = 0; i < dictist.length; i++) {
            var data = {};
            data.label = dictist[i].hospitalName;
            data.value = dictist[i].hospitalId;
            arr.push(data);
          }
          _this8.hospitalList = arr;
        }
      });
    },
    selConfirm: function selConfirm(e) {
      console.log(e, '--------', e[0].label);
      // this.relationship = e[0].label
      this.hospitalId = e[0].value;
      this.hospitalName = e[0].label;
    },
    openKeshi: function openKeshi() {
      if (this.hospitalId) {
        uni.navigateTo({
          url: '/my/keshi/index?hospitalId=' + this.hospitalId
        });
      } else {
        uni.showToast({
          title: '请选择医院',
          icon: 'none'
        });
      }
    },
    openPay: function openPay() {
      if (!this.hospitalName) {
        uni.showToast({
          title: '请选择医院',
          icon: 'none',
          duration: 1000
        });
        return;
      }
      // if(!this.orderTakingUserName){
      // 	uni.showToast({
      // 		title: '请选择陪诊员',
      // 		icon: 'none',
      // 		duration: 1000
      // 	})
      // 	return
      // }
      // console.log(this.serviceType,'this.serviceTypethis.serviceType')
      if (this.serviceType == 1) {
        if (!this.patientName) {
          uni.showToast({
            title: '请选择就诊人',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.hopeTime) {
          uni.showToast({
            title: '请选择期望就诊时间',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.departmentName) {
          uni.showToast({
            title: '请选择科室',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.addressId) {
          uni.showToast({
            title: '请选择接送地址',
            icon: 'none',
            duration: 1000
          });
          return;
        }
      }
      if (this.serviceType == 2 || this.serviceType == 3 || this.serviceType == 4) {
        if (!this.patientName) {
          uni.showToast({
            title: '请选择就诊人',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.hopeTime) {
          uni.showToast({
            title: '请选择期望就诊时间',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.departmentName) {
          uni.showToast({
            title: '请选择科室',
            icon: 'none',
            duration: 1000
          });
          return;
        }
      }
      if (this.serviceType == 5) {
        if (!this.patientName) {
          uni.showToast({
            title: '请选择就诊人',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.hopeTime) {
          uni.showToast({
            title: '请选择期望处理时间',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.addressId) {
          uni.showToast({
            title: '请选择收件信息',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.reportType) {
          uni.showToast({
            title: '请选择报告类型',
            icon: 'none',
            duration: 1000
          });
          return;
        }
      }
      if (this.serviceType == 6) {
        if (!this.patientName) {
          uni.showToast({
            title: '请选择就诊人',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.hopeTime) {
          uni.showToast({
            title: '请选择期望就诊时间',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.departmentName) {
          uni.showToast({
            title: '请选择科室',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.exclusiveType) {
          uni.showToast({
            title: '请选择专享归属',
            icon: 'none',
            duration: 1000
          });
          return;
        }
      }
      if (this.serviceType == 7) {
        if (!this.patientName) {
          uni.showToast({
            title: '请选择就诊人',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.drugsType) {
          uni.showToast({
            title: '请选择药物类型',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.drugsName) {
          uni.showToast({
            title: '请准确输入药物名称',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.hopeTime) {
          uni.showToast({
            title: '请选择期望处理时间',
            icon: 'none',
            duration: 1000
          });
          return;
        }
        if (!this.addressId) {
          uni.showToast({
            title: '请选择收件信息',
            icon: 'none',
            duration: 1000
          });
          return;
        }
      }
      if (this.checked == false) {
        uni.showToast({
          title: '请勾选服务条款同意书',
          icon: 'none',
          duration: 1000
        });
        return;
      }
      this.showPay = true;
    },
    pays: function pays() {
      var _this9 = this;
      if (this.isPay == false) {
        return;
      }
      var that = this;
      this.isPay = false;
      that.$Request.get("/app/orders/generateOrder", {
        serviceId: that.serviceId,
        type: 1,
        hospitalId: that.hospitalId,
        patientId: that.patientId,
        hopeTime: that.hopeTimes,
        departmentId: that.departmentId,
        remarks: that.remarks,
        phone: that.phone,
        serviceNum: that.serviceNum,
        couponId: that.couponId,
        addressId: that.addressId,
        reportType: that.reportType,
        exclusiveType: that.exclusiveType,
        drugsType: that.drugsType,
        drugsName: that.drugsName,
        serviceType: that.serviceType,
        orderType: 1,
        orderTakingUserId: that.orderTakingUserId,
        imgRemarks: that.imgRemarks.length > 0 ? that.imgRemarks.join(',') : ''
      }).then(function (res) {
        if (res.code == 0) {
          that.showPay = false;
          uni.showModal({
            title: '付款提示',
            content: '确认支付' + that.totalprice + '元吗?',
            complete: function complete(re) {
              if (re.confirm) {
                var classify = 1;
                uni.showLoading({
                  title: '加载中...'
                });
                if (that.openWay == 2) {
                  //微信

                  that.$Request.post("/app/wxPay/wxPayOrder", {
                    orderId: res.data.ordersId,
                    classify: 3
                  }).then(function (red) {
                    if (red.code == 0) {
                      uni.requestPayment({
                        provider: 'wxpay',
                        timeStamp: red.data.timestamp,
                        nonceStr: red.data.noncestr,
                        package: red.data.package,
                        signType: red.data.signType,
                        paySign: red.data.sign,
                        success: function success(redd) {
                          uni.hideLoading();
                          uni.showToast({
                            title: '支付成功'
                          });
                          that.isPay = true;
                          setTimeout(function () {
                            uni.switchTab({
                              url: '/pages/order/index'
                            });
                          }, 1000);
                          uni.removeStorageSync('carlist');
                        },
                        fail: function fail(err) {
                          uni.hideLoading();
                          that.isPay = true;
                          that.$queue.showToast('支付失败');
                        }
                      });
                    } else {
                      uni.showToast({
                        title: red.msg,
                        icon: 'none'
                      });
                    }
                  });
                } else if (that.openWay == 1) {//支付宝
                } else if (that.openWay == 3) {
                  //零钱
                  that.$Request.post("/app/orders/payMoney", {
                    ordersId: res.data.ordersId
                  }).then(function (res) {
                    if (res.code == 0) {
                      uni.showToast({
                        title: '支付成功'
                      });
                      that.isPay = true;
                      setTimeout(function () {
                        uni.switchTab({
                          url: '/pages/order/index'
                        });
                      }, 1000);
                      uni.removeStorageSync('EditAddress');
                    } else {
                      that.isPay = true;
                      uni.showToast({
                        title: res.msg,
                        icon: 'none'
                      });
                    }
                  });
                }
              } else {
                uni.hideLoading();
                uni.showToast({
                  title: '已取消',
                  icon: 'none'
                });
                that.isPay = true;
                setTimeout(function () {
                  uni.switchTab({
                    url: '/pages/order/index'
                  });
                }, 1000);
              }
            }
          });
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          });
          _this9.isPay = true;
        }
      });
    },
    pay: function pay() {
      var that = this;
      that.pays();
    },
    callPay: function callPay(response) {
      if (typeof WeixinJSBridge === "undefined") {
        if (document.addEventListener) {
          document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);
        } else if (document.attachEvent) {
          document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));
          document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));
        }
      } else {
        this.onBridgeReady(response);
      }
    },
    onBridgeReady: function onBridgeReady(response) {
      var that = this;
      if (!response.package) {
        return;
      }
      WeixinJSBridge.invoke('getBrandWCPayRequest', {
        "appId": response.appid,
        //公众号名称，由商户传入
        "timeStamp": response.timestamp,
        //时间戳，自1970年以来的秒数
        "nonceStr": response.noncestr,
        //随机串
        "package": response.package,
        "signType": response.signType,
        //微信签名方式：
        "paySign": response.sign //微信签名
      }, function (res) {
        if (res.err_msg === "get_brand_wcpay_request:ok") {
          // 使用以上方式判断前端返回,微信团队郑重提示：
          //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          uni.hideLoading();
          uni.showToast({
            title: '支付成功'
          });
          this.isPay = true;
          setTimeout(function () {
            uni.switchTab({
              url: '/pages/order/index'
            });
          }, 1000);
          uni.removeStorageSync('carlist');
        } else {
          this.isPay = true;
          uni.hideLoading();
        }
        WeixinJSBridge.log(response.err_msg);
      });
    },
    setPayment: function setPayment(name, order) {
      var that = this;
      uni.requestPayment({
        provider: name,
        orderInfo: order,
        //微信、支付宝订单数据
        success: function success(res) {
          uni.hideLoading();
          uni.showToast({
            title: '支付成功'
          });
          that.isPay = true;
          setTimeout(function () {
            uni.switchTab({
              url: '/pages/order/index'
            });
          }, 1000);
          uni.removeStorageSync('carlist');
        },
        fail: function fail(err) {
          that.isPay = true;
          uni.hideLoading();
          console.log(err, 12);
        }
      });
    }
  },
  // computed: {
  // 	price() {
  // 		let price = this.isVip ? this.order.memberMoney : this.order.money
  // 		console.log(price * 1 * this.value)
  // 		return (price * this.value).toFixed(2)
  // 	}
  // }
  onReachBottom: function onReachBottom() {
    this.page = this.page + 1;
    // this.getWaterlist()
    this.getMyList();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.page = 1;
    // this.getWaterlist()
    this.getMyList();
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 98:
/*!******************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?vue&type=style&index=0&lang=css& ***!
  \******************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDet.vue?vue&type=style&index=0&lang=css& */ 99);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDet_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 99:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/game/orderDet.vue?vue&type=style&index=0&lang=css& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[92,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/index/game/orderDet.js.map