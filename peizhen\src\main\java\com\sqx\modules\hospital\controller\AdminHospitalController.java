package com.sqx.modules.hospital.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.common.utils.Result;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.service.HospitalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 医院管理-管理端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@RestController
@Api(value = "医院管理-管理端", tags = {"医院管理"})
@RequestMapping(value = "/admin/hospital/")
public class AdminHospitalController {
    @Autowired
    private HospitalService hospitalService;

    /**
     * 添加医院
     * @param hospital
     * @return
     */
    @ApiOperation("添加医院")
    @PostMapping("addHospital")
    public Result addHospital(Hospital hospital) {
        return hospitalService.addHospital(hospital);
    }

    /**
     * 修改医院
     * @param hospital
     * @return
     */
    @ApiOperation("修改医院")
    @PostMapping("updateHospital")
    public Result updateHospital(Hospital hospital) {
        return hospitalService.updateHospital(hospital);
    }

    /**
     * 获取医院列表
     * @param page
     * @param limit
     * @param hospital
     * @return
     */
    @ApiOperation("获取医院列表")
    @GetMapping("getHospitalList")
    public Result getHospitalList(Integer page, Integer limit, Hospital hospital) {
        return Result.success().put("data", hospitalService.getHospitalList(page, limit, hospital));
    }

    /**
     * 根据医院id查询医院详情
     * @param hospitalId 科室id
     * @return
     */
    @ApiOperation("根据医院id查询医院详情")
    @GetMapping("getHospitalInfo")
    public Result getHospitalInfo(Long hospitalId) {
        return Result.success().put("data", hospitalService.getHospitalInfo(hospitalId));
    }

    /**
     * 根据id删除医院
     * @param hospitalId 科室id
     * @return
     */
    @ApiOperation("根据id删除医院")
    @GetMapping("deleteHospital")
    public Result deleteHospital(Long hospitalId){
        return hospitalService.deleteHospital(hospitalId);
    }

    /**
     * 获取所有医院列表（不分页）
     * @return
     */
    @ApiOperation("获取所有医院列表")
    @GetMapping("selectHospitalList")
    public Result selectHospitalList() {
        List<Hospital> hospitalList = hospitalService.list(
            new QueryWrapper<Hospital>()
                .eq("is_enable", 1)
                .orderByAsc("sort")
        );
        return Result.success().put("data", hospitalList);
    }

    /**
     * 获取医院及医院订单
     * @return
     */
    @ApiOperation("获取医院及医院订单")
    @GetMapping("getHospitalOrderCount")
    public Result getHospitalOrderCount() {
        return Result.success().put("data", hospitalService.getHospitalOrderCount(null,null,null,null));
    }
}

