package com.sqx.modules.app.controller.app;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.annotation.LoginUser;
import com.sqx.modules.app.entity.Address;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.AddressService;
import com.sqx.modules.app.service.AppService;
import com.sqx.modules.app.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 地址
 */
@RestController
@RequestMapping("/app/address")
@Api(value = "用户地址", tags = {"用户地址"})
public class AppAddressController {

    @Autowired
    private AddressService addressService;

    @Login
    @PostMapping("/insertAddress")
    @ApiOperation("添加地址")
    public Result insertAddress(@LoginUser UserEntity user,@RequestBody Address address){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        address.setCreateTime(sdf.format(new Date()));
        if(address.getIsDefault()==1){
            addressService.updateAddressIsDefault(user.getUserId());
        }
        address.setUserId(user.getUserId());
        addressService.save(address);
        return Result.success();
    }

    @Login
    @PostMapping("/updateAddress")
    @ApiOperation("修改地址")
    public Result updateAddress(@LoginUser UserEntity user,@RequestBody Address address){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        address.setCreateTime(sdf.format(new Date()));
        if(address.getIsDefault()==1){
            addressService.updateAddressIsDefault(user.getUserId());
        }
        address.setUserId(user.getUserId());
        addressService.updateById(address);
        return Result.success();
    }

    @Login
    @PostMapping("/deleteAddress")
    @ApiOperation("删除我的地址")
    public Result deleteAddress(Long addressId){
        addressService.removeById(addressId);
        return Result.success();
    }


    @Login
    @GetMapping("/selectAddressListById")
    @ApiOperation("获取我的所有地址")
    public Result selectAddressListById(@LoginUser UserEntity user,Integer page,Integer limit){
        IPage<Address> addressIPage = addressService.page(new Page<>(page, limit), new QueryWrapper<Address>().eq("user_id", user.getUserId()));
        return Result.success().put("data",new PageUtils(addressIPage));
    }

    @Login
    @GetMapping("/selectAddressById")
    @ApiOperation("获取我的默认地址")
    public Result selectAddressById(@LoginUser UserEntity user){
        Address one = addressService.getOne(new QueryWrapper<Address>().eq("user_id", user.getUserId()).orderByDesc("is_default").last(" limit 1"));
        return Result.success().put("data",one);
    }


    @Login
    @GetMapping("selectAddressByAddressId")
    @ApiOperation("根据地址id查询地址详细信息")
    public Result selectAddressByAddressId(Long addressId){
        return Result.success().put("data",addressService.getById(addressId));
    }









}
