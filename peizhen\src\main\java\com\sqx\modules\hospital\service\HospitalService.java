package com.sqx.modules.hospital.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.entity.LetterCity;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
public interface HospitalService extends IService<Hospital> {

    Result addHospital(Hospital hospital);

    Result updateHospital(Hospital hospital);

    IPage<Hospital> getHospitalList(Integer page,Integer limit,Hospital hospital);

    Hospital getHospitalInfo(Long hospitalId);

    Result deleteHospital(Long hospitalId);

    List<LetterCity> getCityList();

    List<Hospital> getHospitalOrderCount(String riderPhone, String hospitalName, String city, String ordersNo);
}
