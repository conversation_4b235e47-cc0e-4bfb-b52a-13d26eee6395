package com.sqx.modules.evaluate.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.evaluate.entity.SysEvaluate;
import com.sqx.modules.evaluate.service.SysEvaluateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "评分", tags = {"管理端-评分"})
@RequestMapping("/admin/evaluate/")
public class AdminSysEvaluateController {
    @Autowired
    private SysEvaluateService evaluateService;

    @ApiOperation("获取用户评价列表")
    @GetMapping("getUserEvaluate")
    public Result getUserEvaluateList(Integer page, Integer limit, SysEvaluate evaluate) {
        return Result.success().put("data", evaluateService.getUserEvaluateList(page, limit, evaluate));
    }

    @ApiOperation("删除评论")
    @GetMapping("deleteEvaluate")
    public Result deleteEvaluate(Long evaluateId) {
        evaluateService.removeById(evaluateId);
        return Result.success();
    }
}

