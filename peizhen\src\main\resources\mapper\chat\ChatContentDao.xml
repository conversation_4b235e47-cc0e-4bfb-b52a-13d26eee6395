<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.chat.dao.ChatContentDao">


    <select id="selectChatContentPage" resultType="Map">
        select c.chat_content_id as chatContentId, c.chat_conversation_id as chatConversationId,
        c.user_id as userId,u.user_name as userName,u.avatar,c.content,c.message_type as messageType,
        c.create_time as createTime,c.width,c.height
        from chat_content c
        left join tb_user u on u.user_id=c.user_id
        where c.chat_conversation_id=#{chatConversationId}
        <if test="content!=null and content!=''">
            and c.content like CONCAT('%',#{content},'%')
        </if>
        order by c.create_time desc
    </select>

    <select id="selectChatContentCountByWx" resultType="Map">
        select *
        from (
                 SELECT c.user_id                     as userId,
                        any_value(cc.user_id)         as ccUserId,
                        any_value(cc.focused_user_id) as ccFocusedUserId,
                        count(*)                      as counts,
                        c.chat_conversation_id        as chatConversationId
                 FROM chat_content c
                          LEFT JOIN chat_conversation cc on c.chat_conversation_id = cc.chat_conversation_id
                 WHERE c.`status` = 0
                   AND now() >= DATE_ADD(c.create_time, INTERVAL #{minute} MINUTE)
                   AND (cc.is_wx_msg IS NULL OR cc.is_wx_msg = 0)
                 GROUP BY c.user_id, c.chat_conversation_id
             ) a
        where counts >= #{counts}
    </select>

    <select id="selectChatContentCountByDx" resultType="Map">
        select *
        from (
                 SELECT c.user_id                     as userId,
                        any_value(cc.user_id)         as ccUserId,
                        any_value(cc.focused_user_id) as ccFocusedUserId,
                        count(*)                      as counts,
                        c.chat_conversation_id        as chatConversationId
                 FROM chat_content c
                          LEFT JOIN chat_conversation cc on c.chat_conversation_id = cc.chat_conversation_id
                 WHERE c.`status` = 0
                   AND now() >= DATE_ADD(c.create_time, INTERVAL #{minute} MINUTE)
                   AND (cc.is_send_msg IS NULL OR cc.is_send_msg = 0)
                 GROUP BY c.user_id, c.chat_conversation_id
             ) a
        where counts >= #{counts}
    </select>

    <update id="updateChatContentStatusByUserIdAndChatId">
        update chat_content
        set status=1
        where user_id!=#{userId}
          and chat_conversation_id=#{chatConversationId}
    </update>

    <select id="selectChatCount" resultType="Integer">
        select count(*)
        from chat_content
        where status = 0
          and user_id!=#{userId}
          and chat_conversation_id in (select chat_conversation_id from chat_conversation where user_id=#{userId}
           or focused_user_id=#{userId})
    </select>


</mapper>