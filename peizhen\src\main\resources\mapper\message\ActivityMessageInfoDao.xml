<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.message.dao.ActivityMessageInfoDao">


    <select id="find" resultType="com.sqx.modules.message.entity.ActivityMessageInfo">
        select * from activity_message_info where state=#{state} order by id desc
    </select>

    <select id="findType" resultType="com.sqx.modules.message.entity.ActivityMessageInfo">
        select * from activity_message_info where type=#{type} order by id desc
    </select>


    <select id="findTypeByUserId" resultType="com.sqx.modules.message.entity.ActivityMessageInfo">
        select * from activity_message_info where state=#{state} and user_id=#{userId} order by id desc
    </select>

    <update id="updateState">
          update activity_message_info s set s.state=#{state} where s.id=#{id}
    </update>

    <update id="updateSendState">
          update activity_message_info s set s.send_state=#{state} where s.id=#{id}
    </update>


</mapper>