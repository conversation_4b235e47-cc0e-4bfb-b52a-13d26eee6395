package com.sqx.modules.patientInfo.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Mapper
public interface PatientInfoDao extends BaseMapper<PatientInfo> {

    IPage<PatientInfo> getPatientList(@Param("pages") Page<PatientInfo> pages, @Param("patientInfo") PatientInfo patientInfo);
}
