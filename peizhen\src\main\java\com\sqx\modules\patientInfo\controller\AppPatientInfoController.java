package com.sqx.modules.patientInfo.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import com.sqx.modules.patientInfo.service.PatientInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(value = "就诊人信息-用户端", tags = {"就诊人信息-用户端"})
@RequestMapping("/app/patientInfo/")
public class AppPatientInfoController {
    @Autowired
    private PatientInfoService patientInfoService;

    @Login
    @ApiOperation("添加或修改就诊人信息")
    @PostMapping("savePatient")
    public Result savePatient(@RequestAttribute("userId") Long userId, PatientInfo patientInfo) {
        patientInfo.setUserId(userId);
        return patientInfoService.savePatient(patientInfo);
    }

    @Login
    @ApiOperation("获取当前登录人的就诊人列表")
    @GetMapping("getPatientList")
    public Result getPatientList(@RequestAttribute("userId") Long userId, Integer page, Integer limit, PatientInfo patientInfo) {
        patientInfo.setUserId(userId);
        return Result.success().put("data", patientInfoService.getPatientList(page, limit, patientInfo));
    }

    @Login
    @ApiOperation("获取就诊人信息")
    @GetMapping("getPatientInfo")
    public Result getPatientInfo(Long patientId) {
        return Result.success().put("data", patientInfoService.getPatientInfo(patientId));
    }

    @Login
    @ApiOperation("删除就诊人")
    @GetMapping("deletePatient")
    public Result deletePatient(@RequestAttribute("userId") Long userId ,Long patientId) {
        return  patientInfoService.deletePatient(userId,patientId);
    }
}

