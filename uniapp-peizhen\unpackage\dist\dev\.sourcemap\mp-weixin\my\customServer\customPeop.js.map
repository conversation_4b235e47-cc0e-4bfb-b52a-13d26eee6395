{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeop.vue?57ed", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeop.vue?20a3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeop.vue?4c61", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeop.vue?fac2", "uni-app:///my/customServer/customPeop.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeop.vue?f3a4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeop.vue?c853"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "status", "iconType", "loadText", "loadmore", "loading", "nomore", "value", "count", "sex", "hage", "age", "options1", "label", "options2", "options3", "people", "page", "limit", "total", "sexs", "workMin", "workMax", "age<PERSON>in", "ageMax", "typeInfo", "keyword", "onLoad", "methods", "searchPeople", "goInfo", "uni", "title", "content", "success", "console", "url", "stringify", "goBack", "icon", "getpeopleList", "city", "authentication", "realName", "sexFunction", "gethl", "type", "getnl", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC4F7vB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAN;MACA,GACA;QACAM;QACAN;MACA,GACA;QACAM;QACAN;MACA,EACA;MACAO;QACAD;QACAN;MACA,GACA;QACAM;QACAN;MACA,EACA;MAAA;MACAQ;QACAF;QACAN;MACA,GACA;QACAM;QACAN;MACA,EACA;MAAA;MACAS;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IAEA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACAC;UACAC;UACAC;UACAC;YACA;cACAC;cACAJ;gBACAK;cACA;YACA;cACAD;YACA;UACA;QACA;MACA;QACAJ;UACAK,wFACAC;QACA;MACA;IACA;IACA;IACAC;MACA;QAAA;QACAP;UACAK,oFACAC;QACA;MACA;QACAN;UACAC;UACAO;QACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAvB;QACAC;QACAT;QACAY;QACAC;QACAC;QACAC;QACAiB;QACAC;QACAC;MACA;MACA;QACAZ;QACA;UACA;UACA;YACA;UACA;YACA;UACA;QAEA;MACA;IACA;IACA;IACAa;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;MACAT;MACA;MACA;MACA;QAAA;QACA;QACA;MACA;QACA;QACA;MACA;MACAA;MACAA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAGAU;MAAA;MACA;QACAC;MACA;MAEA;QACA;UACA;UACA;UACAX;QACA;MACA;IACA;IACAY;MAAA;MACA;QACAD;MACA;MAEA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;;EACAE;IACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpTA;AAAA;AAAA;AAAA;AAA43C,CAAgB,iuCAAG,EAAC,C;;;;;;;;;;;ACAh5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/customServer/customPeop.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/customServer/customPeop.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./customPeop.vue?vue&type=template&id=8900eca6&\"\nvar renderjs\nimport script from \"./customPeop.vue?vue&type=script&lang=js&\"\nexport * from \"./customPeop.vue?vue&type=script&lang=js&\"\nimport style0 from \"./customPeop.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/customServer/customPeop.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeop.vue?vue&type=template&id=8900eca6&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-search/u-search\" */ \"@/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uDropdown: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-dropdown/u-dropdown\" */ \"@/uview-ui/components/u-dropdown/u-dropdown.vue\"\n      )\n    },\n    uDropdownItem: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-dropdown-item/u-dropdown-item\" */ \"@/uview-ui/components/u-dropdown-item/u-dropdown-item.vue\"\n      )\n    },\n    uRate: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-rate/u-rate\" */ \"@/uview-ui/components/u-rate/u-rate.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-loadmore/u-loadmore\" */ \"@/uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.people.length\n  var g1 = _vm.people.length\n  var g2 = g1 > 0 ? _vm.people.length : null\n  var g3 = _vm.people.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeop.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeop.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 100rpx;\" :style=\"people.length==0?'padding-top: 200rpx;':''\">\r\n\t\t<!-- 搜索 -->\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<view class=\"search flex justify-center align-center\" style=\"top: 80rpx;\">\r\n\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t<view class=\"search flex justify-center align-center\">\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t<u-search placeholder=\"搜索护理员\" @clear=\"searchPeople\" @search=\"searchPeople\" @custom=\"searchPeople\"\r\n\t\t\t\t\t\tv-model=\"keyword\"></u-search>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t<!-- 筛选 -->\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view class=\"sx\" style=\"top: 156rpx;\">\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t<view class=\"sx\">\r\n\t\t\t\t<!-- #endif -->\r\n\r\n\t\t\t\t\t<u-dropdown height=\"80rpx\" ref=\"uDropdown\">\r\n\t\t\t\t\t\t<u-dropdown-item @change=\"sexFunction()\" v-model=\"sex\" title=\"性别\"\r\n\t\t\t\t\t\t\t:options=\"options1\"></u-dropdown-item>\r\n\t\t\t\t\t\t<u-dropdown-item @change=\"sexFunction()\" v-model=\"hage\" title=\"护龄\" :options=\"options2\">\r\n\t\t\t\t\t\t</u-dropdown-item>\r\n\t\t\t\t\t\t<u-dropdown-item @change=\"sexFunction()\" v-model=\"age\" title=\"年龄\"\r\n\t\t\t\t\t\t\t:options=\"options3\"></u-dropdown-item>\r\n\t\t\t\t\t</u-dropdown>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 护工列表 -->\r\n\t\t\t\t<view class=\"list flex justify-center flex-wrap\" style=\"margin-top: 180rpx;\" v-if=\"people.length>0\">\r\n\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t<view class=\"list-box-item flex justify-between flex-wrap\" @click=\"goInfo(item.userId)\"\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in people\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"flex align-center\" style=\"height: 100%;\">\r\n\t\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t\t:src=\"item.userCertification && item.userCertification.avatar?item.userCertification.avatar:'../../../static/logo.png'\"\r\n\t\t\t\t\t\t\t\t\tmode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"list-box-item-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex align-center\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.userCertification.name}}\r\n\t\t\t\t\t\t\t\t\t\t<image v-if=\"item.iconImg\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"width: 30rpx;height: 30rpx;margin-left: 20rpx;border-radius: 0;\"\r\n\t\t\t\t\t\t\t\t\t\t\t:src=\"item.iconImg\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.userCertification.sex==2?'女':'男'}} | <text\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"margin: 0 10rpx 0 10rpx;\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"item.userCertification && item.userCertification.age\">{{item.userCertification.age}}岁</text>\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"margin: 0 10rpx 0 10rpx;\" v-else>暂无</text> | <text\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"margin: 0 10rpx 0 10rpx;\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"item.userCertification && item.userCertification.province\">{{item.userCertification.province}}\r\n\t\t\t\t\t\t\t\t\t\t\t{{item.userCertification.city}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"margin: 0 10rpx 0 10rpx;\" v-else>暂无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex align-center\" style=\"line-height: 30rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<u-rate :count=\"count\" v-model=\"value\" active-color=\"#FFAA01\"\r\n\t\t\t\t\t\t\t\t\t\t\tinactive-color=\"#FFAA01\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"margin-left: -5rpx;\"></u-rate>{{item.finalScore}}\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"margin-left: 20rpx;\"\r\n\t\t\t\t\t\t\t\t\t\t\tv-if=\"item.workAge+''\">护龄:{{item.userCertification.workAge}}年</text>\r\n\t\t\t\t\t\t\t\t\t\t<text style=\"margin-left: 20rpx;\" v-else>护龄:暂无</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"list-box-item-status flex justify-center align-center\"\r\n\t\t\t\t\t\t\t\t:style=\"item.workStatus!=0?'background:red;':''\">\r\n\t\t\t\t\t\t\t\t{{item.workStatus==0?'空闲':'忙碌'}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"list-box-item-fw\" v-if=\"item.userCertification\">\r\n\t\t\t\t\t\t\t\t服务项目：{{item.userCertification.serviceName}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"list-box-item-xz flex justify-end align-center\">\r\n\t\t\t\t\t\t<view @click.stop=\"goBack(item)\" class=\"list-box-item-xz-sub\">\r\n\t\t\t\t\t\t\t选择TA\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<u-loadmore v-if=\"people.length>4\" :status=\"status\" :icon-type=\"iconType\" :load-text=\"loadText\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<empty v-if=\"people.length==0\" />\r\n\t\t\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport empty from '@/components/empty.vue'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tempty\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatus: 'loadmore',\r\n\t\t\t\ticonType: 'flower',\r\n\t\t\t\tloadText: {\r\n\t\t\t\t\tloadmore: '轻轻上拉',\r\n\t\t\t\t\tloading: '努力加载中',\r\n\t\t\t\t\tnomore: '实在没有了'\r\n\t\t\t\t},\r\n\t\t\t\tvalue: 1,\r\n\t\t\t\tcount: 1,\r\n\t\t\t\tsex: '不限',\r\n\t\t\t\thage: '不限',\r\n\t\t\t\tage: '不限',\r\n\t\t\t\toptions1: [{\r\n\t\t\t\t\t\tlabel: '不限',\r\n\t\t\t\t\t\tvalue: '不限',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '男',\r\n\t\t\t\t\t\tvalue: '男',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '女',\r\n\t\t\t\t\t\tvalue: '女',\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\toptions2: [{\r\n\t\t\t\t\t\tlabel: '不限',\r\n\t\t\t\t\t\tvalue: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '加冰',\r\n\t\t\t\t\t\tvalue: 2,\r\n\t\t\t\t\t},\r\n\t\t\t\t], //护龄\r\n\t\t\t\toptions3: [{\r\n\t\t\t\t\t\tlabel: '年龄',\r\n\t\t\t\t\t\tvalue: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '加冰',\r\n\t\t\t\t\t\tvalue: 2,\r\n\t\t\t\t\t},\r\n\t\t\t\t], //年龄\r\n\t\t\t\tpeople: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\ttotal: '',\r\n\t\t\t\tsexs: '', // 性别 1男 2女\r\n\t\t\t\tworkMin: '', //护龄最小值\r\n\t\t\t\tworkMax: '', //护龄最大值\r\n\t\t\t\tageMin: '', //年龄最小值\r\n\t\t\t\tageMax: '', //年龄最大值\r\n\t\t\t\ttypeInfo: {},\r\n\t\t\t\tkeyword: '',\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.typeInfo = JSON.parse(decodeURIComponent(option.item))\r\n\t\t\tthis.gethl();\r\n\t\t\tthis.getnl();\r\n\t\t\tthis.getpeopleList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 搜索护理员\r\n\t\t\tsearchPeople() {\r\n\t\t\t\tthis.getpeopleList();\r\n\t\t\t},\r\n\t\t\t//陪珍师详情\r\n\t\t\tgoInfo(userid) {\r\n\t\t\t\tif (!uni.getStorageSync('token')) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/my/customServer/customRem?userId=' + userid + '&info=' + encodeURIComponent(JSON\r\n\t\t\t\t\t\t\t.stringify(this.typeInfo))\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 携带参数跳转\r\n\t\t\tgoBack(item) {\r\n\t\t\t\tif (item.workStatus == 0) { //不忙碌\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/my/peihu/order?hguserId=' + item.userId + '&info=' + encodeURIComponent(JSON\r\n\t\t\t\t\t\t\t.stringify(this.typeInfo))\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择空闲状态的护理员',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// uni.navigateBack()\r\n\t\t\t},\r\n\t\t\t//护工列表\r\n\t\t\tgetpeopleList() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tlimit: this.limit,\r\n\t\t\t\t\tsex: this.sexs,\r\n\t\t\t\t\tworkMin: this.workMin,\r\n\t\t\t\t\tworkMax: this.workMax,\r\n\t\t\t\t\tageMin: this.ageMin,\r\n\t\t\t\t\tageMax: this.ageMax,\r\n\t\t\t\t\tcity: uni.getStorageSync('city'),\r\n\t\t\t\t\tauthentication: 2,\r\n\t\t\t\t\trealName: this.keyword\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/user/getNursingListV5\", data).then(res => {\r\n\t\t\t\t\tuni.stopPullDownRefresh()\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.total = res.data.pages\r\n\t\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\t\tthis.people = res.data.records\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.people = [...this.people, ...res.data.records]\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//年龄回调\r\n\t\t\tsexFunction(e) {\r\n\t\t\t\tif (this.sex == '男') {\r\n\t\t\t\t\tthis.sexs = 1\r\n\t\t\t\t} else if (this.sex == '女') {\r\n\t\t\t\t\tthis.sexs = 2\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.sexs = ''\r\n\t\t\t\t}\r\n\t\t\t\t//护龄\r\n\t\t\t\tconsole.log(this.hage, '++++++++++++++')\r\n\t\t\t\tlet i = this.hage.indexOf('-')\r\n\t\t\t\tlet j = this.hage.indexOf('年')\r\n\t\t\t\tif (i != -1) { //找到了\r\n\t\t\t\t\tthis.workMin = this.hage.substring(0, i)\r\n\t\t\t\t\tthis.workMax = this.hage.substring(i + 1, j)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.workMin = this.hage.substring(0, j)\r\n\t\t\t\t\tthis.workMax = ''\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(this.workMin)\r\n\t\t\t\tconsole.log(this.workMax)\r\n\t\t\t\t//年龄\r\n\t\t\t\tlet x = this.age.indexOf('-')\r\n\t\t\t\tlet z = this.age.indexOf('岁')\r\n\t\t\t\tif (x != -1) {\r\n\t\t\t\t\tthis.ageMin = this.age.substring(0, x)\r\n\t\t\t\t\tthis.ageMax = this.age.substring(x + 1, z)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.ageMin = this.age.substring(0, z)\r\n\t\t\t\t\tthis.ageMax = ''\r\n\t\t\t\t}\r\n\t\t\t\tthis.getpeopleList();\r\n\t\t\t},\r\n\r\n\r\n\t\t\tgethl() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '护龄'\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tlet arr = JSON.parse(JSON.stringify(res.data).replace(/code/g, 'label'))\r\n\t\t\t\t\t\tthis.options2 = arr\r\n\t\t\t\t\t\tconsole.log(arr)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetnl() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '年龄'\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tlet arr = JSON.parse(JSON.stringify(res.data).replace(/code/g, 'label'))\r\n\t\t\t\t\t\tthis.options3 = arr\r\n\t\t\t\t\t\t// console.log(arr)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tif (this.page < this.total) {\r\n\t\t\t\tthis.page += 1\r\n\t\t\t\tthis.status = 'loading'\r\n\t\t\t\tthis.getpeopleList()\r\n\t\t\t} else {\r\n\t\t\t\tthis.status = 'nomore'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tthis.page = 1\r\n\t\t\tthis.getpeopleList()\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n\r\n\t.search {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tz-index: 999;\r\n\r\n\t\t.search-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.sx {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tposition: fixed;\r\n\t\ttop: 76rpx;\r\n\t\tz-index: 999;\r\n\t}\r\n\r\n\t.list {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tz-index: 1;\r\n\r\n\t\t.list-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: auto;\r\n\r\n\t\t\t.list-box-item {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\t// height: 200rpx;\r\n\t\t\t\tbackground-color: #ffffff;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 160rpx;\r\n\t\t\t\t\theight: 160rpx;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\tz-index: 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.list-box-item-info {\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\r\n\t\t\t\t\tview:nth-of-type(1) {\r\n\t\t\t\t\t\tcolor: #1E1F31;\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tview:nth-of-type(2) {\r\n\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tview:nth-of-type(3) {\r\n\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.list-box-item-status {\r\n\t\t\t\t\twidth: 85rpx;\r\n\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\tbackground: #468EF8;\r\n\t\t\t\t\tborder-radius: 18rpx 0px 0px 18rpx;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tmargin-top: 36rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.list-box-item-fw {\r\n\t\t\t\t\tpadding-left: 200rpx;\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tpadding-right: 20rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.list-box-item-xz {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tpadding-right: 20rpx;\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t\t\t.list-box-item-xz-sub {\r\n\t\t\t\t\t\tpadding: 10rpx 30rpx;\r\n\t\t\t\t\t\tbackground-color: #468EF8;\r\n\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeop.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeop.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627336\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}