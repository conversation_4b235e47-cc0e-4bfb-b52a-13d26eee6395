{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/agent.vue?1d70", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/agent.vue?06ef", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/agent.vue?0f4b", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/agent.vue?ae4d", "uni-app:///my/other/agent.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/agent.vue?df6e", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/agent.vue?4f0c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "<PERSON><PERSON><PERSON>", "month<PERSON><PERSON>", "yearMoney", "page", "limit", "list", "feiRate", "province", "city", "district", "onLoad", "onShow", "methods", "copyClick", "uni", "success", "title", "icon", "goTx", "url", "getUserInfo", "getDetail", "type", "getAgentList", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCgExvB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EAEA;EACAC,2BAEA;EACAC;IACAC;MACAC;QACAf;QACAgB;UACAD;YACAC;cACAD;gBACAE;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACAJ;QACAK;MACA;IACA;IACAC;MAAA;MACA;QACA;UAEA;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACApB;QACAC;QACA;MACA;;MACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAoB;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAAu1C,CAAgB,6rCAAG,EAAC,C;;;;;;;;;;;ACA32C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/other/agent.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/other/agent.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./agent.vue?vue&type=template&id=8417721e&\"\nvar renderjs\nimport script from \"./agent.vue?vue&type=script&lang=js&\"\nexport * from \"./agent.vue?vue&type=script&lang=js&\"\nimport style0 from \"./agent.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/other/agent.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./agent.vue?vue&type=template&id=8417721e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./agent.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./agent.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"box\">\n\t\t\t<view class=\"\" style=\"padding: 50upx 50upx;\">\n\t\t\t\t<view>您可享受 [ {{province}}{{city?city:''}}{{district?district:''}} ] 当前区域{{feiRate}}的佣金奖励</view>\n\t\t\t\t<view class=\"flex align-center justify-between margin-top\">\n\t\t\t\t\t<view  >\n\t\t\t\t\t\t<view class=\"margin-bottom-sm\">佣金奖励</view>\n\t\t\t\t\t\t<view style=\"font-size: 58upx;\">{{yearMoney?yearMoney:'0'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"btn\" @click=\"goTx()\">提现</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"boxbtn\">\n\t\t\t\t<view class=\"flex-sub \">\n\t\t\t\t\t<view class=\"text-sm margin-bottom-xs\">本年收益</view>\n\t\t\t\t\t<view style=\"font-size: 40upx;\">{{yearMoney?yearMoney:'0'}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex-sub \">\n\t\t\t\t\t<view class=\"text-sm margin-bottom-xs\">本月收益</view>\n\t\t\t\t\t<view style=\"font-size: 40upx;\">{{monthMoney?monthMoney:'0'}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex-sub \">\n\t\t\t\t\t<view class=\"text-sm margin-bottom-xs\">今日收益</view>\n\t\t\t\t\t<view style=\"font-size: 40upx;\">{{dayMoney?dayMoney:'0'}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"bg margin padding\" style=\"border-radius: 24upx;\">\n\t\t\t<view class=\"flex align-center margin-bottom\">\n\t\t\t\t<view style=\"background: #0274FB;width: 6upx;height: 32upx;\"></view>\n\t\t\t\t<view class=\"text-30 margin-left-xs\">收益明细</view>\n\t\t\t</view>\n\t\t\t<!-- <view class=\"margin-tb\" style=\"width: 100%;height: 1rpx;background: #E6E6E6;\"></view> -->\n\t\t\t<view v-for=\"(item,index) in list\" :key=\"index\">\n\t\t\t\t<view class=\"flex align-center justify-between\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"flex align-center\">\n\t\t\t\t\t\t\t<view class=\"  \">{{item.userName}}</view>\n\t\t\t\t\t\t\t<!-- <image src=\"../static/copy.png\" style=\"width: 30rpx;height: 30rpx;margin-left: 5upx;\"\n\t\t\t\t\t\t\t\************=\"copyClick(item.title)\"></image> -->\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"margin-tb-sm\">{{item.content}}</view>\n\t\t\t\t\t\t<view style=\"color: #999999;\">{{item.createTime}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex align-center\" style=\"font-size: 38upx;font-weight: bold;\">\n\t\t\t\t\t\t<view v-if=\"item.type==1\">+</view>\n\t\t\t\t\t\t<view v-if=\"item.type==2\">-</view>\n\t\t\t\t\t\t<view>{{item.money}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"list.length-1!=index\" class=\"margin-tb\" style=\"width: 100%;height: 1rpx;background: #E6E6E6;\"></view>\n\t\t\t</view>\n\t\t\t<view class=\"\" style=\"width: 100%;\" v-if=\"list.length==0\">\n\t\t\t\t<image src=\"../../static/images/empty.png\" mode=\"widthFix\"></image>\n\t\t\t\t<view style=\"width: 100%;text-align: center;\">暂无数据</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport empty from \"../../components/empty.vue\"\n\texport default {\n\t\tcomponents:{\n\t\t\tempty:empty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdayMoney: '',\n\t\t\t\tmonthMoney: '',\n\t\t\t\tyearMoney: '',\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10,\n\t\t\t\tlist: [],\n\t\t\t\tfeiRate:'',\n\t\t\t\tprovince:'',\n\t\t\t\tcity:'',\n\t\t\t\tdistrict:''\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getUserInfo()\n\t\t\tthis.getDetail()\n\t\t\tthis.getAgentList()\n\t\t\t\n\t\t},\n\t\tonShow() {\n\n\t\t},\n\t\tmethods: {\n\t\t\tcopyClick(copy) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: copy,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tuni.getClipboardData({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: \"复制成功\",\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoTx() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/my/wallet/wallet'\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetUserInfo() {\n\t\t\t\tthis.$Request.get(\"/app/user/selectUserById\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.province = res.data.province\n\t\t\t\t\t\tthis.city = res.data.city\n\t\t\t\t\t\tthis.district = res.data.district\n\t\t\t\t\t\tthis.feiRate = parseFloat(res.data.feiRate *100).toFixed(0)+'%'//代理商佣金比例\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetDetail() {\n\t\t\t\tlet data = {\n\t\t\t\t\ttype: 2\n\t\t\t\t}\n\t\t\t\tthis.$Request.get(\"/app/orders/selectTeamStatisticsByType\", data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.dayMoney = res.data.dayMoney\n\t\t\t\t\t\tthis.monthMoney = res.data.monthMoney\n\t\t\t\t\t\tthis.yearMoney = res.data.yearMoney\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetAgentList() {\n\t\t\t\tlet data = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit,\n\t\t\t\t\t// classify: 20\n\t\t\t\t}\n\t\t\t\tthis.$Request.get(\"/app/userMoney/getAgentProfitList\", data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif (this.page == 1) this.list = []\n\t\t\t\t\t\tthis.list = [...this.list, ...res.data.records]\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tthis.page = this.page + 1;\n\t\t\tthis.getAgentList()\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\tthis.getAgentList()\n\t\t},\n\t}\n</script>\n\n\n<style lang=\"less\">\n\tpage {\n\t\tbackground: #F2F2F2;\n\t}\n\n\t.bg {\n\t\tbackground: #FFFFFF;\n\t}\n\n\t.box {\n\t\tmargin: 30upx;\n\n\t\tbackground: linear-gradient(90deg, #6C91FE 0%, #4965F9 100%);\n\t\tborder-radius: 24upx;\n\t\tcolor: #FFFFFF;\n\t}\n\n\t.btn {\n\t\tbackground: linear-gradient(90deg, #9DB6FF 0%, #7D92FF 100%);\n\t\tborder-radius: 35upx;\n\t\tpadding: 16upx 45upx;\n\t\tcolor: #FFFFFF;\n\t}\n\n\t.boxbtn {\n\t\tpadding: 30upx 50upx;\n\t\tbackground: linear-gradient(-90deg, #6B90FE 0%, #4B68F9 100%);\n\t\tborder-radius: 0px 0px 24upx 24upx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./agent.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./agent.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621953\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}