package com.sqx.modules.orders.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.response.MyOrderResponse;
import com.sqx.modules.orders.response.OrderAllResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface OrdersDao extends BaseMapper<Orders> {


    IPage<MyOrderResponse> selectOrderList(Page page,Integer createTime,Integer distance,String longitude,String latitude);


    /**
     * 查看所有订单
     */
    IPage<OrderAllResponse> queryOrdersAll(@Param("iPage") IPage<Orders> iPage, @Param("type") Long type, @Param("name") String name, String userName, @Param("status") Long status, @Param("userId") Long userId, @Param("ordersNo") String ordersNo, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("hospitalId") Long hospitalId);

    List<OrderAllResponse> ordersListExcel( @Param("type") Long type, @Param("name") String name, @Param("status") Long status, @Param("userId") Long userId, @Param("ordersNo") String ordersNo,@Param("startTime")String startTime,@Param("endTime")String endTime);

    IPage<Map<String,Object>> selectMyTakeOrders(Page<Map<String,Object>> page,@Param("userId") Long userId,@Param("status") Integer status);


    Map<String,Object> selectOrdersCountAndMoney(Long userId);

    int selectMyOrdersCount(@Param("userId") Long userId,@Param("time") String time);

    int selectTakeOrdersCount(@Param("userId") Long userId,@Param("time") String time);

    Integer countOrdersByCreateTime(@Param("time")String time,@Param("flag")Integer flag);

    Double sumOrdersByCreateTime(@Param("time")String time,@Param("flag")Integer flag);

    IPage<Map<String,Object>> incomeAnalysisOrders(Page<Map<String,Object>> page,@Param("time")String time,@Param("flag")Integer flag);

    BigDecimal selectOrdersMoneyByUserId(Long userId,String startTime,String endTime);

    int selectOrdersCountByUserId(Long userId,String startTime,String endTime);

    BigDecimal selectOrderScoreByUserId(Long userId);

    BigDecimal selectOrdersRefundMoneyByUserId(Long userId,String startTime,String endTime);

    Integer selectOrdersRefundCountByUserId(Long userId,String startTime,String endTime ,Integer status);

    Integer getOrdersRemind(Long userId);
    
    int updateOrdersIsRemind(Long userId);

    Double selectOrdersMoneyCountByUserId(Long userId,Integer type,String time);

    Integer selectUserCountByInvitationCode(String invitationCode,Integer type);

    IPage<Map<String,Object>> selectTeamOrdersList(Page<Map<String,Object>> page,@Param("userId") Long userId,@Param("type") Integer type,@Param("status") Integer status);

    IPage<Map<String,Object>> selectTeamUserList(Page<Map<String,Object>> page,@Param("invitationCode") String invitationCode,@Param("type") Integer type,@Param("userId") Long userId);

    IPage<Map<String,Object>> selectTeamUserList2(Page<Map<String,Object>> page,@Param("type") Integer type,@Param("userId") Long userId);

    List<Map<String,Object>> selectNewestOrders();

    IPage<Orders> selectMyOrder(@Param("pages") Page<Orders> pages, @Param("orders") Orders orders);

    IPage<Orders> selectNowDayOrders(@Param("pages") Page<Orders> pages, @Param("userId") Long userId);

    Integer getUserNowHasOrder(@Param("orderTakingUserId") Long orderTakingUserId, @Param("hopeTime") Date hopeTime);

    BigDecimal getAllMoney(@Param("time") String time, @Param("flag") Integer flag, @Param("state") Integer state);

    Integer orderCount(@Param("time") String time, @Param("flag") Integer flag);

    BigDecimal settlement(@Param("time") String time, @Param("flag") Integer flag);

    int getOrdersCount(String time, Integer flag, Integer type);

    List<Orders> getOrderLast(int i, String time);

    IPage<Orders> errandGetOrderList(@Param("pages") Page<Orders> pages, @Param("latitude") Double latitude, @Param("longitude") Double longitude, @Param("city") String city, @Param("createTime") Integer createTime, @Param("hopeTime") Integer hopeTime, String hospitalName, Long userId);

    Integer rideCancelOrder(Long orderId);

    IPage<HashMap<String, Object>> getProfitList(@Param("pages") Page<HashMap<String, Object>> pages, @Param("time") String time, @Param("flag") Integer flag);

    Integer selectOrdersCount(Integer flag, String time,  Integer state);

    BigDecimal selectOrdersMoney(Integer flag, String time, Integer state);

    IPage<HashMap<String, Object>> getPingIncome(@Param("pages") Page<HashMap<String, Object>> pages, @Param("flag") Integer flag, @Param("time") String time);

    BigDecimal orderStatistics(Integer flag, String time, int type);

    BigDecimal selectShopRateMoney(@Param("userId") Long userId);

    IPage<HashMap<String, Object>> getRateUserList(@Param("pages") Page<HashMap<String, Object>> pages, @Param("userId") Long userId);

    IPage<HashMap<String, Object>> getUserList(@Param("pages") Page<HashMap<String, Object>> pages, @Param("invitationCode") String invitationCode);
}
