{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/customer.vue?fece", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/customer.vue?2f13", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/customer.vue?58f4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/customer.vue?4e5d", "uni-app:///my/setting/customer.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/customer.vue?9b3c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/customer.vue?9c3d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "image", "isWeiXin", "weixin", "webviewStyles", "progress", "color", "onLoad", "console", "onPullDownRefresh", "uni", "methods", "copyHref", "success", "saveImg", "imgArr", "urls", "current", "rests", "title", "mask", "duration", "icon", "window", "goChat", "url", "goLoginInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsB3vB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IAAA;IAOA;IACA;MACA;QACA;UACAC;UACA;QACA;MACA;IACA;IACA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;EACA;;EACAC;IACA;IACAC;MAAA;MACAF;QACAV;QACAa;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAC;MACA;MACAL;QACAM;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACAR;QACAS;QACAC;QACAC;QACAC;MACA;MACAC;IACA;IACA;IACAC;MACA;MACA;QACAd;UACAe;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACAhB;QACAe;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnHA;AAAA;AAAA;AAAA;AAA8iC,CAAgB,y8BAAG,EAAC,C;;;;;;;;;;;ACAlkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/setting/customer.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/setting/customer.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./customer.vue?vue&type=template&id=4bda3ebc&\"\nvar renderjs\nimport script from \"./customer.vue?vue&type=script&lang=js&\"\nexport * from \"./customer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./customer.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/setting/customer.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer.vue?vue&type=template&id=4bda3ebc&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer.vue?vue&type=script&lang=js&\"", "<template>\n\t<view style=\"height: 100vh;margin: 32upx;\">\n\t\t<view style=\"text-align: center;background: #FFFFFF;padding: 40upx;border-radius: 32upx;\">\n\t\t\t<view style=\"font-size: 38upx;\">添加客服微信咨询</view>\n\t\t\t<view style=\"font-size: 32upx;margin-top: 32upx;\">微信号：{{weixin}}</view>\n\t\t\t<view @click=\"copyHref\"\n\t\t\t\tstyle=\"background: #557EFD;width:200upx;margin-top: 32upx;font-size: 30upx;margin-left: 36%;color: #FFFFFF;padding: 4upx 20upx;border-radius: 24upx;\">\n\t\t\t\t一键复制</view>\n\n\t\t\t<image @click=\"saveImg\" mode=\"aspectFit\" style=\"margin-top: 32upx\" :src=\"image\"></image>\n\t\t\t<view style=\"font-size: 28upx;margin-top: 32upx\" v-if=\"isWeiXin\">{{ isWeiXin ? '长按识别上方二维码' : '' }}</view>\n\n\n\t\t\t<view @click=\"goChat\"\n\t\t\t\tstyle=\"width:260upx;margin-top: 32upx;font-size: 30upx;margin-left: 28%;color: #557EFD;padding: 4upx 20upx;border-radius: 24upx;\">\n\t\t\t\t联系在线客服</view>\n\t\t\t<view v-if=\"isWeiXin\" style=\"font-size: 24upx;margin-top: 80upx\" @click=\"rests\">无法识别？</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\timage: 'https://game.shengqianxiong.com.cn/custom.jpg',\n\t\t\t\tisWeiXin: false,\n\t\t\t\tweixin: '710070994',\n\t\t\t\twebviewStyles: {\n\t\t\t\t\tprogress: {\n\t\t\t\t\t\tcolor: '#1A1929 '\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\t// #ifdef H5\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\n\t\t\t\tthis.isWeiXin = true;\n\t\t\t}\n\t\t\t// #endif\n\t\t\t//获取客服二维码\n\t\t\tthis.$Request.getT('/app/common/type/1').then(res => {\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tif (res.data && res.data.value) {\n\t\t\t\t\t\tconsole.log(res.data.value)\n\t\t\t\t\t\tthis.image = res.data.value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.$Request.getT('/app/common/type/44').then(res => {\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tif (res.data && res.data.value) {\n\t\t\t\t\t\tthis.weixin = res.data.value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tuni.stopPullDownRefresh(); // 停止刷新\n\t\t},\n\t\tmethods: {\n\t\t\t//邀请码复制\n\t\t\tcopyHref() {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: this.weixin,\n\t\t\t\t\tsuccess: r => {\n\t\t\t\t\t\tthis.$queue.showToast('复制成功');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tsaveImg() {\n\t\t\t\tlet that = this;\n\t\t\t\tlet imgArr = []\n\t\t\t\timgArr.push(that.image);\n\t\t\t\t//预览图片\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: imgArr,\n\t\t\t\t\tcurrent: imgArr[0]\n\t\t\t\t});\n\t\t\t\t// uni.saveImageToPhotosAlbum({\n\t\t\t\t// \tfilePath: that.image,\n\t\t\t\t// \tsuccess(res) {\n\t\t\t\t// \t\tthat.$queue.showToast('保存成功');\n\t\t\t\t// \t}\n\t\t\t\t// });\n\t\t\t},\n\t\t\trests() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已刷新请再次长按识别',\n\t\t\t\t\tmask: false,\n\t\t\t\t\tduration: 1500,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\twindow.location.reload();\n\t\t\t},\n\t\t\t// 在线客服\n\t\t\tgoChat() {\n\t\t\t\tlet token = this.$queue.getData('token');\n\t\t\t\tif (token) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/my/setting/chat'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.goLoginInfo();\n\t\t\t\t}\n\t\t\t},\n\t\t\t//统一登录跳转\n\t\t\tgoLoginInfo() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/public/loginphone'\n\t\t\t\t});\n\t\t\t},\n\t\t}\n\t};\n</script>\n\n<style>\n\t/* @import '../../static/css/index.css'; */\n\n\tpage {\n\t\tbackground: #F5F5F5;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447624826\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}