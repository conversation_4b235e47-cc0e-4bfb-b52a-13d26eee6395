package com.sqx.modules.department.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sqx.modules.department.entity.Department;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入模板
 */
@Data
public class DepartmentIn extends Department implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 医院名称
     */
    @Excel(name = "医院名称",width = 25)
    private String hospitalName;

    /**
     * 上级科室名称
     */
    @Excel(name = "上级科室名称",width = 20)
    private String parentName;

}
