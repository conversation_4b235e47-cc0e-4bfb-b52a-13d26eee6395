package com.sqx.modules.tbCoupon.controller.couponUser;

import com.sqx.common.utils.Result;
import com.sqx.modules.sys.controller.AbstractController;
import com.sqx.modules.tbCoupon.entity.TbCouponUser;
import com.sqx.modules.tbCoupon.service.TbCouponUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(value = "管理端-优惠券", tags = {"管理端-用户优惠券"})
@RequestMapping(value = "/admin/couponUser/")
public class AdminTbCouponUserController extends AbstractController {

    @Autowired
    private TbCouponUserService couponUserService;


    @ApiOperation("查看指定用户优惠券列表")
    @GetMapping(value = "getMyCouponList")
    public Result getMyCouponList(Integer page, Integer limit, TbCouponUser couponUser) {
        return Result.success().put("data", couponUserService.getMyCouponList( page, limit, couponUser));
    }

    @ApiOperation("赠送用户优惠券")
    @PostMapping(value = "giveUserCoupon")
    public Result giveUserCoupon(String userIds,Long couponId,Integer num){
        return couponUserService.giveUserCoupon(userIds,couponId,num);
    }
}