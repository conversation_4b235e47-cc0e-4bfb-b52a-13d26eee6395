package com.sqx.modules.ai.controller;

import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.entity.AiModelConfig;
import com.sqx.modules.ai.service.AiModelConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * AI模型配置管理Controller
 */
@RestController
@RequestMapping("/ai/model")
@Api(value = "AI模型配置管理", tags = {"AI模型配置管理"})
public class AiModelConfigController {

    @Autowired
    private AiModelConfigService modelConfigService;

    /**
     * 分页查询模型配置列表
     */
    @GetMapping("/list")
    @ApiOperation("分页查询模型配置列表")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = modelConfigService.queryPage(params);
        return Result.success().put("data", page);
    }

    /**
     * 查询启用的模型列表
     */
    @GetMapping("/enabled")
    @ApiOperation("查询启用的模型列表")
    public Result getEnabledModels() {
        return modelConfigService.getEnabledModels();
    }

    /**
     * 根据ID查询模型配置详情
     */
    @GetMapping("/{id}")
    @ApiOperation("查询模型配置详情")
    public Result info(@PathVariable("id") Long id) {
        AiModelConfig modelConfig = modelConfigService.getById(id);
        if (modelConfig == null) {
            return Result.error("模型配置不存在");
        }
        return Result.success().put("data", modelConfig);
    }

    /**
     * 保存模型配置
     */
    @PostMapping("/save")
    @ApiOperation("保存模型配置")
    public Result save(@RequestBody AiModelConfig modelConfig) {
        return modelConfigService.saveModelConfig(modelConfig);
    }

    /**
     * 更新模型配置
     */
    @PostMapping("/update")
    @ApiOperation("更新模型配置")
    public Result update(@RequestBody AiModelConfig modelConfig) {
        return modelConfigService.updateModelConfig(modelConfig);
    }

    /**
     * 删除模型配置
     */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除模型配置")
    public Result delete(@PathVariable("id") Long id) {
        return modelConfigService.deleteModelConfig(id);
    }

    /**
     * 启用/禁用模型
     */
    @PostMapping("/toggle/{id}")
    @ApiOperation("启用/禁用模型")
    public Result toggleStatus(@PathVariable("id") Long id, @RequestParam Integer status) {
        return modelConfigService.toggleModelStatus(id, status);
    }
}
