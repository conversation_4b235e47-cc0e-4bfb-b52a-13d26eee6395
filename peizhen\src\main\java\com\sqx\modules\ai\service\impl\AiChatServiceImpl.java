package com.sqx.modules.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Query;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.dao.AiMessageDao;
import com.sqx.modules.ai.entity.AiConversation;
import com.sqx.modules.ai.entity.AiMessage;
import com.sqx.modules.ai.entity.AiModelConfig;
import com.sqx.modules.ai.service.AiChatService;
import com.sqx.modules.ai.service.AiConversationService;
import com.sqx.modules.ai.service.AiModelConfigService;
import com.sqx.modules.ai.utils.OpenAiApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI聊天服务实现
 */
@Slf4j
@Service
public class AiChatServiceImpl implements AiChatService {

    @Autowired
    private AiMessageDao messageDao;

    @Autowired
    private AiConversationService conversationService;

    @Autowired
    private AiModelConfigService modelConfigService;

    @Autowired
    private OpenAiApiClient apiClient;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Result sendMessage(Long userId, Long conversationId, String message, String modelCode) {
        try {
            // 验证对话是否存在且属于当前用户
            AiConversation conversation = conversationService.getOne(
                new QueryWrapper<AiConversation>()
                    .eq("id", conversationId)
                    .eq("user_id", userId)
                    .eq("status", 1)
            );

            if (conversation == null) {
                return Result.error("对话不存在或无权限访问");
            }

            // 如果指定了模型代码，验证并更新对话使用的模型
            if (StringUtils.isNotBlank(modelCode) && !modelCode.equals(conversation.getModelCode())) {
                Result switchResult = conversationService.switchModel(conversationId, userId, modelCode);
                if (!"success".equals(switchResult.get("msg"))) {
                    return switchResult;
                }
                conversation.setModelCode(modelCode);
            }

            // 获取模型配置
            AiModelConfig modelConfig = modelConfigService.getByModelCode(conversation.getModelCode());
            if (modelConfig == null) {
                return Result.error("AI模型配置不存在或未启用");
            }

            // 保存用户消息
            AiMessage userMessage = new AiMessage();
            userMessage.setConversationId(conversationId);
            userMessage.setUserId(userId);
            userMessage.setRole("user");
            userMessage.setContent(message);
            userMessage.setModelCode(conversation.getModelCode());
            userMessage.setCreateTime(dateFormat.format(new Date()));
            messageDao.insert(userMessage);

            // 获取对话历史（最近20条消息）
            List<AiMessage> historyMessages = messageDao.selectConversationMessages(conversationId, 20);
            
            // 构建API请求消息列表
            List<OpenAiApiClient.ChatMessage> apiMessages = historyMessages.stream()
                .map(msg -> new OpenAiApiClient.ChatMessage(msg.getRole(), msg.getContent()))
                .collect(Collectors.toList());

            // 调用AI API
            OpenAiApiClient.ChatResponse apiResponse = apiClient.sendChatRequest(modelConfig, apiMessages);

            if (!apiResponse.isSuccess()) {
                // 保存错误信息
                AiMessage errorMessage = new AiMessage();
                errorMessage.setConversationId(conversationId);
                errorMessage.setUserId(userId);
                errorMessage.setRole("assistant");
                errorMessage.setContent("抱歉，AI服务暂时不可用，请稍后再试。");
                errorMessage.setModelCode(conversation.getModelCode());
                errorMessage.setErrorMessage(apiResponse.getErrorMessage());
                errorMessage.setCreateTime(dateFormat.format(new Date()));
                messageDao.insert(errorMessage);

                return Result.error("AI回复失败: " + apiResponse.getErrorMessage());
            }

            // 保存AI回复
            AiMessage assistantMessage = new AiMessage();
            assistantMessage.setConversationId(conversationId);
            assistantMessage.setUserId(userId);
            assistantMessage.setRole("assistant");
            assistantMessage.setContent(apiResponse.getContent());
            assistantMessage.setModelCode(conversation.getModelCode());
            assistantMessage.setPromptTokens(apiResponse.getPromptTokens());
            assistantMessage.setCompletionTokens(apiResponse.getCompletionTokens());
            assistantMessage.setTotalTokens(apiResponse.getTotalTokens());
            assistantMessage.setResponseTime(apiResponse.getResponseTime());
            assistantMessage.setCreateTime(dateFormat.format(new Date()));
            messageDao.insert(assistantMessage);

            // 更新对话统计
            conversationService.updateConversationStats(conversationId);

            // 如果是第一次对话且标题是默认的，根据用户消息生成标题
            if (conversation.getMessageCount() == 0 && "新对话".equals(conversation.getTitle())) {
                String newTitle = generateConversationTitle(message);
                conversationService.updateConversationTitle(conversationId, userId, newTitle);
            }

            // 返回AI回复
            Map<String, Object> result = new HashMap<>();
            result.put("userMessage", userMessage);
            result.put("assistantMessage", assistantMessage);
            Map<String, Object> tokenUsage = new HashMap<>();
            tokenUsage.put("promptTokens", apiResponse.getPromptTokens());
            tokenUsage.put("completionTokens", apiResponse.getCompletionTokens());
            tokenUsage.put("totalTokens", apiResponse.getTotalTokens());
            result.put("tokenUsage", tokenUsage);

            return Result.success().put("data", result);

        } catch (Exception e) {
            log.error("发送消息失败", e);
            return Result.error("发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public Result getConversationHistory(Long conversationId, Long userId, Integer page, Integer limit) {
        try {
            // 验证对话权限
            AiConversation conversation = conversationService.getOne(
                new QueryWrapper<AiConversation>()
                    .eq("id", conversationId)
                    .eq("user_id", userId)
                    .eq("status", 1)
            );

            if (conversation == null) {
                return Result.error("对话不存在或无权限访问");
            }

            // 分页查询消息历史
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("limit", limit);
            IPage<AiMessage> pageInfo = new Query<AiMessage>().getPage(params);
            IPage<AiMessage> result = messageDao.selectConversationMessagesPage(pageInfo, conversationId);

            return Result.success().put("data", new PageUtils(result));

        } catch (Exception e) {
            log.error("获取对话历史失败", e);
            return Result.error("获取对话历史失败: " + e.getMessage());
        }
    }

    @Override
    public Result sendMessageStream(Long userId, Long conversationId, String message, String modelCode) {
        // 流式响应暂时不实现，返回普通响应
        return sendMessage(userId, conversationId, message, modelCode);
    }

    /**
     * 根据用户消息生成对话标题
     */
    private String generateConversationTitle(String message) {
        if (StringUtils.isBlank(message)) {
            return "新对话";
        }
        
        // 简单的标题生成逻辑：取前20个字符
        String title = message.length() > 20 ? message.substring(0, 20) + "..." : message;
        
        // 移除换行符
        title = title.replaceAll("[\r\n]+", " ");
        
        return title;
    }
}
