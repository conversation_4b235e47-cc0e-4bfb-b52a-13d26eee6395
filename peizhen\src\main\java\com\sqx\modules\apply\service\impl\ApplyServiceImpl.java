package com.sqx.modules.apply.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.DateUtils;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.apply.dao.ApplyDao;
import com.sqx.modules.apply.entity.Apply;
import com.sqx.modules.apply.service.ApplyService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 申请
 */
@Service
public class ApplyServiceImpl extends ServiceImpl<ApplyDao, Apply> implements ApplyService {


    @Override
    public Result insertApply(Apply apply){
        Apply oldApply = selectApplyByUserIdAndClassify(apply.getUserId(), apply.getClassify());
        if(oldApply!=null){
            return Result.error("您已经提交过了！");
        }
        apply.setStatus(1);
        apply.setCreateTime(DateUtils.format(new Date()));
        baseMapper.insert(apply);
        return Result.success();
    }

    @Override
    public Result updateApply(Apply apply){
        apply.setStatus(1);
        baseMapper.updateById(apply);
        return Result.success();
    }

    @Override
    public Result deleteApply(Long applyId){
        baseMapper.deleteById(applyId);
        return Result.success();
    }


    @Override
    public Result selectApplyList(Integer page,Integer limit,String applyName,String applyPhone,Integer status,Integer classify){
        IPage<Apply> applyPage = baseMapper.selectPage(new Page<>(page, limit),
                new QueryWrapper<Apply>()
                        .eq(StringUtils.isNotBlank(applyName), "apply_name", applyName)
                        .eq(StringUtils.isNotBlank(applyPhone), "apply_phone", applyPhone)
                        .eq(status != null && status != 0, "status", status)
                        .eq(classify!=null,"classify",classify).orderByDesc("create_time"));
        return Result.success().put("data",new PageUtils(applyPage));
    }

    @Override
    public Apply selectApplyByUserIdAndClassify(Long userId,Integer classify){
        return baseMapper.selectOne(new QueryWrapper<Apply>().eq("user_id",userId).eq("classify",classify));
    }

    @Override
    public Result auditApply(String ids,Integer status,String content){
        for(String id:ids.split(",")){
            Apply apply = baseMapper.selectById(Long.parseLong(id));
            if(apply.getStatus().equals(1)){
                if(status==1){
                    apply.setStatus(2);
                    apply.setApplyContent("通过");
                }else{
                    apply.setStatus(3);
                    apply.setAuditContent(content);
                }
                baseMapper.updateById(apply);
            }
        }
        return Result.success();
    }




}
