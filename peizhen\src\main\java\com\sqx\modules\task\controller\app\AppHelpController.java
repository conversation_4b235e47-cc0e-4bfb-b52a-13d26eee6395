package com.sqx.modules.task.controller.app;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.task.entity.HelpOrder;
import com.sqx.modules.task.entity.HelpTake;
import com.sqx.modules.task.service.HelpOrderService;
import com.sqx.modules.task.service.HelpTakeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021/1/8
 */
@RestController
@Api(value = "跑腿订单", tags = {"跑腿订单"})
@RequestMapping(value = "/app/help")
public class AppHelpController {


    @Autowired
    private HelpOrderService helpOrderService;
    @Autowired
    private HelpTakeService helpTakeService;


    @Login
    @PostMapping("/saveHelpOrder")
    @ApiOperation("发布跑腿订单")
    @ResponseBody
    public Result saveHelpOrder(@RequestBody HelpOrder helpOrder,@RequestAttribute Long userId){
        helpOrder.setUserId(userId);
        return helpOrderService.saveBody(helpOrder);
    }

    @Login
    @PostMapping("/updateHelpOrderByStatus/{ids}/{status}/{content}")
    @ApiOperation("审核跑腿订单")
    @ResponseBody
    public Result updateHelpOrderByStatus(@PathVariable("ids") String ids,@PathVariable("status") Integer status,@PathVariable("content") String content){
        return helpOrderService.updateHelpOrderByStatus(ids, status, content);
    }

    @Login
    @PostMapping("/updateHelpOrder")
    @ApiOperation("完善需求")
    @ResponseBody
    public Result updateHelpOrder(@RequestBody HelpOrder helpOrder){
        return helpOrderService.updateHelpOrderById(helpOrder);
    }

    @Login
    @PostMapping("/deleteHelpOrder")
    @ApiOperation("删除")
    @ResponseBody
    public Result deleteHelpOrder(Long helpOrderId){
        return helpOrderService.deleteById(helpOrderId);
    }


    @Login
    @PostMapping("/closeOrder")
    @ApiOperation("确认送达")
    @ResponseBody
    public Result closeOrder(Long helpTakeId,Long helpOrderId,String code){
        return helpTakeService.closeOrder(helpTakeId, helpOrderId, code);
    }

    @Login
    @PostMapping("/saveHelpTake")
    @ApiOperation("接单")
    @ResponseBody
    public Result saveHelpTake(@RequestBody HelpTake helpTake,@RequestAttribute Long userId){
        helpTake.setUserId(userId);
        return helpTakeService.saveBody(helpTake);
    }

    @Login
    @PostMapping("/endHelpTake")
    @ApiOperation("放弃接单")
    @ResponseBody
    public Result endHelpTake(Long id){
        return helpTakeService.endHelpTake(id);
    }


    @GetMapping("selectNewHelpOrderList")
    @ApiOperation("最新跑腿")
    @ResponseBody
    public Result selectNewHelpOrderList(Integer page,Integer limit,Long gameId,String latitude,String longitude,Integer sort){
        return helpOrderService.selectNewHelpOrderList(page,limit,gameId,latitude,longitude,sort);
    }


    @GetMapping("/selectHelpOrderByClassifyList")
    @ApiOperation("根据分类查找跑腿")
    @ResponseBody
    public Result selectHelpOrderByClassifyList(Integer page,Integer limit,Long classifyId,Long gameId){
        return helpOrderService.selectHelpOrderByClassifyList(page,limit,classifyId,gameId);
    }


    @GetMapping("/selectHelpOrderByNameList")
    @ApiOperation("根据名称模糊查找跑腿")
    @ResponseBody
    public Result selectHelpOrderByContentList(Integer page,Integer limit,String content,Long gameId){
        return helpOrderService.selectHelpOrderByContentList(page,limit,content,gameId);
    }

    @GetMapping("/selectHelpOrderDetails")
    @ApiOperation("查看跑腿详细信息")
    @ResponseBody
    public Result selectHelpOrderDetails(Long helpOrderId){
        HelpOrder helpOrder = helpOrderService.selectHelpOrderById(helpOrderId);
        return Result.success().put("data",helpOrder);
    }

    @Login
    @GetMapping("/selectRunHelpOrder")
    @ApiOperation("我的订单-跑腿订单")
    @ResponseBody
    public Result selectRunHelpOrder(Integer page,Integer limit,Integer status,@RequestAttribute Long userId){
        return helpTakeService.selectRunHelpOrder(page,limit,status,userId);
    }

    @Login
    @GetMapping("/selectCreateHelpOrder")
    @ApiOperation("我的订单-发布订单")
    @ResponseBody
    public Result selectCreateHelpOrder(Integer page,Integer limit,Integer status,@RequestAttribute Long userId,Long gameId){
        return helpOrderService.selectCreateHelpOrder(page,limit,status,userId,gameId);

    }

    @Login
    @GetMapping("/selectStatusHelpOrder")
    @ApiOperation("查询待审核的订单")
    @ResponseBody
    public Result selectStatusHelpOrder(Integer page,Integer limit,String phone,String content,Integer status,Long gameId){
        return helpOrderService.selectStatusHelpOrder(page,limit,phone,content,status,gameId);

    }

    @Login
    @GetMapping("/selectHelpTakeList")
    @ApiOperation("查询已接单的接单订单")
    @ResponseBody
    public Result selectHelpTakeList(Integer page,Integer limit,Integer status,String phone){
        return helpTakeService.selectRunHelpOrder(page,limit,status,phone,null,null);
    }


}