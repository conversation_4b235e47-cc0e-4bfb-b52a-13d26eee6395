<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="模型名称" prop="modelName">
            <el-input v-model="dataForm.modelName" placeholder="模型名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模型代码" prop="modelCode">
            <el-input v-model="dataForm.modelCode" placeholder="模型代码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="模型类型" prop="modelType">
            <el-select v-model="dataForm.modelType" placeholder="请选择模型类型" style="width: 100%;">
              <el-option label="DeepSeek" value="deepseek"></el-option>
              <el-option label="Kimi" value="kimi"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number v-model="dataForm.sortOrder" :min="0" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="API地址" prop="apiUrl">
        <el-input v-model="dataForm.apiUrl" placeholder="API地址"></el-input>
      </el-form-item>
      <el-form-item label="API密钥" prop="apiKey">
        <el-input v-model="dataForm.apiKey" type="password" placeholder="API密钥" show-password></el-input>
      </el-form-item>
      <el-row>
        <el-col :span="8">
          <el-form-item label="最大Token" prop="maxTokens">
            <el-input-number v-model="dataForm.maxTokens" :min="1" :max="32000" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="温度" prop="temperature">
            <el-input-number v-model="dataForm.temperature" :min="0" :max="2" :step="0.1" :precision="2" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Top P" prop="topP">
            <el-input-number v-model="dataForm.topP" :min="0" :max="1" :step="0.1" :precision="2" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="频率惩罚" prop="frequencyPenalty">
            <el-input-number v-model="dataForm.frequencyPenalty" :min="-2" :max="2" :step="0.1" :precision="2" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存在惩罚" prop="presencePenalty">
            <el-input-number v-model="dataForm.presencePenalty" :min="-2" :max="2" :step="0.1" :precision="2" style="width: 100%;"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="是否启用" prop="isEnabled">
        <el-radio-group v-model="dataForm.isEnabled">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" type="textarea" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        modelName: '',
        modelCode: '',
        apiUrl: '',
        apiKey: '',
        modelType: '',
        maxTokens: 4000,
        temperature: 0.7,
        topP: 1.0,
        frequencyPenalty: 0.0,
        presencePenalty: 0.0,
        isEnabled: 1,
        sortOrder: 0,
        remark: ''
      },
      dataRule: {
        modelName: [
          { required: true, message: '模型名称不能为空', trigger: 'blur' }
        ],
        modelCode: [
          { required: true, message: '模型代码不能为空', trigger: 'blur' }
        ],
        apiUrl: [
          { required: true, message: 'API地址不能为空', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ],
        apiKey: [
          { required: true, message: 'API密钥不能为空', trigger: 'blur' }
        ],
        modelType: [
          { required: true, message: '模型类型不能为空', trigger: 'change' }
        ],
        maxTokens: [
          { required: true, message: '最大Token不能为空', trigger: 'blur' }
        ],
        temperature: [
          { required: true, message: '温度参数不能为空', trigger: 'blur' }
        ],
        topP: [
          { required: true, message: 'Top P参数不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/ai/model/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.modelName = data.data.modelName
              this.dataForm.modelCode = data.data.modelCode
              this.dataForm.apiUrl = data.data.apiUrl
              this.dataForm.apiKey = data.data.apiKey
              this.dataForm.modelType = data.data.modelType
              this.dataForm.maxTokens = data.data.maxTokens
              this.dataForm.temperature = data.data.temperature
              this.dataForm.topP = data.data.topP
              this.dataForm.frequencyPenalty = data.data.frequencyPenalty
              this.dataForm.presencePenalty = data.data.presencePenalty
              this.dataForm.isEnabled = data.data.isEnabled
              this.dataForm.sortOrder = data.data.sortOrder
              this.dataForm.remark = data.data.remark
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/ai/model/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
