package com.sqx.modules.department.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.vo.DepartmentOut;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Mapper
public interface DepartmentDao extends BaseMapper<Department> {

    List<DepartmentOut> departmentExcelOut(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("department") Department department);
}
