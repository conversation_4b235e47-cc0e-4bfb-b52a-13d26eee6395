package com.sqx.modules.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.entity.UserMoneyDetails;
import com.sqx.modules.app.service.UserMoneyDetailsService;
import com.sqx.modules.app.service.UserMoneyService;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.orders.response.OrderAllResponse;
import com.sqx.modules.taking.entity.Game;
import com.sqx.modules.taking.service.GameService;
import com.sqx.modules.task.dao.HelpOrderDao;
import com.sqx.modules.task.dao.HelpTakeDao;
import com.sqx.modules.task.entity.HelpOrder;
import com.sqx.modules.task.entity.HelpTake;
import com.sqx.modules.task.service.HelpTakeService;
import com.sqx.modules.utils.AmountCalUtils;
import com.sqx.modules.utils.excel.ExcelData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 接单订单
 * <AUTHOR>
 * @date 2021/1/8
 */
@Service
public class HelpTakeServiceImpl extends ServiceImpl<HelpTakeDao, HelpTake> implements HelpTakeService {

    @Autowired
    private HelpTakeDao helpTakeDao;
    @Autowired
    private HelpOrderDao helpOrderDao;
    @Autowired
    private UserService userService;
    @Autowired
    private UserMoneyService userMoneyService;
    @Autowired
    private UserMoneyDetailsService userMoneyDetailsService;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private GameService gameService;
    private static ReentrantReadWriteLock reentrantReadWriteLock=new ReentrantReadWriteLock(true);


    @Override
    public Result selectHelpTake(int page,int limit){
        return Result.success().put("data",helpTakeDao.selectList(new QueryWrapper<>()));
    }

    @Override
    public Result selectRunHelpOrder(int page,int limit,Integer status,Long userId){
        Page<Map<String,Object>> pages=new Page<>(page,limit);
        IPage<Map<String, Object>> mapIPage = helpTakeDao.selectRunHelpOrder(pages, status, userId);
        return Result.success().put("data",new PageUtils(mapIPage));
    }

    @Override
    public Result selectRunHelpOrder(int page,int limit,Integer status,String phone,String startTime,String endTime){
        phone = phone.trim();
        Page<Map<String,Object>> pages=new Page<>(page,limit);
        IPage<Map<String, Object>> mapIPage = helpTakeDao.selectRunHelpOrderList(pages, status, phone,startTime,endTime);
        return Result.success().put("data",new PageUtils(mapIPage));
    }

    @Override
    public ExcelData helpTakeListExcel(Integer status, String phone,String startTime,String endTime){
        phone = phone.trim();
        List<Map<String, Object>> mapIPage = helpTakeDao.helpTakeListExcel( status, phone,startTime,endTime);
        ExcelData data = new ExcelData();
        data.setName("接单列表");
        List<String> titles = new ArrayList();
        titles.add("编号");titles.add("发单用户");titles.add("接单用户");titles.add("姓名");
        titles.add("手机号");titles.add("地址");titles.add("提交内容");titles.add("接单价格");titles.add("期望送达时间");
        titles.add("状态");titles.add("创建时间");
        data.setTitles(titles);
        List<List<Object>> rows = new ArrayList();
        for(Map<String, Object> map:mapIPage){
            List<Object> row = new ArrayList();
            row.add(map.get("helpTakeId"));
            row.add(map.get("userName"));
            row.add(map.get("helpTakeUserName"));
            row.add(map.get("name"));
            row.add(map.get("phone"));
            row.add(String.valueOf(map.get("province"))+String.valueOf(map.get("city"))+String.valueOf(map.get("district"))+String.valueOf(map.get("detailsAddress")));
            row.add(map.get("serviceName"));
            row.add(map.get("money"));
            row.add(map.get("deliveryTime"));
            String status1 = String.valueOf(map.get("status"));
            //1 已接单  2 已送达  3已下架
            if("1".equals(status1)){
                row.add("已接单");
            }else if("2".equals(status1)){
                row.add("已送达");
            }else if("3".equals(status1)){
                row.add("已放弃");
            }else{
                row.add("未知");
            }
            row.add(map.get("createTime"));
            rows.add(row);
        }
        data.setRows(rows);
        return data;



    }

    @Override
    public HelpTake selectHelpTakeById(Long helpTakeId){
        HelpTake helpTake = helpTakeDao.selectById(helpTakeId);
        if(helpTake!=null){
            UserEntity userEntity = userService.selectUserById(helpTake.getUserId());
            helpTake.setUser(userEntity);
        }
        return helpTake;
    }

    @Override
    public Integer countHelpTakeByCreateTime(String time,Integer flag){
        return helpTakeDao.countHelpTakeByCreateTime(time, flag);
    }

    @Override
    public  Double sumMoneyBySend(String time,Integer flag){
        return helpTakeDao.sumMoneyBySend(time, flag);
    }


    /**
     *  接单
     * @param helpTake  接单实体类
     * @return 是否接单成功
     */
    @Override
    public Result saveBody(HelpTake helpTake){
        reentrantReadWriteLock.writeLock().lock();
        try{
            HelpOrder helpOrder = helpOrderDao.selectById(helpTake.getOrderId());
            if(helpOrder.getStatus()!=2){
                return Result.error("系统繁忙，请刷新后重试！");
            }
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            helpTake.setStatus(1);
            helpTake.setCreateTime(sdf.format(new Date()));
            helpTake.setMoney(helpOrder.getCommission());
            helpTakeDao.insertHelpTake(helpTake);
            helpOrder.setHelpTakeId(helpTake.getId());
            helpOrder.setStatus(3);
            int code = (int) ((Math.random() * 9 + 1) * 1000);
            helpOrder.setCode(String.valueOf(code));
            helpOrderDao.updateById(helpOrder);
            UserEntity user = userService.selectUserById(helpOrder.getUserId());
            if (user.getClientid() != null) {
                userService.pushToSingle("任务通知","您的任务已被接单！" , user.getClientid());
            }
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setContent("您的任务已被接单！");
            messageInfo.setTitle("任务通知");
            messageInfo.setState(String.valueOf(5));
            messageInfo.setUserName(user.getUserName());
            messageInfo.setUserId(String.valueOf(user.getUserId()));
            messageService.saveBody(messageInfo);
            return Result.success();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            reentrantReadWriteLock.writeLock().unlock();
        }
        return Result.error("系统繁忙，请刷新后重试！");
    }


    @Override
    public Result endHelpTake(Long id){
        reentrantReadWriteLock.writeLock().lock();
        try{
            HelpTake helpTake = helpTakeDao.selectById(id);
            if(!helpTake.getStatus().equals(1)){
                return Result.error("系统繁忙，请稍后再试！");
            }
            helpTake.setStatus(3);
            helpTakeDao.deleteById(helpTake.getId());
            HelpOrder helpOrder = helpOrderDao.selectById(helpTake.getOrderId());
            helpOrder.setHelpTakeId(-1L);
            helpOrder.setStatus(2);
            helpOrderDao.updateById(helpOrder);
            UserEntity user = userService.selectUserById(helpOrder.getUserId());
            if (user.getClientid() != null) {
                userService.pushToSingle("订单通知","您的订单已被取消，系统已经帮你重新发布！" , user.getClientid());
            }
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setContent("您的订单已被取消，系统已经帮你重新发布！");
            messageInfo.setTitle("订单通知");
            messageInfo.setState(String.valueOf(5));
            messageInfo.setUserName(user.getUserName());
            messageInfo.setUserId(String.valueOf(user.getUserId()));
            messageService.saveBody(messageInfo);
            return Result.success();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            reentrantReadWriteLock.writeLock().unlock();
        }
        return Result.error("系统繁忙，请刷新后重试！");
    }





    /**
     *  确认送达
     * @param helpTakeId  接单id
     * @param helpOrderId  派单id
     * @param code 收货码
     * @return  是否送达成功
     */
    @Override
    public Result closeOrder(Long helpTakeId,Long helpOrderId,String code){
        HelpOrder helpOrder = helpOrderDao.selectById(helpOrderId);
        if(helpOrder.getStatus()!=3){
            return Result.error("请刷新后重试！");
        }
        if(!helpOrder.getCode().equals(code)){
            return Result.error("收货码不正确！");
        }
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        HelpTake helpTake = helpTakeDao.selectById(helpTakeId);
        helpOrder.setStatus(4);
        helpTake.setStatus(2);
        helpTake.setEndTime(date);
        helpOrderDao.updateById(helpOrder);
        helpTakeDao.updateById(helpTake);
        userMoneyService.updateMoney(1,helpTake.getUserId(),helpTake.getMoney());
        Game game = gameService.getById(helpOrder.getGameId());
        UserMoneyDetails userMoneyDetails=new UserMoneyDetails();
        userMoneyDetails.setUserId(helpTake.getUserId());
        userMoneyDetails.setTitle("[接单完成]："+game.getGameName());
        userMoneyDetails.setContent("增加金额:"+helpTake.getMoney());
        userMoneyDetails.setType(1);
        userMoneyDetails.setMoney(helpTake.getMoney());
        userMoneyDetails.setCreateTime(date);
        userMoneyDetailsService.save(userMoneyDetails);
        BigDecimal sub = AmountCalUtils.sub(helpOrder.getMoney(), helpTake.getMoney());
        UserEntity userEntity = userService.selectUserById(helpOrder.getUserId());
        if (userEntity.getClientid() != null) {
            userService.pushToSingle("订单通知","您的订单已经完成！" , userEntity.getClientid());
        }
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setContent("您的订单已经完成！");
        messageInfo.setTitle("订单通知");
        messageInfo.setState(String.valueOf(5));
        messageInfo.setUserName(userEntity.getUserName());
        messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
        messageService.saveBody(messageInfo);
        return Result.success();
    }


    @Override
    public Result closeOrders(Long helpOrderId){
        HelpOrder helpOrder = helpOrderDao.selectById(helpOrderId);
        if(helpOrder.getStatus()!=3){
            return Result.error("请刷新后重试！");
        }
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        HelpTake helpTake = helpTakeDao.selectById(helpOrder.getHelpTakeId());
        helpOrder.setStatus(4);
        helpTake.setStatus(2);
        helpTake.setEndTime(date);
        helpOrderDao.updateById(helpOrder);
        helpTakeDao.updateById(helpTake);
        userMoneyService.updateMoney(1,helpTake.getUserId(),helpTake.getMoney());
        Game game = gameService.getById(helpOrder.getGameId());
        UserMoneyDetails userMoneyDetails=new UserMoneyDetails();
        userMoneyDetails.setUserId(helpTake.getUserId());
        userMoneyDetails.setTitle("[接单完成]："+game.getGameName());
        userMoneyDetails.setContent("增加金额:"+helpTake.getMoney());
        userMoneyDetails.setType(1);
        userMoneyDetails.setMoney(helpTake.getMoney());
        userMoneyDetails.setCreateTime(date);
        userMoneyDetailsService.save(userMoneyDetails);
        UserEntity userEntity = userService.selectUserById(helpOrder.getUserId());
        if (userEntity.getClientid() != null) {
            userService.pushToSingle("订单通知","您的订单已经完成！" , userEntity.getClientid());
        }
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setContent("您的订单已经完成！");
        messageInfo.setTitle("订单通知");
        messageInfo.setState(String.valueOf(5));
        messageInfo.setUserName(userEntity.getUserName());
        messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
        messageService.saveBody(messageInfo);
        return Result.success();
    }



    @Override
    public Result updateHelpTakeById(HelpTake helpTake){
        helpTakeDao.updateById(helpTake);
        return Result.success();
    }

    @Override
    public Result deleteById(Long id){
        helpTakeDao.deleteById(id);
        return Result.success();
    }





}
