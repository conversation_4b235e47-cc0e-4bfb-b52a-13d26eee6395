package com.sqx.modules.taking.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.dao.UserBrowseDao;
import com.sqx.modules.app.dao.UserCertificationDao;
import com.sqx.modules.app.dao.UserDao;
import com.sqx.modules.app.entity.UserCertification;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserBrowseService;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.orders.dao.OrdersDao;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.search.service.AppSearchService;
import com.sqx.modules.taking.dao.GameDao;
import com.sqx.modules.taking.dao.OrderTakingCommentDao;
import com.sqx.modules.taking.dao.OrderTakingDao;
import com.sqx.modules.taking.entity.OrderTaking;
import com.sqx.modules.taking.response.*;
import com.sqx.modules.taking.service.OrderTakingService;
import com.sqx.modules.task.dao.HelpTakeDao;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OrderTakingServiceImpl extends ServiceImpl<OrderTakingDao, OrderTaking> implements OrderTakingService {

    @Autowired
    private UserCertificationDao userCertificationDao;
    @Autowired
    private OrderTakingDao orderTakingDao;
    @Autowired
    private OrdersDao ordersDao;
    @Autowired
    private AppSearchService appSearchService;
    @Autowired
    private GameDao gameDao;
    @Autowired
    private UserBrowseService userBrowseService;
    @Autowired
    private UserDao userDao;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private UserService userService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private HelpTakeDao helpTakeDao;
    @Autowired
    private UserBrowseDao userBrowseDao;
    @Autowired
    private OrderTakingCommentDao orderTakingCommentDao;


    @Override
    public Result queryTaking(String city,String longitude, String latitude, Long userId, String like, Integer condition, Long bannerid, Long isRecommend, String id, IPage iPage, Long sex, String by,Integer classify,String salesNum) {
        String str = null;
        if (like != null) {
            str = "%" + like + "%";
            //添加搜索记录
            appSearchService.insetAppSearch(like, userId);
        }
        IPage<OrderTakingResponse> i = orderTakingDao.queryTaking(iPage,city, longitude, latitude, str, condition, bannerid, isRecommend, id, sex, by,classify,salesNum);
        return Result.success().put("data", new PageUtils(i));
    }

    @Override
    public Result queryLowTaking(Integer page,Integer limit,String city,String longitude, String latitude, Long userId, String like, Integer condition, Long bannerid, Long isRecommend, String id,  Long sex, String by,Integer classify,String salesNum) {
        String str = null;
        if (like != null) {
            str = "%" + like + "%";
            //添加搜索记录
            appSearchService.insetAppSearch(like, userId);
        }
        if (page == null || limit==null) {
            return Result.success().put("data", orderTakingDao.queryLowTaking(city, longitude, latitude, str, condition, bannerid, isRecommend, id, sex, by,classify,salesNum));
        } else {
            return Result.success().put("data", new PageUtils(orderTakingDao.queryLowTakings(new Page<>(page,limit),city, longitude, latitude, str, condition, bannerid, isRecommend, id, sex, by,classify,salesNum)));
        }
    }


    @Override
    public Result queryTakingDetails(Long id, Long userId,String longitude,String latitude) {
        TakingDetailsResponse takingDetailsResponse = baseMapper.queryTakingDetails(id,longitude,latitude);
        //添加浏览足迹
        userBrowseService.addAmount(userId,takingDetailsResponse.getUserId(), id);
        //添加访客
        userBrowseService.addVisitor(userId, takingDetailsResponse.getUserId());
        return Result.success().put("data",takingDetailsResponse);
    }

    @Override
    public Result queryTakingDetails(Long id,String longitude,String latitude) {
        TakingDetailsResponse takingDetailsResponse = baseMapper.queryTakingDetails(id,longitude,latitude);
        return Result.success().put("data",takingDetailsResponse);
    }

    @Override
    public Result insertOrderTaking(String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money, BigDecimal memberMoney,String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,String detailsImg,Integer authentication,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        OrderTaking orderTaking = new OrderTaking();
        //游戏类型id
        orderTaking.setGameId(gameId);
        orderTaking.setAuthentication(authentication);
        orderTaking.setMinNum(minNum);
        orderTaking.setServiceName(serviceName);
        orderTaking.setCarType(carType);
        orderTaking.setSafeguard(safeguard);
        //我的段位
        orderTaking.setDetailadd(detailadd);
        orderTaking.setSalesNum(0);
        orderTaking.setCount(0);
        orderTaking.setMyLevel(myLevel);
        //接单段位
        orderTaking.setOrderLevel(orderLevel);
        //接单时间
        orderTaking.setRegion(region);
        orderTaking.setOrderTakingTime(orderTakingTime);
        orderTaking.setUnit(unit);
        orderTaking.setClassify(classify);
        orderTaking.setOrderTakingArea(orderTakingArea);
        orderTaking.setOldMoney(money);
        orderTaking.setMoney(money);
        if(memberMoney==null){
            memberMoney=money;
        }
        orderTaking.setMemberMoney(memberMoney);
        //语音介绍
        orderTaking.setVoiceIntroduce(voiceIntroduce);
        //主页图片
        orderTaking.setHomepageImg(homepageImg);
        orderTaking.setDetailsImg(detailsImg);
        //创建时间
        orderTaking.setCreateTime(simpleDateFormat.format(new Date()));
        //修改时间
        orderTaking.setUpdateTime(simpleDateFormat.format(new Date()));
        //接单状态0进行中1待审核2已取消
        orderTaking.setStatus(0);
        //发布人
        orderTaking.setUserId(userId);
        //城市
        orderTaking.setCity(city);
        //精度
        orderTaking.setLongitude(longitude);
        //维度
        orderTaking.setLatitude(latitude);
        orderTaking.setIsdelete((long) 0);
        orderTaking.setSec(sec);
        orderTaking.setIsRecommend("1");
        int i = baseMapper.insert(orderTaking);
        if (i > 0) {
            return Result.success("发布成功！");
        } else {
            return Result.error("发布失败！");
        }
    }

    @Override
    public Result selectMyRelease(Long userId, Long page, Long limit, String status) {
        if (page == null || limit == null) {
            return Result.error("分页的条件为空！");
        } else {
            Page<MyReleaseResponse> iPage = new Page<>(page, limit);
            return Result.success().put("data", new PageUtils(baseMapper.selectMyRelease(iPage, userId, status)));
        }
    }


    @Override
    public Result deleteOrderTaking(Long id) {
        if (id == null) {
            return Result.error("删除id为空");
        } else {
            int i = baseMapper.deleteById(id);
            if (i > 0) {
                return Result.success();
            } else {
                return Result.error();
            }
        }
    }

    @Override
    public Result queryAllOrderTaking(Integer page,Integer limit,String city, String gameId, Long status, String userName, Long userId,Integer classify) {
        String str = null;
        if (userName != null && !(userName.equals(""))) {
            str = "%" + userName + "%";
        }
        IPage<TakingResponse> iPage1 = baseMapper.queryAllOrderTaking(new Page<TakingResponse>(page,limit),city, gameId, status, str,userId,classify);
        return Result.success().put("data", new PageUtils(iPage1));
    }

    @Override
    public Result auditorOrderTaking(Long id, Integer status,String content) {
        OrderTaking orderTaking = baseMapper.selectById(id);
        if (orderTaking == null) {
            return Result.error("接单信息不存在！");
        }else if(orderTaking.getStatus()!=1){
            return Result.error("已经审核过了！");
        }else {
            orderTaking.setStatus(status);
            orderTaking.setContent(content);
            baseMapper.updateById(orderTaking);

            UserEntity userEntity = userService.selectUserById(orderTaking.getUserId());
            MessageInfo messageInfo=new MessageInfo();
            if(status==0){
                messageInfo.setContent("您发布的订单审核通过了");
            }else{
                messageInfo.setContent("您发布的订单审核被拒绝了，原因："+content);
            }

            messageInfo.setTitle("发布信息审核通知");
            messageInfo.setState(String.valueOf(4));
            messageInfo.setUserName(userEntity.getUserName());
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            messageInfo.setCreateAt(simpleDateFormat.format(new Date()));
            messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
            messageInfo.setIsSee("0");
            messageService.saveBody(messageInfo);
            if(StringUtil.isNotBlank(userEntity.getClientid())){
                userService.pushToSingle(messageInfo.getTitle(),messageInfo.getContent(),userEntity.getClientid());
            }

            return Result.success();
        }
    }

    @Override
    public Result updateTakingStatus(Long id, Integer status,String content) {
        OrderTaking orderTaking = new OrderTaking();
        orderTaking.setId(id);
        orderTaking.setStatus(status);
        orderTaking.setContent(content);
        baseMapper.updateById(orderTaking);
        return Result.success();
    }

    @Override
    public Result deleteTaking(Long id) {
        OrderTaking orderTaking = baseMapper.selectById(id);
        if (orderTaking != null) {
            orderTaking.setIsdelete((long) 1);
            baseMapper.update(orderTaking, new QueryWrapper<OrderTaking>().eq("id", id));
            return Result.success();
        } else {
            return Result.error("订单不存在！");
        }
    }

    @Override
    public Result queryTakingOrder(Long id) {
        OrderTaking orderTaking = baseMapper.selectById(id);
//        if (orderTaking != null) {
//            orderTaking.setGame(gameDao.selectById(orderTaking.getGameId()));
//        }
        return Result.success().put("data", orderTaking);
    }

    @Override
    public Result updateTakingOrder(Long id, String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money, String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,Integer authentication,String detailsImg,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        OrderTaking orderTaking = new OrderTaking();
        orderTaking.setId(id);
        orderTaking.setDetailadd(detailadd);
        //游戏类型id
        orderTaking.setServiceName(serviceName);
        orderTaking.setCarType(carType);
        orderTaking.setSafeguard(safeguard);
        orderTaking.setGameId(gameId);
        //我的段位
        orderTaking.setMyLevel(myLevel);
        orderTaking.setAuthentication(authentication);
        orderTaking.setMinNum(minNum);
        //接单段位
        orderTaking.setOrderLevel(orderLevel);
        //接单时间
        orderTaking.setRegion(region);
        orderTaking.setOrderTakingTime(orderTakingTime);
        orderTaking.setUnit(unit);
        orderTaking.setClassify(classify);
        orderTaking.setDetailsImg( detailsImg);
        //接单大区
        orderTaking.setOrderTakingArea(orderTakingArea);
        CommonInfo rateInfo = commonInfoService.findOne(155);
        BigDecimal rate = money.multiply(new BigDecimal(rateInfo.getValue()));
        BigDecimal moneys = money.subtract(rate);
        orderTaking.setOldMoney(moneys);
        orderTaking.setMoney(money);
        //计算会员价格
        CommonInfo rateMemberInfo = commonInfoService.findOne(156);
        BigDecimal rateMember = money.multiply(new BigDecimal(rateMemberInfo.getValue()));
        orderTaking.setMemberMoney(rateMember);
        //语音介绍
        orderTaking.setVoiceIntroduce(voiceIntroduce);
        //主页图片
        orderTaking.setHomepageImg(homepageImg);
        //创建时间
        orderTaking.setUpdateTime(simpleDateFormat.format(new Date()));
        //接单状态0进行中1待审核2已取消
        orderTaking.setStatus(0);
        //发布人
        orderTaking.setUserId(userId);
        //城市
        orderTaking.setCity(city);
        //精度
        orderTaking.setLongitude(longitude);
        //维度
        orderTaking.setLatitude(latitude);
        orderTaking.setSec(sec);
        baseMapper.updateById(orderTaking);
        return Result.success();
    }

    @Override
    public Result updateTakingOrders(Long id, String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money,BigDecimal memberMoney,BigDecimal oldMoney, String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,Integer authentication,String detailsImg,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        OrderTaking orderTaking = new OrderTaking();
        orderTaking.setId(id);
        orderTaking.setDetailadd(detailadd);
        //游戏类型id
        orderTaking.setServiceName(serviceName);
        orderTaking.setCarType(carType);
        orderTaking.setSafeguard(safeguard);
        orderTaking.setRegion(region);
        orderTaking.setGameId(gameId);
        orderTaking.setSec(sec);
        orderTaking.setClassify(classify);
        orderTaking.setUnit(unit);
        orderTaking.setAuthentication(authentication);
        orderTaking.setDetailsImg( detailsImg);
        orderTaking.setMinNum(minNum);
        //我的段位
        orderTaking.setMyLevel(myLevel);
        //接单段位
        orderTaking.setOrderLevel(orderLevel);
        //接单时间
        orderTaking.setOrderTakingTime(orderTakingTime);
        //接单大区
        orderTaking.setOrderTakingArea(orderTakingArea);
        //价格
        orderTaking.setMoney(money);
        orderTaking.setMemberMoney(memberMoney);
        orderTaking.setOldMoney(oldMoney);
        //语音介绍
        orderTaking.setVoiceIntroduce(voiceIntroduce);
        //主页图片
        orderTaking.setHomepageImg(homepageImg);
        //创建时间
        orderTaking.setUpdateTime(simpleDateFormat.format(new Date()));
        //发布人
        orderTaking.setUserId(userId);
        //城市
        orderTaking.setCity(city);
        //精度
        orderTaking.setLongitude(longitude);
        //维度
        orderTaking.setLatitude(latitude);
        baseMapper.updateById(orderTaking);
        return Result.success();
    }


    @Override
    public Result queryMyTakingOrder(Long userId, Long page, Long limit, Long status) {
        Page<OrderTaking> ipage = new Page<>(page, limit);
        IPage<OrderTaking> iPage = baseMapper.selectPage(ipage, new QueryWrapper<OrderTaking>().eq("status", 0).or().eq("status", 2));
        List<OrderTaking> orderTakings = iPage.getRecords();
        for (OrderTaking orderTaking : orderTakings) {
            if (orderTaking != null) {
                QueryWrapper queryWrapper = new QueryWrapper<Orders>().eq("order_taking_id", orderTaking.getId());
                if (status != null && status != 0) {
                    queryWrapper.eq("state", status);
                }
                List<Orders> orders = ordersDao.selectList(queryWrapper);
                for (Orders order : orders) {
                    UserEntity user = userDao.selectOne(new QueryWrapper<UserEntity>().eq("user_id", order.getUserId()));
                    order.setUser(user);
                }
                orderTaking.setOrders(orders);
            }
        }
        return Result.success().put("data", iPage);
    }


    @Override
    public Result selectShopData(Long userId,String startTime,String endTime){
        //总收益
        BigDecimal sumOrdersMoney = ordersDao.selectOrdersMoneyByUserId(userId, null, null);
        //总销量
        int sumOrdersCount = ordersDao.selectOrdersCountByUserId(userId,null,null);
        //用户评价
        BigDecimal sumOrdersScore = ordersDao.selectOrderScoreByUserId(userId);
        //订单收入
        BigDecimal ordersMoney = ordersDao.selectOrdersMoneyByUserId(userId, startTime, endTime);
        //总订单数
        int ordersCount = ordersDao.selectOrdersCountByUserId(userId,startTime,endTime);
        //退款金额
        BigDecimal refundMoney = ordersDao.selectOrdersRefundMoneyByUserId(userId, startTime, endTime);
        //访客人数
        Integer userBrowseCount = userBrowseDao.selectUserBrowseCountByUserId(userId, startTime, endTime);
        //已取消
        Integer ordersRefundCount = ordersDao.selectOrdersRefundCountByUserId(userId, startTime, endTime,3);
        //待完成
        Integer ordersUnderwayCount = ordersDao.selectOrdersRefundCountByUserId(userId, startTime, endTime,1);
         ordersUnderwayCount += ordersDao.selectOrdersRefundCountByUserId(userId, startTime, endTime,5);
        //已完成
        Integer ordersAccomplishCount = ordersDao.selectOrdersRefundCountByUserId(userId, startTime, endTime,2);
        //已评价
        Integer commentCount = orderTakingCommentDao.selectCommntCountByUserId(userId, startTime, endTime);
        //上架中
        Integer shangCount = orderTakingDao.selectOrderTakingCountByUserId(userId,0);
        //已下架
        Integer xiaCount = orderTakingDao.selectOrderTakingCountByUserId(userId,2);
        Map<String,Object> result=new HashMap<>();
        result.put("sumOrdersMoney",sumOrdersMoney);
        result.put("sumOrdersCount",sumOrdersCount);
        result.put("sumOrdersScore",sumOrdersScore.setScale(2,BigDecimal.ROUND_HALF_DOWN));
        result.put("ordersMoney",ordersMoney);
        result.put("ordersCount",ordersCount);
        result.put("refundMoney",refundMoney);
        result.put("userBrowseCount",userBrowseCount);
        result.put("ordersRefundCount",ordersRefundCount);
        result.put("ordersUnderwayCount",ordersUnderwayCount);
        result.put("ordersAccomplishCount",ordersAccomplishCount);
        result.put("commentCount",commentCount);
        result.put("shangCount",shangCount);
        result.put("xiaCount",xiaCount);
        return Result.success().put("data",result);
    }


}
