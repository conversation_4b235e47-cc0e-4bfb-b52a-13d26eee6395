<template>
	<view class="ai-container">
		<!-- 顶部操作栏 -->
		<view class="top-bar">
			<view class="page-header">
				<text class="page-title">AI助手</text>
				<text class="page-subtitle">{{ getSelectedModelName() }}</text>
			</view>
			<view class="top-actions">
				<view class="action-btn model-btn" @click="selectModel">
					<u-icon name="setting" size="18" color="#666"></u-icon>
				</view>
				<view class="action-btn new-chat-btn" @click="createNewChat">
					<u-icon name="plus" size="20" color="#fff"></u-icon>
				</view>
			</view>
		</view>

		<!-- 对话列表 -->
		<view class="conversation-list">
			<view v-if="conversationList.length === 0 && !loading" class="empty-state">
				<view class="empty-icon">
					<u-icon name="chat" size="80" color="#ddd"></u-icon>
				</view>
				<text class="empty-title">还没有对话记录</text>
				<text class="empty-subtitle">开始你的第一次AI对话吧</text>
				<view class="empty-action">
					<view class="start-chat-btn" @click="createNewChat">
						<u-icon name="plus" size="20" color="#fff" style="margin-right: 8rpx;"></u-icon>
						<text class="btn-text">开始对话</text>
					</view>
				</view>
			</view>

			<view v-for="item in conversationList" :key="item.id" class="conversation-item" @click="enterChat(item)">
				<view class="conversation-avatar">
					<view class="avatar-icon">
						<u-icon name="chat" size="24" color="#0175FE"></u-icon>
					</view>
				</view>
				<view class="conversation-content">
					<view class="conversation-header">
						<text class="conversation-title">{{ item.title || '新对话' }}</text>
						<view class="conversation-meta">
							<text class="conversation-model">{{ getModelName(item.modelCode) }}</text>
							<text class="update-time">{{ formatTime(item.updateTime) }}</text>
						</view>
					</view>
					<view class="conversation-preview">
						<text class="last-message">{{ item.lastMessage || '开始新的对话...' }}</text>
					</view>
				</view>
				<view class="conversation-actions" @click.stop>
					<u-icon name="more-dot-fill" size="20" color="#ccc" @click="showActions(item)"></u-icon>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<u-loadmore :status="loadmoreStatus" @loadmore="loadMore" v-if="conversationList.length > 0"></u-loadmore>

		<!-- 操作菜单 -->
		<u-action-sheet :list="actionList" v-model="showActionSheet" @click="handleAction"></u-action-sheet>

		<!-- 编辑标题弹窗 -->
		<u-modal v-model="showEditTitle" title="编辑标题" :show-cancel-button="true" @confirm="updateTitle" @cancel="cancelEdit">
			<view class="edit-title-content">
				<input
					v-model="editTitleValue"
					placeholder="请输入对话标题"
					maxlength="50"
					class="title-input"
				/>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'

	export default {
		components: {
			empty
		},
		data() {
			return {
				conversationList: [],
				loading: false,
				creating: false,
				page: 1,
				limit: 20,
				loadmoreStatus: 'loadmore',
				showActionSheet: false,
				currentItem: null,
				actionList: [
					{ text: '编辑标题', value: 'edit' },
					{ text: '删除对话', value: 'delete', color: '#f56c6c' }
				],
				showEditTitle: false,
				editTitleValue: '',
				selectedModel: 'deepseek-chat', // 默认模型
				modelList: []
			}
		},
		onLoad() {
			this.loadModels()
			this.loadConversations()
		},
		onShow() {
			// 从模型选择页面返回时刷新
			const selectedModel = uni.getStorageSync('selectedAiModel')
			if (selectedModel && selectedModel !== this.selectedModel) {
				this.selectedModel = selectedModel
			}
			// 刷新对话列表
			this.refreshData()
		},
		onPullDownRefresh() {
			this.refreshData()
		},
		onReachBottom() {
			this.loadMore()
		},
		methods: {
			// 加载可用模型
			loadModels() {
				this.$Request.get('/app/ai/chat/models').then(res => {
					console.log('模型列表:', res)
					if (res.code === 0) {
						this.modelList = res.data || []
						// 设置默认选中的模型
						const savedModel = uni.getStorageSync('selectedAiModel')
						if (savedModel) {
							this.selectedModel = savedModel
						} else if (this.modelList.length > 0) {
							this.selectedModel = this.modelList[0].modelCode
							uni.setStorageSync('selectedAiModel', this.selectedModel)
						}
					} else {
						console.error('加载模型失败:', res.msg)
					}
				}).catch(error => {
					console.error('加载模型失败:', error)
				})
			},

			// 加载对话列表
			loadConversations(refresh = false) {
				if (this.loading) return

				this.loading = true
				if (refresh) {
					this.page = 1
					this.conversationList = []
				}

				let data = {
					page: this.page,
					limit: this.limit
				}

				this.$Request.get('/app/ai/chat/conversation/list', data).then(res => {
					console.log('对话列表:', res)
					if (res.code === 0) {
						const newList = res.data.list || []
						if (refresh) {
							this.conversationList = newList
						} else {
							this.conversationList.push(...newList)
						}

						// 更新加载状态
						if (newList.length < this.limit) {
							this.loadmoreStatus = 'nomore'
						} else {
							this.loadmoreStatus = 'loadmore'
							this.page++
						}
					} else {
						console.error('加载对话列表失败:', res.msg)
						this.$queue.showToast(res.msg || '加载失败，请重试')
					}
				}).catch(error => {
					console.error('加载对话列表失败:', error)
					this.$queue.showToast('加载失败，请重试')
				}).finally(() => {
					this.loading = false
					uni.stopPullDownRefresh()
				})
			},

			// 刷新数据
			refreshData() {
				this.loadConversations(true)
			},

			// 加载更多
			loadMore() {
				if (this.loadmoreStatus === 'loadmore') {
					this.loadConversations()
				}
			},

			// 创建新对话
			async createNewChat() {
				if (this.creating) return

				if (!this.selectedModel) {
					this.$queue.showToast('请先选择AI模型')
					return
				}

				this.creating = true
				try {
					// 直接跳转到聊天页面，不需要先创建对话
					uni.navigateTo({
						url: `/pages/ai/chat?modelCode=${this.selectedModel}&isNew=true`
					})
				} catch (error) {
					console.error('跳转失败:', error)
					this.$queue.showToast('跳转失败，请重试')
				} finally {
					this.creating = false
				}
			},

			// 进入聊天页面
			enterChat(item) {
				uni.navigateTo({
					url: `/pages/ai/chat?conversationId=${item.id}&modelCode=${item.modelCode}`
				})
			},

			// 选择模型
			selectModel() {
				uni.navigateTo({
					url: '/pages/ai/models'
				})
			},

			// 显示操作菜单
			showActions(item) {
				this.currentItem = item
				this.showActionSheet = true
			},

			// 处理操作
			handleAction(index) {
				const action = this.actionList[index]
				if (action.value === 'edit') {
					this.editTitle()
				} else if (action.value === 'delete') {
					this.deleteConversation()
				}
			},

			// 编辑标题
			editTitle() {
				this.editTitleValue = this.currentItem.title || ''
				this.showEditTitle = true
			},

			// 更新标题
			updateTitle() {
				if (!this.editTitleValue.trim()) {
					this.$queue.showToast('标题不能为空')
					return
				}

				let data = { title: this.editTitleValue.trim() }

				this.$Request.post(`/app/ai/chat/conversation/${this.currentItem.id}/title`, data).then(res => {
					console.log('更新标题:', res)
					if (res.code === 0) {
						// 更新本地数据
						const index = this.conversationList.findIndex(item => item.id === this.currentItem.id)
						if (index !== -1) {
							this.conversationList[index].title = this.editTitleValue.trim()
						}
						this.$queue.showToast('标题更新成功')
					} else {
						this.$queue.showToast(res.msg || '更新失败')
					}
				}).catch(error => {
					console.error('更新标题失败:', error)
					this.$queue.showToast('更新失败，请重试')
				})
			},

			// 取消编辑
			cancelEdit() {
				this.editTitleValue = ''
			},

			// 删除对话
			deleteConversation() {
				uni.showModal({
					title: '确认删除',
					content: '删除后无法恢复，确定要删除这个对话吗？',
					success: (res) => {
						if (res.confirm) {
							this.$Request.post(`/app/ai/chat/conversation/${this.currentItem.id}/delete`).then(result => {
								console.log('删除对话:', result)
								if (result.code === 0) {
									// 从列表中移除
									const index = this.conversationList.findIndex(item => item.id === this.currentItem.id)
									if (index !== -1) {
										this.conversationList.splice(index, 1)
									}
									this.$queue.showToast('删除成功')
								} else {
									this.$queue.showToast(result.msg || '删除失败')
								}
							}).catch(error => {
								console.error('删除对话失败:', error)
								this.$queue.showToast('删除失败，请重试')
							})
						}
					}
				})
			},

			// 获取模型名称
			getModelName(modelCode) {
				const model = this.modelList.find(m => m.modelCode === modelCode)
				return model ? model.modelName : modelCode
			},

			// 获取当前选中的模型名称
			getSelectedModelName() {
				const model = this.modelList.find(m => m.modelCode === this.selectedModel)
				return model ? model.modelName : '选择模型'
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return ''

				// 处理iOS兼容性：将 "yyyy-MM-dd HH:mm:ss" 格式转换为 "yyyy/MM/dd HH:mm:ss"
				let processedTimeStr = timeStr
				if (typeof timeStr === 'string') {
					processedTimeStr = timeStr.replace(/-/g, '/')
				}

				const time = new Date(processedTimeStr)

				// 检查日期是否有效
				if (isNaN(time.getTime())) {
					console.warn('无效的时间格式:', timeStr)
					return ''
				}

				const now = new Date()
				const diff = now - time

				if (diff < 60000) { // 1分钟内
					return '刚刚'
				} else if (diff < 3600000) { // 1小时内
					return Math.floor(diff / 60000) + '分钟前'
				} else if (diff < 86400000) { // 24小时内
					return Math.floor(diff / 3600000) + '小时前'
				} else if (diff < 604800000) { // 7天内
					return Math.floor(diff / 86400000) + '天前'
				} else {
					// 超过7天显示具体日期
					return time.toLocaleDateString('zh-CN', {
						month: 'short',
						day: 'numeric'
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.ai-container {
		// background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		min-height: 100vh;
	}

	.top-bar {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
		position: sticky;
		top: 0;
		z-index: 100;
	}

	.page-header {
		flex: 1;
	}

	.page-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 4rpx;
	}

	.page-subtitle {
		font-size: 24rpx;
		color: #666;
		display: block;
	}

	.top-actions {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.action-btn {
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.model-btn {
		background: #f5f5f5;
		border: 1rpx solid #e0e0e0;
	}

	.new-chat-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
	}

	.conversation-list {
		padding: 20rpx;
	}

	.empty-state {
		text-align: center;
		padding: 120rpx 40rpx;
		background: rgba(255, 255, 255, 0.9);
		margin: 20rpx;
		border-radius: 24rpx;
		backdrop-filter: blur(10px);
	}

	.empty-icon {
		margin-bottom: 32rpx;
	}

	.empty-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 12rpx;
	}

	.empty-subtitle {
		font-size: 28rpx;
		color: #666;
		display: block;
		margin-bottom: 48rpx;
	}

	.empty-action {
		display: flex;
		justify-content: center;
	}

	.start-chat-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;
		padding: 24rpx 48rpx;
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	}

	.btn-text {
		color: #fff;
		font-size: 28rpx;
		font-weight: 500;
	}

	.conversation-item {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		margin-bottom: 16rpx;
		border-radius: 20rpx;
		padding: 24rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		transition: all 0.3s ease;
	}

	.conversation-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
	}

	.conversation-avatar {
		margin-right: 20rpx;
	}

	.avatar-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.conversation-content {
		flex: 1;
		min-width: 0;
	}

	.conversation-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 12rpx;
	}

	.conversation-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		line-height: 1.4;
		flex: 1;
		margin-right: 16rpx;
	}

	.conversation-meta {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		gap: 8rpx;
	}

	.conversation-model {
		font-size: 22rpx;
		color: #667eea;
		background: rgba(102, 126, 234, 0.1);
		padding: 6rpx 12rpx;
		border-radius: 12rpx;
		border: 1rpx solid rgba(102, 126, 234, 0.2);
		white-space: nowrap;
	}

	.update-time {
		font-size: 22rpx;
		color: #999;
		white-space: nowrap;
	}

	.conversation-preview {
		margin-top: 8rpx;
	}

	.last-message {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.conversation-actions {
		padding: 16rpx;
		margin-left: 8rpx;
	}

	.edit-title-content {
		padding: 40rpx 0;
	}

	.title-input {
		width: 100%;
		padding: 24rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		font-size: 30rpx;
		outline: none;
		background: #fafafa;
		transition: all 0.3s ease;
	}

	.title-input:focus {
		border-color: #667eea;
		background: #fff;
		box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
	}
</style>
