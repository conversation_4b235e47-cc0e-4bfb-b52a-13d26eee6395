package com.sqx.modules.orders.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MyOrderResponse implements Serializable {

    private Long ordersId;
    /**
     * 订单状态0待支付1进行中2已完成3已退款
     */
    private String state;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 接单id
      */
    private Long orderTakingId;
    /**
     *接单用户id
     */
    private Long userId;
    /**
     * 头像
      */
    private String avatar;
    /**
     * 游戏名称
     */
    private String userName;
    /**
     *游戏名称
     */
    private String gameName;
    /**
     * 数量
      */
    private int orderNumber;
    /**
     * 价格
     */
    private Double payMoney;

    private String gameImg;


    private Integer commentCount;
    private Integer classify;
    private String unit;
    private String myLevel;
    private String homepageImg;
    private String province;
    private String city;
    private String district;
    private String detailsAddress;
    private BigDecimal rate;
    private BigDecimal zhiRate;
    private Long zhiUserId;
    private BigDecimal feiRate;
    private Long feiUserId;
    private BigDecimal pingRate;
    private String code;
    private String carNo;
    private String carType;
    private String carColor;
    private String carName;
    private String carPhone;
    private String startImg;
    private String endImg;
    private String distance;

}
