package com.sqx.modules.appointInformation.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-08
 */
@Data
public class AppointInformation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预约信息id
     */
    @TableId(value = "appoint_id", type = IdType.AUTO)
    @ApiModelProperty("预约信息id")
    private Long appointId;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Long ordersId;

    /**
     * 医院id
     */
    @ApiModelProperty("医院id")
    private Long hospitalId;

    /**
     * 类型 1陪诊 2陪护
     */
    @ApiModelProperty("类型 1陪诊 2陪护 3助浴 4护理")
    private Integer type;

    /**
     * 就诊人信息id
     */
    @ApiModelProperty("就诊人信息id")
    private Long patientId;

    /**
     * 预约时间
     */
    @ApiModelProperty("预约时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private String hopeTime;

    /**
     * 科室id
     */
    @ApiModelProperty("科室id")
    private Long departmentId;


    /**
     * 服务id
     */
    @ApiModelProperty("服务id")
    private Long serviceId;

    /**
     * 床号(详:字典)
     */
    @ApiModelProperty("床号(详:字典)")
    private String badNo;

    /**
     * 症状(详:字典)
     */
    @ApiModelProperty("症状(详:字典)")
    private String symptom;

    /**
     * 自理能力(详:字典)
     */
    @ApiModelProperty("自理能力(详:字典)")
    private String selfAbility;

    /**
     * 护理需求(详:字典)
     */
    @ApiModelProperty("护理需求(详:字典)")
    private String nursingNeeds;

    /**
     * 服务天数
     */
    @ApiModelProperty("服务天数")
    private Integer serviceNum;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String phone;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 图片备注
     */
    @ApiModelProperty("图片备注")
    private String imgRemarks;

    /**
     * 经度(医院地址)
     */
    @ApiModelProperty("经度(医院地址)")
    private Double lat;

    /**
     * 纬度(医院地址)
     */
    @ApiModelProperty("纬度(医院地址)")
    private Double lng;

    /**
     * 省(医院地址)
     */
    @ApiModelProperty("省(医院地址)")
    private String province;

    /**
     * 市(医院地址)
     */
    @ApiModelProperty("市(医院地址)")
    private String city;

    /**
     * 县(医院地址)
     */
    @ApiModelProperty("县(医院地址)")
    private String district;

    /**
     * 详细地址(医院地址)
     */
    @ApiModelProperty("详细地址(医院地址)")
    private String detailsAddress;
    /**
     * 医院名称
     */
    @ApiModelProperty("医院名称")
    private String hospitalName;

    /**
     * 科室名称
     */
    @ApiModelProperty("科室名称")
    private String departmentName;
    /**
     * 用户收货地址id
     */
    @ApiModelProperty("用户收货地址id")
    private Long addressId;
    /**
     * 省(用户地址)
     */
    @ApiModelProperty("省(用户地址)")
    private String userProvince;
    /**
     * 市(用户地址)
     */
    @ApiModelProperty("市(用户地址)")
    private String userCity;
    /**
     * 县(用户地址)
     */
    @ApiModelProperty("县(用户地址)")
    private String userDistrict;
    /**
     * 详细地址(用户地址)
     */
    @ApiModelProperty("详细地址(用户地址)")
    private String userDetailsAddress;
    /**
     * 经度(用户地址)
     */
    @ApiModelProperty("经度(用户地址)")
    private Double userLat;
    /**
     * 纬度(用户地址)
     */
    @ApiModelProperty("纬度(用户地址)")
    private Double userLng;
    /**
     * 报告类型(详:字典)
     */
    @ApiModelProperty("报告类型(详:字典)")
    private String reportType;

    /**
     * 专享归属(详:字典)
     */
    @ApiModelProperty("专享归属(详:字典)")
    private String exclusiveType;

    /**
     * 药物类型(详:字典)
     */
    @ApiModelProperty("药物类型(详:字典)")
    private String drugsType;
    /**
     * 药物名称
     */
    @ApiModelProperty("药物名称")
    private String drugsName;
    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serviceName;
    /**
     * 收货人姓名
     */
    @ApiModelProperty("收货人姓名")
    private String userName;
    /**
     * 收货人电话
     */
    @ApiModelProperty("收货人电话")
    private String userPhone;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    private Integer serviceType;

    @TableField(exist = false)
    private PatientInfo patientInfo;

    /**
     * 服务图片
     */
    @TableField(exist = false)
    private String img;

    /**
     * 服务标签
     */
    @TableField(exist = false)
    private String tags;


    /**
     * 医院图片
     */
    @TableField(exist = false)
    private String hospitalImg;


    /**
     * 一级科室名称
     */
    @TableField(exist = false)
    private String departmentOneName;
    /**
     * 二级科室名称
     */
    @TableField(exist = false)
    private String departmentTwoName;
    /**
     * 医院等级
     */
    @TableField(exist = false)
    @ApiModelProperty("医院等级")
    private String hospitalLevel;

    /**
     * 医院类型
     */
    @TableField(exist = false)
    @ApiModelProperty("医院类型")
    private String hospitalType;
}
