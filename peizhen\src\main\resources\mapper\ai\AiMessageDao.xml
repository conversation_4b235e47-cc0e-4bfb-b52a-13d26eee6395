<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.ai.dao.AiMessageDao">

    <!-- 查询对话的消息列表 -->
    <select id="selectConversationMessages" resultType="com.sqx.modules.ai.entity.AiMessage">
        SELECT id, conversation_id, user_id, role, content, model_code,
               prompt_tokens, completion_tokens, total_tokens, response_time,
               error_message, create_time
        FROM ai_message
        WHERE conversation_id = #{conversationId}
        ORDER BY id ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 分页查询对话的消息列表 -->
    <select id="selectConversationMessagesPage" resultType="com.sqx.modules.ai.entity.AiMessage">
        SELECT id, conversation_id, user_id, role, content, model_code,
               prompt_tokens, completion_tokens, total_tokens, response_time,
               error_message, create_time
        FROM ai_message
        WHERE conversation_id = #{conversationId}
        ORDER BY id ASC
    </select>

    <!-- 统计对话的消息数量和token消耗 -->
    <select id="selectConversationStats" resultType="com.sqx.modules.ai.entity.AiMessage">
        SELECT COUNT(*) as messageCount,
               COALESCE(SUM(total_tokens), 0) as totalTokens
        FROM ai_message
        WHERE conversation_id = #{conversationId}
    </select>

</mapper>
