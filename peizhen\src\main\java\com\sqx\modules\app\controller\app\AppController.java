package com.sqx.modules.app.controller.app;


import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.annotation.LoginUser;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.AppService;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.rewardLevel.entity.RewardLevel;
import com.sqx.modules.rewardLevel.service.RewardLevelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * APP登录授权
 */
@RestController
@RequestMapping("/app/user")
@Api(value = "APP管理", tags = {"APP管理"})
public class AppController {


    @Autowired
    private AppService appService;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private RewardLevelService levelService;
    @Autowired
    private UserService userService;
    @Autowired
    private OrdersService ordersService;

    @RequestMapping(value = "/selectUserByIds", method = RequestMethod.GET)
    @ApiOperation("获取用户详细信息")
    @ResponseBody
    public Result selectUserByIds(Long userId) {
        return Result.success().put("data", userService.selectUserById(userId));
    }

    @Login
    @RequestMapping(value = "/updatePwd", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("用户端修改密码")
    public Result updatePwd(@LoginUser UserEntity user, String pwd, String oldPwd) {
        if (!user.getPassword().equals(DigestUtils.sha256Hex(oldPwd))) {
            return Result.error("原始密码不正确！");
        }
        if (pwd.equals(oldPwd)) {
            return Result.error("新密码不能与旧密码相同！");
        }
        user.setPassword(DigestUtils.sha256Hex(pwd));
        userService.updateById(user);
        return Result.success();
    }

    @Login
    @RequestMapping(value = "/updatePhone", method = RequestMethod.POST)
    @ApiOperation("用户端换绑手机号")
    @ResponseBody
    public Result updatePhone(@RequestAttribute("userId") Long userId, @RequestParam String phone, @RequestParam String msg) {
        return userService.updatePhone(phone, msg, userId);
    }

    @Login
    @RequestMapping(value = "/updateUser", method = RequestMethod.POST)
    @ApiOperation("用户修改个人信息")
    @ResponseBody
    public Result updateUser(@RequestBody UserEntity userEntity, @RequestAttribute("userId") Long userId) {
        userEntity.setUserId(userId);
        userService.updateById(userEntity);
        return Result.success();
    }

    @Login
    @RequestMapping(value = "/updateUserImageUrl", method = RequestMethod.POST)
    @ApiOperation("用户修改头像")
    @ResponseBody
    public Result updateUserImageUrl(@LoginUser UserEntity user, String avatar) {
        user.setAvatar(avatar);
        userService.updateById(user);
        return Result.success();
    }

    @Login
    @RequestMapping(value = "/updateUserName", method = RequestMethod.POST)
    @ApiOperation("用户修改昵称")
    @ResponseBody
    public Result updateUserName(@LoginUser UserEntity user, String userName) {
        user.setUserName(userName);
        userService.updateById(user);
        return Result.success();
    }

    @Login
    @RequestMapping(value = "/selectUserById", method = RequestMethod.GET)
    @ApiOperation("获取用户详细信息")
    @ResponseBody
    public Result selectUserById(@RequestAttribute("userId") Long userId) {
        UserEntity userEntity = userService.getById(userId);
        if (userEntity != null) {
            if (userEntity.getRate() == null) {
                userEntity.setRate(new BigDecimal(commonInfoService.findOne(206).getValue()));
                userService.updateById(userEntity);
            }
            if (userEntity.getZhiRate() == null) {
                userEntity.setZhiRate(new BigDecimal(commonInfoService.findOne(207).getValue()));
                userService.updateById(userEntity);
            }
            if (userEntity.getFeiRate() == null) {
                userEntity.setFeiRate(new BigDecimal(commonInfoService.findOne(208).getValue()));
                userService.updateById(userEntity);
            }
            if (userEntity.getRewardId() == null) {
                CommonInfo info = commonInfoService.findOne(339);
                if (info != null) {
                    RewardLevel rewardLevel = levelService.getById(info.getValue());
                    if (rewardLevel != null) {
                        userEntity.setRewardId(rewardLevel.getRewardId());
                        userEntity.setLevelName(rewardLevel.getLevelName());
                        userEntity.setIconImg(rewardLevel.getIconImg());
                        userService.updateById(userEntity);
                    }
                }
            }
        }
        return Result.success().put("data", userEntity);
    }

    @RequestMapping(value = "/selectNewApp", method = RequestMethod.GET)
    @ApiOperation("升级检测")
    @ResponseBody
    public Result selectNewApp() {
        return Result.success().put("data", appService.selectNewApp());
    }

    @RequestMapping(value = "/updateClientId", method = RequestMethod.GET)
    @ApiOperation("绑定ClientId")
    @ResponseBody
    public Result updateClientId(String clientId, Long userId) {
        UserEntity userEntity = new UserEntity();
        userEntity.setUserId(userId);
        userEntity.setClientid(clientId);
        return Result.success();
    }

    @GetMapping(value = "/getNursingList")
    @ApiOperation("获取护工列表")
    @ResponseBody
    public Result getNursingList(Integer page, Integer limit, Integer sex, Integer workMin, Integer workMax, Integer ageMin, Integer ageMax, String city) {
        return Result.success().put("data", userService.getNursingList(page, limit, sex, workMin, workMax, ageMin, ageMax, city));
    }

    @GetMapping(value = "/getNursingListV5")
    @ApiOperation("获取护工列表")
    @ResponseBody
    public Result getNursingListV5(Integer page, Integer limit, Integer sex, Integer workMin, Integer workMax, Integer ageMin, Integer ageMax, String city, Integer authentication, Integer orderCount, Integer finalScore,String realName,String phone) {
        return Result.success().put("data", userService.getNursingListV5(page, limit, sex, workMin, workMax, ageMin, ageMax, city, authentication, orderCount, finalScore,realName, phone));
    }

    @Login
    @ApiOperation("获取优惠券数量、投诉记录、余额")
    @GetMapping("getUserData")
    public Result getUserData(@RequestAttribute("userId") Long userId) {
        return Result.success().put("data", userService.getUserData(userId));
    }
}
