package com.sqx.modules.evaluate.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.evaluate.entity.SysEvaluate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Mapper
public interface SysEvaluateDao extends BaseMapper<SysEvaluate> {

    String getTotalScore(@Param("userId") Long userId);

    IPage<SysEvaluate> getUserEvaluateList(@Param("pages") Page<SysEvaluate> pages, @Param("evaluate") SysEvaluate evaluate);
}
