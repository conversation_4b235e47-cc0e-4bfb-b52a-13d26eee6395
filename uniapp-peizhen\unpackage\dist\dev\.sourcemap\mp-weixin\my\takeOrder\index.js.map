{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/index.vue?d64c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/index.vue?65f6", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/index.vue?50eb", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/index.vue?2883", "uni-app:///my/takeOrder/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/index.vue?3de3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/index.vue?6700"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "mescrollBody", "meTabs", "empty", "data", "goods", "tabs", "title", "status", "tabIndex", "game", "page", "limit", "userId", "nick<PERSON><PERSON>", "avatar", "customStyle", "color", "border", "customStyle1", "onLoad", "onShow", "methods", "bindtakeDetail", "console", "uni", "url", "downCallback", "upCallback", "res", "ret", "tabChange", "cancelOrder", "content", "success", "id", "that", "clickItem", "focusedUserId", "goNav"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4DxvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;EAAA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IAAA;IACA;MACAC;MAAA;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAL;MACAM;MACAC;MACAC;QACAC;QACAC;MAAA,yDAEA,8DACA,iEACA,gEACA,6BACA;MACAC;QACAF;QACAC;MAAA,0DAEA,+DACA,kEACA,iEACA;IAEA;EACA;EACAE;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACA;MACAC;QACAC;MACA;IACA;IACA,YACAC;MACA;MACA;MACA;MACA;IACA;IACA,wDACAC;MAAA;MACA;MAEA;QACApB;QACAG;QACAC;MACA;MACA;QACAa;QACA;QACA;QACAI;UACA;YACA;cACAC;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;UAAA;QAEA;QACA;QACA;UACA;QACA;QACA;MAEA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAR;QACAlB;QACA0B;QACAC;UACA;YACA;cACAC;cACA3B;YACA;YACA4B;cACA;gBAEAA;cACA;YACA;UACA;QACA;MACA;IAEA;IACA;IACAC;MAAA;MACA;QACAxB;QACAyB;MACA;MACA;QACA;UACA;UACAb;YACAC,yEACA;UACA;QACA;MACA;IACA;IACAa;MACAd;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/OA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/takeOrder/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/takeOrder/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=9842d816&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/takeOrder/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=9842d816&\"", "var components\ntry {\n  components = {\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-button/u-button\" */ \"@/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goods.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<!-- 菜单悬浮的原理: 通过给菜单添加position:sticky实现, 用法超简单, 仅APP端的低端机不兼容 https://caniuse.com/#feat=css-sticky -->\n<template>\n\t<view>\n\t\t<!-- 对于mescroll-body: 需设置:sticky=\"true\", 此应避免在mescroll-body标签前面加其他非定位的元素, 否则下拉区域会被挤出, 无法会隐藏.-->\n\t\t<!-- 对于mescroll-uni: 则无需设置:sticky=\"true\", 无其他限制和要求 -->\n\t\t<!-- sticky吸顶悬浮的菜单, 父元素必须是 mescroll -->\n\t\t<view class=\"sticky-tabs\">\n\t\t\t<me-tabs v-model=\"tabIndex\" nameKey='title' :tabs=\"tabs\" @change=\"tabChange\"></me-tabs>\n\t\t</view>\n\t\t<mescroll-body :sticky=\"true\" ref=\"mescrollRef\" @init=\"mescrollInit\" @down=\"downCallback\" @up=\"upCallback\">\n\t\t\t<!-- 数据列表 -->\n\t\t\t<view class=\"margin-lr-sm margin-top-16 padding-sm bg radius\" v-for=\"(item,index) in goods\" :key='index'\n\t\t\t\t@click=\"bindtakeDetail(item.ordersId)\">\n\t\t\t\t<view class=\"flex justify-between\">\n\t\t\t\t\t<view class=\"text-blue\">{{item.statusName}}</view>\n\t\t\t\t\t<view class=\"text-white\">{{item.createTime}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\" u-flex u-p-t-30\">\n\t\t\t\t\t<view class=\"u-m-r-10\">\n\t\t\t\t\t\t<!-- <u-avatar :src=\"item.homepageImg\" mode=\"square\" size=\"100\"></u-avatar> -->\n\t\t\t\t\t\t<!-- <u-avatar :src=\"order.user.avatar\" size=\"68\"></u-avatar> -->\n\t\t\t\t\t\t<image :src=\"item.homepageImg\" style=\"width: 140rpx;height: 140rpx;\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-flex-1 text-white margin-left-xs\">\n\t\t\t\t\t\t<view class=\"text-30  text-bold u-line-1\" style=\"width: 500rpx;\">{{item.myLevel}}</view>\n\t\t\t\t\t\t<!-- <view class=\"u-font-16  text-bold\">标题</view> -->\n\n\t\t\t\t\t\t<view class=\"u-font-14 margin-top-xs u-tips-color flex justify-between\">\n\t\t\t\t\t\t\t<view class=\"text-white\">\n\t\t\t\t\t\t\t\t<view style=\"display: inline-flex;\">\n\t\t\t\t\t\t\t\t\t<view class=\"margin-top-xs text-white text-df\"\n\t\t\t\t\t\t\t\t\t\tv-for=\"(item,index) in item.gameName \" :key=\"index\"\n\t\t\t\t\t\t\t\t\t\tstyle=\"margin-right: 10rpx;\">{{item}}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"text-white text-right margin-top-sm\">\n\t\t\t\t\t\t\t实收: <text class=\"text-lg\">{{item.oldMoney*item.orderNumber}}元</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"margin-top-sm\" v-if=\"item.remarks\">\n\t\t\t\t\t备注：<text class=\"text-red\">{{item.remarks}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex justify-end u-p-t-20\">\n\t\t\t\t\t<u-button v-if=\"item.state == 1\" :custom-style=\"customStyle\" shape=\"circle\" :plain=\"true\"\n\t\t\t\t\t\t@click=\"cancelOrder(item,3)\">拒接接单</u-button>\n\t\t\t\t\t<!-- <u-button v-if=\"item.state == 3\" :custom-style=\"customStyle\" shape=\"circle\" :plain=\"true\" @click=\"delOrder(item)\">拒接接单</u-button> -->\n\t\t\t\t\t<u-button v-if=\"item.state == 1\" :custom-style=\"customStyle1\" shape=\"circle\" :plain=\"true\"\n\t\t\t\t\t\t@click=\"cancelOrder(item,2)\">完成接单</u-button>\n\t\t\t\t\t<u-button :custom-style=\"customStyle\" shape=\"circle\" :plain=\"true\" @click=\"clickItem(item)\">联系TA\n\t\t\t\t\t</u-button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<empty v-if=\"goods.length == 0\"></empty>\n\t\t</mescroll-body>\n\t</view>\n</template>\n\n<script>\n\timport MescrollMixin from \"../components/mescroll-uni/components/mescroll-uni/mescroll-mixins.js\";\n\timport mescrollBody from \"../components/mescroll-uni/components/mescroll-body/mescroll-body.vue\";\n\timport meTabs from \"../components/mescroll-uni/me-tabs/me-tabs.vue\";\n\timport empty from '../../components/empty.vue'\n\n\texport default {\n\t\tmixins: [MescrollMixin], // 使用mixin\n\t\tcomponents: {\n\t\t\tmescrollBody,\n\t\t\tmeTabs,\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tgoods: [], // 数据列表\n\t\t\t\ttabs: [{\n\t\t\t\t\t\ttitle: '全部',\n\t\t\t\t\t\tstatus: ''\n\t\t\t\t\t}, {\n\t\t\t\t\t\ttitle: '进行中',\n\t\t\t\t\t\tstatus: '1'\n\t\t\t\t\t}, {\n\t\t\t\t\t\ttitle: '已完成',\n\t\t\t\t\t\tstatus: '2'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ttitle: '已拒绝',\n\t\t\t\t\t\tstatus: '3'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttabIndex: 0, // tab下标\n\t\t\t\tgame: [],\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10,\n\t\t\t\tuserId: 0,\n\t\t\t\tstatus: 1,\n\t\t\t\tnickName: '',\n\t\t\t\tavatar: '',\n\t\t\t\tcustomStyle: {\n\t\t\t\t\tcolor: '#999999',\n\t\t\t\t\tborder: '2rpx solid #999999',\n\t\t\t\t\t// backgroundColor: '#1E1F31',\n\t\t\t\t\tborder: \"8rpx\",\n\t\t\t\t\twidth: '180rpx',\n\t\t\t\t\theight: '54rpx',\n\t\t\t\t\tmargin: \"0 0 0 20rpx\"\n\t\t\t\t},\n\t\t\t\tcustomStyle1: {\n\t\t\t\t\tcolor: '#557EFD',\n\t\t\t\t\tborder: '2rpx solid #557EFD',\n\t\t\t\t\t// backgroundColor: '#1E1F31',\n\t\t\t\t\tborder: \"8rpx\",\n\t\t\t\t\twidth: '180rpx',\n\t\t\t\t\theight: '54rpx',\n\t\t\t\t\tmargin: \"0 0 0 20rpx\"\n\t\t\t\t},\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.$queue.showLoading(\"加载中...\");\n\t\t\tthis.userId = uni.getStorageSync('userId')\n\t\t\tthis.nickName = uni.getStorageSync('nickName')\n\t\t},\n\t\tonShow() {\n\t\t\tthis.downCallback()\n\t\t\tthis.upCallback(this.page)\n\t\t},\n\t\tmethods: {\n\t\t\t// 接单详情\n\t\t\tbindtakeDetail(e) {\n\t\t\t\tconsole.log(e)\n\t\t\t\tlet id = e\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: './takeDetail?id=' + id\n\t\t\t\t})\n\t\t\t},\n\t\t\t/*下拉刷新的回调 */\n\t\t\tdownCallback() {\n\t\t\t\t// 这里加载你想下拉刷新的数据, 比如刷新轮播数据\n\t\t\t\t// loadSwiper();\n\t\t\t\t// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )\n\t\t\t\tthis.mescroll.resetUpScroll()\n\t\t\t},\n\t\t\t/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */\n\t\t\tupCallback(page) {\n\t\t\t\tlet curTab = this.tabs[this.tabIndex].status\n\n\t\t\t\tlet data = {\n\t\t\t\t\tstatus: curTab,\n\t\t\t\t\tpage: page.num,\n\t\t\t\t\tlimit: page.size\n\t\t\t\t}\n\t\t\t\tthis.$Request.get('/app/orders/selectMyTakeOrders', data).then(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.mescroll.endBySize(res.data.list.length, res.data.totalCount)\n\t\t\t\t\tif (page.num == 1) this.goods = []; //如果是第一页需手动制空列表\n\t\t\t\t\tres.data.list.forEach(ret => {\n\t\t\t\t\t\tswitch (ret.state) {\n\t\t\t\t\t\t\tcase 0:\n\t\t\t\t\t\t\t\tret.statusName = '待接单'\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 1:\n\t\t\t\t\t\t\t\tret.statusName = '进行中'\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 2:\n\t\t\t\t\t\t\t\tret.statusName = '已完成'\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 3:\n\t\t\t\t\t\t\t\tret.statusName = '已拒绝'\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\tthis.goods = [...this.goods, ...res.data.list]; //追加新数据\n\t\t\t\t\tfor (let i = 0; i < this.goods.length; i++) {\n\t\t\t\t\t\tthis.goods[i].gameName = this.goods[i].gameName.split(\",\");\n\t\t\t\t\t}\n\t\t\t\t\tthis.mescroll.endSuccess(res.data.total.length); // 隐藏加载状态栏\n\n\t\t\t\t}).catch(() => {\n\t\t\t\t\t//联网失败, 结束加载\n\t\t\t\t\tthis.mescroll.endErr();\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 切换菜单\n\t\t\ttabChange() {\n\t\t\t\tthis.goods = []; // 置空列表,显示加载进度条\n\t\t\t\tthis.mescroll.resetUpScroll()\n\t\t\t},\n\t\t\t// 取消订单\n\t\t\tcancelOrder(e, status) {\n\t\t\t\tlet that = this\n\t\t\t\tlet content = ''\n\t\t\t\tif (status == 3) {\n\t\t\t\t\tcontent = '确定拒绝接单吗？'\n\t\t\t\t} else if (status == 2) {\n\t\t\t\t\tcontent = '确定订单已经完成吗？如果未完成，客户投诉将采取封号处理'\n\t\t\t\t}\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: content,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\t\t\t\tstatus\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.get('/app/orders/cancelOrder', data).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\n\t\t\t\t\t\t\t\t\tthat.mescroll.resetUpScroll()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t})\n\n\t\t\t},\n\t\t\t//\n\t\t\tclickItem: function(options) {\n\t\t\t\tlet data = {\n\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\tfocusedUserId: options.ordersUserId\n\t\t\t\t}\n\t\t\t\tthis.$Request.postJson('/app/chat/insertChatConversation ', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tlet id = this.userId == res.data.userId ? res.data.focusedUserId : this.userId\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/msg/im?chatConversationId=' + res.data.chatConversationId +\n\t\t\t\t\t\t\t\t'&byUserId=' + id\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tgoNav(e) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: e\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/*\n\tsticky生效条件：\n\t1、父元素不能overflow:hidden或者overflow:auto属性。(mescroll-body设置:sticky=\"true\"即可, mescroll-uni本身没有设置overflow)\n\t2、必须指定top、bottom、left、right4个值之一，否则只会处于相对定位\n\t3、父元素的高度不能低于sticky元素的高度\n\t4、sticky元素仅在其父元素内生效,所以父元素必须是 mescroll\n\t*/\n\t.sticky-tabs {\n\t\tz-index: 990;\n\t\tposition: sticky;\n\t\ttop: var(--window-top);\n\t\t// background-color: #fff;\n\t}\n\n\t// 使用mescroll-uni,则top为0\n\t.mescroll-uni,\n\t::v-deep .mescroll-uni {\n\t\t.sticky-tabs {\n\t\t\ttop: 0;\n\t\t}\n\t}\n\n\t.demo-tip {\n\t\tpadding: 18upx;\n\t\tfont-size: 24upx;\n\t\ttext-align: center;\n\t}\n\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.tabber {\n\t\twidth: 100%;\n\t\tbackground: #ffffff;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tjustify-content: flex-end;\n\t\theight: 127rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627133\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}