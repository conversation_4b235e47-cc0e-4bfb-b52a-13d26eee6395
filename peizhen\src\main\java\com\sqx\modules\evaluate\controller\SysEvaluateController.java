package com.sqx.modules.evaluate.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.evaluate.entity.SysEvaluate;
import com.sqx.modules.evaluate.service.SysEvaluateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.java.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@RestController
@Api(value = "评分", tags = {"用户端-评分"})
@RequestMapping("/app/evaluate/")
public class SysEvaluateController {
    @Autowired
    private SysEvaluateService evaluateService;


    @ApiOperation("获取用户评价列表")
    @GetMapping("getUserEvaluate")
    public Result getUserEvaluateList(Integer page, Integer limit, SysEvaluate evaluate) {
        return Result.success().put("data", evaluateService.getUserEvaluateList(page, limit, evaluate));
    }

    @Login
    @ApiOperation("用户评价")
    @PostMapping("addEvaluate")
    public Result addEvaluate(@RequestAttribute("userId") Long userId, SysEvaluate evaluate) {
        evaluate.setUserId(userId);
        return evaluateService.addEvaluate(evaluate);
    }
    @Login
    @ApiOperation("删除评价")
    @GetMapping("deleteEvaluate")
    public Result deleteEvaluate(@RequestAttribute("userId") Long userId, Long evaluateId) {
        return evaluateService.deleteEvaluate(userId, evaluateId, 0);
    }

    @ApiOperation("获取评论详情")
    @GetMapping("getEvaluateInfo")
    public Result getEvaluateInfo(Long evaluateId) {
        return Result.success().put("data", evaluateService.getById(evaluateId));
    }
}

