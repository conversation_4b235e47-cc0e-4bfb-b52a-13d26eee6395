package com.sqx.modules.riderLocation.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.riderLocation.entity.RiderLocation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RiderLocationDao extends BaseMapper<RiderLocation> {

    Double calculateDistance(Double lng, Double lat, Double lng1, Double lat1);

    Long getDistanceNearRide(String longitude, String latitude, Double cashDeposit, Double distance);

    IPage<RiderLocation> selectAllRiderLocation(@Param("pages") Page<RiderLocation> pages, @Param("riderLocation") RiderLocation riderLocation);

    List<RiderLocation> selectRiderList(@Param("riderPhone") String riderPhone, @Param("hospitalName") String hospitalName, @Param("city") String city, @Param("ordersNo") String ordersNo);
}
