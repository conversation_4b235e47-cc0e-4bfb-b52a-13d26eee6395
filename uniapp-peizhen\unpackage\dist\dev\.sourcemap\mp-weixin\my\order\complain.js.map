{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/complain.vue?74ba", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/complain.vue?7915", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/complain.vue?c411", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/complain.vue?cb97", "uni-app:///my/order/complain.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/complain.vue?cd6c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/complain.vue?1aec"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "msgContents", "stars", "imageList", "sendDate", "content", "title", "image", "ordersId", "Imgo", "byUserId", "by<PERSON><PERSON><PERSON>", "ordersNo", "onLoad", "appid", "imei", "p", "md", "app_version", "plus_version", "os", "net", "methods", "getUserInfo", "removeImg", "console", "close", "send", "uni", "icon", "userId", "state", "userName", "platform", "platformId", "url", "setTimeout", "showCancel", "addImages", "count", "sourceType", "success", "that", "filePath", "name", "config", "info"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACoD3vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACA;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAL;MACAM;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;UACAC;UACAvB;QACA;QACA;MACA;MACA;QACAsB;UACAC;UACAvB;QACA;QACA;MACA;MACA;MACA;MACA;QACA;QACAI;QACAJ;QACAC;QACAuB;QACAzB;QACA0B;QACAC;QACAC;QACAC;QACAC;MACA;QACA;UACAP;YACAtB;UACA;UACA8B;YACA;YACAR;cACAO;YACA;UACA;QACA;UACAP;UACAA;YACAS;YACA/B;YACAD;UACA;QACA;MACA;IACA;IAAA;IACAiC;MAAA;MACA;MACAV;QACAW;QACAC;QACAC;UACA;YACAC;YACAd;cAAA;cACAO;cAAA;cACA;cACAQ;cACAC;cACAH;gBACA;kBACAhB;kBACAiB;gBACA;gBAEAd;cACA;YACA;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;UACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1NA;AAAA;AAAA;AAAA;AAA8iC,CAAgB,y8BAAG,EAAC,C;;;;;;;;;;;ACAlkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/order/complain.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/order/complain.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./complain.vue?vue&type=template&id=888db1a2&\"\nvar renderjs\nimport script from \"./complain.vue?vue&type=script&lang=js&\"\nexport * from \"./complain.vue?vue&type=script&lang=js&\"\nimport style0 from \"./complain.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/order/complain.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complain.vue?vue&type=template&id=888db1a2&\"", "var components\ntry {\n  components = {\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.Imgo.length\n  var g1 = _vm.Imgo.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complain.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complain.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\" style=\"background-color: #ffffff;\">\n\t\t<view class=\"feedback-title\">投诉标题</view>\n\t\t<view class=\"feedback-body\">\n\t\t\t{{title}}<!-- <input class=\"feedback-input\" v-model=\"sendDate.title\" placeholder=\"请输入投诉标题 \" /> -->\n\t\t</view>\n\t\t<view class=\"feedback-title\">投诉内容</view>\n\t\t<view class=\"feedback-body\" style=\"height: 300upx;\">\n\t\t\t<u-input v-model=\"sendDate.content\" type=\"textarea\" placeholder=\"请详细描述你的问题... \" height=\"300\"/>\n\t\t\t<!-- <input class=\"feedback-input\" type=\"textarea\" v-model=\"sendDate.content\" placeholder=\"请详细描述你的问题... \"  /> -->\n\t\t\t<!-- <textarea placeholder=\"请详细描述你的问题...\" style=\"\" v-model=\"sendDate.content\" class=\"feedback-textare\" /> -->\n\t\t</view>\n\t\t<!-- <view class=\"feedback-title\">QQ/邮箱</view>\n\t\t<view class=\"feedback-body\">\n\t\t\t<input class=\"feedback-input\" v-model=\"sendDate.contact\" placeholder=\"方便我们联系你 \" />\n\t\t</view> -->\n\t\t<view class=\"feedback-title\">投诉图</view>\n\t\t<view class=\"margin-lr-sm\">\n\t\t\t<view class=\"flex \" style=\"overflow: hidden;flex-direction: initial;flex-wrap: wrap;\">\n\t\t\t\t<view v-if=\"Imgo.length\">\n\t\t\t\t\t<view class=\" flex margin-right-sm flex-wrap\">\n\t\t\t\t\t\t<view class=\"flex margin-top-xs\"\n\t\t\t\t\t\t\tstyle=\"width: 200rpx;height: 200rpx;margin-right: 5rpx;position: relative;\"\n\t\t\t\t\t\t\tv-for=\"(image,index) in Imgo\" :key=\"index\">\n\t\t\t\t\t\t\t<image :src=\"image\" style=\"width: 100%;height: 100%;\"></image>\n\t\t\t\t\t\t\t<view style=\"z-index: 9;position: absolute;top: -15rpx;right: -15rpx;\"\n\t\t\t\t\t\t\t\t@click=\"removeImg(index)\">\n\t\t\t\t\t\t\t\t<u-icon name=\"close-circle-fill\" color=\"#2979ff\" size=\"50rpx\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"\" @click=\"addImages(2)\" v-if=\"Imgo.length<=5\">\n\t\t\t\t\t<view style=\"width: 200rpx;height: 200rpx;background: #f4f5f6;\"\n\t\t\t\t\t\tclass=\"flex justify-center align-center\">\n\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t<view class=\"text-center\">\n\t\t\t\t\t\t\t\t<u-icon name=\"plus\" color=\"#666666\" size=\"28\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"text-center\">选择图片</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<button type=\"primary\" style=\"\" class=\"feedback-submit\" @tap=\"send\">提交</button>\n\t</view>\n</template>\n\n<script>\n\timport configdata from '@/common/config.js';\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmsgContents: ['界面显示错乱', '启动缓慢，卡出翔了', 'UI无法直视，丑哭了', '偶发性崩溃'],\n\t\t\t\tstars: [1, 2, 3, 4, 5],\n\t\t\t\timageList: [],\n\t\t\t\tsendDate: {\n\t\t\t\t\tcontent: '',\n\t\t\t\t\t// contact: '',\n\t\t\t\t\ttitle: '',\n\t\t\t\t\timage: \"\",\n\t\t\t\t},\n\t\t\t\tordersId: '',\n\t\t\t\tImgo: [],\n\t\t\t\tbyUserId: '',\n\t\t\t\tbyuserName: '',\n\t\t\t\ttitle:'',\n\t\t\t\tordersNo:''\n\t\t\t};\n\t\t},\n\t\tonLoad(e) {\n\t\t\tthis.ordersId = e.id\n\t\t\tthis.byUserId = e.byUserId\n\t\t\tthis.title = e.title\n\t\t\tthis.ordersNo = e.ordersNo\n\t\t\tthis.getUserInfo()\n\t\t\tlet deviceInfo = {\n\t\t\t\tappid: plus.runtime.appid,\n\t\t\t\timei: plus.device.imei, //设备标识\n\t\t\t\tp: plus.os.name === 'Android' ? 'a' : 'i', //平台类型，i表示iOS平台，a表示Android平台。\n\t\t\t\tmd: plus.device.model, //设备型号\n\t\t\t\tapp_version: plus.runtime.version,\n\t\t\t\tplus_version: plus.runtime.innerVersion, //基座版本号\n\t\t\t\tos: plus.os.version,\n\t\t\t\tnet: '' + plus.networkinfo.getCurrentType()\n\t\t\t};\n\t\t\tthis.sendDate = Object.assign(deviceInfo, this.sendDate);\n\t\t},\n\t\tmethods: {\n\t\t\tgetUserInfo() {\n\t\t\t\tthis.$Request.get(\"/app/user/selectUserById?userId=\"+this.byUserId).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.byuserName = res.data.userName\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 详情图删除\n\t\t\tremoveImg(index) {\n\t\t\t\tconsole.log(index)\n\t\t\t\tthis.Imgo.splice(index, 1)\n\t\t\t},\n\t\t\tclose(e) {\n\t\t\t\tthis.imageList.splice(e, 1);\n\t\t\t},\n\t\t\tsend() {\n\t\t\t\t//发送反馈\n\t\t\t\tthis.sendDate.image = this.Imgo\n\t\t\t\tthis.sendDate.image = this.sendDate.image.toString()\n\n\t\t\t\t// console.log(JSON.stringify(this.sendDate));\n\t\t\t\t// if (!this.sendDate.title) {\n\t\t\t\t// \tuni.showToast({\n\t\t\t\t// \t\ticon: 'none',\n\t\t\t\t// \t\ttitle: '请输入投诉标题'\n\t\t\t\t// \t});\n\t\t\t\t// \treturn;\n\t\t\t\t// }\n\t\t\t\tif (!this.sendDate.content) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入投诉内容'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (!this.Imgo) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请上传投诉图片'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.$queue.showLoading('加载中...');\n\t\t\t\tlet userId = this.$queue.getData(\"userId\");\n\t\t\t\tthis.$Request.postJson('/app/message/insertMessage', {\n\t\t\t\t\t// ordersId: this.ordersId,\n\t\t\t\t\tbyUserId: this.byUserId,\n\t\t\t\t\ttitle: this.title,\n\t\t\t\t\timage: this.sendDate.image,\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\tcontent: this.sendDate.content,\n\t\t\t\t\tstate: 3,\n\t\t\t\t\tuserName: this.byuserName,\n\t\t\t\t\tplatform:3,\n\t\t\t\t\tplatformId:this.ordersId,\n\t\t\t\t\turl:this.ordersNo\n\t\t\t\t}).then(res => {\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '投诉成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t// uni.navigateBack();\n\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\turl:'/my/order/tousuList'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\ttitle: '投诉失败',\n\t\t\t\t\t\t\tcontent: res.msg\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}, // 图片上传\n\t\t\taddImages(e) {\n\t\t\t\tlet that = this\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 6,\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tfor (let i = 0; i < res.tempFilePaths.length; i++) {\n\t\t\t\t\t\t\tthat.$queue.showLoading(\"上传中...\");\n\t\t\t\t\t\t\tuni.uploadFile({ // 上传接口\n\t\t\t\t\t\t\t\turl: this.config(\"APIHOST1\") + '/alioss/upload', //真实的接口地址\n\t\t\t\t\t\t\t\t// url: 'https://xichewap.xianmxkj.com/sqx_fast/alioss/upload',\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePaths[i],\n\t\t\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\t\t\tsuccess: (uploadFileRes) => {\n\t\t\t\t\t\t\t\t\tif (e == 2) {\n\t\t\t\t\t\t\t\t\t\tconsole.log(JSON.parse(uploadFileRes.data).data)\n\t\t\t\t\t\t\t\t\t\tthat.Imgo.push(JSON.parse(uploadFileRes.data).data)\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tconfig: function(name) {\n\t\t\t\tvar info = null;\n\t\t\t\tif (name) {\n\t\t\t\t\tvar name2 = name.split(\".\"); //字符分割\n\t\t\t\t\tif (name2.length > 1) {\n\t\t\t\t\t\tinfo = configdata[name2[0]][name2[1]] || null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tinfo = configdata[name] || null;\n\t\t\t\t\t}\n\t\t\t\t\tif (info == null) {\n\t\t\t\t\t\tlet web_config = cache.get(\"web_config\");\n\t\t\t\t\t\tif (web_config) {\n\t\t\t\t\t\t\tif (name2.length > 1) {\n\t\t\t\t\t\t\t\tinfo = web_config[name2[0]][name2[1]] || null;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tinfo = web_config[name] || null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn info;\n\t\t\t},\n\t\t}\n\t};\n</script>\n\n<style>\n\t@font-face {\n\t\tfont-family: uniicons;\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t\tsrc: url('https://img-cdn-qiniu.dcloud.net.cn/fonts/uni.ttf') format('truetype');\n\t}\n\n\tpage {\n\t\tbackground-color: #ffffff !important;\n\t}\n\n\tview {\n\t\tfont-size: 28upx;\n\t}\n\n\n\t/*问题反馈*/\n\t.feedback-title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 20upx;\n\t\tcolor: #8f8f94;\n\t\tfont-size: 28upx;\n\t}\n\n\t.feedback-star-view.feedback-title {\n\t\tjustify-content: flex-start;\n\t\tmargin: 0;\n\t}\n\n\t.feedback-body {\n\t\tfont-size: 32upx;\n\t\tpadding: 16upx;\n\t\tmargin: 16upx;\n\t\tborder-radius: 16upx;\n\t\tbackground: #F5F5F5;\n\t\t/* color: #FFF; */\n\t}\n\n\t.feedback-textare {\n\t\theight: 200upx;\n\t\tfont-size: 34upx;\n\t\tline-height: 50upx;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tpadding: 20upx 30upx 0;\n\t}\n\n\t.feedback-input {\n\t\tfont-size: 32upx;\n\t\theight: 60upx;\n\t\t/* padding: 15upx 20upx; */\n\t\tline-height: 60upx;\n\t}\n\n\n\t.feedback-submit {\n\t\tbackground: #0175FE;\n\t\tcolor: #ffffff;\n\t\tmargin: 20upx;\n\t\tmargin-top: 32upx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complain.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complain.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447624588\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}