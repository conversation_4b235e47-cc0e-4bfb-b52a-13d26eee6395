package com.sqx.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * AI流式消息实体
 * 用于存储流式输出的分块数据
 */
@Data
@TableName("ai_stream_message")
public class AiStreamMessage {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 流式任务ID，用于标识一次完整的AI回复
     */
    private String taskId;
    
    /**
     * 对话ID
     */
    private Long conversationId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 消息ID（关联ai_message表）
     */
    private Long messageId;
    
    /**
     * 分块序号，从1开始
     */
    private Integer chunkIndex;
    
    /**
     * 分块内容
     */
    private String chunkContent;
    
    /**
     * 累积内容（从开始到当前分块的完整内容）
     */
    private String accumulatedContent;
    
    /**
     * 是否为最后一个分块
     */
    private Boolean isLast;
    
    /**
     * 任务状态：processing-处理中, completed-已完成, error-错误
     */
    private String status;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;
}
