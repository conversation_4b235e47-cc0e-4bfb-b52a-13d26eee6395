package com.sqx.modules.orders.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.utils.excel.ExcelData;
import com.sqx.modules.utils.excel.ExportExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/orders")
@Api(value = "订单信息", tags = {"订单信息"})
public class OrdersController {
    @Autowired
    private OrdersService ordersService;
    @Autowired
    private UserService userService;

    /**
     * 查看所有订单
     *
     * @param page
     * @param limit
     * @param name
     * @param status
     * @return
     */
    @RequestMapping("/queryOrders")
    public Result queryOrdersAll(Long page, Long limit, Long type, String name,String userName, Long status, Long userId, String ordersNo, String startTime, String endTime,Long hospitalId) {
        return Result.success().put("data", ordersService.queryOrdersAll(page, limit, type, name,userName, status, userId, ordersNo, startTime, endTime,hospitalId));
    }

    @GetMapping("/ordersListExcel")
    @ApiOperation("订单导出")
    public void ordersListExcel(Long type, String name, Long status, Long userId, String ordersNo, String startTime, String endTime, HttpServletResponse response) throws Exception {
        ExcelData data = ordersService.ordersListExcel(type, name, status, userId, ordersNo, startTime, endTime);
        ExportExcelUtils.exportExcel(response, "订单列表.xlsx", data);
    }

    /**
     * 删除订单
     */
    @RequestMapping("/deleteOrders")
    public Result deleteOrders(Long id) {
        return ordersService.deleteOrder(id);
    }

    @GetMapping("/selectMyTakeOrders")
    @ApiOperation("查询我的接单")
    public Result selectMyTakeOrders(Integer page, Integer limit, Long userId, String state) {
        return Result.success().put("data", ordersService.selectMyTakeOrders(page, limit, userId, state));
    }

    @ApiOperation("修改订单状态")
    @GetMapping("/cancelOrder")
    public Result cancelOrder(Long id, String status, BigDecimal refundMoney) {
        Orders orders = ordersService.getById(id);
        return ordersService.cancelOrder(id, status, orders.getCode(), null, null,refundMoney);
    }

    @ApiOperation("转单")
    @PostMapping("/giveOrdersUser")
    public Result giveOrdersUser(Long userId, Long orderId) {
        UserEntity userEntity = userService.queryByUserId(userId);
        return ordersService.giveOrdersUser(userEntity, orderId);
    }

    @GetMapping("/selectTeamOrdersList")
    @ApiOperation("获取团队订单")
    public Result selectTeamOrdersList(Long userId, Integer page, Integer limit, Integer type, Integer status) {
        return ordersService.selectTeamOrdersList(page, limit, userId, type, status);
    }


    @GetMapping("/selectTeamUserList")
    @ApiOperation("获取团队列表")
    public Result selectTeamUserList(Long userId, Integer page, Integer limit, Integer type) {
        UserEntity user = userService.selectUserById(userId);
        return ordersService.selectTeamUserList(page, limit, user.getInvitationCode(), type, userId);
    }

    @GetMapping("/selectTeamStatistics")
    @ApiOperation("团队统计")
    public Result selectTeamStatistics(Long userId, Integer type) {
        UserEntity user = userService.selectUserById(userId);
        Double teamMoney = ordersService.selectOrdersMoneyCountByUserId(user.getUserId(), type, null);
        Integer teamCount = ordersService.selectUserCountByInvitationCode(user.getInvitationCode(), type);
        Map<String, Object> result = new HashMap<>();
        result.put("teamMoney", teamMoney);
        result.put("teamCount", teamCount);
        return Result.success().put("data", result);
    }

    @PostMapping("/distributionOrder")
    @ApiOperation("派单")
    public Result distributionOrder(Long orderId, Long userId) {
        return ordersService.distributionOrder(orderId, userId);
    }
    @GetMapping("/getProfitList")
    @ApiOperation("师傅收益排行榜")
    public Result getProfitList(Integer page,Integer limit,  String time, Integer flag){
        return Result.success().put("data", ordersService.getProfitList(page,limit,time,flag));
    }

    @GetMapping("/selectOrdersDate")
    @ApiOperation("订单统计")
    public Result selectOrdersDate(Integer flag,String time){
        return Result.success().put("data", ordersService.selectOrdersDate(flag,time));
    }
    @GetMapping("/getPingIncome")
    @ApiOperation("获取平台收入分析")
    public Result getPingIncome(Integer flag,String time,Integer page,Integer limit){
        return Result.success().put("data", ordersService.getPingIncome(flag,time,page,limit));
    }

    @GetMapping("/orderStatistics")
    @ApiOperation("订单收益统计")
    public Result orderStatistics(Integer flag,String time){
        return Result.success().put("data", ordersService.orderStatistics(flag,time));
    }

}
