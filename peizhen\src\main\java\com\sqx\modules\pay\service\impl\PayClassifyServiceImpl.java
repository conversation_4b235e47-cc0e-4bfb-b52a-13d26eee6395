package com.sqx.modules.pay.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.modules.pay.dao.PayClassifyDao;
import com.sqx.modules.pay.entity.PayClassify;
import com.sqx.modules.pay.service.PayClassifyService;
import org.springframework.stereotype.Service;

/**
 * 充值分类
 */
@Service
public class PayClassifyServiceImpl extends ServiceImpl<PayClassifyDao, PayClassify> implements PayClassifyService {


    @Override
    public int updatePayClassifyCouponId(Long payClassifyId){
        return baseMapper.updatePayClassifyCouponId(payClassifyId);
    }

   

}
