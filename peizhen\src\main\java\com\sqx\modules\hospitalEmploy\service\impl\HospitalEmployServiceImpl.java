package com.sqx.modules.hospitalEmploy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalEmploy.entity.HospitalEmploy;
import com.sqx.modules.hospitalEmploy.dao.HospitalEmployDao;
import com.sqx.modules.hospitalEmploy.service.HospitalEmployService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.sqx.modules.hospitalModular.service.HospitalModularService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Service
public class HospitalEmployServiceImpl extends ServiceImpl<HospitalEmployDao, HospitalEmploy> implements HospitalEmployService {

    @Autowired
    private HospitalEmployDao hospitalEmployDao;
    @Autowired
    private HospitalModularService modularService;

    @Override
    public Result addHospitalEmploy(HospitalEmploy hospitalEmploy) {
        HospitalModular hospitalModular = modularService.getById(hospitalEmploy.getModularId());
        if (hospitalModular == null) {
            return Result.error("当前模块不存在");
        }
        return Result.upStatus(hospitalEmployDao.insert(hospitalEmploy));
    }

    @Override
    public Result updateHospitalEmploy(HospitalEmploy hospitalEmploy) {
        HospitalModular hospitalModular = modularService.getById(hospitalEmploy.getModularId());
        if (hospitalModular == null) {
            return Result.error("当前模块不存在");
        }
        return Result.upStatus(hospitalEmployDao.updateById(hospitalEmploy));

    }

    @Override
    public Result deleteHospitalEmploy(Long serviceId) {

        return Result.upStatus(hospitalEmployDao.deleteById(serviceId));
    }

    @Override
    public HospitalEmploy getHospitalEmployInfo(Long serviceId) {
        return hospitalEmployDao.selectById(serviceId);

    }

    @Override
    public IPage<HospitalEmploy> getHospitalEmployList(Integer page, Integer limit, HospitalEmploy hospitalEmploy) {
        if(hospitalEmploy.getModularId()!=null){
            HospitalModular modular = modularService.getById(hospitalEmploy.getModularId());
            if(modular==null || modular.getIsEnable()!=1){
                return null;
            }
        }
        Page<HospitalEmploy> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        IPage<HospitalEmploy> selectPage = hospitalEmployDao.selectPage(pages, new QueryWrapper<>(hospitalEmploy).orderByAsc("sort"));
        for (HospitalEmploy record : selectPage.getRecords()) {
            record.setModularName(modularService.getById(record.getModularId()).getModularName());
        }
        return selectPage;


    }

    @Override
    public List<HospitalEmploy> getEmployList(Integer authentication) {
        return baseMapper.getEmployList(authentication);




    }
}
