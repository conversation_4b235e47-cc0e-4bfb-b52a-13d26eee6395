{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/takeDetail.vue?be43", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/takeDetail.vue?d426", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/takeDetail.vue?9533", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/takeDetail.vue?f7d9", "uni-app:///my/takeOrder/takeDetail.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/takeDetail.vue?8336", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/takeOrder/takeDetail.vue?4997"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "customStyle", "color", "border", "customStyle1", "id", "order", "user", "game", "isTrue", "vipMoney", "price", "onLoad", "console", "uni", "title", "methods", "bindGps", "latitude", "longitude", "name", "address", "success", "fail", "bindphone", "content", "phoneNumber", "binddetail", "url", "getOrder", "toFixed", "cancelOrder", "status", "that", "pay", "ordersId", "setTimeout", "icon", "goMsg", "userId", "focusedUserId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2H7vB;EACAC;IAAA;IACA;MACAC;QACAC;QACAC;MAAA,yDAEA,8DACA,iEACA,gEACA,6BACA;MACAC;QACAF;QACAC;MAAA,0DAEA,+DACA,kEACA,iEACA,8BACA;MACAE;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAV;MACAW;IACA;EACA;EACAC;IACAC;IACA;IACAA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAH;QACAI;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACAT;QACA;QACAU;UACAV;QACA;MACA;IACA;IACA;IACAW;MACA;MACAV;QACAC;QACAU;QACAH;UACA;YACAT;YACAC;cACAY;YACA;UACA;YACAb;UACA;QACA;MACA;IACA;IACAc;MACAd;MACAC;QAEAc;MACA;IACA;IACAC;MAAA;MACA;QACAxB;MACA;MACA;QACA;UACA;UAEA;UACA,qEACAyB;UACA,6FACAA;UACAjB;QACA;MACA;IACA;IACA;IACAkB;MACA;MACA;MACA;QACAN;MACA;QACAA;MACA;MACAX;QACAC;QACAU;QACAH;UACA;YACA;cACAjB;cACA2B;YACA;YACAC;cACA;gBACAA;cACA;YACA;UACA;QACA;MACA;IAEA;IACA;IACAC;MACA;MACApB;QACAC;QACAU;QACAH;UACA;YACAT;YACAoB;cACAE;YACA;cACA;gBACArB;kBACAC;gBACA;gBACAqB;kBACAtB;gBACA;cACA;gBACAA;kBACAC;kBACAsB;gBACA;cACA;YACA;UACA;YACAxB;UACA;QACA;MAEA;IAEA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAyB;MAAA;MACA;QACAC;QACAC;MACA;MACA;QACA;UACA,6FACAjC;UACAO;YACAc,yEACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnUA;AAAA;AAAA;AAAA;AAAgjC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACApkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/takeOrder/takeDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/takeOrder/takeDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./takeDetail.vue?vue&type=template&id=b40b48b6&\"\nvar renderjs\nimport script from \"./takeDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./takeDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./takeDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/takeOrder/takeDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeDetail.vue?vue&type=template&id=b40b48b6&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-button/u-button\" */ \"@/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeDetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"padding\">\n\t\t<!-- <view class=\"bg padding radius\">\n\t\t\t<view class=\"flex\">\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<image src=\"../../static/images/place1.png\" style=\"width: 80rpx;height: 80rpx;\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"text-white margin-left-sm\">\n\t\t\t\t\t<view>欢乐<text>19999999999999</text></view>\n\t\t\t\t\t<view class=\"margin-top-xs\">西安智能大厦</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view> -->\n\t\t<view class=\"bg padding radius\">\n\t\t\t<view class=\" u-flex \" @click=\"binddetail(order)\">\n\t\t\t\t<view class=\"u-m-r-10\">\n\t\t\t\t\t<!-- <u-avatar :src=\"order.user.avatar\" size=\"68\"></u-avatar> -->\n\t\t\t\t\t<image :src=\"order.orderTaking.homepageImg\" style=\"width: 140rpx;height: 140rpx;\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-flex-1 text-white\">\n\t\t\t\t\t<view class=\"u-font-14  text-bold padding-bottom-xs u-line-1\" style=\"width: 480rpx;\">\n\t\t\t\t\t\t{{order.orderTaking.myLevel}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- <view class=\"margin-top-xs margin-bottom-xs \"><text class=\"padding-xs text-sm\">{{order.orderNumber}}</text></view> -->\n\t\t\t\t\t<view class=\"padding-top-xs text-lg\">\n\t\t\t\t\t\t<text style=\"font-size: 20rpx;\">￥</text>\n\t\t\t\t\t\t{{order.orderTaking.oldMoney}}元/{{order.orderTaking.unit}}<text\n\t\t\t\t\t\t\tstyle=\"font-size: 20rpx;margin-left: 10rpx;\">*</text>\n\t\t\t\t\t\t<text style=\"color: #666666;\">{{order.orderNumber}}{{order.orderTaking.unit}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex justify-between margin-top\">\n\t\t\t\t<view style=\"width: 120upx;\">实付款</view>\n\t\t\t\t<view class=\"text-white text-lg\">\n\t\t\t\t\t<text class=\"text-sm\">￥</text>{{price}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"bg padding radius margin-top\">\n\t\t\t<view class=\"text-lg text-bold\">\n\t\t\t\t服务信息\n\t\t\t</view>\n\t\t\t<view class=\"margin-right-xs\">\n\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t<view class=\"text-bold\" style=\"width: 165rpx;\">服务类型</view>\n\t\t\t\t\t<view class=\"text-white\">\n\t\t\t\t\t\t<text v-for=\"(item,index) in order.orderTaking.gameId\" :key=\"index\"\n\t\t\t\t\t\t\tclass=\"margin-left-sm\">{{item}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t<view class=\"text-bold\" style=\"width: 165rpx;\">服务时间</view>\n\t\t\t\t\t<view class=\"text-white\">{{order.startTime}}时</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t<view class=\"text-bold\" style=\"width: 165rpx;\">服务地点</view>\n\t\t\t\t\t<view class=\"text-white\" @tap=\"bindGps(order.latitude,order.longitude,order.detailsAddress)\">\n\t\t\t\t\t\t{{order.city}}{{order.district}}{{order.detailsAddress}}\n\t\t\t\t\t\t<u-icon name=\"map\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t<view class=\"text-bold\" style=\"width: 165rpx;\">联系方式</view>\n\t\t\t\t\t<view class=\"text-white\" @click=\"bindphone(order.phone)\">{{order.phone}}<u-icon name=\"phone\"></u-icon>\n\t\t\t\t\t  <text class=\"margin-left-sm\"> {{order.name}}</text></view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\" margin-top-lg\">\n\t\t\t\t\t<view class=\"text-bold\" style=\"width: 165rpx;\">备注</view>\n\t\t\t\t\t<view class=\"text-white margin-top\">{{order.remarks}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- <view class=\"flex justify-between margin-top\">\n\t\t\t\t<view @click=\"goMsg\">\n\t\t\t\t\t<u-icon name=\"chat\" size=\"32\" color=\"#557EFD\" class=\"margin-right-sm\"></u-icon>联系TA\n\t\t\t\t</view>\n\t\t\t\t<view class=\"text-white\">实付：<text class=\"text-lg text-bold\">{{order.payMoney}}元</text></view>\n\t\t\t</view> -->\n\t\t</view>\n\t\t<view class=\"bg padding radius margin-top\" style=\"margin-bottom: 140rpx;\">\n\t\t\t<view class=\"text-lg text-bold\">\n\t\t\t\t订单信息\n\t\t\t</view>\n\t\t\t<view class=\"margin-right-xs\">\n\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t<view class=\"text-bold\" style=\"width: 165rpx;\">订单编号</view>\n\t\t\t\t\t<view class=\"text-white\">{{order.ordersNo}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t<view class=\"text-bold\" style=\"width: 165rpx;\">下单时间</view>\n\t\t\t\t\t<view class=\"text-white\">{{order.createTime}}</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- <view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t<view style=\"width: 120upx;\">支付方式</view>\n\t\t\t\t\t<view class=\"text-white\">{{order.createTime}}</view>\n\t\t\t\t</view>\n -->\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- <view class=\"flex tabber padding-top-sm padding-bottom-sm align-center\" v-if=\"isTrue == 0\">\n\t\t\t<u-button @click=\"cancelOrder(order)\" shape=\"circle\" class=\"margin-right-sm \" :custom-style=\"customStyle\"\n\t\t\t\t:hair-line=\"false\">取消订单\n\t\t\t</u-button>\n\t\t\t<u-button @click=\"pay\" class=\"margin-right-sm \" shape=\"circle\" :custom-style=\"customStyle2\"\n\t\t\t\t:hair-line=\"false\">立即支付\n\t\t\t</u-button>\n\t\t</view> -->\n\t\t<view class=\"flex tabber padding-top-sm padding-bottom-sm align-center margin-right-xs padding-right\"\n\t\t\tv-if=\"order.state ==1\">\n\t\t\t<u-button v-if=\"order.state == 1\" :custom-style=\"customStyle\" shape=\"circle\" :plain=\"true\"\n\t\t\t\t@click=\"cancelOrder(order,3)\">拒接接单</u-button>\n\t\t\t<!-- <u-button v-if=\"item.state == 3\" :custom-style=\"customStyle\" shape=\"circle\" :plain=\"true\" @click=\"delOrder(item)\">拒接接单</u-button> -->\n\t\t\t<u-button v-if=\"order.state == 1\" :custom-style=\"customStyle1\" shape=\"circle\" :plain=\"true\"\n\t\t\t\t@click=\"cancelOrder(order,2)\">完成接单</u-button>\n\t\t\t<!-- <u-button :custom-style=\"customStyle\" shape=\"circle\" :plain=\"true\" @click=\"clickItem(item)\">联系TA\n\t\t\t</u-button> -->\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcustomStyle: {\n\t\t\t\t\tcolor: '#999999',\n\t\t\t\t\tborder: '2rpx solid #999999',\n\t\t\t\t\t// backgroundColor: '#1E1F31',\n\t\t\t\t\tborder: \"8rpx\",\n\t\t\t\t\twidth: '180rpx',\n\t\t\t\t\theight: '54rpx',\n\t\t\t\t\tmargin: \"0 0 0 20rpx\"\n\t\t\t\t},\n\t\t\t\tcustomStyle1: {\n\t\t\t\t\tcolor: '#557EFD',\n\t\t\t\t\tborder: '2rpx solid #557EFD',\n\t\t\t\t\t// backgroundColor: '#1E1F31',\n\t\t\t\t\tborder: \"8rpx\",\n\t\t\t\t\twidth: '180rpx',\n\t\t\t\t\theight: '54rpx',\n\t\t\t\t\tmargin: \"0 0 0 20rpx\"\n\t\t\t\t},\n\t\t\t\tid: '',\n\t\t\t\torder: {\n\t\t\t\t\tuser: {},\n\t\t\t\t\tgame: {}\n\t\t\t\t},\n\t\t\t\tisTrue: 0,\n\t\t\t\tvipMoney: '',\n\t\t\t\tdata: [],\n\t\t\t\tprice: ''\n\t\t\t}\n\t\t},\n\t\tonLoad(e) {\n\t\t\tconsole.log(e)\n\t\t\tvar jsObject = JSON.stringify(e);\n\t\t\tconsole.log(jsObject)\n\t\t\tthis.isTrue = e.isTrue\n\t\t\tif (this.isTrue) {\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '订单详情'\n\t\t\t\t})\n\t\t\t}\n\t\t\tthis.id = e.id\n\t\t\tthis.getOrder()\n\t\t},\n\t\tmethods: {\n\t\t\t// 一键导航\n\t\t\tbindGps(latitude, longitude, name) {\n\t\t\t\tuni.openLocation({\n\t\t\t\t\tlatitude: latitude - 0, //要去的纬度-地址       \n\t\t\t\t\tlongitude: longitude - 0, //要去的经度-地址\n\t\t\t\t\tname: name, //地址名称\n\t\t\t\t\taddress: name, //详细地址名称\n\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\tconsole.log('导航成功');\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(error) {\n\t\t\t\t\t\tconsole.log(error)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 拨打电话\n\t\t\tbindphone(phone) {\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '是否拨打电话',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tconsole.log('用户点击确定', that.phone);\n\t\t\t\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\t\t\t\tphoneNumber: phone //仅为示例\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tbinddetail(e) {\n\t\t\t\tconsole.log(e)\n\t\t\t\tuni.navigateTo({\n\n\t\t\t\t\turl: '/pages/index/game/order?id=' + e.orderTakingId\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetOrder() {\n\t\t\t\tlet data = {\n\t\t\t\t\tid: this.id\n\t\t\t\t}\n\t\t\t\tthis.$Request.get('/app/orders/queryOrders', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.order = res.data\n\n\t\t\t\t\t\tthis.order.orderTaking.gameId = this.order.orderTaking.gameId.split(\",\");\n\t\t\t\t\t\tthis.price = (res.data.orderTaking.oldMoney * res.data.orderNumber)\n\t\t\t\t\t\t\t.toFixed(2)\n\t\t\t\t\t\tthis.vipMoney = (res.data.orderTaking.money * res.data.orderNumber - res.data.payMoney * 1)\n\t\t\t\t\t\t\t.toFixed(2)\n\t\t\t\t\t\tconsole.log(this.vipMoney, 'vipvipvipv')\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 取消订单\n\t\t\tcancelOrder(e, status) {\n\t\t\t\tlet that = this\n\t\t\t\tlet content = ''\n\t\t\t\tif (status == 3) {\n\t\t\t\t\tcontent = '确定拒绝接单吗？'\n\t\t\t\t} else if (status == 2) {\n\t\t\t\t\tcontent = '确定订单已经完成吗？如果未完成，客户投诉将采取封号处理'\n\t\t\t\t}\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: content,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\t\t\t\tstatus\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.get('/app/orders/cancelOrder', data).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tthat.getOrder()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t})\n\n\t\t\t},\n\t\t\t// 支付订单\n\t\t\tpay() {\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '付款提示',\n\t\t\t\t\tcontent: '确认支付' + that.order.payMoney + '元吗?',\n\t\t\t\t\tsuccess: function(re) {\n\t\t\t\t\t\tif (re.confirm) {\n\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\tthat.$Request.post(\"/app/orders/payMoney\", {\n\t\t\t\t\t\t\t\tordersId: that.order.ordersId,\n\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (re.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t})\n\n\t\t\t},\n\t\t\n\t\t\t// cancelOrder(e) {\n\t\t\t// \tlet data = {\n\t\t\t// \t\tid: e.ordersId,\n\t\t\t// \t\tstatus: '3'\n\t\t\t// \t}\n\t\t\t// \tthis.$Request.get('/app/orders/cancelOrder', data).then(res => {\n\t\t\t// \t\tif (res.code == 0) {\n\t\t\t// \t\t\tuni.showToast({\n\t\t\t// \t\t\t\ttitle: '取消成功',\n\t\t\t// \t\t\t\ticon: 'none'\n\t\t\t// \t\t\t})\n\t\t\t// \t\t\tsetTimeout(function() {\n\t\t\t// \t\t\t\tuni.navigateBack()\n\t\t\t// \t\t\t}, 1000)\n\t\t\t// \t\t}\n\t\t\t// \t})\n\t\t\t// },\n\t\t\tgoMsg() {\n\t\t\t\tlet data = {\n\t\t\t\t\tuserId: uni.getStorageSync('userId'),\n\t\t\t\t\tfocusedUserId: this.order.user.userId\n\t\t\t\t}\n\t\t\t\tthis.$Request.postJson('/app/chat/insertChatConversation ', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tlet id = this.order.user.userId == res.data.userId ? res.data.focusedUserId : this.order\n\t\t\t\t\t\t\t.user.userId\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/msg/im?chatConversationId=' + res.data.chatConversationId +\n\t\t\t\t\t\t\t\t'&byUserId=' + id\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground: #f7f7f7;\n\t}\n\n\t.bg {\n\t\tbackground: #FFFFFF;\n\t}\n\n\t.tabber {\n\t\twidth: 100%;\n\t\tbackground: #ffffff;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tjustify-content: flex-end;\n\t\theight: 127rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./takeDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621946\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}