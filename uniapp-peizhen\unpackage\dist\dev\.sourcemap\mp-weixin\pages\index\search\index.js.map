{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/search/index.vue?8e03", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/search/index.vue?aa95", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/search/index.vue?4f97", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/search/index.vue?f36e", "uni-app:///pages/index/search/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/search/index.vue?7d89", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/search/index.vue?cc6e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "defaultKeyword", "keyword", "oldKeywordList", "hotKeywordList", "keywordList", "forbid", "isShowKeywordList", "limit", "page", "userId", "isVip", "value1", "value2", "value3", "defaultIndex", "filterData", "label", "value", "onLoad", "methods", "change", "getSearchList", "console", "oldDelete", "uni", "content", "success", "doSearch", "title", "icon", "duration", "like", "condition", "salesNum", "by", "city", "latitude", "longitude", "goBack", "hotToggle", "goOrder", "url", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC6HvwB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA,EACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA,EACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;IAEA;EACA;EACAC;IACA;IACA;MACA;IACA;IACA;EAEA;EACAC;IACA;IACAC;MAEA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAC;QACA;UACA;UACA;UACA;UACA;UACA;QAGA;MACA;IACA;IACA;IACAC;MAAA;MACAC;QACAC;QACAC;UACA;YACAJ;YACA;cACA;gBACA;cACA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAK;MAAA;MACA;MACA;MACA;QACAH;UACAI;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAC;QACAxB;QACAC;QACAwB;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;QACAC;MACA;MACA;QACA;UACA;YACA;YACA;YACA;cACA;YACA;UACA;QACA;MACA;QACA;UACA;YACA;YACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACAd;IACA;IACA;IACAe;MACA;IACA;IACA;IACAC;MACA;QACAhB;UACAiB;QACA;MACA;QACAjB;UACAiB;QACA;MACA;IACA;EAEA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChUA;AAAA;AAAA;AAAA;AAAgkC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACAplC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/search/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/search/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=23e8c801&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/search/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=23e8c801&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-search/u-search\" */ \"@/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    renDropdownFilter: function () {\n      return import(\n        /* webpackChunkName: \"components/ren-dropdown-filter/ren-dropdown-filter\" */ \"@/components/ren-dropdown-filter/ren-dropdown-filter.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.keywordList.length\n  var g1 = _vm.oldKeywordList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<view class=\"search-box\">\n\t\t\t<!-- mSearch组件 如果使用原样式，删除组件元素-->\n\t\t\t<!-- <mSearch class=\"mSearch-input-box\" :mode=\"2\" button=\"inside\" :placeholder=\"defaultKeyword\"\n\t\t\t\t@search=\"doSearch(false)\" @input=\"inputChange\" @confirm=\"doSearch(false)\" v-model=\"keyword\"></mSearch> -->\n\t\t\t<!-- 原样式 如果使用原样式，恢复下方注销代码 -->\n\n\t\t\t<!-- <view class=\"input-box\">\n\t\t\t\t<input type=\"text\" :adjust-position=\"true\" :placeholder=\"defaultKeyword\" @input=\"inputChange\" v-model=\"keyword\" @confirm=\"doSearch(false)\"\n\t\t\t\t placeholder-class=\"placeholder-class\" confirm-type=\"search\">\n\t\t\t</view>\n\t\t\t<view class=\"search-btn\" @tap=\"doSearch(false)\">搜索</view> -->\n\t\t\t<u-search style=\"width: 100%;\" placeholder=\"输入搜索内容\" :focus=\"true\" v-model=\"keyword\" :show-action=\"true\"\n\t\t\t\t:animation=\"true\" shape=\"square\" bg-color=\"#F7F7F7\" color=\"#1A1A1A\" action-text=\"取消\" @custom=\"goBack()\"\n\t\t\t\t@search=\"doSearch(false)\"></u-search>\n\t\t\t<!-- 原样式 end -->\n\t\t\t\n\t\t</view>\n\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t<view style=\"position: relative;top: 90upx;left: 0;right: 0;z-index:1;\">\n\t\t<!-- #endif -->\n\t\t<!-- #ifndef MP-WEIXIN -->\n\t\t<view style=\"position: relative;top: 46px;left: 0;right: 0;z-index:1;\">\n\t\t<!-- #endif -->\n\t\t\t<view v-show=\"isShowKeywordList\">\n\t\t\t\t<ren-dropdown-filter :filterData='filterData' :border=\"false\" :defaultIndex='defaultIndex'\n\t\t\t\t\t@onSelected='change' class=\"u-skeleton-rect\">\n\t\t\t\t</ren-dropdown-filter>\n\t\t\t</view>\n\t\t\t<view class=\"search-keyword\">\n\t\t\t\t<scroll-view class=\"keyword-list-box\" v-show=\"isShowKeywordList\" scroll-y>\n\t\t\t\t\t<view class=\"margin-lr-sm padding-top-16 \">\n\t\t\t\t\t\t<view class=\"flex justify-between padding-sm radius margin-top-xs\" v-for=\"(item, index) in keywordList\"\n\t\t\t\t\t\t\t:key=\"index\" @click=\"goOrder(item)\" style=\"background-color: #FFFFFF;\">\n\t\t\t\t\t\t\t<image :src=\"item.homepageImg?item.homepageImg: '../../../static/logo.png'\"\n\t\t\t\t\t\t\t\tstyle=\"width: 200rpx;height: 200rpx;border-radius: 10rpx;\"></image>\n\t\t\t\t\t\t\t<view class=\"flex-sub margin-left text-white flex flex-direction justify-between\">\n\t\t\t\t\t\t\t\t<view class=\"flex justify-between align-center\">\n\t\t\t\t\t\t\t\t\t<view class=\"flex \">\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.authentication == 2||item.authentication == 3\">\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"../../../static/images/qi.png\" style=\"width: 28rpx;height: 28rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t</image>\n\t\t\t\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"\" v-if=\"item.authentication == 1||item.authentication == 3\">\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"../../../static/images/geren.png\"\n\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"width: 28rpx;height: 28rpx;margin-left: 10rpx;\"></image>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"margin-right-xs u-line-1\"\n\t\t\t\t\t\t\t\t\t\t\tstyle=\"margin-top: -2px;display: inline-block;margin-left: 10rpx;width: 260rpx;\">\n\t\t\t\t\t\t\t\t\t\t\t{{item.myLevel}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t\t\t{{item.distance/1000}}km\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"flex radius flex-wrap\" style=\"line-height: 34upx;\">\n\t\t\t\t\t\t\t\t\t<!-- <image :src=\"item.gameImg\" style=\"width: 34rpx;height: 32rpx;\" ></image> -->\n\t\t\t\t\t\t\t\t\t<text class=\"margin-right-xs margin-top-xs\" v-for=\"(item,index) in item.gameName\"\n\t\t\t\t\t\t\t\t\t\t:key=\"index\">{{item}}</text>\n\t\t\t\t\t\t\t\t\t<!-- <text>{{item.myLevel}}</text> -->\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"flex\" style=\"align-items: center;font-size: 24rpx;\" v-if=\"item.count\">\n\t\t\t\t\t\t\t\t\t<view style=\"color: #999999;background: #F2F2F2; padding: 5rpx 10rpx;\">\n\t\t\t\t\t\t\t\t\t\t已服务{{item.count}}人\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\n\t\t\t\t\t\t\t\t<view style=\"width: 100%;display: flex;justify-content: space-between;align-items: center;\">\n\t\t\t\t\t\t\t\t\t<view style=\"color:#FF1200;font-size: 31rpx;\">\n\t\t\t\t\t\t\t\t\t\t￥{{isVip? item.memberMoney :item.money}}/<text>{{item.unit}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\t\tstyle=\"background: #557EFD;color: #ffffff;padding: 15rpx 25rpx;border-radius: 45rpx;\">\n\t\t\t\t\t\t\t\t\t\t预约服务\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- <view v-if=\"keywordList.length == 0\">\n\t\t\t\t\t\t暂无数据\n\t\t\t\t\t</view> -->\n\t\t\t\t\t<empty v-if=\"keywordList.length == 0\"></empty>\n\t\t\t\t</scroll-view>\n\t\t\t\t<scroll-view class=\"keyword-box\" v-show=\"!isShowKeywordList\" scroll-y>\n\t\t\t\t\t<view class=\"keyword-block\">\n\t\t\t\t\t\t<view class=\"keyword-list-header\">\n\t\t\t\t\t\t\t<view>热门搜索</view>\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<image @tap=\"hotToggle\" :src=\"'/static/images/index/attention'+forbid+'.png'\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"keyword\" v-if=\"forbid==''\">\n\t\t\t\t\t\t\t<view v-for=\"(keyword,index) in hotKeywordList\" @tap=\"doSearch(keyword)\" :key=\"index\">\n\t\t\t\t\t\t\t\t{{keyword}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"hide-hot-tis\" v-else>\n\t\t\t\t\t\t\t<view>当前搜热已隐藏</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"keyword-block\" v-if=\"oldKeywordList.length>0\">\n\t\t\t\t\t\t<view class=\"keyword-list-header\">\n\t\t\t\t\t\t\t<view>历史记录</view>\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<image @tap=\"oldDelete\" src=\"/static/images/index/delete.png\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"keyword\">\n\t\t\t\t\t\t\t<view v-for=\"(keyword,index) in oldKeywordList\" @tap=\"doSearch(keyword)\" :key=\"index\">\n\t\t\t\t\t\t\t\t{{keyword}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport empty from '@/components/empty.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdefaultKeyword: \"\",\n\t\t\t\tkeyword: \"\",\n\t\t\t\toldKeywordList: [], //历史记录\n\t\t\t\thotKeywordList: [], //热搜\n\t\t\t\tkeywordList: [], //搜索列表\n\t\t\t\tforbid: '',\n\t\t\t\tisShowKeywordList: false,\n\t\t\t\tlimit: 10,\n\t\t\t\tpage: 1,\n\t\t\t\tuserId: '',\n\t\t\t\tisVip: false,\n\t\t\t\tvalue1: '',\n\t\t\t\tvalue2: '',\n\t\t\t\tvalue3: '',\n\t\t\t\tdefaultIndex: [0, 0, 0],\n\t\t\t\tfilterData: [\n\t\t\t\t\t[{\n\t\t\t\t\t\t\tlabel: '智能优选',\n\t\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tlabel: '距离优先',\n\t\t\t\t\t\t\tvalue: 3,\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tlabel: '人气优先',\n\t\t\t\t\t\t\tvalue: 2,\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tlabel: '同城',\n\t\t\t\t\t\t\tvalue: 1,\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\t[{\n\t\t\t\t\t\t\tlabel: '销量',\n\t\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tlabel: '从高到低',\n\t\t\t\t\t\t\tvalue: 'desc',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tlabel: '从低到高',\n\t\t\t\t\t\t\tvalue: 'asc',\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\t[{\n\t\t\t\t\t\t\tlabel: '价格',\n\t\t\t\t\t\t\tvalue: '',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tlabel: '从高到低',\n\t\t\t\t\t\t\tvalue: 'desc',\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tlabel: '从低到高',\n\t\t\t\t\t\t\tvalue: 'asc',\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t],\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.userId = uni.getStorageSync('userId')\n\t\t\tif (this.userId) {\n\t\t\t\tthis.getSearchList()\n\t\t\t}\n\t\t\tthis.isVip = uni.getStorageSync('isVIP')\n\n\t\t},\n\t\tmethods: {\n\t\t\t// 筛选\n\t\t\tchange(e) {\n\n\t\t\t\tthis.value1 = e[0][0].value\n\t\t\t\tthis.value2 = e[1][0].value\n\t\t\t\tthis.value3 = e[2][0].value\n\t\t\t\tthis.doSearch(this.keyword)\n\t\t\t\t// this.mescroll.resetUpScroll()\n\t\t\t},\n\t\t\t// 获取搜索历史\n\t\t\tgetSearchList() {\n\t\t\t\tthis.$Request.get(\"/app/Search/selectAppSearchNum\").then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.oldKeywordList = res.data.userSearchName\n\t\t\t\t\t\t// for (let i = 0; i < this.oldKeywordList.length; i++) {\n\t\t\t\t\t\t// \tthis.oldKeywordList[i].gameName = this.oldKeywordList[i].gameName.split(\",\");\n\t\t\t\t\t\t// }\n\t\t\t\t\t\tthis.hotKeywordList = res.data.allSerchName\n\n\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//清除历史搜索\n\t\t\toldDelete() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\tcontent: '确定清除历史搜索记录？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\tthis.$Request.get(\"/app/Search/deleteAppSearch\").then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tthis.getSearchList()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//执行搜索\n\t\t\tdoSearch(keyword) {\n\t\t\t\tthis.keyword = keyword === false ? this.keyword : keyword;\n\t\t\t\tthis.isShowKeywordList = true;\n\t\t\t\tif (!this.keyword) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入内容',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tlet data = {\n\t\t\t\t\tlike: this.keyword,\n\t\t\t\t\tlimit: this.limit,\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tcondition: this.value1, //智能优选\n\t\t\t\t\tsalesNum: this.value2, //不限男女\n\t\t\t\t\tby: this.value3, //价格\n\t\t\t\t\tcity: uni.getStorageSync('city'),\n\t\t\t\t\tlatitude: uni.getStorageSync('latitude'),\n\t\t\t\t\tlongitude: uni.getStorageSync('longitude')\n\t\t\t\t}\n\t\t\t\tif (this.userId) {\n\t\t\t\t\tthis.$Request.get(\"/app/orderTaking/queryTaking\", data).then(res => {\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tif (this.page == 1) this.keywordList = [];\n\t\t\t\t\t\t\tthis.keywordList = [...this.keywordList, ...res.data.list]\n\t\t\t\t\t\t\tfor (let i = 0; i < this.keywordList.length; i++) {\n\t\t\t\t\t\t\t\tthis.keywordList[i].gameName = this.keywordList[i].gameName.split(\",\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.$Request.get(\"/app/orderTaking/queryTakings\", data).then(res => {\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tif (this.page == 1) this.keywordList = [];\n\t\t\t\t\t\t\tthis.keywordList = [...this.keywordList, ...res.data.list]\n\t\t\t\t\t\t\tfor (let i = 0; i < this.keywordList.length; i++) {\n\t\t\t\t\t\t\t\tthis.keywordList[i].gameName = this.keywordList[i].gameName.split(\",\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 点击取消返回首页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack()\n\t\t\t},\n\t\t\t//热门搜索开关\n\t\t\thotToggle() {\n\t\t\t\tthis.forbid = this.forbid ? '' : '_forbid';\n\t\t\t},\n\t\t\t// 跳转订单\n\t\t\tgoOrder(e) {\n\t\t\t\tif (this.userId) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/game/order?id=' + e.id\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tthis.page = this.page + 1;\n\t\t\tthis.doSearch(false);\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\tthis.doSearch(false);\n\t\t},\n\t}\n</script>\n<style>\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.search-box {\n\t\twidth: 100%;\n\t\t/* background-color: rgb(242, 242, 242); */\n\t\tpadding: 15upx 2.5%;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tbackground-color: #FFFFFF;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 9;\n\t\t/* #ifdef H5 */\n\t\tposition: fixed;\n\t\ttop: 45px;\n\t\tleft: 0;\n\t\tright: 0;\n\t\t/* #endif */\n\t}\n\n\t.search-box .mSearch-input-box {\n\t\twidth: 100%;\n\t}\n\n\t.search-box .input-box {\n\t\twidth: 85%;\n\t\tflex-shrink: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.search-box .search-btn {\n\t\twidth: 15%;\n\t\tmargin: 0 0 0 2%;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tflex-shrink: 0;\n\t\tfont-size: 28upx;\n\t\tcolor: #fff;\n\t\tbackground: linear-gradient(to right, #ff9801, #ff570a);\n\t\tborder-radius: 60upx;\n\t}\n\n\t.search-box .input-box>input {\n\t\twidth: 100%;\n\t\theight: 60upx;\n\t\tfont-size: 32upx;\n\t\tborder: 0;\n\t\tborder-radius: 60upx;\n\t\t-webkit-appearance: none;\n\t\t-moz-appearance: none;\n\t\tappearance: none;\n\t\tpadding: 0 3%;\n\t\tmargin: 0;\n\t\tbackground-color: #ffffff;\n\t}\n\n\t.placeholder-class {\n\t\tcolor: #9e9e9e;\n\t}\n\n\t.search-keyword {\n\t\twidth: 100%;\n\t\t\n\t}\n\n\t.keyword-list-box {\n\t\theight: calc(100vh - 110upx);\n\t\tpadding-top: 10upx;\n\t\t/* border-radius: 20upx 20upx 0 0; */\n\t\t/* background-color: #fff; */\n\t}\n\n\t.keyword-entry-tap {\n\t\tbackground-color: #eee;\n\t}\n\n\t.keyword-entry {\n\t\twidth: 94%;\n\t\theight: 80upx;\n\t\tmargin: 0 3%;\n\t\tfont-size: 30upx;\n\t\tcolor: #333;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tborder-bottom: solid 1upx #e7e7e7;\n\t}\n\n\t.keyword-entry image {\n\t\twidth: 60upx;\n\t\theight: 60upx;\n\t}\n\n\t.keyword-entry .keyword-text,\n\t.keyword-entry .keyword-img {\n\t\theight: 80upx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.keyword-entry .keyword-text {\n\t\twidth: 90%;\n\t}\n\n\t.keyword-entry .keyword-img {\n\t\twidth: 10%;\n\t\tjustify-content: center;\n\t}\n\n\t.keyword-box {\n\t\theight: calc(100vh - 110upx);\n\t\t/* border-radius: 20upx 20upx 0 0; */\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.keyword-box .keyword-block {\n\t\tpadding: 10upx 0;\n\t}\n\n\t.keyword-box .keyword-block .keyword-list-header {\n\t\twidth: 94%;\n\t\tpadding: 10upx 3%;\n\t\tfont-size: 27upx;\n\t\tfont-weight: 700;\n\t\t/* color: #FFFFFF; */\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t}\n\n\t.keyword-box .keyword-block .keyword-list-header image {\n\t\twidth: 40upx;\n\t\theight: 40upx;\n\t}\n\n\t.keyword-box .keyword-block .keyword {\n\t\twidth: 94%;\n\t\tpadding: 3px 3%;\n\t\tdisplay: flex;\n\t\tflex-flow: wrap;\n\t\tjustify-content: flex-start;\n\t}\n\n\t.keyword-box .keyword-block .hide-hot-tis {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tfont-size: 28upx;\n\t\tcolor: #FFFFFF;\n\t}\n\n\t.keyword-box .keyword-block .keyword>view {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 10upx;\n\t\tpadding: 0 20upx;\n\t\tmargin: 10upx 20upx 10upx 0;\n\t\theight: 60upx;\n\t\tfont-size: 28upx;\n\t\tbackground-color: #FFFFFF;\n\t\tcolor: #343546;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627045\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}