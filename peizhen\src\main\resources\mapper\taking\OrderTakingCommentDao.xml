<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.taking.dao.OrderTakingCommentDao">

    <select id="selectOrderTakingComment" resultType="Map">
        SELECT t.id,
               u.user_name   as userName,
               u.avatar,
               t.content,
               t.score,
               t.create_time as createTime,
               t.user_id     as userId
        FROM taking_commnt t
                 LEFT JOIN tb_user u ON t.user_id = u.user_id
        WHERE t.order_taking_id = #{takingId}
        order by t.create_time desc
    </select>

    <select id="selectOrderTakingCommentByUserId" resultType="Map">
        SELECT t.id,
               u.user_name   as userName,
               u.avatar,
               t.content,
               t.score,
               t.create_time as createTime,
               t.user_id     as userId
        FROM taking_commnt t
                 LEFT JOIN tb_user u ON t.user_id = u.user_id
        WHERE t.order_taking_id in (select id from order_taking where user_id = #{userId})
        order by t.create_time desc
    </select>

    <select id="selectCommentNumber" resultType="int">
        SELECT count(*)
        FROM comment_fabulous c
        WHERE c.taking_comment_id = #{id}
    </select>

    <select id="selectGoodsNum" resultType="com.sqx.modules.taking.entity.CommentFabulous">
        SELECT *
        FROM comment_fabulous
        WHERE taking_comment_id = #{commentId}
          and user_id = #{userId}
    </select>
    <delete id="deleteGoodsNum">
        DELETE
        FROM comment_fabulous
        WHERE id = #{id}
    </delete>
    <insert id="insertGoodsNum">
        INSERT
        comment_fabulous ( taking_comment_id, user_id )
		VALUES	(
        #{commentId},
        #{userId}
        )
    </insert>


    <select id="selectAvgScore" resultType="Double">
        select score / counts
        from (select ifnull(sum(score), 0) as score, count(*) as counts
              from taking_commnt
              where order_taking_id = #{orderTakingId}) a
    </select>

    <select id="selectCommntCountByUserId" resultType="Integer">
        select count(1) from sys_evaluate where rider_user_id = #{userId}
        <if test="startTime!=null and startTime!=''">
            and date_format(create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>
</mapper>