package com.sqx.modules.pay.controller.app;


import com.sqx.common.utils.Result;
import com.sqx.modules.pay.entity.PayClassify;
import com.sqx.modules.pay.service.PayClassifyService;
import com.sqx.modules.tbCoupon.entity.TbCoupon;
import com.sqx.modules.tbCoupon.service.TbCouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/16
 */
@Slf4j
@RestController
@Api(value = "充值分类", tags = {"充值分类"})
@RequestMapping(value = "/app/payClassify")
public class AppPayClassifyController {

    @Autowired
    private PayClassifyService payClassifyService;
    @Autowired
    private TbCouponService couponService;


    @GetMapping("/selectPayClassifyList")
    @ApiOperation("查询充值分类")
    public Result selectPayClassifyList(){
        List<PayClassify> list = payClassifyService.list();
        for(PayClassify payClassify:list){
            if(payClassify.getCouponId()!=null){
                payClassify.setCoupon(couponService.getById(payClassify.getCouponId()));
            }
        }
        return Result.success().put("data",list);
    }




}