{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/work.vue?431b", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/work.vue?b9e5", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/work.vue?286b", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/work.vue?0fa3", "uni-app:///my/other/work.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/work.vue?5b62", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/work.vue?f71a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userName", "phone", "content", "contentTitle", "onLoad", "onShow", "methods", "get<PERSON><PERSON>", "Changekefu", "uni", "phoneNumber", "extInfo", "url", "corpId", "success", "submit", "title", "icon", "classify", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyCvvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MAAA;MACA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QAAA;QACAC;UACAC;QACA;MACA;QAAA;QACA;QAEAhB;UACAiB;YACAC;UACA;UACAC;UACAC;QACA;MAUA;QAAA;QACAL;UACAG;QACA;MACA;IACA;IACAG;MACA;QACAN;UACAO;UACAC;QACA;QACA;MACA;MACA;QACAR;UACAO;UACAC;QACA;QACA;MACA;MAEA;QACAjB;QACAC;QACAiB;MACA;MAEA;QACA;UACAT;YACAO;YACAC;UACA;UACAE;YACAV;UACA;QAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtIA;AAAA;AAAA;AAAA;AAAs1C,CAAgB,4rCAAG,EAAC,C;;;;;;;;;;;ACA12C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/other/work.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/other/work.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./work.vue?vue&type=template&id=8e573a56&\"\nvar renderjs\nimport script from \"./work.vue?vue&type=script&lang=js&\"\nexport * from \"./work.vue?vue&type=script&lang=js&\"\nimport style0 from \"./work.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/other/work.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./work.vue?vue&type=template&id=8e573a56&\"", "var components\ntry {\n  components = {\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./work.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./work.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<image class=\"bgimg\" src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/07/20fbd74a515bc261473d8ac593e1a045.png\" mode=\"widthFix\"></image>\n\n\t\t<view class=\"name\">\n\t\t\t<view class=\"name-box\">\n\t\t\t\t<image src=\"../static/name.png\" mode=\"\"></image>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"sm\">\n\t\t\t<view class=\"sm-box\">\n\t\t\t\t<view class=\"sm-box-title\">{{contentTitle}}</view>\n\t\t\t\t<view class=\"sm-box-sm\" v-html=\"content\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"lx\">\n\t\t\t<view class=\"lx-box\">\n\t\t\t\t<view class=\"lx-box-con\">\n\t\t\t\t\t<view class=\"lx-box-con-item\">\n\t\t\t\t\t\t<view class=\"lx-box-con-item-t\">联系人姓名</view>\n\t\t\t\t\t\t<u-input placeholder=\"请输入联系人姓名\" v-model=\"userName\" :clearable=\"false\"></u-input>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"line\"></view>\n\t\t\t\t\t<view class=\"lx-box-con-item\" style=\"margin-top: 30rpx;\">\n\t\t\t\t\t\t<view class=\"lx-box-con-item-t\">联系人手机号</view>\n\t\t\t\t\t\t<u-input placeholder=\"请输入联系人手机号\" v-model=\"phone\" :clearable=\"false\"></u-input>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"btn\">\n\t\t\t<view class=\"btn-box\">\n\t\t\t\t<view class=\"btn-box-l\" @click=\"submit()\">确认提交</view>\n\t\t\t\t<view class=\"btn-box-r\" @click=\"Changekefu()\">直接联系</view>\n\t\t\t</view>\n\t\t</view>\n\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuserName: '',\n\t\t\t\tphone: '',\n\t\t\t\tcontent:'',\n\t\t\t\tcontentTitle:''\n\t\t\t\t\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getwenben()\n\t\t},\n\t\tonShow() {\n\n\t\t},\n\t\tmethods: {\n\t\t\tgetwenben() {\n\t\t\t// 招聘岗位描述 315\n\t\t\tthis.$Request.get('/app/common/type/315').then(res => {\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tthis.contentTitle = res.data.min\n\t\t\t\t\tthis.content = res.data.value\n\t\t\t\t}\n\t\t\t});\n\t\t\t},\n\t\t\tChangekefu() {\n\t\t\t\tlet SelKeFu = this.$queue.getData('SelKeFu');\n\t\t\t\tif (SelKeFu + '' == '1') { //手机号\n\t\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\t\tphoneNumber: uni.getStorageSync('kefuPhone')\n\t\t\t\t\t});\n\t\t\t\t} else if (SelKeFu + '' == '2') { //企业微信\n\t\t\t\t\tlet that = this\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\twx.openCustomerServiceChat({\n\t\t\t\t\t\textInfo: {\n\t\t\t\t\t\t\turl: that.$queue.getData('kefu')\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcorpId: that.$queue.getData('kefuAppId'),\n\t\t\t\t\t\tsuccess(res) {}\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\twindow.location.href = that.$queue.getData('kefu');\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef APP\n\t\t\t\t\tlet kefu = that.$queue.getData('kefu')\n\t\t\t\t\tconsole.log(kefu)\n\t\t\t\t\tplus.runtime.openURL(kefu, function(res) {});\n\t\t\t\t\t// #endif\n\t\t\t\t} else { //客服二维码页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/kefu/kefu'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tsubmit() {\n\t\t\t\tif (!this.userName) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入联系人姓名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.phone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入联系人手机号码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tlet data = {\n\t\t\t\t\tuserName: this.userName,\n\t\t\t\t\tphone: this.phone,\n\t\t\t\t\tclassify: 1\n\t\t\t\t}\n\n\t\t\t\tthis.$Request.postJson(\"//app/cityAgency/insertCityAgency\", data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '提交成功！',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t}, 1000)\n\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"less\">\n\t.content {\n\t\twidth: 100%;\n\t\tmin-height: 100vh;\n\t}\n\n\t.bgimg {\n\t\twidth: 100%;\n\t\tposition: relative;\n\t}\n\n\t.name {\n\t\twidth: 100%;\n\t\theight: auto;\n\t\tposition: absolute;\n\t\ttop: 530rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tz-index: 999;\n\n\t\t.name-box {\n\t\t\twidth: 686rpx;\n\t\t\theight: auto;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\n\t\t\timage {\n\t\t\t\twidth: 400rpx;\n\t\t\t\theight: 110rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.sm {\n\t\twidth: 100%;\n\t\theight: 600rpx;\n\t\tposition: absolute;\n\t\ttop: 615rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\n\t\t.sm-box {\n\t\t\twidth: 613rpx;\n\t\t\theight: 100%;\n\t\t\tborder-radius: 24rpx;\n\t\t\tbackground-color: #E8EEFF;\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: center;\n\n\t\t\t.sm-box-title {\n\t\t\t\twidth: 100%;\n\t\t\t\tmargin-top: 50rpx;\n\t\t\t\tcolor: #2E40F7;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 800;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\n\t\t\t.sm-box-sm {\n\t\t\t\twidth: 544rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tcolor: #2E40F7;\n\t\t\t\tfont-size: 24rpx;\n\n\t\t\t}\n\t\t}\n\t}\n\n\t.lx {\n\t\twidth: 100%;\n\t\t// height: 321rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tposition: absolute;\n\t\ttop: 1250rpx;\n\n\t\t.lx-box {\n\t\t\twidth: 613rpx;\n\t\t\theight: 100%;\n\t\t\tbackground-color: #E8EEFF;\n\t\t\tborder-radius: 24rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tpadding-bottom: 30upx;\n\n\t\t\t.lx-box-con {\n\t\t\t\twidth: 547rpx;\n\t\t\t\theight: 100%;\n\t\t\t}\n\n\t\t\t.line {\n\t\t\t\twidth: 100%;\n\t\t\t\tborder: 1rpx solid #B4BCFF;\n\t\t\t\tmargin-top: 10rpx;\n\t\t\t}\n\n\t\t\t.lx-box-con-item {\n\t\t\t\twidth: 100%;\n\t\t\t\tmargin-top: 50rpx;\n\n\t\t\t\t.lx-box-con-item-t {\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t// font-weight: 800;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tposition: absolute;\n\t\ttop: 1660rpx;\n\n\t\t.btn-box {\n\t\t\twidth: 613rpx;\n\t\t\theight: 100%;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.btn-box-l {\n\t\t\t\twidth: 290rpx;\n\t\t\t\theight: 100%;\n\t\t\t\tborder-radius: 44rpx;\n\t\t\t\tbackground-color: #97A8F8;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.btn-box-r {\n\t\t\t\twidth: 290rpx;\n\t\t\t\theight: 100%;\n\t\t\t\tborder-radius: 44rpx;\n\t\t\t\tbackground-color: #435DF0;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./work.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./work.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621959\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}