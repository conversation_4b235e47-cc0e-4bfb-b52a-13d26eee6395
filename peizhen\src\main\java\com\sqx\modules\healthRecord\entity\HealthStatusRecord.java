package com.sqx.modules.healthRecord.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;

/**
 * <p>
 * 健康状况记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class HealthStatusRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "record_id", type = IdType.AUTO)
    @ApiModelProperty("记录ID")
    private Long recordId;

    /**
     * 患者ID，关联patient_info表
     */
    @ApiModelProperty("患者ID")
    private Long patientId;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 记录日期
     */
    @ApiModelProperty("记录日期")
    private LocalDate recordDate;

    /**
     * 记录时间
     */
    @ApiModelProperty("记录时间")
    private LocalTime recordTime;

    /**
     * 记录类型(1日常记录 2体检记录 3就诊记录 4复查记录 5紧急记录)
     */
    @ApiModelProperty("记录类型(1日常记录 2体检记录 3就诊记录 4复查记录 5紧急记录)")
    private Integer recordType;

    /**
     * 健康状态(1优秀 2良好 3一般 4较差 5差)
     */
    @ApiModelProperty("健康状态(1优秀 2良好 3一般 4较差 5差)")
    private Integer healthStatus;

    /**
     * 症状描述
     */
    @ApiModelProperty("症状描述")
    private String symptoms;

    /**
     * 体重(kg)
     */
    @ApiModelProperty("体重(kg)")
    private BigDecimal weight;

    /**
     * 身高(cm)
     */
    @ApiModelProperty("身高(cm)")
    private BigDecimal height;

    /**
     * BMI指数
     */
    @ApiModelProperty("BMI指数")
    private BigDecimal bmi;

    /**
     * 收缩压(mmHg)
     */
    @ApiModelProperty("收缩压(mmHg)")
    private Integer bloodPressureSystolic;

    /**
     * 舒张压(mmHg)
     */
    @ApiModelProperty("舒张压(mmHg)")
    private Integer bloodPressureDiastolic;

    /**
     * 心率(次/分)
     */
    @ApiModelProperty("心率(次/分)")
    private Integer heartRate;

    /**
     * 体温(℃)
     */
    @ApiModelProperty("体温(℃)")
    private BigDecimal bodyTemperature;

    /**
     * 血糖(mmol/L)
     */
    @ApiModelProperty("血糖(mmol/L)")
    private BigDecimal bloodSugar;

    /**
     * 当日用药情况
     */
    @ApiModelProperty("当日用药情况")
    private String medicationTaken;

    /**
     * 用药依从性(1完全依从 2基本依从 3部分依从 4不依从)
     */
    @ApiModelProperty("用药依从性(1完全依从 2基本依从 3部分依从 4不依从)")
    private Integer medicationAdherence;

    /**
     * 运动时长(分钟)
     */
    @ApiModelProperty("运动时长(分钟)")
    private Integer exerciseDuration;

    /**
     * 运动类型
     */
    @ApiModelProperty("运动类型")
    private String exerciseType;

    /**
     * 睡眠时长(小时)
     */
    @ApiModelProperty("睡眠时长(小时)")
    private BigDecimal sleepHours;

    /**
     * 睡眠质量(1很好 2好 3一般 4差 5很差)
     */
    @ApiModelProperty("睡眠质量(1很好 2好 3一般 4差 5很差)")
    private Integer sleepQuality;

    /**
     * 情绪状态(1很好 2好 3一般 4差 5很差)
     */
    @ApiModelProperty("情绪状态(1很好 2好 3一般 4差 5很差)")
    private Integer moodStatus;

    /**
     * 疼痛等级(0无痛 1-10级)
     */
    @ApiModelProperty("疼痛等级(0无痛 1-10级)")
    private Integer painLevel;

    /**
     * 疼痛部位
     */
    @ApiModelProperty("疼痛部位")
    private String painLocation;

    /**
     * 饮食记录
     */
    @ApiModelProperty("饮食记录")
    private String dietNotes;

    /**
     * 特殊事件记录
     */
    @ApiModelProperty("特殊事件记录")
    private String specialEvents;

    /**
     * 医生建议
     */
    @ApiModelProperty("医生建议")
    private String doctorAdvice;

    /**
     * 下次随访日期
     */
    @ApiModelProperty("下次随访日期")
    private LocalDate nextFollowupDate;

    /**
     * 风险评估(1低风险 2中风险 3高风险)
     */
    @ApiModelProperty("风险评估(1低风险 2中风险 3高风险)")
    private Integer riskAssessment;

    /**
     * 数据来源(1手动录入 2设备同步 3医院导入 4第三方接口)
     */
    @ApiModelProperty("数据来源(1手动录入 2设备同步 3医院导入 4第三方接口)")
    private Integer dataSource;

    /**
     * 设备信息(如果是设备同步)
     */
    @ApiModelProperty("设备信息")
    private String deviceInfo;

    /**
     * 是否异常(0正常 1异常)
     */
    @ApiModelProperty("是否异常(0正常 1异常)")
    private Integer isAbnormal;

    /**
     * 异常指标说明
     */
    @ApiModelProperty("异常指标说明")
    private String abnormalIndicators;

    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    private Long createdBy;

    /**
     * 更新人ID
     */
    @ApiModelProperty("更新人ID")
    private Long updatedBy;

    /**
     * 是否删除(0未删除 1已删除)
     */
    @ApiModelProperty("是否删除(0未删除 1已删除)")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    // 关联查询字段
    @TableField(exist = false)
    @ApiModelProperty("患者姓名")
    private String patientName;

    @TableField(exist = false)
    @ApiModelProperty("用户昵称")
    private String userName;

    @TableField(exist = false)
    @ApiModelProperty("附件列表")
    private java.util.List<HealthRecordAttachment> attachments;
}
