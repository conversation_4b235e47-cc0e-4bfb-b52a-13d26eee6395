package com.sqx.modules.rewardLevel.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.rewardLevel.entity.RewardLevel;
import com.sqx.modules.rewardLevel.service.RewardLevelService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 *等级分佣管理-用户端
 * <AUTHOR>
 * @since 2023-05-12
 */
@RestController
@RequestMapping("/admin/rewardLevel/")
public class RewardLevelController {

    @Autowired
    private RewardLevelService levelService;

    /**
     * 添加分佣比例
     * @param rewardLevel 等级奖励信息
     * @return
     */
    @ApiOperation("添加分佣比例")
    @PostMapping("saveLevel")
    public Result saveLevel(RewardLevel rewardLevel) {
        return levelService.saveLevel(rewardLevel);
    }

    /**
     * 获取分佣比例列表
     * @param page
     * @param limit
     * @param rewardLevel 等级奖励信息
     * @return
     */
    @ApiOperation("获取分佣比例列表")
    @GetMapping("getLevelList")
    public Result getLevelList(Integer page, Integer limit, RewardLevel rewardLevel) {
        return Result.success().put("data", levelService.getLevelList(page, limit, rewardLevel));
    }

    /**
     * 删除分佣比例信息
     * @param rewardId 等级id
     * @return
     */
    @ApiOperation("删除分佣比例信息")
    @PostMapping("deleteLevelById")
    public Result deleteLevelById(Long rewardId) {
        return levelService.removeById(rewardId) ? Result.success() : Result.error();
    }
}

