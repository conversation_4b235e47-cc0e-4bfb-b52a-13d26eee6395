package com.sqx.modules.apply.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.apply.entity.Apply;
import com.sqx.modules.apply.service.ApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(value = "申请", tags = {"申请"})
@RequestMapping(value = "/apply")
public class ApplyController {

    @Autowired
    private ApplyService applyService;

    @PostMapping("/insertApply")
    @ApiOperation("发起申请")
    public Result insertApply(@RequestBody Apply apply){
        return applyService.insertApply(apply);
    }

    @PostMapping("/updateApply")
    @ApiOperation("修改申请")
    public Result updateApply(@RequestBody Apply apply){
        return applyService.updateApply(apply);
    }

    @PostMapping("/deleteApply")
    @ApiOperation("删除申请")
    public Result deleteApply(Long applyId){
        return applyService.deleteApply(applyId);
    }

    @GetMapping("/selectApplyList")
    @ApiOperation("查询申请列表")
    public Result selectApplyList(Integer page,Integer limit,String applyName,String applyPhone,Integer status,Integer classify){
        return applyService.selectApplyList(page, limit, applyName, applyPhone, status, classify);
    }

    @GetMapping("/selectApplyByUserIdAndClassify")
    @ApiOperation("查询申请详情")
    public Result selectApplyByUserIdAndClassify(Long userId,Integer classify){
        return Result.success().put("data",applyService.selectApplyByUserIdAndClassify(userId, classify));
    }

    @PostMapping("/auditApply")
    @ApiOperation("审核申请")
    public Result auditApply(String ids,String content,Integer status){
        return applyService.auditApply(ids, status, content);
    }


    






}
