/**
 * AI对话状态管理
 */
const aiStore = {
	// 本地缓存键名
	CACHE_KEYS: {
		CONVERSATIONS: 'ai_conversations',
		SELECTED_MODEL: 'ai_selected_model',
		CHAT_HISTORY: 'ai_chat_history_',
		USER_SETTINGS: 'ai_user_settings'
	},

	/**
	 * 获取本地对话列表缓存
	 */
	getCachedConversations() {
		try {
			const cached = uni.getStorageSync(this.CACHE_KEYS.CONVERSATIONS)
			return cached ? JSON.parse(cached) : []
		} catch (error) {
			console.error('获取对话缓存失败:', error)
			return []
		}
	},

	/**
	 * 缓存对话列表
	 * @param {Array} conversations 
	 */
	setCachedConversations(conversations) {
		try {
			uni.setStorageSync(this.CACHE_KEYS.CONVERSATIONS, JSON.stringify(conversations))
		} catch (error) {
			console.error('缓存对话列表失败:', error)
		}
	},

	/**
	 * 添加或更新单个对话缓存
	 * @param {Object} conversation 
	 */
	updateCachedConversation(conversation) {
		const conversations = this.getCachedConversations()
		const index = conversations.findIndex(c => c.id === conversation.id)
		
		if (index !== -1) {
			conversations[index] = { ...conversations[index], ...conversation }
		} else {
			conversations.unshift(conversation)
		}
		
		this.setCachedConversations(conversations)
	},

	/**
	 * 删除对话缓存
	 * @param {string} conversationId 
	 */
	removeCachedConversation(conversationId) {
		const conversations = this.getCachedConversations()
		const filtered = conversations.filter(c => c.id !== conversationId)
		this.setCachedConversations(filtered)
		
		// 同时删除对话历史缓存
		this.removeChatHistory(conversationId)
	},

	/**
	 * 获取选中的AI模型
	 */
	getSelectedModel() {
		return uni.getStorageSync(this.CACHE_KEYS.SELECTED_MODEL) || 'deepseek-chat'
	},

	/**
	 * 设置选中的AI模型
	 * @param {string} modelCode 
	 */
	setSelectedModel(modelCode) {
		uni.setStorageSync(this.CACHE_KEYS.SELECTED_MODEL, modelCode)
	},

	/**
	 * 获取对话历史缓存
	 * @param {string} conversationId 
	 */
	getChatHistory(conversationId) {
		try {
			const cached = uni.getStorageSync(this.CACHE_KEYS.CHAT_HISTORY + conversationId)
			return cached ? JSON.parse(cached) : []
		} catch (error) {
			console.error('获取聊天历史缓存失败:', error)
			return []
		}
	},

	/**
	 * 缓存对话历史
	 * @param {string} conversationId 
	 * @param {Array} messages 
	 */
	setChatHistory(conversationId, messages) {
		try {
			// 只缓存最近的50条消息，避免缓存过大
			const limitedMessages = messages.slice(-50)
			uni.setStorageSync(
				this.CACHE_KEYS.CHAT_HISTORY + conversationId, 
				JSON.stringify(limitedMessages)
			)
		} catch (error) {
			console.error('缓存聊天历史失败:', error)
		}
	},

	/**
	 * 添加消息到历史缓存
	 * @param {string} conversationId 
	 * @param {Object} message 
	 */
	addMessageToHistory(conversationId, message) {
		const history = this.getChatHistory(conversationId)
		history.push(message)
		this.setChatHistory(conversationId, history)
	},

	/**
	 * 删除对话历史缓存
	 * @param {string} conversationId 
	 */
	removeChatHistory(conversationId) {
		uni.removeStorageSync(this.CACHE_KEYS.CHAT_HISTORY + conversationId)
	},

	/**
	 * 获取用户设置
	 */
	getUserSettings() {
		try {
			const settings = uni.getStorageSync(this.CACHE_KEYS.USER_SETTINGS)
			return settings ? JSON.parse(settings) : {
				autoSave: true,
				soundEnabled: true,
				maxHistoryDays: 30,
				defaultModel: 'deepseek-chat'
			}
		} catch (error) {
			console.error('获取用户设置失败:', error)
			return {}
		}
	},

	/**
	 * 保存用户设置
	 * @param {Object} settings 
	 */
	setUserSettings(settings) {
		try {
			const currentSettings = this.getUserSettings()
			const newSettings = { ...currentSettings, ...settings }
			uni.setStorageSync(this.CACHE_KEYS.USER_SETTINGS, JSON.stringify(newSettings))
		} catch (error) {
			console.error('保存用户设置失败:', error)
		}
	},

	/**
	 * 清理过期缓存
	 * @param {number} maxDays 最大保留天数
	 */
	cleanExpiredCache(maxDays = 30) {
		try {
			const conversations = this.getCachedConversations()
			const cutoffTime = new Date()
			cutoffTime.setDate(cutoffTime.getDate() - maxDays)
			
			const validConversations = conversations.filter(conv => {
				const updateTime = new Date(conv.updateTime || conv.createTime)
				return updateTime > cutoffTime
			})
			
			// 删除过期的对话历史缓存
			conversations.forEach(conv => {
				const updateTime = new Date(conv.updateTime || conv.createTime)
				if (updateTime <= cutoffTime) {
					this.removeChatHistory(conv.id)
				}
			})
			
			this.setCachedConversations(validConversations)
			console.log(`清理了 ${conversations.length - validConversations.length} 个过期对话缓存`)
		} catch (error) {
			console.error('清理过期缓存失败:', error)
		}
	},

	/**
	 * 获取缓存统计信息
	 */
	getCacheStats() {
		try {
			const conversations = this.getCachedConversations()
			let totalMessages = 0
			let totalSize = 0
			
			conversations.forEach(conv => {
				const history = this.getChatHistory(conv.id)
				totalMessages += history.length
				totalSize += JSON.stringify(history).length
			})
			
			return {
				conversationCount: conversations.length,
				totalMessages,
				totalSize: Math.round(totalSize / 1024) + 'KB'
			}
		} catch (error) {
			console.error('获取缓存统计失败:', error)
			return { conversationCount: 0, totalMessages: 0, totalSize: '0KB' }
		}
	},

	/**
	 * 清空所有AI相关缓存
	 */
	clearAllCache() {
		try {
			Object.values(this.CACHE_KEYS).forEach(key => {
				if (key.endsWith('_')) {
					// 对于带后缀的键，需要遍历删除
					const conversations = this.getCachedConversations()
					conversations.forEach(conv => {
						uni.removeStorageSync(key + conv.id)
					})
				} else {
					uni.removeStorageSync(key)
				}
			})
			console.log('已清空所有AI缓存')
		} catch (error) {
			console.error('清空缓存失败:', error)
		}
	},

	/**
	 * 同步本地缓存与服务器数据
	 * @param {Array} serverConversations 服务器对话列表
	 */
	syncWithServer(serverConversations) {
		try {
			const localConversations = this.getCachedConversations()
			const mergedConversations = []
			
			// 以服务器数据为准，合并本地缓存的额外信息
			serverConversations.forEach(serverConv => {
				const localConv = localConversations.find(local => local.id === serverConv.id)
				mergedConversations.push({
					...serverConv,
					// 保留本地的一些状态信息
					localLastViewTime: localConv?.localLastViewTime,
					localUnreadCount: localConv?.localUnreadCount
				})
			})
			
			this.setCachedConversations(mergedConversations)
		} catch (error) {
			console.error('同步缓存失败:', error)
		}
	}
}

export default aiStore
