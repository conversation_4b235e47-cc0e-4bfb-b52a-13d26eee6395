package com.sqx.modules.apply.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * @description apply
 * <AUTHOR>
 * @date 2022-08-05
 */
@Data
public class Apply implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 申请id
     */
    @TableId(type = IdType.AUTO)
    private Long applyId;

    /**
     * 姓名
     */
    private String applyName;

    /**
     * 电话
     */
    private String applyPhone;

    /**
     * 年龄
     */
    private String applyAge;

    /**
     * 内容
     */
    private String applyContent;

    /**
    * 分类 1推广员 2代理商
     */
    private Integer classify;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 状态 1待审核 2通过 3拒绝
     */
    private Integer status;

    /**
     * 审核内容
     */
    private String auditContent;

    /**
     * 创建时间
     */
    private String createTime;

    public Apply() {}
}
