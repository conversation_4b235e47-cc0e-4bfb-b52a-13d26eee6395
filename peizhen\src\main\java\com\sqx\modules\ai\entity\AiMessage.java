package com.sqx.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * AI对话消息实体类
 */
@Data
@ApiModel("AI对话消息")
@TableName("ai_message")
public class AiMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("消息ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("对话会话ID")
    private Long conversationId;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("角色")
    private String role;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("模型代码")
    private String modelCode;

    @ApiModelProperty("输入token数")
    private Integer promptTokens;

    @ApiModelProperty("输出token数")
    private Integer completionTokens;

    @ApiModelProperty("总token数")
    private Integer totalTokens;

    @ApiModelProperty("响应时间")
    private Integer responseTime;

    @ApiModelProperty("错误信息")
    private String errorMessage;

    @ApiModelProperty("创建时间")
    private String createTime;
}
