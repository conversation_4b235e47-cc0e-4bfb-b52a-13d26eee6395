package com.sqx.modules.appointInformation.dao;

import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Mapper
public interface AppointInformationDao extends BaseMapper<AppointInformation> {

    AppointInformation selectByOrdersId(@Param("ordersId") Long ordersId);
}
