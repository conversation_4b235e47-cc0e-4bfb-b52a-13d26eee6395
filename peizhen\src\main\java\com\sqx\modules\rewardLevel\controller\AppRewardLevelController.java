package com.sqx.modules.rewardLevel.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.rewardLevel.entity.RewardLevel;
import com.sqx.modules.rewardLevel.service.RewardLevelService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 等级分佣管理-管理端
 * <AUTHOR>
 * @since 2023-05-12
 */
@RestController
@RequestMapping("/app/rewardLevel/")
public class AppRewardLevelController {

    @Autowired
    private RewardLevelService levelService;


    /**
     * 获取分佣比例列表
     * @param page
     * @param limit
     * @param rewardLevel 等级奖励信息
     * @return
     */
    @ApiOperation("获取分佣比例列表")
    @GetMapping("getLevelList")
    public Result getLevelList(Integer page, Integer limit, RewardLevel rewardLevel) {
        return Result.success().put("data", levelService.getLevelList(page, limit, rewardLevel));
    }

    /**
     * 获取等级奖励信息
     * @param rewardId 等级id
     * @return
     */
    @ApiOperation("获取等级奖励信息")
    @GetMapping("getLevelInfo")
   public Result getLevelInfo(Long rewardId){
        return Result.success().put("data", levelService.getById(rewardId));
   }
}

