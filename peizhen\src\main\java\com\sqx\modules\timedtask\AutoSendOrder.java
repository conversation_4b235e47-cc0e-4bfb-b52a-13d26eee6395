package com.sqx.modules.timedtask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.dao.UserDao;
import com.sqx.modules.app.dao.UserMoneyDao;
import com.sqx.modules.app.dao.UserMoneyDetailsDao;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.entity.UserMoneyDetails;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.sqx.modules.appointInformation.service.AppointInformationService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;

import com.sqx.modules.orders.dao.OrdersDao;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.pay.controller.app.AliPayController;
import com.sqx.modules.pay.dao.PayDetailsDao;
import com.sqx.modules.pay.entity.PayDetails;
import com.sqx.modules.pay.service.WxService;
import com.sqx.modules.rewardLevel.entity.RewardLevel;
import com.sqx.modules.rewardLevel.service.RewardLevelService;
import com.sqx.modules.riderLocation.service.RiderLocationService;
import com.sqx.modules.utils.SenInfoCheckUtil;
import com.sun.org.apache.bcel.internal.generic.I2F;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class AutoSendOrder {
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private OrdersDao ordersDao;
    @Autowired
    private RiderLocationService locationService;
    @Autowired
    private OrdersService ordersService;
    @Autowired
    private UserService userService;
    @Autowired
    private AppointInformationService informationService;
    @Autowired
    private RewardLevelService levelService;

    //定时自动给陪诊人推单
    @Scheduled(cron = "0/2 * * * * ?", zone = "Asia/Shanghai")
    public void autoSendOrder() {
        CommonInfo three = commonInfoService.findOne(316);
        if ("是".equals(three.getValue())) {
            CommonInfo two = commonInfoService.findOne(317);
            CommonInfo one = commonInfoService.findOne(318);
            CommonInfo four = commonInfoService.findOne(319);
            Double cashDeposit = Double.valueOf(four.getValue());
            Double distance = Double.valueOf(one.getValue());
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            List<Orders> ordersList = ordersService.list(new QueryWrapper<Orders>().eq("state", 4));
            for (Orders orders : ordersList) {
                UserEntity userEntity = userService.getById(orders.getUserId());
                LocalDateTime createTime = LocalDateTime.parse(orders.getCreateTime(), fmt);
                LocalDateTime expireTime = createTime.plusMinutes(Long.parseLong(two.getValue()));
                if (LocalDateTime.now().isAfter(expireTime)) {
                    Long userId = locationService.getDistanceNearRide(orders.getLongitude(), orders.getLatitude(), cashDeposit, distance);
                    UserEntity riderEntity = userService.getById(userId);
                    if (riderEntity != null) {
                        orders.setOrderTakingUserId(riderEntity.getUserId());
                        orders.setState("5");
                        ordersService.updateById(orders);
                        //师傅消息推送
                        AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", orders.getOrdersId()));
                        if (StringUtils.isNotEmpty(riderEntity.getShopOpenId())) {
                            ordersService.sendOrderMeg(orders, riderEntity, information, 2, 13, 2);
                            userService.pushToSingleRider("系统自动推单", "系统已为您自动派单，请及时前往派送！", riderEntity.getClientid());
                            userService.sendMsgDXB(riderEntity.getPhone(), "autosend", 0);
                        }
                        //用户消息推送
                        if (userEntity != null && StringUtils.isNotBlank(userEntity.getOpenId())) {
                            ordersService.sendOrderMeg(orders, userEntity, information, 1, 2, 1);
                            userService.pushToSingle("订单被接单", "师傅电话:" + riderEntity.getPhone(), userEntity.getClientid());
                            userService.sendMsgDXB(userEntity.getPhone(), "autosend", 0);
                        }

                    }
                }

            }


        }
    }

    /**
     * 订单超时取消
     *
     * @return
     */
    @Scheduled(cron = "0 0/1 * * * ?", zone = "Asia/Shanghai")
    public void overOrder() {
        String overTime = commonInfoService.findOne(325).getValue();
        List<Orders> ordersList = ordersService.list(new QueryWrapper<Orders>().eq("state", 0));
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (Orders orders : ordersList) {
            LocalDateTime createTime = LocalDateTime.parse(orders.getCreateTime(), df);
            if (LocalDateTime.now().isAfter(createTime.plusMinutes(Long.parseLong(overTime)))) {
                orders.setState("3");
                orders.setUpdateTime(df.format(LocalDateTime.now()));
                ordersService.updateById(orders);
            }
        }
    }

    /**
     * 获取指定时间几分钟前的时间
     *
     * @return
     */
    public String getCurrentTime(int i) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(Calendar.MINUTE, -i);
        Date beforeD = beforeTime.getTime();
        String time = sdf.format(beforeD);
        return time;
    }

    /**
     * 订单完成奖励周期初始化(每周一早上6点执行)
     */
    @Scheduled(cron = " 0 0 6 ? * 1", zone = "Asia/Shanghai")
    public void rewardCycleWeek() {
        CommonInfo one = commonInfoService.findOne(347);
        if (one == null || StringUtils.isBlank(one.getValue())) {
            return;
        }
        int value = Integer.parseInt(one.getValue());
        if (value == 1) {
            initUserRate();
        }
    }

    /**
     * 订单完成奖励周期初始化(每月一号上午6点执行一次)
     */
    @Scheduled(cron = "0 0 6 1 * ?", zone = "Asia/Shanghai")
    public void rewardCycleMonth() {
        CommonInfo one = commonInfoService.findOne(347);
        if (one == null || StringUtils.isBlank(one.getValue())) {
            return;
        }
        int value = Integer.parseInt(one.getValue());
        if (value == 2) {
            initUserRate();
        }
    }


    public void initUserRate() {
        String level = commonInfoService.findOne(339).getValue();
        UserEntity userEntity = new UserEntity();

        //如果没有配置分佣等级,则选择默认配置分佣比例
        if (StringUtils.isNotBlank(level)) {
            RewardLevel rewardLevel = levelService.getById(level);
            userEntity.setRate(rewardLevel.getScale());
        } else {
            String defaultRate = commonInfoService.findOne(206).getValue();
            userEntity.setRate(new BigDecimal(defaultRate));
        }
        userService.update(userEntity, new QueryWrapper<>());

    }
}
