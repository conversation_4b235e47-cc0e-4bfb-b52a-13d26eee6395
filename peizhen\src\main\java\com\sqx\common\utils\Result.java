package com.sqx.common.utils;

import org.apache.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 *
 */
public class Result extends HashMap<String, Object> {
	private static final long serialVersionUID = 1L;
	
	public Result () {
		put("code", 0);
		put("msg", "success");
	}
	
	public static Result error() {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, "未知异常，请联系管理员");
	}
	
	public static Result error(String msg) {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, msg);
	}
	
	public static Result error(int code, String msg) {
		Result r = new Result();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}
	public static Result upStatus(Integer rows) {
		return rows > 0 ? success() : error();
	}

	public static Result success(String msg) {
		Result r = new Result();
		r.put("msg", msg);
		return r;
	}
	
	public static Result success(Map<String, Object> map) {
		Result r = new Result();
		r.putAll(map);
		return r;
	}
	
	public static Result success() {
		return new Result();
	}

	@Override
    public Result  put(String key, Object value) {
		super.put(key, value);
		return this;
	}
}
