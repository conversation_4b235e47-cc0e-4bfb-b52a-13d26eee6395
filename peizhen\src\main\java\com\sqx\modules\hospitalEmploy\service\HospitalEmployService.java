package com.sqx.modules.hospitalEmploy.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalEmploy.entity.HospitalEmploy;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
public interface HospitalEmployService extends IService<HospitalEmploy> {

    Result addHospitalEmploy(HospitalEmploy hospitalEmploy);

    Result updateHospitalEmploy(HospitalEmploy hospitalEmploy);

    Result deleteHospitalEmploy(Long serviceId);

    HospitalEmploy getHospitalEmployInfo(Long serviceId);

    IPage<HospitalEmploy> getHospitalEmployList(Integer page,Integer limit,HospitalEmploy hospitalEmploy);

    List<HospitalEmploy> getEmployList(Integer authentication);
}
