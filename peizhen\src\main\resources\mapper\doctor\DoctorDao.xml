<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.doctor.dao.DoctorDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sqx.modules.doctor.entity.Doctor">
        <id column="doctor_id" property="doctorId" />
        <result column="hospital_id" property="hospitalId" />
        <result column="department_id" property="departmentId" />
        <result column="doctor_name" property="doctorName" />
        <result column="doctor_code" property="doctorCode" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="gender" property="gender" />
        <result column="birth_date" property="birthDate" />
        <result column="id_card" property="idCard" />
        <result column="avatar" property="avatar" />
        <result column="title" property="title" />
        <result column="professional_level" property="professionalLevel" />
        <result column="education" property="education" />
        <result column="specialty" property="specialty" />
        <result column="introduction" property="introduction" />
        <result column="tag" property="tag" />
        <result column="remarks" property="remarks" />
        <result column="qualification_cert" property="qualificationCert" />
        <result column="license_number" property="licenseNumber" />
        <result column="practice_scope" property="practiceScope" />
        <result column="entry_date" property="entryDate" />
        <result column="work_status" property="workStatus" />
        <result column="sort" property="sort" />
        <result column="is_enable" property="isEnable" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="hospital_name" property="hospitalName" />
        <result column="department_name" property="departmentName" />
    </resultMap>

    <!-- 分页查询医生列表（带关联查询） -->
    <select id="selectDoctorPage" resultMap="BaseResultMap">
        SELECT 
            d.*,
            h.hospital_name,
            dept.department_name
        FROM doctor d
        LEFT JOIN hospital h ON d.hospital_id = h.hospital_id
        LEFT JOIN department dept ON d.department_id = dept.department_id
        <where>
            <if test="doctorName != null and doctorName != ''">
                AND d.doctor_name LIKE CONCAT('%', #{doctorName}, '%')
            </if>
            <if test="hospitalId != null">
                AND d.hospital_id = #{hospitalId}
            </if>
            <if test="departmentId != null">
                AND d.department_id = #{departmentId}
            </if>
            <if test="workStatus != null">
                AND d.work_status = #{workStatus}
            </if>
            <if test="isEnable != null">
                AND d.is_enable = #{isEnable}
            </if>
        </where>
        ORDER BY d.sort ASC, d.create_time DESC
    </select>

</mapper>
