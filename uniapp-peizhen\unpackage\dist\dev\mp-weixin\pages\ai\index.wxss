@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ai-container.data-v-2e7a9c24 {
  min-height: 100vh;
}
.top-bar.data-v-2e7a9c24 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.page-header.data-v-2e7a9c24 {
  flex: 1;
}
.page-title.data-v-2e7a9c24 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}
.page-subtitle.data-v-2e7a9c24 {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.top-actions.data-v-2e7a9c24 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.action-btn.data-v-2e7a9c24 {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.model-btn.data-v-2e7a9c24 {
  background: #f5f5f5;
  border: 1rpx solid #e0e0e0;
}
.new-chat-btn.data-v-2e7a9c24 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.conversation-list.data-v-2e7a9c24 {
  padding: 20rpx;
}
.empty-state.data-v-2e7a9c24 {
  text-align: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  margin: 20rpx;
  border-radius: 24rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.empty-icon.data-v-2e7a9c24 {
  margin-bottom: 32rpx;
}
.empty-title.data-v-2e7a9c24 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}
.empty-subtitle.data-v-2e7a9c24 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 48rpx;
}
.empty-action.data-v-2e7a9c24 {
  display: flex;
  justify-content: center;
}
.start-chat-btn.data-v-2e7a9c24 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}
.btn-text.data-v-2e7a9c24 {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}
.conversation-item.data-v-2e7a9c24 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  margin-bottom: 16rpx;
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}
.conversation-item.data-v-2e7a9c24:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
}
.conversation-avatar.data-v-2e7a9c24 {
  margin-right: 20rpx;
}
.avatar-icon.data-v-2e7a9c24 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.conversation-content.data-v-2e7a9c24 {
  flex: 1;
  min-width: 0;
}
.conversation-header.data-v-2e7a9c24 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}
.conversation-title.data-v-2e7a9c24 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
}
.conversation-meta.data-v-2e7a9c24 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}
.conversation-model.data-v-2e7a9c24 {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.2);
  white-space: nowrap;
}
.update-time.data-v-2e7a9c24 {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
}
.conversation-preview.data-v-2e7a9c24 {
  margin-top: 8rpx;
}
.last-message.data-v-2e7a9c24 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.conversation-actions.data-v-2e7a9c24 {
  padding: 16rpx;
  margin-left: 8rpx;
}
.edit-title-content.data-v-2e7a9c24 {
  padding: 40rpx 0;
}
.title-input.data-v-2e7a9c24 {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  outline: none;
  background: #fafafa;
  transition: all 0.3s ease;
}
.title-input.data-v-2e7a9c24:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

