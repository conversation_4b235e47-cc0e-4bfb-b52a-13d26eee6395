package com.sqx.modules.app.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.annotation.LoginUser;
import com.sqx.modules.app.entity.Address;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.AddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 地址
 */
@RestController
@RequestMapping("/address")
@Api(value = "用户地址", tags = {"用户地址"})
public class AddressController {

    @Autowired
    private AddressService addressService;

    @GetMapping("/selectAddressListById")
    @ApiOperation("获取我的所有地址")
    public Result selectAddressListById(Long userId,Integer page,Integer limit){
        IPage<Address> addressIPage = addressService.page(new Page<>(page, limit), new QueryWrapper<Address>().eq("user_id", userId));
        return Result.success().put("data",new PageUtils(addressIPage));
    }




}
