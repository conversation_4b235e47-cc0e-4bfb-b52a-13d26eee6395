package com.sqx.modules.riderLocation.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.riderLocation.service.RiderLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags={"定时任务-定时任务"})
@RestController
@RequestMapping("/app/timedtask")
public class AppRiderLocationController {

    @Autowired
    private RiderLocationService riderLocationService;

    @ApiOperation("骑手定时更新经纬度")
    @GetMapping(value = "riderLocation")
    public Result riderLocation(Long userId, Double lng, Double lat, String province, String city, String district,String addressDetails){

        return riderLocationService.riderLocation(userId, lng, lat, province, city, district,addressDetails);
    }


}
