{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/team.vue?62b1", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/team.vue?5d14", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/team.vue?bb60", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/team.vue?5629", "uni-app:///my/other/team.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/team.vue?aa29", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/team.vue?7240"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "applyName", "applyPhone", "applyContent", "applyId", "imgBg", "onLoad", "onShow", "methods", "getBgImg", "getcity", "uni", "success", "console", "that", "Changekefu", "extInfo", "url", "corpId", "getDetail", "classify", "submit", "title", "icon", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2CvvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC,2BAEA;EACAC;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAC;QACAC;UACAC;UACAA;UACAA;UACAA;UACAC;QACA;MACA;IACA;IACAC;MACA;MAEApB;QACAqB;UACAC;QACA;QACAC;QACAN;MACA;IAUA;IACAO;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACAV;UACAW;UACAC;QACA;QACA;MACA;MACA;QACAZ;UACAW;UACAC;QACA;QACA;MACA;MACA;QACAZ;UACAW;UACAC;QACA;QACA;MACA;MACA;QACAtB;QACAC;QACAC;QACAiB;MACA;MAEA;QACA;UACAT;YACAW;YACAC;UACA;UACAC;YACAb;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAAs1C,CAAgB,4rCAAG,EAAC,C;;;;;;;;;;;ACA12C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/other/team.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/other/team.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./team.vue?vue&type=template&id=534c5821&\"\nvar renderjs\nimport script from \"./team.vue?vue&type=script&lang=js&\"\nexport * from \"./team.vue?vue&type=script&lang=js&\"\nimport style0 from \"./team.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/other/team.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=template&id=534c5821&\"", "var components\ntry {\n  components = {\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<image class=\"bgimg\" :src=\"imgBg\" mode=\"widthFix\"></image>\r\n\r\n\t\t<view class=\"info\">\r\n\t\t\t<view class=\"info-box\">\r\n\t\t\t\t<view class=\"info-box-title\">\r\n\t\t\t\t\t推广申请信息\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"\" v-if=\"applyId\" style=\"text-align: center;color: red;\">\r\n\t\t\t\t\t信息已提交，请等待后台审核或联系客服\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-box-item\">\r\n\t\t\t\t\t<view class=\"info-box-item-name\">联系人姓名</view>\r\n\t\t\t\t\t<u-input placeholder=\"请输入联系人姓名\" v-model=\"applyName\" :clearable=\"false\"></u-input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<view class=\"info-box-item\">\r\n\t\t\t\t\t<view class=\"info-box-item-name\">联系人手机号</view>\r\n\t\t\t\t\t<u-input placeholder=\"请输入联系人手机号\" v-model=\"applyPhone\" :clearable=\"false\"></u-input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<view class=\"info-box-item\" @click=\"getcity()\">\r\n\t\t\t\t\t<view class=\"info-box-item-name\">地址</view>\r\n\t\t\t\t\t<u-input placeholder=\"请选择所在区域地址\" v-model=\" applyContent\" @click=\"getcity()\" :disabled=\"true\"\r\n\t\t\t\t\t\t:clearable=\"false\">\r\n\t\t\t\t\t</u-input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sub-box-l\" @click=\"submit()\" v-if=\"applyId!=1\">确认提交</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <view class=\"sub\">\r\n\t\t\t<view class=\"sub-box\">\r\n\t\t\t\t<view class=\"sub-box-l\" @click=\"submit()\">确认提交</view>\r\n\t\t\t\t<view class=\"sub-box-r\" @click=\"Changekefu()\">直接联系 </view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapplyName: '',\r\n\t\t\t\tapplyPhone: '',\r\n\t\t\t\tapplyContent: '',\r\n\t\t\t\tapplyId: '',\r\n\t\t\t\timgBg: '',\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getDetail()\r\n\t\t\tthis.getBgImg()\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//获取背景图\r\n\t\t\tgetBgImg() {\r\n\t\t\t\tthis.$Request.get('/app/common/type/335').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.imgBg = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetcity() {\r\n\t\t\t\t// console.log(55555)\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconsole.log('位置名称：' + res.name);\r\n\t\t\t\t\t\tconsole.log('详细地址：' + res.address);\r\n\t\t\t\t\t\tconsole.log('纬度：' + res.latitude);\r\n\t\t\t\t\t\tconsole.log('经度：' + res.longitude);\r\n\t\t\t\t\t\tthat.applyContent = res.address + res.name\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tChangekefu() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\twx.openCustomerServiceChat({\r\n\t\t\t\t\textInfo: {\r\n\t\t\t\t\t\turl: that.$queue.getData('kefu')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcorpId: that.$queue.getData('kefuAppId'),\r\n\t\t\t\t\tsuccess(res) {}\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\twindow.location.href = that.$queue.getData('kefu');\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP\r\n\t\t\t\tlet kefu = that.$queue.getData('kefu')\r\n\t\t\t\tconsole.log(kefu)\r\n\t\t\t\tplus.runtime.openURL(kefu, function(res) {});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetDetail() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tclassify: 1\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/apply/selectApplyByUserIdAndClassify\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0 && res.data) {\r\n\t\t\t\t\t\tthis.applyName = res.data.applyName\r\n\t\t\t\t\t\tthis.applyPhone = res.data.applyPhone\r\n\t\t\t\t\t\tthis.applyContent = res.data.applyContent\r\n\t\t\t\t\t\tthis.applyId = res.data.status\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsubmit() {\r\n\t\t\t\tif (!this.applyName) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入联系人姓名',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.applyPhone) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入联系人手机号码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.applyContent) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入地址',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tapplyName: this.applyName,\r\n\t\t\t\t\tapplyPhone: this.applyPhone,\r\n\t\t\t\t\tapplyContent: this.applyContent,\r\n\t\t\t\t\tclassify: 1\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.$Request.postJson(\"/app/apply/insertApply\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '提交成功！',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\">\r\n\tpage {\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\r\n\t.bgimg {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.info {\r\n\t\twidth: 680upx;\r\n\t\theight: auto;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tposition: absolute;\r\n\t\ttop: 700rpx;\r\n\t\t// background-image: url('../static/zsbg.png');\r\n\t\tbackground: linear-gradient(0deg, #3A88FF 0%, #B7E1FF 100%);\r\n\t\tborder-radius: 32upx;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tmargin: 30upx;\r\n\t\tpadding: 30upx;\r\n\r\n\t\t.info-box {\r\n\t\t\twidth: 620rpx;\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tborder-radius: 32upx;\r\n\t\t\tpadding: 30upx;\r\n\r\n\t\t\t.info-box-title {\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t.info-box-item {\r\n\t\t\t\tmargin-top: 30rpx;\r\n\r\n\t\t\t\t.info-box-item-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.line {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tborder: 1rpx solid #EAEAEA;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.sub-box-l {\r\n\t\t// width: 290rpx;\r\n\t\theight: 88rpx;\r\n\t\tbackground: #435DF0;\r\n\t\tborder-radius: 44rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 60upx;\r\n\t}\r\n\r\n\t.sub {\r\n\t\twidth: 100%;\r\n\t\theight: 88rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tposition: absolute;\r\n\t\ttop: 1430rpx;\r\n\r\n\t\t.sub-box {\r\n\t\t\twidth: 613rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t.sub-box-l {\r\n\t\t\t\twidth: 290rpx;\r\n\t\t\t\theight: 88rpx;\r\n\t\t\t\tbackground: #97A8F8;\r\n\t\t\t\tborder-radius: 44rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\r\n\t\t\t.sub-box-r {\r\n\t\t\t\twidth: 290rpx;\r\n\t\t\t\theight: 88rpx;\r\n\t\t\t\tbackground: #435DF0;\r\n\t\t\t\tborder-radius: 44rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./team.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621956\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}