<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.sys.dao.SysUserRoleDao">

	<delete id="deleteBatch">
		delete from sys_user_role where role_id in
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</delete>

	<select id="queryRoleIdList" resultType="long">
		select role_id from sys_user_role where user_id = #{value}
	</select>
</mapper>