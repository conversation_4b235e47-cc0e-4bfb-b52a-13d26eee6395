package com.sqx.modules.hospitalModular.controller.app;


import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.sqx.modules.hospitalModular.service.HospitalModularService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务类型管理-用户端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@RestController
@Api(value = "服务类型管理-用户端", tags = {"服务类型管理-用户端"})
@RequestMapping("/app/hospitalModular/")
public class AppHospitalModularController {
    @Autowired
    private HospitalModularService modularService;

    /**
     * 获取服务类型详情
     * @param modularId 服务id
     * @return
     */
    @ApiOperation("获取服务类型详情")
    @GetMapping("getModularInfo")
    public Result getModularInfo(Long modularId) {
        return Result.success().put("data", modularService.getModularInfo(modularId));
    }

    /**
     * 获取服务类型列表
     * @param page
     * @param limit
     * @param hospitalModular
     * @return
     */
    @ApiOperation("获取服务类型列表")
    @GetMapping("getModularList")
    public Result getModularList(Integer page, Integer limit, HospitalModular hospitalModular) {
        hospitalModular.setIsEnable(1);
        return Result.success().put("data", modularService.getModularList(page, limit, hospitalModular));
    }
}

