package com.sqx.modules.taking.controller.app;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.taking.service.OrderTakingCommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/app/takingComment")
@Api(value = "APP评论|点赞", tags = {"APP评论|点赞"})
public class AppOrderTakingCommentController {

    private OrderTakingCommentService orderTakingCommentService;

    /**
     * 查看接单下的所有评论内容  时间  评论人  评论人图像  评论点赞次数
     *
     * @param page
     * @param limit
     * @param id
     */
    @CrossOrigin
    @GetMapping("/selectOrderTakingComment")
    @ApiOperation("查看评论")
    public Result selectOrderTakingComment(Integer page, Integer limit, Long id) {
        return orderTakingCommentService.selectOrderTakingComment(page, limit, id);
    }


    @CrossOrigin
    @GetMapping("/selectOrderTakingCommentByUserId")
    @ApiOperation("查看评论")
    public Result selectOrderTakingCommentByUserId(Integer page, Integer limit, Long userId) {
        return orderTakingCommentService.selectOrderTakingCommentByUserId(page, limit, userId);
    }

    /**
     * 有赞时取消点赞  没赞时点赞
     *
     * @param commentId
     * @param userId
     * @return
     */
    @Login
    @GetMapping("/updateGoodsNum")
    @ApiOperation("点赞评论")
    public Result updateGoodsNum(Long commentId, @RequestAttribute("userId") Long userId) {
        return orderTakingCommentService.updateGoodsNum(commentId, userId);
    }

    /**
     * 添加评论
     */
    @Login
    @PostMapping("/addGoodsNum")
    @ApiOperation("添加评论")
    public Result addGoodsNum(Long id, @RequestAttribute("userId") Long userId, String content, Integer score,Long ordersId) {
        return orderTakingCommentService.addGoodsNum(id, userId, content, score,ordersId);
    }

    @Login
    @GetMapping("/selectTakingCommentCount")
    @ApiOperation("查询评论次数")
    public Result selectTakingCommentCount(Long ordersId, @RequestAttribute("userId") Long userId) {
        return Result.success().put("data",orderTakingCommentService.selectTakingCommentCount(ordersId, userId));
    }


}
