# 陪诊系统架构文档

## 项目概述

陪诊系统是一个完整的医疗陪诊服务平台，包含后端服务、管理后台、陪诊师端和用户端四个主要模块。系统采用前后端分离架构，支持多端应用。

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    陪诊系统整体架构                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 用户小程序端  │  │ 陪诊师小程序端│  │  管理后台    │         │
│  │uniapp-peizhen│  │peizhen-shifu│  │peizhen--admin│         │
│  │   (Vue.js)  │  │   (Vue.js)  │  │   (Vue.js)  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │                 │                 │               │
│         └─────────────────┼─────────────────┘               │
│                           │                                 │
│                  ┌─────────────┐                           │
│                  │   后端服务   │                           │
│                  │   peizhen   │                           │
│                  │(Spring Boot)│                           │
│                  └─────────────┘                           │
│                           │                                 │
│                  ┌─────────────┐                           │
│                  │   数据库     │                           │
│                  │   MySQL     │                           │
│                  └─────────────┘                           │
└─────────────────────────────────────────────────────────────┘
```

## 模块详细说明

### 1. 后端服务 (peizhen)

**技术栈：**
- Spring Boot 2.6.11
- MyBatis Plus 3.2.0
- MySQL 8.0.17
- Redis
- Shiro (权限管理)
- Swagger (API文档)

**主要功能模块：**

#### 核心业务模块
- **订单管理 (orders)** - 陪诊订单的创建、管理、状态跟踪
- **会员管理 (member)** - 用户信息管理、认证
- **医院管理 (hospital)** - 医院信息、科室管理
- **陪诊师管理 (hospitalEmploy)** - 陪诊师信息、认证、调度
- **支付管理 (pay)** - 支付宝、微信支付集成
- **评价管理 (evaluate)** - 服务评价系统
- **消息管理 (message)** - 系统消息、推送

#### 辅助功能模块
- **文件管理 (file/oss)** - 文件上传、云存储
- **聊天系统 (chat/chats)** - 实时通讯
- **任务系统 (task)** - 定时任务管理
- **优惠券 (tbCoupon)** - 优惠券系统
- **积分奖励 (rewardLevel)** - 积分体系
- **位置服务 (riderLocation)** - 位置跟踪
- **搜索服务 (search)** - 搜索功能
- **横幅管理 (banner)** - 广告横幅

#### 系统管理模块
- **用户系统 (sys)** - 系统用户、角色、权限
- **应用管理 (app)** - 移动端应用管理
- **数据源 (datasource)** - 多数据源配置

### 2. 管理后台 (peizhen--admin)

**技术栈：**
- Vue.js 2.5.16
- Element UI 2.8.2
- Webpack 3.6.0
- Axios 0.17.1

**主要功能：**
- 系统管理员界面
- 订单管理和监控
- 用户和陪诊师管理
- 财务管理
- 数据统计和报表
- 系统配置

### 3. 陪诊师端 (peizhen-shifu)

**技术栈：**
- uni-app (Vue.js)
- uView UI组件库

**主要功能：**
- 陪诊师注册和认证
- 接单管理
- 订单处理流程
- 收入统计
- 个人信息管理
- 实时聊天

### 4. 用户端 (uniapp-peizhen)

**技术栈：**
- uni-app (Vue.js)
- uView UI组件库

**主要功能：**
- 用户注册和登录
- 陪诊服务预约
- 陪诊师选择
- 订单跟踪
- 在线支付
- 服务评价
- 实时聊天

## 数据库设计

### 核心数据表
- **用户表** - 存储用户基本信息
- **陪诊师表** - 陪诊师信息和资质
- **订单表** - 陪诊订单详情
- **医院表** - 医院和科室信息
- **支付表** - 支付记录
- **评价表** - 服务评价
- **消息表** - 系统消息

## 第三方集成

### 支付系统
- 支付宝支付 (alipay-sdk-java 4.10.29)
- 微信支付 (wxpay-sdk 0.0.3)

### 云服务
- 阿里云OSS (文件存储)
- 阿里云短信服务
- 腾讯云短信服务

### 其他服务
- 微信公众号/小程序 (weixin-java-mp 3.6.0)
- 个推消息推送
- 百度地图API

## 部署架构

### 开发环境
- 本地开发服务器
- 本地MySQL数据库
- 本地Redis缓存

### 生产环境
- Spring Boot应用服务器
- MySQL主从数据库
- Redis集群
- Nginx反向代理
- Docker容器化部署

## 安全机制

- **认证授权：** Apache Shiro + JWT
- **数据加密：** 敏感数据加密存储
- **接口安全：** API签名验证
- **XSS防护：** 输入过滤和输出编码
- **SQL注入防护：** MyBatis参数化查询

## 监控和日志

- **应用监控：** Spring Boot Actuator
- **日志管理：** Logback日志框架
- **API文档：** Swagger自动生成
- **性能监控：** Druid数据库连接池监控

## 开发规范

### 代码结构
```
peizhen/
├── src/main/java/com/sqx/
│   ├── common/          # 公共组件
│   ├── config/          # 配置类
│   ├── modules/         # 业务模块
│   └── SqxApplication.java
├── src/main/resources/
│   ├── mapper/          # MyBatis映射文件
│   ├── application.yml  # 配置文件
│   └── static/          # 静态资源
└── pom.xml             # Maven配置
```

### 命名规范
- 包名：com.sqx.modules.{模块名}
- 类名：驼峰命名法
- 方法名：驼峰命名法
- 数据库表：下划线命名法

## 版本信息

- **系统版本：** 6.0.0
- **Java版本：** 1.8
- **Spring Boot版本：** 2.6.11
- **Vue.js版本：** 2.5.16
- **数据库版本：** MySQL 8.0.17

## 项目启动说明

### 后端启动
1. 配置数据库连接
2. 启动Redis服务
3. 运行SqxApplication.java

### 前端启动
1. 安装依赖：`npm install`
2. 开发模式：`npm run dev`
3. 生产构建：`npm run build`

---

*文档更新时间：2025-08-03*
*维护团队：开发团队*
