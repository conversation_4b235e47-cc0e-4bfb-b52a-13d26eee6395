package com.sqx.modules.task.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sqx.modules.app.entity.UserEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  help_take
 * <AUTHOR> 2021-01-07
 */
@Data
@TableName("help_take")
public class HelpTake implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接单id
     */
    @TableId
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 实际收益
     */
    private BigDecimal money;

    /**
     * 状态  1 已接单  2 已送达  3已下架
     */
    private Integer status;

    /**
     * 接单时间
     */
    private String createTime;

    /**
     * 送达时间 结束时间
     */
    private String endTime;

    @TableField(exist = false)
    private UserEntity user;


}
