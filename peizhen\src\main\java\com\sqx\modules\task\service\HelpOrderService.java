package com.sqx.modules.task.service;


import com.sqx.common.utils.Result;
import com.sqx.modules.task.entity.HelpOrder;

public interface HelpOrderService {

    Result selectHelpOrder(int page, int limit);

    Result selectNewHelpOrderList(int page,int limit,Long gameId,String latitude,String longitude,Integer sort);

    Result selectHelpOrderByClassifyList(int page,int limit,Long classifyId,Long gameId);

    Result selectHelpOrderByContentList(int page,int limit,String content,Long gameId);

    HelpOrder selectHelpOrderById(Long helpOrderId );

    Result selectCreateHelpOrder(int page,int limit,Integer status,Long userId,Long gameId);

    Result selectStatusHelpOrder(int page,int limit,String phone,String content,Integer status,Long gameId);

    Result saveBody(HelpOrder helpOrder);

    Result saveBodys(HelpOrder helpOrder);

    Result updateHelpOrderByStatus(String ids,Integer status,String content);

    Result updateHelpOrderById(HelpOrder helpOrder);

    Result updateHelpOrderByIds(HelpOrder helpOrder);

    boolean updateById(HelpOrder helpOrder);

    Result deleteById(Long id);

    Integer countHelpOrderByCreateTime( String time, Integer flag);

    Double sumPrice( String time, Integer flag);




}
