<template>
	<el-tabs v-model="activeName" @tab-click="handleClick">
		<el-tab-pane label="医院管理" name="first">
			<!-- <div style="display: inline-block;margin: 3px;">
				<span>状态：</span>
				<el-select v-model="status1" style="width:150px;margin-left: 10px;" @change="animeDat(status1)">
					<el-option v-for="item in statesnum" :key="item.value" :label="item.label" :value="item.value">
					</el-option>
				</el-select>&nbsp;&nbsp;&nbsp;&nbsp;
			</div> -->
			<!-- <div style="display: inline-block;margin: 3px;">
				<span>服务分类：</span>
				<el-select v-model="gameId" style="width:150px;margin-left: 10px;" @change="animeDat(gameId)">
					<el-option v-for="item in homeData1" :key="item.value" :label="item.gameName"
						:value="item.id">
					</el-option>
				</el-select>&nbsp;&nbsp;&nbsp;
			</div> -->
			<div style="display: inline-block;margin: 3px;">
				<div style="position: relative;display: inline-block;">
					<span>医院名称：</span>
					<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入医院名称"
						v-model="content1"></el-input>&nbsp;&nbsp;
				</div>
			</div>
			<div style="display: inline-block;">
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="select1">查询
				</el-button>
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleans">重置
				</el-button>
				<el-button style="margin:0 0 20px 20px;" :disabled="!isAuth('universalMission:add')" size="mini"
					type="primary" icon="document" @click="classifyStair(1)">添加</el-button>
			</div>
			<el-table v-loading="tableDataLoading" :data="tableData.records">
				<el-table-column fixed prop="hospitalId" label="编号"></el-table-column>
				<el-table-column fixed prop="hospitalName" label="医院名称"></el-table-column>
				<el-table-column prop="hospitalImg" label="医院图片" width="180">
					<template slot-scope="scope">
						<span v-if="scope.row.hospitalImg">
							<img v-for="(item,index) in scope.row.hospitalImg.split(',')" :key="index" :src="item"
								width="40" height="40" style="margin:3px" />
						</span>
						　
					</template>
				</el-table-column>
				<el-table-column prop="hospitalLevel" label="医院等级"></el-table-column>
				<el-table-column prop="hospitalType" label="医院类型"></el-table-column>
				<el-table-column prop="hospitalDetails" label="医院简介" width="200">
					<template slot-scope="scope">
						<el-popover placement="top-start" title="" trigger="hover">
							<div style="display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3;overflow: hidden;max-height:80px;"
								slot="reference">{{scope.row.hospitalDetails}}</div>
							<div style="width:500px;height: auto;">{{scope.row.hospitalDetails}}</div>
						</el-popover>
					</template>
				</el-table-column>
				<el-table-column prop="departmentDetails" label="重点科室" width="200">
					<template slot-scope="scope">
						<el-popover placement="top-start" title="" trigger="hover">
							<div style="display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3;overflow: hidden;max-height:80px;"
								slot="reference">{{scope.row.departmentDetails}}</div>
							<div style="width:500px;height: auto;">{{scope.row.departmentDetails}}</div>
						</el-popover>
					</template>
				</el-table-column>
				<el-table-column prop="sort" label="排序"></el-table-column>
				<el-table-column prop="province" label="省"></el-table-column>
				<el-table-column prop="city" label="市"></el-table-column>
				<el-table-column prop="district" label="区"></el-table-column>
				<el-table-column prop="addressDetails" label="详细地址" width="160"></el-table-column>
				<el-table-column prop="hospitalLng" label="经度" width="160"></el-table-column>
				<el-table-column prop="hospitalLat" label="纬度" width="160"></el-table-column>
				<!-- <el-table-column fixed="right" prop="isEnable" label="状态" width="100">
					<template slot-scope="scope">
						<span style="color: #4f9dec;" v-if="scope.row.isEnable === 1 ">开启</span>
						<span style="color: #4f9dec;" v-if="scope.row.isEnable === 0 ">关闭</span>
					</template>
				</el-table-column> -->
				<el-table-column prop="isEnable" label="状态" fixed="right">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.isEnable"
							@change="change(scope.row)"
							:disabled="!isAuth('universalMission:update')" :active-value="openValue2"
							:inactive-value="closeValue2" active-color="#13ce66" inactive-color="#ff4949">
						</el-switch>
				
					</template>
				</el-table-column>
				<el-table-column fixed="right" label="操作" width="300">
					<template slot-scope="scope">

						<el-button size="mini" type="primary" :disabled="!isAuth('universalMission:update')"
							@click="classifyStair(2, scope.row)" style="margin: 3px;">编辑
						</el-button>
						<el-button size="mini" type="danger" :disabled="!isAuth('universalMission:delete')"
							@click="deleteStair(scope.row)" style="margin: 3px;">删除
						</el-button>
						<el-button size="mini" type="primary" @click="classifyQx(scope.row)" style="margin: 3px;">科室管理
						</el-button>
						<el-button size="mini" type="success" @click="viewDoctors(scope.row)" style="margin: 3px;">医生列表
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center;margin-top: 10px;">
				<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
					:page-sizes="[10, 20, 30, 40]" :page-size="limit" :current-page="page"
					layout="total,sizes, prev, pager, next" :total="tableData.total">
				</el-pagination>
			</div>
		</el-tab-pane>

		<el-tab-pane label="医生管理" name="second">
			<!-- 医生管理内容 -->
			<div style="display: inline-block;margin: 3px;">
				<div style="position: relative;display: inline-block;">
					<span>医生姓名：</span>
					<el-input style="width: 200px;" @keydown.enter.native="searchDoctors" placeholder="请输入医生姓名"
						v-model="doctorSearchForm.doctorName"></el-input>&nbsp;&nbsp;
				</div>
			</div>
			<div style="display: inline-block;margin: 3px;">
				<span>所属医院：</span>
				<el-select v-model="doctorSearchForm.hospitalId" placeholder="选择医院" clearable @change="onDoctorHospitalChange" style="width: 200px;">
					<el-option
						v-for="item in hospitalOptions"
						:key="item.hospitalId"
						:label="item.hospitalName"
						:value="item.hospitalId">
					</el-option>
				</el-select>&nbsp;&nbsp;
			</div>
			<div style="display: inline-block;margin: 3px;">
				<span>所属科室：</span>
				<el-select v-model="doctorSearchForm.departmentId" placeholder="选择科室" clearable style="width: 200px;">
					<el-option
						v-for="item in departmentOptions"
						:key="item.departmentId"
						:label="item.departmentName"
						:value="item.departmentId">
					</el-option>
				</el-select>&nbsp;&nbsp;
			</div>
			<div style="display: inline-block;">
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="searchDoctors">查询
				</el-button>
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="resetDoctorSearch">重置
				</el-button>
				<el-button style="margin:0 0 20px 20px;" size="mini" type="primary" icon="document" @click="addDoctor">添加医生</el-button>
			</div>
			<el-table v-loading="doctorTableLoading" :data="doctorTableData.list">
				<el-table-column prop="doctorId" label="编号" width="80"></el-table-column>
				<el-table-column prop="avatar" label="头像" width="80">
					<template slot-scope="scope">
						<img v-if="scope.row.avatar" :src="scope.row.avatar" width="50" height="50" style="border-radius: 50%"/>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column prop="doctorName" label="医生姓名"></el-table-column>
				<el-table-column prop="doctorCode" label="工号"></el-table-column>
				<el-table-column prop="hospitalName" label="医院"></el-table-column>
				<el-table-column prop="departmentName" label="科室"></el-table-column>
				<el-table-column prop="title" label="职称"></el-table-column>
				<el-table-column prop="phone" label="联系电话"></el-table-column>
				<el-table-column prop="gender" label="性别">
					<template slot-scope="scope">
						<span v-if="scope.row.gender === 1">男</span>
						<span v-else-if="scope.row.gender === 2">女</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column prop="workStatus" label="工作状态">
					<template slot-scope="scope">
						<el-tag v-if="scope.row.workStatus === 1" type="success">在职</el-tag>
						<el-tag v-else-if="scope.row.workStatus === 2" type="danger">离职</el-tag>
						<el-tag v-else-if="scope.row.workStatus === 3" type="warning">休假</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="isEnable" label="状态">
					<template slot-scope="scope">
						<el-switch
							v-model="scope.row.isEnable"
							:active-value="1"
							:inactive-value="0"
							@change="updateDoctorStatus(scope.row)">
						</el-switch>
					</template>
				</el-table-column>
				<el-table-column fixed="right" label="操作" width="150">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" @click="editDoctor(scope.row)" style="margin: 3px;">编辑</el-button>
						<el-button size="mini" type="danger" @click="deleteDoctor(scope.row)" style="margin: 3px;">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center;margin-top: 10px;">
				<el-pagination @size-change="handleDoctorSizeChange" @current-change="handleDoctorCurrentChange"
					:page-sizes="[10, 20, 30, 40]" :page-size="doctorLimit" :current-page="doctorPage"
					layout="total,sizes, prev, pager, next" :total="doctorTableData.totalCount">
				</el-pagination>
			</div>
		</el-tab-pane>

		<!-- 添加、修改医生弹窗 -->
		<el-dialog :title="doctorDialogTitle" :visible.sync="doctorDialogVisible" width="800px" :close-on-click-modal="false">
			<el-form :model="doctorForm" :rules="doctorRules" ref="doctorForm" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="医生姓名" prop="doctorName">
							<el-input v-model="doctorForm.doctorName" placeholder="医生姓名"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="医生工号" prop="doctorCode">
							<el-input v-model="doctorForm.doctorCode" placeholder="医生工号"></el-input>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="所属医院" prop="hospitalId">
							<el-select v-model="doctorForm.hospitalId" placeholder="请选择医院" style="width: 100%" @change="onDoctorFormHospitalChange">
								<el-option
									v-for="item in hospitalOptions"
									:key="item.hospitalId"
									:label="item.hospitalName"
									:value="item.hospitalId">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="所属科室" prop="departmentId">
							<el-select v-model="doctorForm.departmentId" placeholder="请选择科室" style="width: 100%">
								<el-option
									v-for="item in doctorFormDepartmentOptions"
									:key="item.departmentId"
									:label="item.departmentName"
									:value="item.departmentId">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="联系电话" prop="phone">
							<el-input v-model="doctorForm.phone" placeholder="联系电话"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="邮箱" prop="email">
							<el-input v-model="doctorForm.email" placeholder="邮箱"></el-input>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="性别" prop="gender">
							<el-radio-group v-model="doctorForm.gender">
								<el-radio v-for="item in genderList" :key="item.code" :label="parseInt(item.code)">{{item.value}}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="出生日期" prop="birthDate">
							<el-date-picker
								v-model="doctorForm.birthDate"
								type="date"
								placeholder="选择日期"
								style="width: 100%"
								value-format="yyyy-MM-dd">
							</el-date-picker>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="身份证号" prop="idCard">
							<el-input v-model="doctorForm.idCard" placeholder="身份证号"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="职称" prop="title">
							<el-select v-model="doctorForm.title" placeholder="请选择职称" style="width: 100%">
								<el-option v-for="item in titleList" :key="item.code" :label="item.value" :value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="学历" prop="education">
							<el-select v-model="doctorForm.education" placeholder="请选择学历" style="width: 100%">
								<el-option v-for="item in educationList" :key="item.code" :label="item.value" :value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="工作状态" prop="workStatus">
							<el-select v-model="doctorForm.workStatus" placeholder="请选择工作状态" style="width: 100%">
								<el-option v-for="item in workStatusList" :key="item.code" :label="item.value" :value="parseInt(item.code)"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="执业证书编号" prop="licenseNumber">
							<el-input v-model="doctorForm.licenseNumber" placeholder="执业证书编号"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="排序" prop="sort">
							<el-input-number v-model="doctorForm.sort" :min="0" style="width: 100%"></el-input-number>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="专业特长" prop="specialty">
					<el-input v-model="doctorForm.specialty" type="textarea" placeholder="专业特长"></el-input>
				</el-form-item>

				<el-form-item label="执业范围" prop="practiceScope">
					<el-input v-model="doctorForm.practiceScope" type="textarea" placeholder="执业范围"></el-input>
				</el-form-item>

				<el-form-item label="标签" prop="tag">
					<el-input v-model="doctorForm.tag" placeholder="标签，多个用逗号分隔"></el-input>
				</el-form-item>

				<el-form-item label="医生简介" prop="introduction">
					<el-input v-model="doctorForm.introduction" type="textarea" :rows="3" placeholder="医生简介"></el-input>
				</el-form-item>

				<el-form-item label="备注" prop="remarks">
					<el-input v-model="doctorForm.remarks" type="textarea" :rows="2" placeholder="备注"></el-input>
				</el-form-item>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="是否启用" prop="isEnable">
							<el-radio-group v-model="doctorForm.isEnable">
								<el-radio :label="1">启用</el-radio>
								<el-radio :label="0">禁用</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<span slot="footer" class="dialog-footer">
				<el-button @click="doctorDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="saveDoctorForm">确定</el-button>
			</span>
		</el-dialog>

		<!-- 添加、修改医院 -->
		<el-dialog :title="titles" :visible.sync="dialogFormVisible" center width="70%">
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">医院名称：</span>
				<el-input style="width:50%;" v-model="hospitalName" type="text" placeholder="请输入医院名称"></el-input>
			</div>
			<div style="margin-bottom: 10px;display:flex;">
				<span style="width: 200px;display: inline-block;text-align: right;">医院LOGO：</span>
				<div class="imgs" v-for="(item,index) in icon" :key="index">
					<img width="100%" class="images" height="100%" :src="item" alt="">
					<span class="dels">
						<i class="el-icon-delete" @click="clear2(index)"></i>
					</span>
				</div>

				<div class="imgs" style="width: 50%;" v-if="icon.length==0">
					<el-upload :action="$http.adornUrl('alioss/upload')" list-type="picture-card"
							   :show-file-list="false" :on-success="handleUploadSuccess2" :on-progress="onprogress2">
						<el-progress v-if="percentage2>0 && percentage2<100" type="circle" :percentage="percentage2">
						</el-progress>
						<i v-else class="el-icon-plus"></i>
					</el-upload>
				</div>
			</div>
			<div style="margin-bottom: 10px;display:flex;">
				<span style="width: 200px;display: inline-block;text-align: right;">医院图片：</span>
				<div class="imgs" v-for="(item,index) in hospitalImg" :key="index">
					<img width="100%" class="images" height="100%" :src="item" alt="">
					<span class="dels">
						<i class="el-icon-delete" @click="clear(index)"></i>
					</span>
				</div>

				<div class="imgs" style="width: 50%;" v-if="hospitalImg.length==0">
					<el-upload :action="$http.adornUrl('alioss/upload')" list-type="picture-card"
						:show-file-list="false" :on-success="handleUploadSuccess" :on-progress="onprogress1">
						<el-progress v-if="percentage1>0 && percentage1<100" type="circle" :percentage="percentage1">
						</el-progress>
						<i v-else class="el-icon-plus"></i>
					</el-upload>
				</div>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">医院等级：</span>
				<el-select v-model="hospitalLevel" style="width:50%;">
					<el-option v-for="item in djData" :key="item.value" :label="item.value" :value="item.value">
					</el-option>
				</el-select>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">医院类型：</span>
				<el-select v-model="hospitalType" style="width:50%;">
					<el-option v-for="item in lxData" :key="item.value" :label="item.value" :value="item.value">
					</el-option>
				</el-select>&nbsp;&nbsp;&nbsp;
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">医院简介：</span>
				<el-input style="width:50%;" v-model="hospitalDetails" type="textarea" :rows="4" placeholder="请输入医院简介"></el-input>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">重点科室：</span>
				<el-input style="width:50%;" v-model="departmentDetails" type="textarea" :rows="4" placeholder="请输入重点科室"></el-input>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">排序：</span>
				<el-input style="width:50%;" v-model="sort" type="number" min="0" placeholder="请输入排序"></el-input>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">服务地区：</span>
				<el-cascader style="width:50%" size="large" :options="options" ref="cascaderAddr" v-model="storeAddress"
					:placeholder="storeAddre" @change="handleChange55" :value="storeAddre">
				</el-cascader>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">详细地址：</span>
				<el-input v-model="addressDetails" @keydown.enter.native="select" style="width:50%;"
					placeholder="请输入详细地址">
				</el-input>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">纬度：</span>
				<el-input v-model="latitude" style="width:50%;" placeholder="请输入纬度" disabled></el-input>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">经度：</span>
				<el-input v-model="longitude" style="width:50%;" placeholder="请输入经度" disabled></el-input>
			</div>
			<div>
				<div id="container1" style="width:80%;height:500px;margin-left: 10%;"></div>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取 消</el-button>
				<el-button type="primary" @click="StairNoticeTo()">确 定</el-button>
			</div>
		</el-dialog>
		<!-- 科室管理弹框 -->
		<el-dialog title="科室管理" :visible.sync="dialogFormVisible2" center width="70%">
			<div style="display: inline-block;margin: 3px;">
				<div style="position: relative;display: inline-block;">
					<span>科室名称：</span>
					<el-input style="width: 200px;" @keydown.enter.native="selectKs" placeholder="请输入科室名称"
						v-model="departmentNameT"></el-input>&nbsp;&nbsp;
				</div>
			</div>
			<div style="display: inline-block;">
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="selectKs">查询
				</el-button>
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleansKs">重置
				</el-button>
				<el-button style="margin:0 0 20px 20px;" :disabled="!isAuth('universalMission:add')" size="mini"
					type="primary" icon="document" @click="compile(1)">添加</el-button>
				<el-upload   :action="$http.adornUrl('admin/department/departmentListExcelIn?hospitalId='+hospitalId)" :headers="{'token':token}"  :on-success="handleUploadSuccessExcel" style="display: inline-block;margin:10px;" :show-file-list="false" >
					<el-button style='margin-left:15px;' size="mini" type="warning" icon="document" plain >导入Excel
					</el-button>
				</el-upload>
				<el-button style='margin-left:15px;' size="mini" type="warning" icon="document" @click="exportBtn" >导出Excel
				</el-button>
				<el-button style='margin-left:15px;' size="mini" type="info" icon="document" >
					<a :href="urlDr" style="color: #fff;text-decoration:none">下载导入模板</a>
				</el-button>
			</div>
			<el-table v-loading="tableDataLoadingK" :data="tableDataK.records">
				<el-table-column prop="departmentId" label="编号"></el-table-column>
				<el-table-column prop="departmentName" label="科室名称"></el-table-column>

				<el-table-column prop="sort" label="排序"></el-table-column>
				<!-- <el-table-column prop="isEnable" label="状态" width="100">
					<template slot-scope="scope">
						<span style="color: #4f9dec;" v-if="scope.row.isEnable === 1 ">开启</span>
						<span style="color: #4f9dec;" v-if="scope.row.isEnable === 0 ">关闭</span>
					</template>
				</el-table-column> -->
				<el-table-column label="操作" width="280">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" @click="classifyQxEr(scope.row)" style="margin: 3px;">二级科室
						</el-button>
						<el-button size="mini" type="primary" :disabled="!isAuth('universalMission:update')"
							@click="compile(2, scope.row)" style="margin: 3px;">编辑
						</el-button>
						<el-button size="mini" type="danger" :disabled="!isAuth('universalMission:delete')"
							@click="deleteStairKs(scope.row,1)" style="margin: 3px;">删除
						</el-button>
						<el-button size="mini" type="success" @click="viewDoctorsByDepartment(scope.row)" style="margin: 3px;">医生列表
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center;margin-top: 10px;">
				<el-pagination @size-change="handleSizeChangeK1" @current-change="handleCurrentChangeK1"
					:page-sizes="[10, 20, 30, 40]" :page-size="limitK1" :current-page="pageK1"
					layout="total,sizes, prev, pager, next" :total="tableDataK.total">
				</el-pagination>
			</div>
		</el-dialog>
		<!-- 二级科室 -->
		<el-dialog title="二级科室" :visible.sync="dialogFormVisibleEr" center width="60%">
			<div style="text-align: right;margin-right:10px">
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="selectKsEr">刷新
				</el-button>
			</div>
			<el-table v-loading="tableDataLoadingK2" :data="tableDataK2.records">
				<el-table-column prop="departmentId" label="编号"></el-table-column>
				<el-table-column prop="departmentName" label="科室名称"></el-table-column>
		
				<el-table-column prop="sort" label="排序"></el-table-column>
				
				<!-- <el-table-column prop="isEnable" label="状态" width="100">
					<template slot-scope="scope">
						<span style="color: #4f9dec;" v-if="scope.row.isEnable === 1 ">开启</span>
						<span style="color: #4f9dec;" v-if="scope.row.isEnable === 0 ">关闭</span>
					</template>
				</el-table-column> -->
				<el-table-column label="操作" width="180">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" :disabled="!isAuth('universalMission:update')"
							@click="compile(2, scope.row)" style="margin: 3px;">编辑
						</el-button>
						<el-button size="mini" type="danger" :disabled="!isAuth('universalMission:delete')"
							@click="deleteStairKs(scope.row,2)" style="margin: 3px;">删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center;margin-top: 10px;">
				<el-pagination @size-change="handleSizeChangeK2" @current-change="handleCurrentChangeK2"
					:page-sizes="[10, 20, 30, 40]" :page-size="limitK2" :current-page="pageK2"
					layout="total,sizes, prev, pager, next" :total="tableDataK2.total">
				</el-pagination>
			</div>
		</el-dialog>
		<!-- 添加 -->
		<el-dialog :title="titles" :visible.sync="dialogFormVisible3" center>
			<div style = "margin-bottom: 10px;" v-if="titles=='添加科室'">
				<span style = "width: 200px;display: inline-block;text-align: right;">选择分类：</span>
				<el-select v-model = "parentIdnum" placeholder = "请选择分类" style = "width:50%;">
				  <el-option v-for = "item in classnum" :key = "item.value" :label = "item.label" :value = "item.value">
				  </el-option>
				</el-select>
			</div>
			<div style="margin-bottom: 10px;" v-if = "titles=='添加科室'&&parentIdnum==1">
				<span style="width: 200px;display: inline-block;text-align: right;">上级科室：</span>
				<el-select v-model = "parentIds" placeholder = "请选择上级科室" style = "width:50%;" @change = "handleChange(parentIds)">
				  <el-option v-for = "item in tableDataKList" :key = "item.departmentId" :label = "item.departmentName" :value = "item.departmentId">
				  </el-option>
				</el-select>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">科室名称：</span>
				<el-input style="width:50%;" v-model="departmentName" type="text" placeholder="请输入科室名称"></el-input>
			</div>
			<div style="margin-bottom: 10px;">
				<span style="width: 200px;display: inline-block;text-align: right;">排序：</span>
				<el-input style="width:50%;" v-model="sort" type="number" min="0" placeholder="请输入排序"></el-input>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogFormVisible3 = false">取 消</el-button>
				<el-button type="primary" @click="CompileNoticeTo()">确 定</el-button>
			</div>
		</el-dialog>


	</el-tabs>
</template>

<script>
	import {
		quillEditor
	} from 'vue-quill-editor'
	import 'quill/dist/quill.core.css'
	import 'quill/dist/quill.snow.css'
	import 'quill/dist/quill.bubble.css'
	import quillConfig from '../locality/quill-config.js'
	import axios from 'axios';
	import {
		jsonp
	} from 'vue-jsonp'
	import {
		provinceAndCityData,
		regionData,
		provinceAndCityDataPlus,
		regionDataPlus,
		CodeToText,
		TextToCode
	} from 'element-china-area-data'
	// import {
	// 	registry
	// } from 'gulp'
	var cityOptions = []
	var geocoder, map, markersArray = [];
	export default {
		components: {
			quillEditor
		},
		data() {
			return {
				dialogVisible: false,
				totalMoney: 0,
				limit: 10,
				page: 1,
				size1: 10,
				page1: 1,
				phone1: '',
				status1: 0,
				describes: '',
				taskNum: '',
				taskMoney: '',
				statesnum: [{
						label: '全部',
						value: 0
					},
					{
						label: '待接单',
						value: 2
					},
					{
						label: '进行中',
						value: 3
					},
					{
						label: '已完成',
						value: 4
					},
					{
						label: '已取消',
						value: 5
					},
				],

				helpMaintainId: '',
				content1: '',
				classifyIcon: '',
				classifyId: '',
				classifyUrl: '',
				classifyName: '',
				classifyDeatilsName: '',
				activeName: 'first',
				tableDataLoading4: false,
				tableDataLoading: false,
				tableDataLoadingK: false,
				tableDataLoadingK2:false,
				dialogFormVisible: false,
				dialogFormVisibleEr:false,
				dialogFormVisible2: false,
				dialogFormVisible3: false,
				tableData: {},
				tableDataK: {},
				tableDataKList:[],

				// quillOption: quillConfig,
				options: regionData,
				storeAddress: [],
				storeAddre: '请选择城市',
				// 医院········
				hospitalId: '',
				hospitalName: '', //医院名称
				hospitalImg: [], //医院图片
				icon:[],//医院logo
				hospitalLevel: '', //医院等级
				hospitalType: '', //医院类型
				hospitalDetails: '', //医院简介
				departmentDetails: '', //科室简介
				addressDetails: '', //地址详细
				latitude: '', //精度
				longitude: '', //维度
				province: '', //省
				city: '', //市
				district: '', //区
				percentage1: 0,
				percentage2: 0,
				isEnable: 1,
				departmentName:'',//科室名称
				sort:0,

				// 医生管理相关数据
				doctorSearchForm: {
					doctorName: '',
					hospitalId: '',
					departmentId: ''
				},
				doctorTableData: {
					list: [],
					totalCount: 0
				},
				doctorTableLoading: false,
				doctorPage: 1,
				doctorLimit: 10,
				hospitalOptions: [],
				departmentOptions: [],

				// 医生弹窗相关
				doctorDialogVisible: false,
				doctorDialogTitle: '添加医生',
				doctorFormDepartmentOptions: [],
				// 字典数据
				genderList: [],        // 性别字典
				titleList: [],         // 职称字典
				educationList: [],     // 学历字典
				workStatusList: [],    // 工作状态字典
				doctorForm: {
					doctorId: '',
					hospitalId: '',
					departmentId: '',
					doctorName: '',
					doctorCode: '',
					phone: '',
					email: '',
					gender: 1,
					birthDate: '',
					idCard: '',
					title: '',
					education: '',
					specialty: '',
					introduction: '',
					tag: '',
					remarks: '',
					licenseNumber: '',
					practiceScope: '',
					workStatus: 1,
					sort: 0,
					isEnable: 1
				},
				doctorRules: {
					doctorName: [
						{ required: true, message: '医生姓名不能为空', trigger: 'blur' }
					],
					hospitalId: [
						{ required: true, message: '请选择医院', trigger: 'change' }
					],
					departmentId: [
						{ required: true, message: '请选择科室', trigger: 'change' }
					],
					phone: [
						{ pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
					],
					email: [
						{ type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
					],
					idCard: [
						{ pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '身份证号格式不正确', trigger: 'blur' }
					]
				},

				phone: '',
				titles: '添加',
				homeData1: [],
				info: {
					stockDate: this.getNowTime(), //日期
				},
				info2: {
					stockDate2: this.getNowTime2(), //日期
				},
				startTime: '',
				endTime: '',
				djData: [],
				lxData: [],
				departmentNameT: '',
				parentId:0,
				parentIds:'',
				tableDataK2:{},
				parentIdnum: '',
				classnum: [
				   {
				     value: 0,
				     label: '一级分类'
				   },
				   {
				     value: 1,
				     label: '二级分类'
				   }
				 ],
				 pageK1:1,
				 limitK1:10,
				 pageK2:1,
				 limitK2:10,
				 departmentId:0,
				 parentIdT:0,
				 token:'',
				 urlDr:'',
				 openValue2: 1,
				 closeValue2: 0,
				 
			}
		},
		methods: {
			//处理默认选中当前日期
			getNowTime() {
				var now = new Date()
				var year = now.getFullYear() //得到年份
				var month = now.getMonth() //得到月份
				var date = now.getDate() //得到日期
				month = month + 1
				month = month.toString().padStart(2, '0')
				date = date.toString().padStart(2, '0')
				var defaultDate = `${year}-${month}-${date}`
				return defaultDate
				this.$set(this.info, 'stockDate', defaultDate)
			},
			//处理默认选中当前日期
			getNowTime2() {
				var now = new Date()
				var year = now.getFullYear() //得到年份
				var month = now.getMonth() - now.getMonth() //得到月份
				var date = now.getDate() - now.getDate() + 1 //得到日期
				month = month + 1
				month = month.toString().padStart(2, '0')
				date = date.toString().padStart(2, '0')
				var defaultDate = `${year}-${month}-${date}`
				return defaultDate
				this.$set(this.info, 'stockDate', defaultDate)
			},
			// 详情跳转
			updates(userId) {
				this.$router.push({
					path: '/userDetail',
					query: {
						userId: userId
					}
				})
			},
			// 图标上传一级分类
			handleAvatarSuccess(file) {
				this.classifyIcon = file.data
			},
			// 图标上传一级分类编辑
			handleAvatarSuccess2(file) {
				this.form.classifyIcon = file.data
			},
			// 删除
			clear(index) {
				this.hospitalImg.splice(index, 1);
			},
			// 删除
			clear2(index) {
				this.icon.splice(index, 1);
			},
			//上传成功
			handleUploadSuccess2(file, fileList) {
				this.icon.push(file.data)
			},
			onprogress2(event, file, fileList) {
				this.percentage2 = parseInt(event.percent)
			},
			//上传成功
			handleUploadSuccess(file, fileList) {
				this.hospitalImg.push(file.data)
			},
			
			onprogress1(event, file, fileList) {
				this.percentage1 = parseInt(event.percent)
			},
			handleSizeChange(val) {
				this.limit = val
				this.dataSelect()
			},
			handleCurrentChange(val) {
				this.page = val
				this.dataSelect()
			},
			handleSizeChangeK1(val) {
				this.limitK1 = val
				this.dataSelectKs()
			},
			handleCurrentChangeK1(val) {
				this.pageK1 = val
				this.dataSelectKs()
			},
			handleSizeChangeK2(val) {
				this.limitK2 = val
				this.dataSelectKsEr()
			},
			handleCurrentChangeK2(val) {
				this.pageK2 = val
				this.dataSelectKsEr()
			},
			handleSizeChange2(val) {
				this.size1 = val
				this.tableDataLoading4 = true
				this.userClass()
			},
			handleCurrentChange2(val) {
				this.page1 = val
				this.tableDataLoading4 = true
				this.userClass()
			},
			// tabs切换
			handleClick(tab, event) {
				this.phone1 = ''
				this.gameId = ''
				this.status1 = 0
				if (tab._props.label == '医院管理') {
					this.page = 1
					this.limit = 10
					this.dataSelect()
				}
				if (tab._props.label == '医生管理') {
					this.doctorPage = 1
					this.doctorLimit = 10
					this.loadHospitalOptions()
					this.searchDoctors()
				}
				if (tab._props.label == '接单管理') {
					this.page = 1
					this.limit = 10
					this.taskdataSelect()
				}
			},
			// 下架
			soldClick(row) {
				this.$confirm(`确定要下架此服务?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$http({
						url: this.$http.adornUrl(`help/outHelpOrder/?helpOrderId=${row.id}`),
						method: 'post',
						data: this.$http.adornData({})
					}).then(({
						data
					}) => {
						if (data.code == 0) {
							this.$message({
								message: '操作成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.dataSelect()
								}
							})
						} else {
							this.$message({
								message: data.msg,
								type: 'error',
								duration: 1500,
								onClose: () => {
									this.dataSelect()
								}
							})
						}

					})
				}).catch(() => {})
			},
			// 添加弹框
			classifyStair(index, row) {
				
				if (index == 1) {
					this.titles = '添加医院'
					this.hospitalName = ''
					this.departmentDetails = ''
					this.hospitalDetails = ''
					this.hospitalImg = []
					this.icon = []
					this.hospitalLevel = ''
					this.hospitalType = ''
					this.addressDetails = ''
					this.latitude = ''
					this.longitude = ''
					this.province = ''
					this.city = ''
					this.district = ''
					this.hospitalId = ''
					this.isEnable = 1
					this.sort = 1
					this.storeAddress = []
					this.storeAddre = '请选择城市'

				} else {
					this.titles = '修改医院'
					this.hospitalId = row.hospitalId
					this.hospitalName = row.hospitalName
					this.departmentDetails = row.departmentDetails
					this.hospitalDetails = row.hospitalDetails
					if(row.hospitalImg){
						this.hospitalImg = row.hospitalImg.split(',')
					}else{
						this.hospitalImg = []
					}
					if(row.icon){
						this.icon = row.icon.split(',')
					}else{
						this.icon = []
					}
					if(row.sort){
						this.sort = row.sort
					}else{
						this.sort = 1
					}
					
					this.hospitalLevel = row.hospitalLevel
					this.hospitalType = row.hospitalType
					this.addressDetails = row.addressDetails
					this.latitude = row.hospitalLat
					this.longitude = row.hospitalLng
					this.province = row.province
					this.city = row.city
					this.district = row.district
					if (row.isEnable) {
						this.isEnable = row.isEnable
					}
					this.storeAddress = []
					this.storeAddre = this.province + '/' + this.city + '/' + this.district
				}
				this.getMyLocation()
				this.dialogFormVisible = true
			},
			// 添加确定
			StairNoticeTo() {
				if (this.hospitalName == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入医院名称',
						type: 'warning'
					})
					return
				}
				if (this.icon.length == 0) {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请上传医院LOGO',
						type: 'warning'
					})
					return
				}
				if (this.hospitalImg.length == 0) {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请上传医院图片',
						type: 'warning'
					})
					return
				}
				if (this.hospitalLevel == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入医院等级',
						type: 'warning'
					})
					return
				}
				if (this.hospitalType == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入医院类型',
						type: 'warning'
					})
					return
				}
				if (this.hospitalDetails == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入医院简介',
						type: 'warning'
					})
					return
				}
				if (this.departmentDetails == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入重点科室',
						type: 'warning'
					})
					return
				}

				if (this.province == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请选择省市区',
						type: 'warning'
					})
					return
				}
				if (this.addressDetails == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入详细地址',
						type: 'warning'
					})
					return
				}
				if (this.latitude == '' ||this.longitude=='') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请经纬度不能为空,请在地图上定位或者输入详细地址后按“回车键”进行获取',
						type: 'warning'
					})
					return
				}
				if (this.titles == '添加医院') {
					var urls = 'admin/hospital/addHospital'
				} else {
					var urls = 'admin/hospital/updateHospital'
				}
				this.$http({
					url: this.$http.adornUrl(urls),
					method: 'post',
					// params: this.$http.adornParams({
					data: this.$http.adornData({
						'hospitalType': this.hospitalType,
						'hospitalName': this.hospitalName,
						'hospitalImg': this.hospitalImg.toString(),
						'icon': this.icon.toString(),
						'hospitalLevel': this.hospitalLevel,
						'addressDetails': this.addressDetails,
						'hospitalLat': this.latitude,
						'hospitalLng': this.longitude,
						'province': this.province,
						'city': this.city,
						'district': this.district,
						'hospitalDetails': this.hospitalDetails,
						'departmentDetails': this.departmentDetails,
						'hospitalId': this.hospitalId,
						'isEnable': this.isEnable,
						'sort':this.sort
					},false,'from')
				}).then(({
					data
				}) => {
					if (data.code == 0) {
						this.dialogFormVisible = false
						this.$message({
							message: '操作成功',
							type: 'success',
							duration: 1500,
							onClose: () => {
								this.dataSelect()
							}
						})

					} else {
						this.$message({
							message: data.msg,
							type: 'warning',
							duration: 1500,
							onClose: () => {

							}
						})
					}

				})
			},
			// 添加、修改科室
			compile(index, rows) {
				this.dataSelectKsL()
				this.dialogFormVisible3 = true
				if(index==1){
					this.titles = '添加科室'
					this.parentId =0
					this.departmentName = ''
					this.sort = 0
					this.isEnable = 1
					this.parentId = 0
					this.parentIds = ''
					this.parentIdnum = ''
					this.departmentId = ''
				}else{
					this.titles = '修改科室'
					this.departmentId = rows.departmentId
					this.departmentName = rows.departmentName
					this.sort = rows.sort
					this.isEnable = rows.isEnable
					this.parentId = rows.parentId
					this.parentIds = rows.parentId
				}
				

			},
			// 修改一级分类确定
			CompileNoticeTo() {
				if (this.departmentName == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入科室名称',
						type: 'warning'
					})
					return
				}

				if (this.titles == '添加科室') {
					var urls = 'admin/department/addDepartment'
				} else {
					var urls = 'admin/department/updateDepartment'
				}
				this.$http({
					url: this.$http.adornUrl(urls),
					method: 'post',
					params: this.$http.adornParams({
						'hospitalId': this.hospitalId,
						'departmentName': this.departmentName,
						'sort': this.sort,
						'isEnable': this.isEnable,
						'departmentId': this.departmentId,
						'parentId':this.parentId
					})
				}).then(({
					data
				}) => {
					if(data.code==0){
						this.dialogFormVisible3 = false
						this.$message({
							message: '操作成功',
							type: 'success',
							duration: 1500,
							onClose: () => {
								if(this.parentId==0){
									this.dataSelectKs()
								}else{
									this.dataSelectKsEr()
								}
								
							}
						})
					}else{
						this.$message({
							message: data.msg,
							type: 'warning',
							duration: 1500,
							onClose: () => {}
						})
					}

				})
			},
			//删除一级
			deleteStair(row) {
				let delid = row.hospitalId
				this.$confirm(`确定删除此条信息?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$http({
						url: this.$http.adornUrl('admin/hospital/deleteHospital'),
						method: 'get',
						params: this.$http.adornParams({
							'hospitalId': delid
						})
					}).then(({
						data
					}) => {
						if(data.code==0){
							this.$message({
								message: '删除成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.dataSelect()
								}
							})
						}else{
							this.$message({
								message: data.msg,
								type: 'warning',
								duration: 1500,
								onClose: () => {}
							})
						}
						
					})
				}).catch(() => {})
			},
			// 删除科室
			deleteStairKs(row,index) {
				let delid = row.departmentId
				this.$confirm(`确定删除此条信息?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$http({
						url: this.$http.adornUrl('admin/department/deleteDepartment'),
						method: 'get',
						params: this.$http.adornParams({
							'departmentId': delid
						})
					}).then(({
						data
					}) => {
						if(data.code==0){
							this.$message({
								message: '删除成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									if(index==1){
										this.dataSelectKs()
									}else{
										this.dataSelectKsEr()
									}
									
									
								}
							})
						}else{
							this.$message({
								message: data.msg,
								type: 'warning',
								duration: 1500,
								onClose: () => {}
							})
						}
						
					})
				}).catch(() => {})
			},
			// 科室列表弹框
			classifyQx(row) {
				this.pageK1 = 1
				this.parentId = 0
				this.hospitalId = row.hospitalId
				this.dataSelectKs()
				this.dialogFormVisible2 = true

			},

			// 查看医生列表（从医院列表）
			viewDoctors(row) {
				this.activeName = 'second'
				this.doctorSearchForm.hospitalId = row.hospitalId
				this.doctorSearchForm.departmentId = ''
				this.doctorPage = 1
				this.loadHospitalOptions()
				this.loadDepartmentOptions(row.hospitalId)
				this.searchDoctors()
			},

			// 查看医生列表（从科室列表）
			viewDoctorsByDepartment(row) {
				// 关闭科室管理弹窗
				this.dialogFormVisible2 = false

				// 切换到医生管理tab
				this.activeName = 'second'

				// 设置搜索条件：医院ID和科室ID
				this.doctorSearchForm.hospitalId = this.hospitalId  // 当前科室管理弹窗对应的医院ID
				this.doctorSearchForm.departmentId = row.departmentId
				this.doctorPage = 1

				// 加载选项数据
				this.loadHospitalOptions()
				this.loadDepartmentOptions(this.hospitalId)

				// 搜索医生
				this.searchDoctors()
			},

			// 查询
			select1() {
				this.page = 1
				this.limit = 10
				this.dataSelect()
			},
			// 重置
			cleans() {
				this.page = 1
				this.phone1 = ''
				this.status1 = 0
				this.content1 = ''
				this.gameId = ''
				this.dataSelect()
			},
			// select选择事件
			animeDat(state) {
				this.page = 1
				this.dataSelect()
			},
			animeDat2(state) {
				this.page = 1
				this.taskdataSelect()
			},
			// 获取医院数据列表
			dataSelect() {
				this.tableDataLoading = true
				this.$http({
					url: this.$http.adornUrl('admin/hospital/getHospitalList'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.page,
						'limit': this.limit,
						// 'phone': this.phone1,
						// 'status': this.status1,
						'hospitalName': this.content1,
						// 'gameId':this.gameId
					})
				}).then(({
					data
				}) => {
					this.tableDataLoading = false
					let returnData = data.data
					if(data.data){
						this.tableData = returnData
					}else{
						this.tableData.records = []
					}
					
				})
			},

			// 获取省市区
			handleChange55(value) {
				value = this.$refs['cascaderAddr'].currentLabels
				this.province = value[0]
				this.city = value[1]
				this.district = value[2]
				// this.form.city = value[1]
				if (this.city == '市辖区') {
					this.city = this.province
				}
				// if (this.form.city == '市辖区') {
				// 	this.region = this.province
				// }
				// var regions = this.region
				// regions = regions.slice(0, regions.length - 1)
				// this.region = regions
				// console.log('regions', regions)
				console.log(this.$refs['cascaderAddr'].currentLabels)
			},
			//定位获得当前位置信息
			getMyLocation() {
				var geolocation = new qq.maps.Geolocation("DSQBZ-5MM3P-HEODO-VG6IX-SBRJE-PSBNX", "省钱兄家政服务");
				geolocation.getIpLocation(this.showPosition, this.showErr);
				// geolocation.getLocation(this.showPosition, this.showErr);//或者用getLocation精确度比较高
			},
			showPosition(position) {
				console.log(position);
				// this.latitude = position.lat;
				// this.longitude = position.lng;
				// this.city = position.city;
				this.setMap();
			},
			showErr(e) {
				console.log("定位失败", e);
				this.getMyLocation(); //定位失败再请求定位，测试使用
			},
			//位置信息在地图上展示
			setMap() {
				//步骤：定义map变量 调用 qq.maps.Map() 构造函数   获取地图显示容器
				//设置地图中心点
				var myLatlng = new qq.maps.LatLng(this.latitude, this.longitude);
				//定义工厂模式函数
				var myOptions = {
					zoom: 13, //设置地图缩放级别
					center: myLatlng, //设置中心点样式
					mapTypeId: qq.maps.MapTypeId.ROADMAP //设置地图样式详情参见MapType
				}
				// //获取dom元素添加地图信息
				var map = new qq.maps.Map(document.getElementById("container1"), myOptions);
				//给地图添加点击事件

				//给定位的位置添加图片标注
				var marker = new qq.maps.Marker({
					position: myLatlng,
					map: map
				});
				// `````````````
				var that = this;
				if (that.longitude == '') {
					var center = new qq.maps.LatLng(34.263161, 108.948024);
				} else {
					var center = new qq.maps.LatLng(that.latitude, that.longitude);
				}

				var map = new qq.maps.Map(document.getElementById("container1"), {
					center: center,
					zoom: 13
				});
				var marker = new qq.maps.Marker({
					position: center,
					map: map
				});
				var latlngBounds = new qq.maps.LatLngBounds();
				qq.maps.event.addListener(map, "click", function(event) {
					console.log(event, qq.maps);
					that.longitude = event.latLng.getLng(); // 经度
					that.latitude = event.latLng.getLat(); // 纬度

					jsonp('https://apis.map.qq.com/ws/geocoder/v1/?location=' + event.latLng.getLat() + ',' + event
						.latLng.getLng() + '&key=DSQBZ-5MM3P-HEODO-VG6IX-SBRJE-PSBNX&get_poi=1&output=jsonp', {
							myCustomUrlParam: 'veryNice'
						}).then(response => {
						console.log('response', response, response.result.address_component.city)
						that.addressDetails = response.result.address
						that.city = response.result.address_component.city
					}).catch(error => {
						// handle error
					}).then(() => {
						// always executed
					});
					if (markersArray) {
						for (let i in markersArray) {
							markersArray[i].setMap(null);
						}
					}
					if (!marker) {
						marker = new qq.maps.Marker({
							map: map,
							position: event.latLng
						});
					} else {
						marker.setPosition(event.latLng)
					}

					// markersArray.push(marker);

				});
				geocoder = new qq.maps.Geocoder({
					complete: function(result) {
						console.log(result);
						that.longitude = result.detail.location.lng;
						that.latitude = result.detail.location.lat;
						map.setCenter(result.detail.location);
						var marker = new qq.maps.Marker({
							map: map,
							position: result.detail.location
						});
						markersArray.push(marker);
					}
				});
			},
			// 地图定位
			select() {
				console.log(this.addressDetails, this.addressDetails.replace(/^\s+|\s+$/gm, ''))

				if (this.addressDetails == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入详细地址',
						type: 'warning'
					});
					return
				} else {
					var add = this.province + this.city + this.district + this.addressDetails
					let that = this
					jsonp('https://apis.map.qq.com/ws/geocoder/v1/?address==' + add +
						'&key=DSQBZ-5MM3P-HEODO-VG6IX-SBRJE-PSBNX&get_poi=1&output=jsonp', {
							myCustomUrlParam: 'veryNice'
						}).then(response => {
						// handle success
						if (response.message == '查询无结果') {
							this.$notify({
								title: '提示',
								duration: 1800,
								message: '详细地址输入有误，请重新输入',
								type: 'warning'
							});
							return
						}
						console.log('response', response)
						that.longitude = response.result.location.lng; // 经度
						that.latitude = response.result.location.lat; // 纬度
						that.city = response.result.address_components.city;
						// 	.city + response.result.address_components.district + response.result.title
						that.setMap()
					}).catch(error => {
						// handle error
					}).then(() => {
						// always executed
					});
				}

			},

			// 获取服务分类启用列表
			homeSelect() {
				this.tableDataLoading = true
				this.$http({
					url: this.$http.adornUrl(`game/queryGame`),
					method: 'get',
					params: this.$http.adornParams({})
				}).then(({
					data
				}) => {
					this.tableDataLoading = false
					let returnData = data.data
					this.homeData1 = []
					if (data.data) {
						for (var i in data.data) {
							this.homeData1.push(data.data[i])
							data.data[i].state = Number(data.data[i].state)
						}
					}   
					this.homeData = data.data
				})
			},
			// 导出
			exportBtnOld() {
				// if (this.endTime == '') {
				// 	this.endTime = this.info.stockDate
				// }
				// if (this.startTime == '') {
				// 	this.startTime = this.info2.stockDate2
				// }
				var endTime = this.endTime
				if (this.endTime != '') {
					endTime = this.endTime + " 23:59:59"
				}
				this.$http({
					url: this.$http.adornUrl('help/helpTakeListExcel'),
					method: 'get',
					responseType: 'blob',
					params: this.$http.adornParams({
						// 'page': page,
						// 'size': this.size,
						'phone': this.phone1,
						'status': this.status1,
						'gameId': this.gameId,
						'startTime': this.startTime,
						'endTime': endTime,
					})
				}).then(({
					data
				}) => {
					let blob = new Blob([data], {
						type: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
					})
					if (window.navigator.msSaveOrOpenBlob) {
						navigator.msSaveBlob(blob)
					} else {
						let url = window.URL.createObjectURL(blob)
						let elink = document.createElement('a')
						elink.download = '万能订单列表.xlsx'
						elink.style.display = 'none'
						elink.href = url
						document.body.appendChild(elink)
						elink.click()
						document.body.removeChild(elink)
					}
				})
			},
			// 医院等级
			djSelect() {
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({
						'type': '医院等级'
					})
				}).then(({
					data
				}) => {
					let returnData = data.data
					this.djData = returnData
				})
			},
			// 医院类型
			lxSelect() {
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({
						'type': '医院类型'
					})
				}).then(({
					data
				}) => {
					let returnData = data.data
					this.lxData = returnData
				})
			},
			// 科室列表
			dataSelectKs() {
				this.tableDataLoadingK = true
				this.$http({
					url: this.$http.adornUrl('admin/department/getDepartmentPageList'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.pageK1,
						'limit': this.limitK1,
						'departmentName': this.departmentNameT,
						'hospitalId': this.hospitalId,
						'parentId':this.parentId
					})
				}).then(({
					data
				}) => {
					this.tableDataLoadingK = false
					let returnData = data.data
					if(data.data){
						this.tableDataK = returnData
					}else{
						this.tableDataK.records = []
					}
						
					
					
				})
			},
			// 查询
			selectKs() {
				this.pageK1 = 1
				this.parentId =0
				this.dataSelectKs()
			},
			// 重置
			cleansKs() {
				this.pageK1 = 1
				this.parentId =0
				this.departmentNameT = ''
				this.dataSelectKs()
			},
			// 二级科室列表弹框
			classifyQxEr(row) {
				this.pageK2 = 1
				this.departmentNameT = ''
				this.parentIdT = row.departmentId
				this.dataSelectKsEr()
				// this.tableDataK2 = row.departmentsList
				this.dialogFormVisibleEr = true
			
			},
			handleChange (parentIds) {
			    this.parentId = parentIds
			},
			// 二级科室列表
			dataSelectKsEr() {
				this.tableDataLoadingK2 = true
				this.$http({
					url: this.$http.adornUrl('admin/department/getDepartmentPageList'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.pageK2,
						'limit': this.limitK2,
						'departmentName': this.departmentNameT,
						'hospitalId': this.hospitalId,
						// 'departmentId':this.departmentId,
						'parentId':this.parentIdT
					})
				}).then(({
					data
				}) => {
					this.tableDataLoadingK2 = false
					let returnData = data.data
					if(data.data){
						this.tableDataK2 = returnData
					}else{
						this.tableDataK2.records = []
					}
						
					
				})
			},
			selectKsEr(){
				this.pageK2 = 1
				this.dataSelectKsEr()
			},
			// 科室列表
			dataSelectKsL() {
				this.$http({
					url: this.$http.adornUrl('admin/department/getDepartmentPageList'),
					method: 'get',
					params: this.$http.adornParams({
						'departmentName': '',
						'hospitalId': this.hospitalId,
						'parentId':0
					})
				}).then(({
					data
				}) => {
					let returnData = data.data
					if(data.code==0){
						this.tableDataKList = returnData.records
					}else{
						this.tableDataKList = []
					}
						
					
					
				})
			},
			// 导出
			exportBtn() {
				// if (this.endTime == '') {
				// 	this.endTime = this.info.stockDate
				// }
				// if (this.startTime == '') {
				// 	this.startTime = this.info2.stockDate2
				// }
				// var endTime = this.endTime
				// if (this.endTime != '') {
				// 	endTime = this.endTime + " 23:59:59"
				// }
				this.$http({
					url: this.$http.adornUrl('admin/department/departmentExcelOut'),
					method: 'get',
					responseType: 'blob',
					params: this.$http.adornParams({
						// 'page': page,
						// 'size': this.size,
						'departmentName': this.departmentNameT,
						'hospitalId': this.hospitalId,
						'parentId':this.parentId
					})
				}).then(({
					data
				}) => {
					let blob = new Blob([data], {
						type: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
					})
					if (window.navigator.msSaveOrOpenBlob) {
						navigator.msSaveBlob(blob)
					} else {
						let url = window.URL.createObjectURL(blob)
						let elink = document.createElement('a')
						elink.download = '科室列表.xlsx'
						elink.style.display = 'none'
						elink.href = url
						document.body.appendChild(elink)
						elink.click()
						document.body.removeChild(elink)
					}
				})
			},
			// 导入文件
			handleUploadSuccessExcel(file, fileList) {
				console.log('导入----',file,fileList)
				if(file.code==0){
					this.$message({
						message: file.msg,
						type: 'success',
						duration: 1500,
						onClose: () => {
							this.pageK1 = 1
							this.dataSelectKs()
						}
					})
				}else{
					this.$message({
						message: file.msg,
						type: 'warning',
						duration: 1500,
						onClose: () => {}
					})
				}
				// this.hospitalImg.push(file.data)
			},
			// 下载导入模板按摩
			// mubanBtn(){
			// 	this.$http({
			// 		url: this.urlDr,
			// 		method: 'get',
			// 		responseType: 'blob',
			// 		params: this.$http.adornParams({
			// 		})
			// 	}).then(({
			// 		data
			// 	}) => {
			// 	})
			// },
			// 获取导入模板链接
			xianshi () {
			  this.$http({
			    url: this.$http.adornUrl('common/type/333'),
			    method: 'get',
			    data: this.$http.adornData({})
			  }).then(({data}) => {
				if (data.code == 0) {
					this.urlDr = data.data.value
				}
			  })
			},
			// 启用与否
			change(row) {
				this.$http({
					url: this.$http.adornUrl('admin/hospital/updateHospital'),
					method: 'post',
					params: this.$http.adornParams({
						'hospitalId': row.hospitalId,
						'isEnable': row.isEnable,
						'city': row.city,
					})
				}).then(({
					data
				}) => {
					if(data.code==0){
						this.$message({
							message: '操作成功',
							type: 'success',
							duration: 1500,
							onClose: () => {
								this.dataSelect()
							}
						})
					}else {
					this.$message({
						message: data.msg,
						type: 'error',
						duration: 1500,
						onClose: () => {
						}
					})
				}
					
				})
			},

			// ========== 医生管理相关方法 ==========
			// 搜索医生
			searchDoctors() {
				this.doctorTableLoading = true
				this.$http({
					url: this.$http.adornUrl('/doctor/list'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.doctorPage,
						'limit': this.doctorLimit,
						'doctorName': this.doctorSearchForm.doctorName,
						'hospitalId': this.doctorSearchForm.hospitalId,
						'departmentId': this.doctorSearchForm.departmentId
					})
				}).then(({data}) => {
					this.doctorTableLoading = false
					if (data && data.code === 0) {
						this.doctorTableData = data.page
					} else {
						this.doctorTableData = {
							list: [],
							totalCount: 0
						}
					}
				})
			},

			// 重置医生搜索
			resetDoctorSearch() {
				this.doctorSearchForm = {
					doctorName: '',
					hospitalId: '',
					departmentId: ''
				}
				this.doctorPage = 1
				this.departmentOptions = []
				this.searchDoctors()
			},

			// 医生分页 - 每页数量变化
			handleDoctorSizeChange(val) {
				this.doctorLimit = val
				this.doctorPage = 1
				this.searchDoctors()
			},

			// 医生分页 - 当前页变化
			handleDoctorCurrentChange(val) {
				this.doctorPage = val
				this.searchDoctors()
			},

			// 加载医院选项
			loadHospitalOptions() {
				this.$http({
					url: this.$http.adornUrl('/admin/hospital/selectHospitalList'),
					method: 'get'
				}).then(({data}) => {
					if (data && data.code === 0) {
						this.hospitalOptions = data.data || []
					}
				})
			},

			// 医院改变时加载科室选项
			onDoctorHospitalChange(hospitalId) {
				this.doctorSearchForm.departmentId = ''
				this.departmentOptions = []
				if (hospitalId) {
					this.loadDepartmentOptions(hospitalId)
				}
			},

			// 加载科室选项
			loadDepartmentOptions(hospitalId) {
				this.$http({
					url: this.$http.adornUrl('/admin/department/selectDepartmentList'),
					method: 'get',
					params: this.$http.adornParams({
						'hospitalId': hospitalId
					})
				}).then(({data}) => {
					if (data && data.code === 0) {
						this.departmentOptions = data.data || []
					}
				})
			},

			// 添加医生
			addDoctor() {
				this.doctorDialogTitle = '添加医生'
				this.doctorDialogVisible = true
				this.resetDoctorForm()
				this.loadHospitalOptionsForForm()
				this.loadDictData() // 加载字典数据

				// 如果搜索条件中有医院和科室，带过去
				if (this.doctorSearchForm.hospitalId) {
					this.doctorForm.hospitalId = this.doctorSearchForm.hospitalId

					// 如果还有科室选择，加载科室选项并设置科室值
					if (this.doctorSearchForm.departmentId) {
						this.loadDepartmentOptionsForForm(this.doctorSearchForm.hospitalId, () => {
							// 科室选项加载完成后设置科室值
							this.doctorForm.departmentId = this.doctorSearchForm.departmentId
						})
					} else {
						// 只有医院选择，加载科室选项
						this.loadDepartmentOptionsForForm(this.doctorSearchForm.hospitalId)
					}
				}
			},

			// 编辑医生
			editDoctor(row) {
				this.doctorDialogTitle = '编辑医生'
				this.doctorDialogVisible = true
				this.loadHospitalOptionsForForm()
				this.loadDictData() // 加载字典数据

				// 获取医生详情
				this.$http({
					url: this.$http.adornUrl(`/doctor/info/${row.doctorId}`),
					method: 'get'
				}).then(({data}) => {
					if (data && data.code === 0) {
						const doctorData = data.doctor
						this.doctorForm = Object.assign({}, doctorData)

						if (this.doctorForm.hospitalId) {
							// 加载科室选项，加载完成后确保科室值正确设置
							this.loadDepartmentOptionsForForm(this.doctorForm.hospitalId, () => {
								// 确保科室值正确设置
								this.doctorForm.departmentId = doctorData.departmentId
							})
						}
					}
				})
			},

			// 删除医生
			deleteDoctor(row) {
				this.$confirm(`确定删除医生 ${row.doctorName} 吗?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$http({
						url: this.$http.adornUrl('/doctor/delete'),
						method: 'post',
						data: this.$http.adornData([row.doctorId], false)
					}).then(({data}) => {
						if (data && data.code === 0) {
							this.$message({
								message: '删除成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.searchDoctors()
								}
							})
						} else {
							this.$message.error(data.msg)
						}
					})
				}).catch(() => {})
			},

			// 更新医生状态
			updateDoctorStatus(row) {
				this.$http({
					url: this.$http.adornUrl('/doctor/updateStatus'),
					method: 'post',
					params: this.$http.adornParams({
						'doctorId': row.doctorId,
						'isEnable': row.isEnable
					})
				}).then(({data}) => {
					if (data && data.code === 0) {
						this.$message({
							message: '操作成功',
							type: 'success',
							duration: 1500
						})
					} else {
						this.$message.error(data.msg)
						// 恢复原状态
						row.isEnable = row.isEnable === 1 ? 0 : 1
					}
				})
			},

			// ========== 医生弹窗相关方法 ==========
			// 重置医生表单
			resetDoctorForm() {
				this.doctorForm = {
					doctorId: '',
					hospitalId: '',
					departmentId: '',
					doctorName: '',
					doctorCode: '',
					phone: '',
					email: '',
					gender: 1,
					birthDate: '',
					idCard: '',
					title: '',
					education: '',
					specialty: '',
					introduction: '',
					tag: '',
					remarks: '',
					licenseNumber: '',
					practiceScope: '',
					workStatus: 1,
					sort: 0,
					isEnable: 1
				}
				this.doctorFormDepartmentOptions = []
				if (this.$refs.doctorForm) {
					this.$refs.doctorForm.resetFields()
				}
			},

			// 为表单加载医院选项
			loadHospitalOptionsForForm() {
				this.$http({
					url: this.$http.adornUrl('/admin/hospital/selectHospitalList'),
					method: 'get'
				}).then(({data}) => {
					if (data && data.code === 0) {
						this.hospitalOptions = data.data || []
					}
				})
			},

			// 表单中医院改变时加载科室选项
			onDoctorFormHospitalChange(hospitalId) {
				this.doctorForm.departmentId = ''
				this.doctorFormDepartmentOptions = []
				if (hospitalId) {
					this.loadDepartmentOptionsForForm(hospitalId)
				}
			},

			// 为表单加载科室选项
			loadDepartmentOptionsForForm(hospitalId, callback) {
				this.$http({
					url: this.$http.adornUrl('/admin/department/selectDepartmentList'),
					method: 'get',
					params: this.$http.adornParams({
						'hospitalId': hospitalId
					})
				}).then(({data}) => {
					if (data && data.code === 0) {
						this.doctorFormDepartmentOptions = data.data || []
						// 如果有回调函数，执行回调
						if (callback && typeof callback === 'function') {
							callback()
						}
					}
				})
			},

			// 保存医生表单
			saveDoctorForm() {
				this.$refs.doctorForm.validate((valid) => {
					if (valid) {
						const url = this.doctorForm.doctorId ? '/doctor/update' : '/doctor/save'
						this.$http({
							url: this.$http.adornUrl(url),
							method: 'post',
							data: this.$http.adornData(this.doctorForm)
						}).then(({data}) => {
							if (data && data.code === 0) {
								this.$message({
									message: '操作成功',
									type: 'success',
									duration: 1500,
									onClose: () => {
										this.doctorDialogVisible = false
										this.searchDoctors()
									}
								})
							} else {
								this.$message.error(data.msg)
							}
						})
					}
				})
			},

			// ========== 字典数据加载方法 ==========
			// 加载字典数据
			loadDictData() {
				// 加载性别字典
				this.loadDictByType('性别', (data) => {
					this.genderList = data
				})

				// 加载职称字典
				this.loadDictByType('医生职称', (data) => {
					this.titleList = data
				})

				// 加载学历字典
				this.loadDictByType('学历', (data) => {
					this.educationList = data
				})

				// 加载工作状态字典
				this.loadDictByType('工作状态', (data) => {
					this.workStatusList = data
				})
			},

			// 根据类型加载字典数据
			loadDictByType(type, callback) {
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': type })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						if (callback && typeof callback === 'function') {
							callback(data.data || [])
						}
					}
				})
			},
		},
		mounted() {
			this.token = this.$cookie.get('token')
			this.homeSelect()
			this.dataSelect()
			this.djSelect()
			this.lxSelect()
			this.xianshi()
		}
	}
</script>

<style>
	.imgs {
		position: relative;
		border-radius: 6px;
		width: 148px;
		height: 148px;
		margin-right: 10px;
		display: inline-block;
	}

	.dels {
		position: absolute;
		top: 0;
		left: 0;
		display: none;
	}

	.dels .el-icon-delete {
		line-height: 148px;
		padding-left: 58px;
		font-size: 25px;
		color: #fff;
	}

	.imgs:hover .dels {
		width: 100%;
		height: 100%;
		background: #000;
		display: block;
		opacity: 0.5;
	}
</style>
