package com.sqx.modules.department.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.vo.DepartmentOut;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
public interface DepartmentService extends IService<Department> {

    Result addDepartment(Department department);

    Result updateDepartment(Department department);

    List<Department> getDepartmentList( Department department);

    Result deleteDepartment(Long departmentId);

    Department getDepartmentInfo(Long departmentId);

    IPage<Department> getDepartmentPageList(Integer page, Integer limit, Department department);

    List<DepartmentOut> departmentExcelOut(String startTime, String endTime, Department department);

    Result departmentListExcelIn(MultipartFile file, Long hospitalId) throws IOException;
}
