{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/index.vue?59f9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/index.vue?9711", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/index.vue?ec69", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/index.vue?98be", "uni-app:///my/setting/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/index.vue?29ed", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/index.vue?a2e8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "onLoad", "methods", "goNav", "uni", "url", "goOut", "title", "content", "success", "console", "icon", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2BxvB;EACAC;IACA,QAEA;EACA;EACAC,2BAEA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACAF;QACAG;QACAC;QACAC;UACA;YACAC;YACAN;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;cACAG;cACAI;YACA;YACAC;cACAR;YACA;UACA;YACAM;UACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAA2iC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACA/jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/setting/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/setting/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=7f4e44be&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/setting/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=7f4e44be&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"text-white padding-lr\">\n\t\t<view class=\"flex  padding-tb\" @click=\"goNav('/pages/public/pwd')\">\n\t\t\t<view class=\"flex-sub text-df\" style=\"line-height: 50upx;\">修改密码</view>\n\t\t\t<image src=\"../../static/images/my/right.png\" style=\"line-height: 50upx;width: 15rpx;height: 30rpx;\"></image>\n\t\t</view>\n\t\t<!-- <view class=\"flex  padding-tb\">\n\t\t\t<view class=\"flex-sub text-df\" style=\"line-height: 50upx;\">帮助中心</view>\n\t\t\t<image src=\"../../static/images/my/right.png\" style=\"line-height: 50upx;width: 15rpx;height: 30rpx;\"></image>\n\t\t</view> -->\n\t\t<view class=\"flex  padding-tb\" @click=\"goNav('/my/setting/xieyi')\">\n\t\t\t<view class=\"flex-sub text-df\" style=\"line-height: 50upx;\">用户协议</view>\n\t\t\t<image src=\"../../static/images/my/right.png\" style=\"line-height: 50upx;width: 15rpx;height: 30rpx;\"></image>\n\t\t</view>\n\t\t<view class=\"flex  padding-tb\" @click=\"goNav('/my/setting/mimi')\">\n\t\t\t<view class=\"flex-sub text-df\" style=\"line-height: 50upx;\">隐私政策</view>\n\t\t\t<image src=\"../../static/images/my/right.png\" style=\"line-height: 50upx;width: 15rpx;height: 30rpx;\"></image>\n\t\t</view>\n\t\t<view class=\"flex  padding-tb\" @click=\"goNav('/my/setting/about')\">\n\t\t\t<view class=\"flex-sub text-df\" style=\"line-height: 50upx;\">关于我们</view>\n\t\t\t<image src=\"../../static/images/my/right.png\" style=\"line-height: 50upx;width: 15rpx;height: 30rpx;\"></image>\n\t\t</view>\n\t\t<view class=\"btn\" @click=\"goOut\" >退出登录</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t\n\t\t},\n\t\tmethods: {\n\t\t\tgoNav(e) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl:e\n\t\t\t\t})\n\t\t\t},\n\t\t\tgoOut() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确定退出登录吗？',\n\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\tuni.removeStorageSync('userName')\n\t\t\t\t\t\t\tuni.removeStorageSync('avatar')\n\t\t\t\t\t\t\tuni.removeStorageSync('userId')\n\t\t\t\t\t\t\tuni.removeStorageSync('token')\n\t\t\t\t\t\t\tuni.removeStorageSync('phone')\n\t\t\t\t\t\t\tuni.removeStorageSync('zhiFuBaoName')\n\t\t\t\t\t\t\tuni.removeStorageSync('zhiFuBao')\n\t\t\t\t\t\t\tuni.removeStorageSync('invitationCode')\n\t\t\t\t\t\t\tuni.removeStorageSync('unionId')\n\t\t\t\t\t\t\tuni.removeStorageSync('openId')\n\t\t\t\t\t\t\tuni.removeStorageSync('isVIP')\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '退出成功！',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t},1000)\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t\t\n\t}\n</script>\n\n<style>\n\t.btn {\n\t\twidth: 100%;\n\t\theight: 80upx;\n\t\tbackground: #557EFD;\n\t\tborder-radius: 6upx;\n\t\ttext-align: center;\n\t\tline-height: 80upx;\n\t\tmargin-top: 40upx;\n\t\tfont-size: 34upx;\n\t\tcolor: #fff;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447624843\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}