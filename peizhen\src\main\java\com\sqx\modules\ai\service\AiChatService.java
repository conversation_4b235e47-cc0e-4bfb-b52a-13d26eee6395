package com.sqx.modules.ai.service;

import com.sqx.common.utils.Result;

/**
 * AI聊天服务接口
 */
public interface AiChatService {

    /**
     * 发送消息并获取AI回复
     */
    Result sendMessage(Long userId, Long conversationId, String message, String modelCode);

    /**
     * 获取对话历史
     */
    Result getConversationHistory(Long conversationId, Long userId, Integer page, Integer limit);

    /**
     * 流式对话（如果需要支持流式响应）
     */
    Result sendMessageStream(Long userId, Long conversationId, String message, String modelCode);
}
