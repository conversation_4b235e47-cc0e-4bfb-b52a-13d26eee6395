package com.sqx.modules.doctor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 医生管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Data
public class Doctor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 医生id
     */
    @TableId(value = "doctor_id", type = IdType.AUTO)
    @ApiModelProperty("医生id")
    private Long doctorId;

    /**
     * 医院id
     */
    @NotNull(message = "医院不能为空")
    @ApiModelProperty("医院id")
    private Long hospitalId;

    /**
     * 科室id
     */
    @NotNull(message = "科室不能为空")
    @ApiModelProperty("科室id")
    private Long departmentId;

    /**
     * 医生姓名
     */
    @NotBlank(message = "医生姓名不能为空")
    @TableField(condition = SqlCondition.LIKE)
    @ApiModelProperty("医生姓名")
    private String doctorName;

    /**
     * 医生工号
     */
    @ApiModelProperty("医生工号")
    private String doctorCode;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 性别 1男 2女
     */
    @ApiModelProperty("性别 1男 2女")
    private Integer gender;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("出生日期")
    private LocalDate birthDate;

    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String idCard;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 职称
     */
    @ApiModelProperty("职称")
    private String title;

    /**
     * 专业技术等级
     */
    @ApiModelProperty("专业技术等级")
    private String professionalLevel;

    /**
     * 学历
     */
    @ApiModelProperty("学历")
    private String education;

    /**
     * 专业特长
     */
    @ApiModelProperty("专业特长")
    private String specialty;

    /**
     * 医生简介
     */
    @ApiModelProperty("医生简介")
    private String introduction;

    /**
     * 标签
     */
    @ApiModelProperty("标签")
    private String tag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 执业资格证书图片
     */
    @ApiModelProperty("执业资格证书图片")
    private String qualificationCert;

    /**
     * 执业证书编号
     */
    @ApiModelProperty("执业证书编号")
    private String licenseNumber;

    /**
     * 执业范围
     */
    @ApiModelProperty("执业范围")
    private String practiceScope;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("入职日期")
    private LocalDate entryDate;

    /**
     * 工作状态 1在职 2离职 3休假
     */
    @ApiModelProperty("工作状态 1在职 2离职 3休假")
    private Integer workStatus;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 是否启用 0否 1是
     */
    @ApiModelProperty("是否启用 0否 1是")
    private Integer isEnable;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    // 关联查询字段
    @TableField(exist = false)
    @ApiModelProperty("医院名称")
    private String hospitalName;

    @TableField(exist = false)
    @ApiModelProperty("科室名称")
    private String departmentName;
}
