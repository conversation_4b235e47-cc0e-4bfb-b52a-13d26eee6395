package com.sqx.modules.tbCoupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Data
public class TbCoupon implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "coupon_id", type = IdType.AUTO)
    private Long couponId;

    /**
     * 优惠券名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String couponName;

    /**
     * 优惠券图片
     */
    private String couponPicture;

    /**
     * 有效期天数
     */
    private Integer validDays;

    /**
     * 优惠券可使用订单最低金额
     */
    private BigDecimal minMoney;

    /**
     * 优惠券抵扣金额
     */
    private BigDecimal money;
    /**
     * 是否删除 0未删除 1已删除
     */
    private Integer deleteFlag;
    /**
     * 是否启用 0未启用 1已启用
     */
    private Integer isEnable;
    /**
     * 优惠券类型 1新手赠送 3免费领取
     */
    private Integer couponType;
    /**
     * 最多领取或购买数量(0为不限制数量)
     */
    private Integer maxReceive;
    /**
     * 优惠券数量
     */
    private Integer couponNum;
    /**
     * 商家昵称
     */
    @TableField(exist = false)
    private String shopName;
}
