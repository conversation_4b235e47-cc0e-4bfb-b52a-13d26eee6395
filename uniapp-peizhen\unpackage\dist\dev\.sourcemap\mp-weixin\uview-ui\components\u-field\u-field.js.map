{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-field/u-field.vue?5b14", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-field/u-field.vue?ec40", "uni-app:///uview-ui/components/u-field/u-field.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-field/u-field.vue?dc91", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-field/u-field.vue?f2b7", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-field/u-field.vue?f9c1", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-field/u-field.vue?0f99"], "names": ["name", "props", "icon", "rightIcon", "required", "label", "password", "clearable", "type", "default", "labelWidth", "labelAlign", "inputAlign", "iconColor", "autoHeight", "errorMessage", "placeholder", "placeholder<PERSON><PERSON><PERSON>", "focus", "fixed", "value", "disabled", "maxlength", "confirmType", "labelPosition", "fieldStyle", "clearSize", "iconStyle", "borderTop", "borderBottom", "trim", "data", "focused", "itemIndex", "computed", "inputWrapStyle", "style", "rightIconStyle", "labelStyle", "justifyContent", "inputMaxlength", "fieldInnerStyle", "methods", "onInput", "onFocus", "onBlur", "setTimeout", "onConfirm", "onClear", "rightIconClick", "fieldClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmDzwB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApCA,gBAqCA;EACAA;EACAC;IACAC;IACAC;IACA;IACA;IACA;IACA;IACAC;IACAC;IACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;IACAC;IACAC;IACAC;IACAC;IACAZ;MACAA;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;QACA;MACA;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;EACA;EACAsB;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACA;MACA;QACAA;MACA;QACA;QACAA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA,2EACAD;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAL;MACA;QACAA;MACA;MAEA;IACA;EACA;EACAM;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvRA;AAAA;AAAA;AAAA;AAA46C,CAAgB,svCAAG,EAAC,C;;;;;;;;;;;ACAh8C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA", "file": "uview-ui/components/u-field/u-field.js", "sourcesContent": ["var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.inputWrapStyle])\n  var s1 = _vm.type == \"textarea\" ? _vm.__get_style([_vm.fieldStyle]) : null\n  var s2 = !(_vm.type == \"textarea\") ? _vm.__get_style([_vm.fieldStyle]) : null\n  var s3 = _vm.rightIcon ? _vm.__get_style([_vm.rightIconStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-field.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-field.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-field\" :class=\"{'u-border-top': borderTop, 'u-border-bottom': borderBottom }\">\n\t\t<view class=\"u-field-inner\" :class=\"[type == 'textarea' ? 'u-textarea-inner' : '', 'u-label-postion-' + labelPosition]\">\n\t\t\t<view class=\"u-label\" :class=\"[required ? 'u-required' : '']\" :style=\"{\n\t\t\t\tjustifyContent: justifyContent, \n\t\t\t\tflex: labelPosition == 'left' ? `0 0 ${labelWidth}rpx` : '1'\n\t\t\t}\">\n\t\t\t\t<view class=\"u-icon-wrap\" v-if=\"icon\">\n\t\t\t\t\t<u-icon size=\"32\" :custom-style=\"iconStyle\" :name=\"icon\" :color=\"iconColor\" class=\"u-icon\"></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<slot name=\"icon\"></slot>\n\t\t\t\t<text class=\"u-label-text\" :class=\"[this.$slots.icon || icon ? 'u-label-left-gap' : '']\">{{ label }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"fild-body\">\n\t\t\t\t<view class=\"u-flex-1 u-flex\" :style=\"[inputWrapStyle]\">\n\t\t\t\t\t<textarea v-if=\"type == 'textarea'\" class=\"u-flex-1 u-textarea-class\" :style=\"[fieldStyle]\" :value=\"value\"\n\t\t\t\t\t :placeholder=\"placeholder\" :placeholderStyle=\"placeholderStyle\" :disabled=\"disabled\" :maxlength=\"inputMaxlength\"\n\t\t\t\t\t :focus=\"focus\" :autoHeight=\"autoHeight\" :fixed=\"fixed\" @input=\"onInput\" @blur=\"onBlur\" @focus=\"onFocus\" @confirm=\"onConfirm\"\n\t\t\t\t\t @tap=\"fieldClick\" />\n\t\t\t\t\t<input\n\t\t\t\t\t\tv-else\n\t\t\t\t\t\t:style=\"[fieldStyle]\"\n\t\t\t\t\t\t:type=\"type\"\n\t\t\t\t\t\tclass=\"u-flex-1 u-field__input-wrap\"\n\t\t\t\t\t\t:value=\"value\"\n\t\t\t\t\t\t:password=\"password || this.type === 'password'\"\n\t\t\t\t\t\t:placeholder=\"placeholder\"\n\t\t\t\t\t\t:placeholderStyle=\"placeholderStyle\"\n\t\t\t\t\t\t:disabled=\"disabled\"\n\t\t\t\t\t\t:maxlength=\"inputMaxlength\"\n\t\t\t\t\t\t:focus=\"focus\"\n\t\t\t\t\t\t:confirmType=\"confirmType\"\n\t\t\t\t\t\t@focus=\"onFocus\"\n\t\t\t\t\t\t@blur=\"onBlur\"\n\t\t\t\t\t\t@input=\"onInput\"\n\t\t\t\t\t\t@confirm=\"onConfirm\"\n\t\t\t\t\t\t@tap=\"fieldClick\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<u-icon :size=\"clearSize\" v-if=\"clearable && value != '' && focused\" name=\"close-circle-fill\" color=\"#c0c4cc\" class=\"u-clear-icon\" @click=\"onClear\"/>\n\t\t\t\t<view class=\"u-button-wrap\"><slot name=\"right\" /></view>\n\t\t\t\t<u-icon v-if=\"rightIcon\" @click=\"rightIconClick\" :name=\"rightIcon\" color=\"#c0c4cc\" :style=\"[rightIconStyle]\" size=\"26\" class=\"u-arror-right\" />\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"errorMessage !== false && errorMessage != ''\" class=\"u-error-message\" :style=\"{\n\t\t\tpaddingLeft: labelWidth + 'rpx'\n\t\t}\">{{ errorMessage }}</view>\n\t</view>\n</template>\n\n<script>\n/**\n * field 输入框\n * @description 借助此组件，可以实现表单的输入， 有\"text\"和\"textarea\"类型的，此外，借助uView的picker和actionSheet组件可以快速实现上拉菜单，时间，地区选择等， 为表单解决方案的利器。\n * @tutorial https://www.uviewui.com/components/field.html\n * @property {String} type 输入框的类型（默认text）\n * @property {String} icon label左边的图标，限uView的图标名称\n * @property {Object} icon-style 左边图标的样式，对象形式\n * @property {Boolean} right-icon 输入框右边的图标名称，限uView的图标名称（默认false）\n * @property {Boolean} required 是否必填，左边您显示红色\"*\"号（默认false）\n * @property {String} label 输入框左边的文字提示\n * @property {Boolean} password 是否密码输入方式(用点替换文字)，type为text时有效（默认false）\n * @property {Boolean} clearable 是否显示右侧清空内容的图标控件(输入框有内容，且获得焦点时才显示)，点击可清空输入框内容（默认true）\n * @property {Number String} label-width label的宽度，单位rpx（默认130）\n * @property {String} label-align label的文字对齐方式（默认left）\n * @property {Object} field-style 自定义输入框的样式，对象形式\n * @property {Number | String} clear-size 清除图标的大小，单位rpx（默认30）\n * @property {String} input-align 输入框内容对齐方式（默认left）\n * @property {Boolean} border-bottom 是否显示field的下边框（默认true）\n * @property {Boolean} border-top 是否显示field的上边框（默认false）\n * @property {String} icon-color 左边通过icon配置的图标的颜色（默认#606266）\n * @property {Boolean} auto-height 是否自动增高输入区域，type为textarea时有效（默认true）\n * @property {String Boolean} error-message 显示的错误提示内容，如果为空字符串或者false，则不显示错误信息\n * @property {String} placeholder 输入框的提示文字\n * @property {String} placeholder-style placeholder的样式(内联样式，字符串)，如\"color: #ddd\"\n * @property {Boolean} focus 是否自动获得焦点（默认false）\n * @property {Boolean} fixed 如果type为textarea，且在一个\"position:fixed\"的区域，需要指明为true（默认false）\n * @property {Boolean} disabled 是否不可输入（默认false）\n * @property {Number String} maxlength 最大输入长度，设置为 -1 的时候不限制最大长度（默认140）\n * @property {String} confirm-type 设置键盘右下角按钮的文字，仅在type=\"text\"时生效（默认done）\n * @event {Function} input 输入框内容发生变化时触发\n * @event {Function} focus 输入框获得焦点时触发\n * @event {Function} blur 输入框失去焦点时触发\n * @event {Function} confirm 点击完成按钮时触发\n * @event {Function} right-icon-click 通过right-icon生成的图标被点击时触发\n * @event {Function} click 输入框被点击或者通过right-icon生成的图标被点击时触发，这样设计是考虑到传递右边的图标，一般都为需要弹出\"picker\"等操作时的场景，点击倒三角图标，理应发出此事件，见上方说明\n * @example <u-field v-model=\"mobile\" label=\"手机号\" required :error-message=\"errorMessage\"></u-field>\n */\nexport default {\n\tname:\"u-field\",\n\tprops: {\n\t\ticon: String,\n\t\trightIcon: String,\n\t\t// arrowDirection: {\n\t\t// \ttype: String,\n\t\t// \tdefault: 'right'\n\t\t// },\n\t\trequired: Boolean,\n\t\tlabel: String,\n\t\tpassword: Boolean,\n\t\tclearable: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 左边标题的宽度单位rpx\n\t\tlabelWidth: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 130\n\t\t},\n\t\t// 对齐方式，left|center|right\n\t\tlabelAlign: {\n\t\t\ttype: String,\n\t\t\tdefault: 'left'\n\t\t},\n\t\tinputAlign: {\n\t\t\ttype: String,\n\t\t\tdefault: 'left'\n\t\t},\n\t\ticonColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#606266'\n\t\t},\n\t\tautoHeight: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\terrorMessage: {\n\t\t\ttype: [String, Boolean],\n\t\t\tdefault: ''\n\t\t},\n\t\tplaceholder: String,\n\t\tplaceholderStyle: String,\n\t\tfocus: Boolean,\n\t\tfixed: Boolean,\n\t\tvalue: [Number, String],\n\t\ttype: {\n\t\t\ttype: String,\n\t\t\tdefault: 'text'\n\t\t},\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tmaxlength: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 140\n\t\t},\n\t\tconfirmType: {\n\t\t\ttype: String,\n\t\t\tdefault: 'done'\n\t\t},\n\t\t// lable的位置，可选为 left-左边，top-上边\n\t\tlabelPosition: {\n\t\t\ttype: String,\n\t\t\tdefault: 'left'\n\t\t},\n\t\t// 输入框的自定义样式\n\t\tfieldStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {}\n\t\t\t}\n\t\t},\n\t\t// 清除按钮的大小\n\t\tclearSize: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 30\n\t\t},\n\t\t// lable左边的图标样式，对象形式\n\t\ticonStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {}\n\t\t\t}\n\t\t},\n\t\t// 是否显示上边框\n\t\tborderTop: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 是否显示下边框\n\t\tborderBottom: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否自动去除两端的空格\n\t\ttrim: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tfocused: false,\n\t\t\titemIndex: 0,\n\t\t};\n\t},\n\tcomputed: {\n\t\tinputWrapStyle() {\n\t\t\tlet style = {};\n\t\t\tstyle.textAlign = this.inputAlign;\n\t\t\t// 判断lable的位置，如果是left的话，让input左边两边有间隙\n\t\t\tif(this.labelPosition == 'left') {\n\t\t\t\tstyle.margin = `0 8rpx`;\n\t\t\t} else {\n\t\t\t\t// 如果lable是top的，input的左边就没必要有间隙了\n\t\t\t\tstyle.marginRight = `8rpx`;\n\t\t\t}\n\t\t\treturn style;\n\t\t},\n\t\trightIconStyle() {\n\t\t\tlet style = {};\n\t\t\tif (this.arrowDirection == 'top') style.transform = 'roate(-90deg)';\n\t\t\tif (this.arrowDirection == 'bottom') style.transform = 'roate(90deg)';\n\t\t\telse style.transform = 'roate(0deg)';\n\t\t\treturn style;\n\t\t},\n\t\tlabelStyle() {\n\t\t\tlet style = {};\n\t\t\tif(this.labelAlign == 'left') style.justifyContent = 'flext-start';\n\t\t\tif(this.labelAlign == 'center') style.justifyContent = 'center';\n\t\t\tif(this.labelAlign == 'right') style.justifyContent = 'flext-end';\n\t\t\treturn style;\n\t\t},\n\t\t// uni不支持在computed中写style.justifyContent = 'center'的形式，故用此方法\n\t\tjustifyContent() {\n\t\t\tif(this.labelAlign == 'left') return 'flex-start';\n\t\t\tif(this.labelAlign == 'center') return 'center';\n\t\t\tif(this.labelAlign == 'right') return 'flex-end';\n\t\t},\n\t\t// 因为uniapp的input组件的maxlength组件必须要数值，这里转为数值，给用户可以传入字符串数值\n\t\tinputMaxlength() {\n\t\t\treturn Number(this.maxlength)\n\t\t},\n\t\t// label的位置\n\t\tfieldInnerStyle() {\n\t\t\tlet style = {};\n\t\t\tif(this.labelPosition == 'left') {\n\t\t\t\tstyle.flexDirection = 'row';\n\t\t\t} else {\n\t\t\t\tstyle.flexDirection = 'column';\n\t\t\t}\n\t\t\t\n\t\t\treturn style;\n\t\t}\n\t},\n\tmethods: {\n\t\tonInput(event) {\n\t\t\tlet value = event.detail.value;\n\t\t\t// 判断是否去除空格\n\t\t\tif(this.trim) value = this.$u.trim(value);\n\t\t\tthis.$emit('input', value);\n\t\t},\n\t\tonFocus(event) {\n\t\t\tthis.focused = true;\n\t\t\tthis.$emit('focus', event);\n\t\t},\n\t\tonBlur(event) {\n\t\t\t// 最开始使用的是监听图标@touchstart事件，自从hx2.8.4后，此方法在微信小程序出错\n\t\t\t// 这里改为监听点击事件，手点击清除图标时，同时也发生了@blur事件，导致图标消失而无法点击，这里做一个延时\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.focused = false;\n\t\t\t}, 100)\n\t\t\tthis.$emit('blur', event);\n\t\t},\n\t\tonConfirm(e) {\n\t\t\tthis.$emit('confirm', e.detail.value);\n\t\t},\n\t\tonClear(event) {\n\t\t\tthis.$emit('input', '');\n\t\t},\n\t\trightIconClick() {\n\t\t\tthis.$emit('right-icon-click');\n\t\t\tthis.$emit('click');\n\t\t},\n\t\tfieldClick() {\n\t\t\tthis.$emit('click');\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/style.components.scss\";\n\t\n.u-field {\n\tfont-size: 28rpx;\n\tpadding: 20rpx 28rpx;\n\ttext-align: left;\n\tposition: relative;\n\tcolor: $u-main-color;\n}\n\n.u-field-inner {\n\t@include vue-flex;\n\talign-items: center;\n}\n\n.u-textarea-inner {\n\talign-items: flex-start;\n}\n\n.u-textarea-class {\n\tmin-height: 96rpx;\n\twidth: auto;\n\tfont-size: 28rpx;\n}\n\n.fild-body {\n\t@include vue-flex;\n\tflex: 1;\n\talign-items: center;\n}\n\n.u-arror-right {\n\tmargin-left: 8rpx;\n}\n\n.u-label-text {\n\t/* #ifndef APP-NVUE */\n\tdisplay: inline-flex;\t\t\n\t/* #endif */\n}\n\n.u-label-left-gap {\n\tmargin-left: 6rpx;\n}\n\n.u-label-postion-top {\n\tflex-direction: column;\n\talign-items: flex-start;\n}\n\n.u-label {\n\twidth: 130rpx;\n\tflex: 1 1 130rpx;\n\ttext-align: left;\n\tposition: relative;\n\t@include vue-flex;\n\talign-items: center;\n}\n\n.u-required::before {\n\tcontent: '*';\n\tposition: absolute;\n\tleft: -16rpx;\n\tfont-size: 14px;\n\tcolor: $u-type-error;\n\theight: 9px;\n\tline-height: 1;\n}\n\n.u-field__input-wrap {\n\tposition: relative;\n\toverflow: hidden;\n\tfont-size: 28rpx;\n\theight: 48rpx;\n\tflex: 1;\n\twidth: auto;\n}\n\n.u-clear-icon {\n\t@include vue-flex;\n\talign-items: center;\n}\n\n.u-error-message {\n\tcolor: $u-type-error;\n\tfont-size: 26rpx;\n\ttext-align: left;\n}\n\n.placeholder-style {\n\tcolor: rgb(150, 151, 153);\n}\n\n.u-input-class {\n\tfont-size: 28rpx;\n}\n\n.u-button-wrap {\n\tmargin-left: 8rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-field.vue?vue&type=style&index=0&id=1c764f86&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-field.vue?vue&type=style&index=0&id=1c764f86&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627545\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./u-field.vue?vue&type=template&id=1c764f86&scoped=true&\"\nvar renderjs\nimport script from \"./u-field.vue?vue&type=script&lang=js&\"\nexport * from \"./u-field.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-field.vue?vue&type=style&index=0&id=1c764f86&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1c764f86\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-field/u-field.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-field.vue?vue&type=template&id=1c764f86&scoped=true&\""], "sourceRoot": ""}