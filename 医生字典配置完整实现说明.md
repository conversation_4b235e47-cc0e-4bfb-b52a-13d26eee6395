# 医生字典配置完整实现说明

## 功能概述

将医生管理中的性别、职称、学历、工作状态等字段改为字典表配置，实现了系统的可配置化管理。

## 实现内容

### 1. 数据库字典配置

#### 1.1 修正的SQL语句

使用变量和LAST_INSERT_ID()来正确处理父子关系：

```sql
-- 性别字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('性别', '性别', NULL, NULL, 1, '性别分类', 0);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('男', NULL, '1', '男', 1, '男性', LAST_INSERT_ID());
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('女', NULL, '2', '女', 2, '女性', LAST_INSERT_ID()-1);

-- 医生职称字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('医生职称', '医生职称', NULL, NULL, 1, '医生职称分类', 0);
SET @title_parent_id = LAST_INSERT_ID();
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('主任医师', NULL, '1', '主任医师', 1, '主任医师职称', @title_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('副主任医师', NULL, '2', '副主任医师', 2, '副主任医师职称', @title_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('主治医师', NULL, '3', '主治医师', 3, '主治医师职称', @title_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('住院医师', NULL, '4', '住院医师', 4, '住院医师职称', @title_parent_id);

-- 学历字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('学历', '学历', NULL, NULL, 1, '学历分类', 0);
SET @education_parent_id = LAST_INSERT_ID();
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('博士', NULL, '1', '博士', 1, '博士学历', @education_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('硕士', NULL, '2', '硕士', 2, '硕士学历', @education_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('本科', NULL, '3', '本科', 3, '本科学历', @education_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('专科', NULL, '4', '专科', 4, '专科学历', @education_parent_id);

-- 工作状态字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('工作状态', '工作状态', NULL, NULL, 1, '医生工作状态分类', 0);
SET @work_status_parent_id = LAST_INSERT_ID();
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('在职', NULL, '1', '在职', 1, '在职状态', @work_status_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('离职', NULL, '2', '离职', 2, '离职状态', @work_status_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('休假', NULL, '3', '休假', 3, '休假状态', @work_status_parent_id);
```

#### 1.2 字典结构说明

每个字典类型包含：
- **父级记录**: `type`字段为字典类型名称，`parent_id`为0
- **子级记录**: `code`为数值编码，`value`为显示文本，`parent_id`为父级ID

### 2. 实体类

Doctor实体类**不需要修改**，因为：
- 数据库字段类型保持不变（仍为Integer）
- 只是前端显示时从字典获取文本
- 注释说明仍然有效

### 3. 前端页面修改

#### 3.1 universalMission页面

**数据结构**：
```javascript
data() {
  return {
    // 字典数据
    genderList: [],        // 性别字典
    titleList: [],         // 职称字典
    educationList: [],     // 学历字典
    workStatusList: []     // 工作状态字典
  }
}
```

**模板修改**：
- 性别：改为使用`genderList`渲染radio选项
- 职称：改为使用`titleList`渲染select选项
- 学历：改为使用`educationList`渲染select选项
- 工作状态：改为使用`workStatusList`渲染select选项

**方法添加**：
- `loadDictData()` - 加载所有字典数据
- `loadDictByType(type, callback)` - 根据类型加载字典

#### 3.2 doctor-add-or-update页面

**相同的修改**：
- 添加字典数据字段
- 修改模板使用字典渲染
- 添加字典加载方法
- 在init方法中调用loadDictData()

#### 3.3 doctorList页面

**搜索条件修改**：
- 工作状态搜索下拉框改为使用字典数据
- 添加字典数据加载方法

### 4. API接口

使用统一的字典查询接口：

```javascript
// 获取字典数据
this.$http({
  url: this.$http.adornUrl('sys/dict/selectDictList'),
  method: 'get',
  params: this.$http.adornParams({ 'type': '字典类型' })
}).then(({ data }) => {
  if (data && data.code === 0) {
    this.dictList = data.data
  }
})
```

### 5. 数据映射关系

#### 5.1 性别映射
- 数据库存储：1, 2
- 字典配置：code='1' value='男', code='2' value='女'
- 前端显示：根据code值显示对应的value文本

#### 5.2 职称映射
- 数据库存储：字符串（如"主任医师"）
- 字典配置：code='1' value='主任医师'
- 前端显示：直接使用value值

#### 5.3 学历映射
- 数据库存储：字符串（如"博士"）
- 字典配置：code='1' value='博士'
- 前端显示：直接使用value值

#### 5.4 工作状态映射
- 数据库存储：1, 2, 3
- 字典配置：code='1' value='在职'
- 前端显示：根据code值显示对应的value文本

## 修改的文件清单

### 1. 数据库文件
- ✅ `peizhen/db/peizhen.sql` - 添加字典数据

### 2. 前端页面文件
- ✅ `peizhen--admin/src/views/universalMission/universalMission.vue` - 医生弹窗使用字典
- ✅ `peizhen--admin/src/views/doctor/doctor-add-or-update.vue` - 独立编辑页面使用字典
- ✅ `peizhen--admin/src/views/doctor/doctorList.vue` - 列表页面搜索条件使用字典

### 3. 后端文件
- ❌ **无需修改** - 实体类、Service、Controller都不需要修改

## 优势

### 1. 可配置性
- 管理员可以在字典管理中添加/修改选项
- 无需修改代码即可调整业务选项
- 支持选项的排序和启用/禁用

### 2. 一致性
- 全系统使用统一的字典数据
- 避免不同页面选项不一致的问题
- 便于数据标准化

### 3. 维护性
- 集中管理字典数据
- 减少硬编码，提高代码质量
- 便于后续扩展和维护

### 4. 扩展性
- 可以轻松添加新的字典类型
- 支持多级字典结构
- 便于国际化扩展

## 使用说明

### 1. 添加新的字典选项
1. 在sys_dict表中添加新的字典项
2. 前端会自动获取最新的字典数据
3. 无需重启服务

### 2. 修改现有选项
1. 直接在数据库中修改sys_dict表
2. 前端重新加载页面即可看到变化

### 3. 注意事项
- 删除字典项前要确保没有关联数据使用
- code值要与数据库字段的数据类型匹配
- order_num字段控制选项的显示顺序

这个字典配置功能使医生管理系统更加规范化和可配置化，提高了系统的灵活性和维护性。
