package com.sqx.modules.department.controller.app;


import com.sqx.common.utils.Result;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.service.DepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * 科室管理-用户端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */

@RestController
@Api(value = "科室管理-用户端", tags = {"科室管理-用户端"})
@RequestMapping(value = "/app/department/")
public class AppDepartmentController {
    @Autowired
    private DepartmentService departmentService;

    /**
     * 获取科室列表
     * @param department
     * @return
     */
    @ApiOperation("获取科室列表")
    @GetMapping("getDepartmentList")
    public Result getDepartmentList( Department department) {
        department.setIsEnable(1);
        return Result.success().put("data", departmentService.getDepartmentList( department));
    }

    /**
     * 获取科室详情
     * @param departmentId
     * @return
     */
    @ApiOperation("获取科室详情")
    @GetMapping("getDepartmentInfo")
    public Result getDepartmentInfo(Long departmentId) {
        return Result.success().put("data", departmentService.getDepartmentInfo(departmentId));
    }
}

