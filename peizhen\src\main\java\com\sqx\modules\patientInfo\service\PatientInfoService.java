package com.sqx.modules.patientInfo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.Result;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
public interface PatientInfoService extends IService<PatientInfo> {

    Result savePatient(PatientInfo patientInfo);

    IPage<PatientInfo> getPatientList( Integer page, Integer limit, PatientInfo patientInfo);

    PatientInfo getPatientInfo(Long patientId);

    Result deletePatient(Long userId,Long patientId);
}
