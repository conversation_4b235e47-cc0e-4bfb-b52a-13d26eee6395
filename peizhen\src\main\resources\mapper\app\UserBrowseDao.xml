<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.app.dao.UserBrowseDao">
    <select id="selectMyBrowse" resultType="com.sqx.modules.app.response.UserFollowResponse">
        SELECT b.id,
               t.user_id,
               t.user_name,
               t.avatar,
               t.sex,
               t.age,
               b.update_time,
               b.taking_id
        FROM user_browse b
                 left join order_taking o on o.id = b.taking_id
                 LEFT JOIN tb_user t ON b.by_browse_id = t.user_id
        WHERE b.user_id = #{userId}
        and o.id is not null
        order by b.update_time desc
    </select>
    <select id="selectMyVisitor1" resultType="com.sqx.modules.app.response.UserFollowResponse">
        SELECT t.user_id,
               t.user_name,
               t.avatar
        FROM user_browse b
                 LEFT JOIN tb_user t ON b.user_id = t.user_id
        WHERE b.by_browse_id = #{userId}
          and t.user_id is not null
        order by b.update_time desc
    </select>
    <select id="selectMyBrowse1" resultType="com.sqx.modules.app.response.UserFollowResponse">
        SELECT t.user_id,
               t.user_name,
               t.avatar
        FROM user_browse b
                 LEFT JOIN tb_user t ON b.by_browse_id = t.user_id
        WHERE b.user_id = #{userId}
          and t.user_id is not null
        order by b.update_time desc
    </select>


    <select id="selectUserBrowseCountByUserId" resultType="Integer">
        select count(*) from user_browse where user_id=#{userId}
        <if test="startTime!=null and startTime!=''">
            and date_format(update_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime!=null and endTime!=''">
            and date_format(update_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>

</mapper>