{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeihu.vue?e0a0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeihu.vue?f222", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeihu.vue?0d98", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeihu.vue?fd29", "uni-app:///my/customServer/customPeihu.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeihu.vue?efd7", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/customServer/customPeihu.vue?eec9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgRemarks", "isPay", "checked", "customStyle", "width", "color", "background", "border", "showxy", "content", "openWay", "showPay", "openLists", "money", "moneys", "showzl", "showbz", "showhl", "list", "list3", "list4", "list5", "couponName", "order", "params", "year", "month", "day", "hour", "minute", "second", "show", "height", "from", "couponId", "<PERSON><PERSON><PERSON><PERSON>", "serviceId", "type", "patient", "patientId", "hospitalName", "departmentOneName", "departmentTwoName", "lng", "lat", "province", "city", "district", "detailsAddress", "badNo", "symptom", "selfAbility", "nursing<PERSON><PERSON>s", "hopeTime", "serviceNum", "phone", "remarks", "orderTakingUserId", "czSel", "youhuiList", "show<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeInfo", "emergencyPhone", "watch", "console", "deep", "onLoad", "image", "text", "id", "onShow", "uni", "that", "methods", "selectCity", "get<PERSON><PERSON><PERSON>", "success", "fail", "title", "icon", "uploudImg", "sourceType", "sizeType", "count", "url", "filePath", "name", "closeImg", "getyhqt", "gotoNav", "getxy", "<PERSON><PERSON><PERSON><PERSON>", "checkboxChange", "qingkong", "youhuiPay", "getMyList", "status", "selectWay", "getInfo", "pays", "complete", "orderId", "classify", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "setTimeout", "ordersId", "pay", "submit", "duration", "sethl", "arr", "getHlList", "res", "item", "setbz", "select", "getBzList", "confirmzl", "getZlList", "gotoHlr", "getNowTime", "te<PERSON><PERSON><PERSON>", "date", "dateCompare", "arr1", "arr2", "d1", "d2", "s", "confirm", "callPay", "document", "onBridgeReady", "WeixinJSBridge", "setPayment", "orderInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,aAAa,yQAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAA0uB,CAAgB,2rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqX9vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;MACAC;MACA;QACA;MACA;QACA;MACA;IAEA;IACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;EACA;EACAC;IACA;IACA;IACA;IACA;IAmBA;MACAC;MACAC;MACAC;IACA;MACAF;MACAC;MACAC;IACA;IACA;EAkCA;EACAC;IACA;IACA;IACA;MACA;MACA;IACA;IACA;IACAC;MACAC;MACAA;MACA;QACAA;MACA;QACAA;MACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;UACAV;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAW;MACA;MACAJ;QACAK;UACAZ;UACAQ;UACAA;UACAA;UACAA;QACA;QACAK;UACAN;YACAO;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAT;QACAU;QACAC;QACAC;QACAP;UACA;UACA;YACAJ;YACAD;cAAA;cACAa;cAAA;cACA;cACAC;cACAC;cACAV;gBACAJ;gBACAD;cACA;YACA;UACA;QACA;MACA;IACA;IACAgB;MACA;IACA;IACAC;MACA;QACAjB;UACAO;UACAC;QACA;MACA;QACA;MACA;IACA;IACAU;MACAlB;QACAa;MAEA;IACA;IACA;IACAM;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACApB;QACAa;MACA;IACA;IACAQ;MACA5B;MACA;QACA;MACA;QACA;MACA;IACA;IACA6B;MACA;MACA;MACA;MACA;IACA;IACAC;MAEA;QACAvB;UACAO;UACAC;QACA;QACA;MACA;MACA;MACA;MACA;MACA;IAEA;IACA;IACAgB;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA9D;MACA;MACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA+D;MACA;QACA;MACA;MACA;MACA;MACA;QACApG;MACA;QACAA;MACA;MACAA;MACAA;MACA;MACAyE;QACA;UACAA;UACAD;YACAO;YACArE;YACA2F;cACA7B;gBACAO;cACA;cACA;gBACA;gBACA;kBAAA;;kBAEAN;oBACA6B;oBACAC;kBACA;oBACA;sBACA/B;wBACAgC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAhC;0BACAL;0BACAA;4BACAO;0BACA;0BACAN;0BACAqC;4BACAtC;8BACAa;4BACA;0BAEA;0BACAb,sBACA;wBACA;wBACAM;0BACAN;0BACAC;0BACAA,sBACA;wBACA;sBACA;oBACA;sBACAA;sBACAD;wBACAO;wBACAC;sBACA;oBACA;kBACA;gBA2CA;gBAAA,CAqCA;kBAAA;kBACAP;oBACAsC;kBACA;oBACA;sBACAvC;wBACAO;sBACA;sBACAN;sBACAqC;wBACAtC;0BACAa;wBACA;sBAEA;sBACAb;oBACA;sBACAC;sBACAD;wBACAO;wBACAC;sBACA;oBACA;kBACA;gBACA;cACA;gBACAR;gBACAA;kBACAO;kBACAC;gBACA;gBACAP;gBACAqC;kBACAtC;oBACAa;kBACA;gBAEA;cACA;YACA;UACA;QACA;UACAb;YACAO;YACAC;UACA;UACAP;QACA;MAEA;IACA;IACAuC;MACA;MACAvC;IAEA;IACA;IACAwC;MACA;QACAzC;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MACA;QACA1C;UACAO;UACAC;UACAkC;QACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA/E;MACA;MACA;QACA;UACAgF;YACAC;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAJ;QACA;MACA;MACA;MACA;IACA;IACA;IACAK;MACAF;IACA;IACA;IACAG;MAAA;MACA;QACApF;MACA;MACA;QACA;UACAgF;YACAC;UACA;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAtF;MACA;MAEA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAuF;MACArD;QACAa;MACA;IACA;IACA;IACAyC;MACA;QACAC;MACA;MACA;MACAC;MACA;QACArG;QACAC;QACAC;QACAC;QACAC;MACAJ;MACAC;MACA;MACA;MACA;IACA;IACA;IACAqG;MACA;MACA;QACAb;QACAc;QACAC;QACAC;MACA;QACAhB;QACAgB;MACA;MACA;QACAhB;QACAc;QACAC;QACAE;MACA;QACAjB;QACAiB;MACA;MAEAC;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MAEA;MACA;MACA;MACA;MACA;MACA;QAAA;QACA;QACA;MACA;QACA/D;UACAO;UACAC;QACA;QACA;MACA;IACA;IAEAwD;MACA;QACA;UACAC;QACA;UACAA;UACAA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACAC,sBACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;QAAA;QACA;MACA,GACA;QACA;UACA;UACA;UACAnE;UACAA;YACAO;UACA;UACAN;UACAqC;YACAtC;cACAa;YACA;UAEA;UACAb;QACA;UACAC;UACAD;QACA;QACAmE;MACA,EACA;IACA;IACAC;MACA;MACApE;QACAgC;QACAqC;QAAA;QACAhE;UACAL;UACAA;YACAO;UACA;UACAN;UACAqC;YACAtC;cACAa;YACA;UAEA;UACAb;QACA;QACAM;UACAL;UACAD;UACAP;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1tCA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/customServer/customPeihu.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/customServer/customPeihu.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./customPeihu.vue?vue&type=template&id=6d2d539a&\"\nvar renderjs\nimport script from \"./customPeihu.vue?vue&type=script&lang=js&\"\nexport * from \"./customPeihu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./customPeihu.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/customServer/customPeihu.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeihu.vue?vue&type=template&id=6d2d539a&\"", "var components\ntry {\n  components = {\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-number-box/u-number-box\" */ \"@/uview-ui/components/u-number-box/u-number-box.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-checkbox/u-checkbox\" */ \"@/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-button/u-button\" */ \"@/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-picker/u-picker\" */ \"@/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uSelect: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-select/u-select\" */ \"@/uview-ui/components/u-select/u-select.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.imgRemarks.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showbz = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showbz = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showzl = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showzl = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.showhl = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.showhl = true\n    }\n    _vm.e6 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e7 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e8 = function ($event) {\n      _vm.showxy = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeihu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeihu.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<!-- 类型 -->\r\n\t\t<view class=\"types flex justify-center\" @click=\"gotoNav(typeInfo)\">\r\n\t\t\t<view class=\"types-box flex justify-center align-center\">\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\" style=\"border: none;\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t<image src=\"../static/qcpz.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t服务类型\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r\">\r\n\t\t\t\t\t\t{{typeInfo.serviceName}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 表单 -->\r\n\t\t<view class=\"types flex justify-center\">\r\n\t\t\t<view class=\"types-box flex justify-center align-center flex-wrap\">\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view @click=\"gotoHlr()\" class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t被护理人\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.patient\" type=\"text\" disabled @click=\"gotoHlr()\" input-align=\"right\"\r\n\t\t\t\t\t\t\tplaceholder=\"请选择被护理人\" />\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<block v-if=\"emergencyPhone\">\r\n\t\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t\t紧急联系人\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t\t<u-input v-model=\"emergencyPhone\" type=\"text\" disabled input-align=\"right\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请选择紧急联系人\" />\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t服务医院\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.hospitalName\" type=\"text\" input-align=\"right\" placeholder=\"请输入服务医院名称\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view @click=\"getAddress()\" class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t医院地址\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click=\"getAddress()\" class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input @click=\"getAddress()\" v-model=\"from.detailsAddress\" type=\"text\" disabled\r\n\t\t\t\t\t\t\tinput-align=\"right\" placeholder=\"请选择医院地址\" />\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t一级科室\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.departmentOneName\" type=\"text\" input-align=\"right\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入一级科室\" />\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t二级科室\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.departmentTwoName\" type=\"text\" input-align=\"right\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入二级科室\" />\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t床号\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.badNo\" type=\"text\" input-align=\"right\" placeholder=\"请输入床号\"\r\n\t\t\t\t\t\t\tstyle=\"margin-right: 30rpx;\" />\r\n\t\t\t\t\t\t<!-- <u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view @click=\"showbz = true\" class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t选择病症\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.symptom\" type=\"text\" disabled @click=\"showbz = true\" input-align=\"right\"\r\n\t\t\t\t\t\t\tplaceholder=\"请选择病症\" />\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view @click=\"showzl = true\" class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t自理能力\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.selfAbility\" type=\"text\" disabled @click=\"showzl = true\"\r\n\t\t\t\t\t\t\tinput-align=\"right\" placeholder=\"请选择自理能力\" />\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view @click=\"showhl = true\" class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t护理需求\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.nursingNeeds\" disabled @click=\"showhl = true\" type=\"text\"\r\n\t\t\t\t\t\t\tinput-align=\"right\" placeholder=\"请选择护理需求\" />\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view @click=\"show = true\" class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t服务时间\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.hopeTime\" @click=\"show = true\" disabled type=\"text\" input-align=\"right\"\r\n\t\t\t\t\t\t\tplaceholder=\"请选择服务时间\" />\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t服务天数\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<!-- <u-input v-model=\"from.serviceNum\" type=\"number\" input-align=\"right\" placeholder=\"请选择服务天数\" />\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon> -->\r\n\t\t\t\t\t\t<u-number-box :disabled-input=\"true\" v-model=\"from.serviceNum\" :min=\"1\"></u-number-box>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t联系电话\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.phone\" type=\"number\" maxlength=\"11\" input-align=\"right\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入联系电话\" style=\"margin-right: 30rpx;\" />\r\n\t\t\t\t\t\t<!-- <u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c flex justify-between align-center\" @click=\"getyhqt\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\">\r\n\t\t\t\t\t\t优惠劵\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r flex align-center\">\r\n\t\t\t\t\t\t<view class=\"margin-right-xs\">{{-couponName?-couponName:'立即使用'}}</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"margin-tb-sm\" style=\"width: 100%;height: 1rpx;background: #F2F2F2;\"></view> -->\r\n\t\t\t\t<view class=\"\" style=\"width: 626rpx;border-bottom: 1rpx solid #F2F2F2;\">\r\n\t\t\t\t\t<view class=\"text-30 flex align-center\"\r\n\t\t\t\t\t\tstyle=\"color: #1E1F31;margin-right: 20upx;font-weight: bold;\">\r\n\t\t\t\t\t\t资料上传\r\n\t\t\t\t\t\t<text style=\"font-size: 20rpx;\">(就诊卡、病例等)</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"img_remarks flex flex-wrap\">\r\n\t\t\t\t\t\t<view class=\"img_remarks-item\" v-for=\"(item,index) in imgRemarks\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image :src=\"item\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<u-icon name=\"close-circle\" @click=\"closeImg(index)\" class=\"close-circle-close\" color=\"red\"\r\n\t\t\t\t\t\t\t\tsize=\"38\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view @click=\"uploudImg()\" class=\"img_remarks-uploud\" v-if=\"imgRemarks.length<9\">\r\n\t\t\t\t\t\t\t<u-icon class=\"img_remarks-uploud-icon\" name=\"camera\" color=\"grey\" size=\"48\"></u-icon>\r\n\t\t\t\t\t\t\t<view class=\"img_remarks-uploud-txt\">\r\n\t\t\t\t\t\t\t\t图片上传\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"types-box-c\" style=\"height: auto !important;\">\r\n\t\t\t\t\t<view class=\"types-box-c-l flex align-center\" style=\"margin-top: 20rpx;\">\r\n\t\t\t\t\t\t特殊需求\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"types-box-c-r\" style=\"margin-top: 20rpx;\">\r\n\t\t\t\t\t\t<u-input v-model=\"from.remarks\" type=\"textarea\" :height=\"height\" placeholder=\"请输入其他服务需求...\" />\r\n\t\t\t\t\t\t<!-- <u-icon name=\"arrow-right\" color=\"#999999\" style=\"margin-left: 10rpx;\" size=\"28\"></u-icon> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 注意事项 -->\r\n\t\t<view class=\"zz flex justify-center\">\r\n\t\t\t<view class=\"zz-box\">\r\n\t\t\t\t<view class=\"zz-box-title\">\r\n\t\t\t\t\t<u-icon name=\"error-circle-fill\" color=\"#ffa722\" style=\"margin-right: 10rpx;\" size=\"32\"></u-icon>\r\n\t\t\t\t\t注意事项\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"zz-box-con\">\r\n\t\t\t\t\t{{typeInfo.mattersThing}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"flex justify-center\"\r\n\t\t\tstyle=\"position: fixed;bottom: 0;background-color: #FFFFFF;width: 100%;padding-top: 30rpx;padding-bottom: 20rpx;\">\r\n\t\t\t<view class=\"\" style=\"width: 686rpx;\">\r\n\t\t\t\t<view class=\"flex justify-center\" style=\"width: 100%;margin-bottom: 20rpx;\">\r\n\r\n\t\t\t\t\t<u-checkbox label-size=\"26rpx\" v-model=\"checked\" @change=\"checkboxChange\" shape=\"circle\">\r\n\t\t\t\t\t\t我已认真阅读预约相关<text @click.stop=\"gotoxieyi()\" style=\"color: #4e86f8;\">《服务条款同意书》</text></u-checkbox>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\" flex justify-between\" style=\"width: 100%;\">\r\n\t\t\t\t\t<view style=\"color: #FF2D01;\">\r\n\t\t\t\t\t\t实付：<text style=\"font-size: 38rpx;font-weight: bold;\">￥</text>\r\n\t\t\t\t\t\t<text style=\"font-size: 52upx;margin-top: 8upx;font-weight: bold;\">{{money}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<u-button :custom-style=\"customStyle\" @click=\"submit\" shape=\"circle\" :hair-line=\"false\">立即支付\r\n\t\t\t\t\t\t</u-button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 协议弹窗 -->\r\n\t\t<u-popup v-model=\"showxy\" mode=\"center\" border-radius=\"24\" width=\"80%\" height=\"60%\">\r\n\t\t\t<view class=\"\" style=\"width: 100%;height: 100%;background-color: #FFFFFF;position: relative;\">\r\n\t\t\t\t<view class=\"flex justify-center\" style=\"width: 100%;height: 85%;\">\r\n\t\t\t\t\t<view class=\"\" style=\"width: 90%;height: 95%;margin-top: 5%;\">\r\n\t\t\t\t\t\t<scroll-view scroll-y=\"true\" style=\"width: 100%;height: 100%;background-color: #FFFFFF;\">\r\n\t\t\t\t\t\t\t<view v-html=\"content\"></view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex justify-center align-center\"\r\n\t\t\t\t\tstyle=\"width: 100%;position: absolute;bottom: 0;background-color: #FFFFFF;height: 15%;\">\r\n\t\t\t\t\t<view @click=\"showxy=false\" class=\"flex justify-center align-center\"\r\n\t\t\t\t\t\tstyle=\"width: 90%;height: 70rpx;border-radius: 40rpx;background-color: #4e86f8;color: #FFFFFF;\">\r\n\t\t\t\t\t\t确认关闭\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t<!-- 服务时间选择 -->\r\n\t\t<u-picker v-model=\"show\" :params=\"params\" mode=\"time\" @confirm=\"confirm\"></u-picker>\r\n\t\t<!--自理能力 -->\r\n\t\t<u-select v-model=\"showzl\" :list=\"list3\" label-name=\"code\" @confirm=\"confirmzl\"></u-select>\r\n\t\t<!-- 病症選擇 -->\r\n\t\t<u-popup v-model=\"showbz\" mode=\"bottom\">\r\n\t\t\t<view class=\"bz flex justify-center\">\r\n\t\t\t\t<view class=\"bz-box\">\r\n\t\t\t\t\t<view class=\"bz-box-title\">\r\n\t\t\t\t\t\t病症选择\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bz-box-select flex justify-center align-center\">\r\n\t\t\t\t\t\t<view class=\"bz-box-select-c flex flex-wrap justify-between\">\r\n\t\t\t\t\t\t\t<view class=\"bz-box-select-item flex justify-center align-center\" @click=\"select(item)\"\r\n\t\t\t\t\t\t\t\t:class=\"item.se==true?'active':''\" v-for=\"(item,index) in list4\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t{{item.value}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bz-box-select-item flex justify-center align-center\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 200rpx;height: 0;\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bz-box-btn flex justify-center align-center\" @click=\"setbz\">\r\n\t\t\t\t\t\t确定\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t<!-- 护理需求 -->\r\n\t\t<u-popup v-model=\"showhl\" mode=\"bottom\">\r\n\t\t\t<view class=\"bz flex justify-center\">\r\n\t\t\t\t<view class=\"bz-box\">\r\n\t\t\t\t\t<view class=\"bz-box-title\">\r\n\t\t\t\t\t\t护理需求\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bz-box-select flex justify-center align-center\">\r\n\t\t\t\t\t\t<view class=\"bz-box-select-c flex flex-wrap justify-between\">\r\n\t\t\t\t\t\t\t<view class=\"bz-box-select-item flex justify-center align-center\" @click=\"select(item)\"\r\n\t\t\t\t\t\t\t\t:class=\"item.se==true?'active':''\" v-for=\"(item,index) in list5\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t{{item.value}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bz-box-select-item flex justify-center align-center\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 200rpx;height: 0;\">\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bz-box-btn flex justify-center align-center\" @click=\"sethl\">\r\n\t\t\t\t\t\t确定\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t<!-- 支付方式选择 -->\r\n\t\t<u-popup v-model=\"showPay\" mode=\"bottom\" border-radius=\"14\" :closeable=\"true\">\r\n\t\t\t<view\r\n\t\t\t\tstyle=\"width: 100%;text-align: center;font-size:38rpx;font-weight: bold;margin-top:15rpx;margin-bottom: 80rpx;\">\r\n\t\t\t\t选择支付方式\r\n\t\t\t</view>\r\n\t\t\t<view style=\"display: flex;\" v-for=\"(item,index) in openLists\" :key='index'>\r\n\t\t\t\t<view style=\"width: 100%;display: flex;height: 100upx;align-items: center;padding: 20upx 30rpx;\"\r\n\t\t\t\t\tv-if=\"item.text === '零钱' && czSel != '否'\">\r\n\t\t\t\t\t<view style=\"display: flex;width:80%;align-items: center;\">\r\n\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\r\n\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 20%;display: flex;justify-content: flex-end;\">\r\n\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item.id)'>\r\n\t\t\t\t\t\t\t<label class=\"tui-radio\">\r\n\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"width: 100%;display: flex;height: 100upx;align-items: center;padding: 20upx 30rpx;\"\r\n\t\t\t\t\tv-if=\"item.text != '零钱'\">\r\n\t\t\t\t\t<view style=\"display: flex;width:80%;align-items: center;\">\r\n\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\r\n\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 20%;display: flex;justify-content: flex-end;\">\r\n\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item.id)'>\r\n\t\t\t\t\t\t\t<label class=\"tui-radio\">\r\n\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view\r\n\t\t\t\tstyle=\"width: 690rpx;height: 80rpx;background:#1789FD;color:#FFFFFF;text-align: center;line-height: 80rpx;border-radius: 50rpx;margin: 30rpx;\"\r\n\t\t\t\t@click=\"pay()\">确认支付</view>\r\n\t\t</u-popup>\r\n\t\t<!-- 优惠券 -->\r\n\t\t<u-popup v-model=\"showYouhuijuan\" mode=\"bottom\" border-radius=\"14\" height=\"900rpx\" :closeable=\"true\">\r\n\t\t\t<view style=\"padding-bottom: 30rpx;\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tstyle=\"width: 100%;text-align: center;font-size:38rpx;font-weight: bold;padding-top:15rpx;padding-bottom:40rpx;\">\r\n\t\t\t\t\t优惠劵\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"margin-right text-right\" @click=\"qingkong\">\r\n\t\t\t\t\t<view class=\"noyouhui\">不使用优惠劵</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"listbox\" v-for=\"(item,ind) in youhuiList\" :key=\"ind\">\r\n\t\t\t\t\t<view class=\"flex align-start justify-between padding-lr\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view style=\"color: #000000;font-size: 30upx;\">{{item.couponName}}</view>\r\n\t\t\t\t\t\t\t<view style=\"color: #999999;font-size: 24upx;margin-top: 10upx;\" v-if=\"item.expirationTime\">\r\n\t\t\t\t\t\t\t\t有效期至{{item.expirationTime}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view style=\"color: #999999;font-size: 24upx;margin-top: 10upx;\" v-else>永久有效\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"color: #FD3C44;font-size: 30upx;\">¥<text\r\n\t\t\t\t\t\t\t\tstyle=\"font-size: 48upx;font-weight: bold;\">{{item.money}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 100%;border-top:1rpx dashed #E6E6E6;margin: 30upx 0upx;\"></view>\r\n\t\t\t\t\t<view class=\"flex align-center justify-between padding-lr\">\r\n\t\t\t\t\t\t<view v-if=\"item.minMoney\">满{{item.minMoney}}元可用</view>\r\n\t\t\t\t\t\t<view v-else>无门槛使用</view>\r\n\t\t\t\t\t\t<view class=\"btnss\" @click=\"youhuiPay(item)\">立即使用</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport configdata from '../../common/config.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timgRemarks: [], //图片备注\r\n\t\t\t\tisPay: true,\r\n\t\t\t\tchecked: false,\r\n\t\t\t\tcustomStyle: {\r\n\t\t\t\t\twidth: '340upx',\r\n\t\t\t\t\tcolor: '#FFFFFF',\r\n\t\t\t\t\tbackground: \"#468EF8\",\r\n\t\t\t\t\tborder: 0\r\n\t\t\t\t},\r\n\t\t\t\tshowxy: false,\r\n\t\t\t\tcontent: '',\r\n\t\t\t\topenWay: 0,\r\n\t\t\t\tshowPay: false,\r\n\t\t\t\topenLists: [],\r\n\t\t\t\tmoney: 0,\r\n\t\t\t\tmoneys: 0,\r\n\t\t\t\tshowzl: false,\r\n\t\t\t\tshowbz: false,\r\n\t\t\t\tshowhl: false,\r\n\t\t\t\tlist: [],\r\n\t\t\t\tlist3: [],\r\n\t\t\t\tlist4: [],\r\n\t\t\t\tlist5: [],\r\n\t\t\t\tcouponName: '',\r\n\t\t\t\torder: {},\r\n\t\t\t\tparams: {\r\n\t\t\t\t\tyear: true,\r\n\t\t\t\t\tmonth: true,\r\n\t\t\t\t\tday: true,\r\n\t\t\t\t\thour: true,\r\n\t\t\t\t\tminute: false,\r\n\t\t\t\t\tsecond: false\r\n\t\t\t\t},\r\n\t\t\t\tshow: false,\r\n\t\t\t\theight: 200,\r\n\t\t\t\tfrom: {\r\n\t\t\t\t\tcouponId: '', //优惠券id\r\n\t\t\t\t\tyouhuiMoney: 0,\r\n\t\t\t\t\tserviceId: '', //服务id\r\n\t\t\t\t\ttype: 1, // 1:陪护 2：陪诊\r\n\t\t\t\t\tpatient: '', //被护理人名称\r\n\t\t\t\t\tpatientId: '', //被护理人id\r\n\t\t\t\t\thospitalName: '', //医院名称\r\n\t\t\t\t\tdepartmentOneName: '', //一级科室名称\r\n\t\t\t\t\tdepartmentTwoName: '', //二级科室名称\r\n\t\t\t\t\tlng: '', // 经度\r\n\t\t\t\t\tlat: '', // 纬度\r\n\t\t\t\t\tprovince: '', // 省\r\n\t\t\t\t\tcity: '', // 市\r\n\t\t\t\t\tdistrict: '', // 区\r\n\t\t\t\t\tdetailsAddress: '', // 详细地址\r\n\t\t\t\t\tbadNo: '', //床号\r\n\t\t\t\t\tsymptom: '', //症状\r\n\t\t\t\t\tselfAbility: '', //自理能力\r\n\t\t\t\t\tnursingNeeds: '', //护理需求\r\n\t\t\t\t\thopeTime: '', //服务时间\r\n\t\t\t\t\tserviceNum: 1, //服务天数\r\n\t\t\t\t\tphone: '', //联系电话\r\n\t\t\t\t\tremarks: '', //特殊需求\r\n\t\t\t\t\torderTakingUserId: '', //护工id\r\n\t\t\t\t},\r\n\t\t\t\tczSel: '否',\r\n\t\t\t\tyouhuiList: [],\r\n\t\t\t\tshowYouhuijuan: false,\r\n\t\t\t\ttypeInfo: {},\r\n\t\t\t\temergencyPhone: '', //紧急联系人\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t'from.serviceNum': function(newName, oldName) {\r\n\t\t\t\tconsole.log(this.from.youhuiMoney)\r\n\t\t\t\tif (this.moneys * parseInt(this.from.serviceNum) > parseFloat(this.from.youhuiMoney)) {\r\n\t\t\t\t\tthis.money = this.moneys * parseInt(this.from.serviceNum) - parseFloat(this.from.youhuiMoney)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.money = 0.01\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\t'from.youhuiMoney': function(newName, oldName) {\r\n\t\t\t\tif (this.moneys * parseInt(this.from.serviceNum) > parseFloat(this.from.youhuiMoney)) {\r\n\t\t\t\t\tthis.money = this.moneys * parseInt(this.from.serviceNum) - parseFloat(this.from.youhuiMoney)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.money = 0.01\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdeep: true\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.czSel = this.$queue.getData('czSel');\r\n\t\t\tthis.from.phone = uni.getStorageSync('phone')\r\n\t\t\tthis.from.orderTakingUserId = option.hguserId\r\n\t\t\tthis.typeInfo = JSON.parse(decodeURIComponent(option.info))\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.openLists = [{\r\n\t\t\t\timage: '/static/images/zhifubao.png',\r\n\t\t\t\ttext: '支付宝',\r\n\t\t\t\tid: 1\r\n\t\t\t}, {\r\n\t\t\t\timage: '/static/images/icon_weixin.png',\r\n\t\t\t\ttext: '微信',\r\n\t\t\t\tid: 2\r\n\t\t\t}, {\r\n\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\ttext: '零钱',\r\n\t\t\t\tid: 3\r\n\t\t\t}];\r\n\t\t\tthis.openWay = 1;\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.openLists = [{\r\n\t\t\t\timage: '/static/images/icon_weixin.png',\r\n\t\t\t\ttext: '微信',\r\n\t\t\t\tid: 2\r\n\t\t\t}, {\r\n\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\ttext: '零钱',\r\n\t\t\t\tid: 3\r\n\t\t\t}];\r\n\t\t\tthis.openWay = 2;\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef H5\r\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\tthis.openLists = [{\r\n\t\t\t\t\timage: '/static/images/zhifubao.png',\r\n\t\t\t\t\ttext: '支付宝',\r\n\t\t\t\t\tid: 1\r\n\t\t\t\t}, {\r\n\t\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\t\ttext: '零钱',\r\n\t\t\t\t\tid: 3\r\n\t\t\t\t}, {\r\n\t\t\t\t\timage: '/static/images/icon_weixin.png',\r\n\t\t\t\t\ttext: '微信',\r\n\t\t\t\t\tid: 2\r\n\t\t\t\t}];\r\n\t\t\t\tthis.openWay = 1;\r\n\t\t\t} else {\r\n\t\t\t\tthis.openLists = [{\r\n\t\t\t\t\timage: '/static/images/zhifubao.png',\r\n\t\t\t\t\ttext: '支付宝',\r\n\t\t\t\t\tid: 1\r\n\t\t\t\t}, {\r\n\t\t\t\t\timage: '/static/images/lingqian.png',\r\n\t\t\t\t\ttext: '零钱',\r\n\t\t\t\t\tid: 3\r\n\t\t\t\t}];\r\n\t\t\t\tthis.openWay = 1;\r\n\t\t\t}\r\n\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tlet that = this\r\n\t\t\t//获取陪护服务id\r\n\t\t\tif (uni.getStorageSync('phserviceId')) {\r\n\t\t\t\tthis.from.serviceId = uni.getStorageSync('phserviceId')\r\n\t\t\t\tthis.getInfo(this.from.serviceId)\r\n\t\t\t}\r\n\t\t\t//获取选择的护理人信息\r\n\t\t\tuni.$on('updateData', (data) => {\r\n\t\t\t\tthat.from.patient = data.realName\r\n\t\t\t\tthat.from.patientId = data.patientId\r\n\t\t\t\tif (data.emergencyPhone) {\r\n\t\t\t\t\tthat.emergencyPhone = data.emergencyPhone\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.emergencyPhone = ''\r\n\t\t\t\t}\r\n\r\n\t\t\t})\r\n\t\t\t//自理能力\r\n\t\t\tthis.getZlList();\r\n\t\t\t//病症\r\n\t\t\tthis.getBzList();\r\n\t\t\t//护理需求\r\n\t\t\tthis.getHlList();\r\n\t\t\t//优惠券列表\r\n\t\t\tthis.getMyList()\r\n\t\t\t//服务协议\r\n\t\t\tthis.getxy()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//根据经纬度获取省市区\r\n\t\t\tselectCity(longitude, latitude) {\r\n\t\t\t\tthis.$Request.get('/app/Login/selectCity?lat=' + latitude + '&lng=' + longitude).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tconsole.log(res.data.result.ad_info)\r\n\t\t\t\t\t\tthis.from.province = res.data.result.ad_info.province // 省\r\n\t\t\t\t\t\tthis.from.city = res.data.result.ad_info.city // 市\r\n\t\t\t\t\t\tthis.from.district = res.data.result.ad_info.district // 区\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//选择医院地址\r\n\t\t\tgetAddress() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tconsole.log(res, '1111111')\r\n\t\t\t\t\t\tthat.from.lat = res.latitude\r\n\t\t\t\t\t\tthat.from.lng = res.longitude\r\n\t\t\t\t\t\tthat.from.detailsAddress = res.name\r\n\t\t\t\t\t\tthat.selectCity(that.from.lng, that.from.lat)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail() {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '拉起地图失败，请重试!',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tuploudImg() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\t//选择图片\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tsourceType: ['camera', 'album'],\r\n\t\t\t\t\tsizeType: 'compressed',\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t//循环上传图片\r\n\t\t\t\t\t\tfor (let i = 0; i < res.tempFilePaths.length; i++) {\r\n\t\t\t\t\t\t\tthat.$queue.showLoading(\"上传中...\");\r\n\t\t\t\t\t\t\tuni.uploadFile({ // 上传接口\r\n\t\t\t\t\t\t\t\turl: configdata.APIHOST1 + '/alioss/upload', //真实的接口地址\r\n\t\t\t\t\t\t\t\t// url: 'https://peizhensf.xianmaxiong.com/sqx_fast/alioss/upload',\r\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePaths[i],\r\n\t\t\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\t\t\t\tthat.imgRemarks.push(JSON.parse(uploadFileRes.data).data)\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcloseImg(index) {\r\n\t\t\t\tthis.imgRemarks.splice(index, 1)\r\n\t\t\t},\r\n\t\t\tgetyhqt() {\r\n\t\t\t\tif (this.youhuiList.length == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '暂无可用优惠券',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.showYouhuijuan = true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgotoNav(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/yxpeizhen/info?data=' + encodeURIComponent(JSON.stringify(item)) + '&type=1',\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//协议\r\n\t\t\tgetxy() {\r\n\t\t\t\tthis.$Request.get('/app/common/type/314').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.content = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//服务协议\r\n\t\t\tgotoxieyi() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/xieyi/xieyi?type=2'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcheckboxChange() {\r\n\t\t\t\tconsole.log(this.checked)\r\n\t\t\t\tif (this.checked == false) {\r\n\t\t\t\t\tthis.showxy = true\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.showxy = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tqingkong() {\r\n\t\t\t\tthis.from.youhuiMoney = 0\r\n\t\t\t\tthis.from.couponId = ''\r\n\t\t\t\tthis.couponName = ''\r\n\t\t\t\tthis.showYouhuijuan = false\r\n\t\t\t},\r\n\t\t\tyouhuiPay(e) {\r\n\r\n\t\t\t\tif (Number(this.money) < Number(e.minMoney)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '使用优惠劵，下单金额必须大于' + e.minMoney + '元',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.from.youhuiMoney = e.money\r\n\t\t\t\tthis.from.couponId = e.id\r\n\t\t\t\tthis.couponName = e.money\r\n\t\t\t\tthis.showYouhuijuan = false\r\n\r\n\t\t\t},\r\n\t\t\t//优惠券列表\r\n\t\t\tgetMyList() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tstatus: 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.getT('/app/couponUser/getMyCouponList', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.youhuiList = res.data.records\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//选择支付方式\r\n\t\t\tselectWay: function(id) {\r\n\t\t\t\tthis.openWay = id;\r\n\t\t\t},\r\n\t\t\t//获取服务详情\r\n\t\t\tgetInfo(serviceId) {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tserviceId: serviceId\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.getT(\"/app/hospitalEmploy/getHospitalEmployInfo\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.order = res.data\r\n\t\t\t\t\t\tthis.money = (res.data.money).toFixed(2)\r\n\t\t\t\t\t\tthis.moneys = (res.data.money).toFixed(2)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tpays() {\r\n\t\t\t\tif (this.isPay == false) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet data = this.from\r\n\t\t\t\tif (this.imgRemarks.length > 0) {\r\n\t\t\t\t\tdata.imgRemarks = this.imgRemarks.join(',')\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdata.imgRemarks = ''\r\n\t\t\t\t}\r\n\t\t\t\tdata.hopeTime = data.hopeTimes\r\n\t\t\t\tdata.orderType = 2\r\n\t\t\t\tthis.isPay = false\r\n\t\t\t\tthat.$Request.getT(\"/app/orders/generateOrder\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.showPay = false;\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '付款提示',\r\n\t\t\t\t\t\t\tcontent: '确认支付' + that.money + '元吗?',\r\n\t\t\t\t\t\t\tcomplete: function(re) {\r\n\t\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tif (re.confirm) {\r\n\t\t\t\t\t\t\t\t\tlet classify = 1;\r\n\t\t\t\t\t\t\t\t\tif (that.openWay == 2) { //微信\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 3\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttimeStamp: red.data.timestamp,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tnonceStr: red.data.noncestr,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpackage: red.data.package,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsignType: red.data.signType,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpaySign: red.data.sign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: function(redd) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'carlist')\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.$queue.showToast(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t'支付失败');\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\t\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(ua)\r\n\t\t\t\t\t\t\t\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassify: 4\r\n\t\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat.callPay(red.data);\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 1\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(JSON.stringify(red))\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.setPayment('wxpay', JSON.stringify(red\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.data));\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t\t\t\t} else if (that.openWay == 1) { //支付宝\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 2\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst div = document.createElement('div')\r\n\t\t\t\t\t\t\t\t\t\t\t\tdiv.innerHTML = red.data //此处form就是后台返回接收到的数据\r\n\t\t\t\t\t\t\t\t\t\t\t\tdocument.body.appendChild(div)\r\n\t\t\t\t\t\t\t\t\t\t\t\tdocument.forms[0].submit()\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payOrder\", {\r\n\t\t\t\t\t\t\t\t\t\t\torderId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t\tclassify: 1\r\n\t\t\t\t\t\t\t\t\t\t}).then(red => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.setPayment('alipay', red.data);\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t} else if (that.openWay == 3) { //零钱\r\n\t\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/orders/payMoney\", {\r\n\t\t\t\t\t\t\t\t\t\t\tordersId: res.data.ordersId,\r\n\t\t\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('EditAddress')\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '已取消',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tpay() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.pays()\r\n\r\n\t\t\t},\r\n\t\t\t//提交订单\r\n\t\t\tsubmit() {\r\n\t\t\t\tif (!this.from.patientId) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择被护理人',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.hospitalName) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入医院名称',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.detailsAddress) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择医院地址',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.departmentOneName) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入一级科室',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.departmentTwoName) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入一级科室',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.badNo) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入床号',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.symptom) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择症状',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.selfAbility) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择自理能力',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.nursingNeeds) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择护理需求',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.hopeTime) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择护服务时间',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.serviceNum) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择服务天数',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.from.phone) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入联系电话',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (this.checked == false) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请勾选服务条款同意书',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.showPay = true;\r\n\t\t\t},\r\n\t\t\t//设置护理需求\r\n\t\t\tsethl() {\r\n\t\t\t\tlet arr = []\r\n\t\t\t\tthis.list5.map(item => {\r\n\t\t\t\t\tif (item.se == true) {\r\n\t\t\t\t\t\tarr.push(item.value)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.from.nursingNeeds = arr.join(',')\r\n\t\t\t\tthis.showhl = false\r\n\t\t\t},\r\n\t\t\t//护理需求列表\r\n\t\t\tgetHlList() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '护理需求'\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tres.data.map(item => {\r\n\t\t\t\t\t\t\titem.se = false\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.list5 = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//设置病症\r\n\t\t\tsetbz() {\r\n\t\t\t\tlet arr = []\r\n\t\t\t\tthis.list4.map(item => {\r\n\t\t\t\t\tif (item.se == true) {\r\n\t\t\t\t\t\tarr.push(item.value)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.from.symptom = arr.join(',')\r\n\t\t\t\tthis.showbz = false\r\n\t\t\t},\r\n\t\t\t//选择病症\r\n\t\t\tselect(item) {\r\n\t\t\t\titem.se = !item.se\r\n\t\t\t},\r\n\t\t\t//病症列表\r\n\t\t\tgetBzList() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '病症'\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tres.data.map(item => {\r\n\t\t\t\t\t\t\titem.se = false\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.list4 = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//自理能力選擇\r\n\t\t\tconfirmzl(e) {\r\n\t\t\t\tthis.from.selfAbility = e[0].value\r\n\t\t\t},\r\n\r\n\t\t\t//自理能力列表\r\n\t\t\tgetZlList() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: '自理能力'\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t// let arr = JSON.parse(JSON.stringify(res.data).replace(/code/g, 'label'))\r\n\t\t\t\t\t\tthis.list3 = res.data\r\n\t\t\t\t\t\t// console.log(arr)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgotoHlr() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/my/other/car?type=1'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//获取当前系统时间\r\n\t\t\tgetNowTime(tempminit) {\r\n\t\t\t\tif (!tempminit) {\r\n\t\t\t\t\ttempminit = 0;\r\n\t\t\t\t}\r\n\t\t\t\tvar date = new Date();\r\n\t\t\t\tdate.setMinutes(date.getMinutes() - tempminit);\r\n\t\t\t\tvar year = date.getFullYear(),\r\n\t\t\t\t\tmonth = date.getMonth() + 1,\r\n\t\t\t\t\tday = date.getDate(),\r\n\t\t\t\t\thour = date.getHours() < 10 ? \"0\" + date.getHours() : date.getHours(),\r\n\t\t\t\t\tminute = date.getMinutes() < 10 ? \"0\" + date.getMinutes() : date.getMinutes(),\r\n\t\t\t\t\tsecond = date.getSeconds() < 10 ? \"0\" + date.getSeconds() : date.getSeconds();\r\n\t\t\t\tmonth >= 1 && month <= 9 ? (month = \"0\" + month) : \"\";\r\n\t\t\t\tday >= 0 && day <= 9 ? (day = \"0\" + day) : \"\";\r\n\t\t\t\tvar timer = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;\r\n\t\t\t\t/* console.log(timer); */\r\n\t\t\t\treturn timer;\r\n\t\t\t},\r\n\t\t\t//比较时间大小\r\n\t\t\tdateCompare(startStr, endStr) {\r\n\t\t\t\tvar d1, d2, s, arr, arr1, arr2;\r\n\t\t\t\tif (startStr.length > 10) {\r\n\t\t\t\t\tarr = startStr.split(\" \");\r\n\t\t\t\t\tarr1 = arr[0].split(\"-\");\r\n\t\t\t\t\tarr2 = arr[1].split(\":\");\r\n\t\t\t\t\td1 = new Date(arr1[0], arr1[1] - 1, arr1[2], arr2[0], arr2[1], arr2[2]);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tarr = startStr.split(\"-\");\r\n\t\t\t\t\td1 = new Date(arr[0], arr[1], arr[2]);\r\n\t\t\t\t}\r\n\t\t\t\tif (endStr.length > 10) {\r\n\t\t\t\t\tarr = endStr.split(\" \");\r\n\t\t\t\t\tarr1 = arr[0].split(\"-\");\r\n\t\t\t\t\tarr2 = arr[1].split(\":\");\r\n\t\t\t\t\td2 = new Date(arr1[0], arr1[1] - 1, arr1[2], arr2[0], arr2[1], arr2[2]);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tarr = endStr.split(\"-\");\r\n\t\t\t\t\td2 = new Date(arr[0], arr[1], arr[2]);\r\n\t\t\t\t}\r\n\r\n\t\t\t\ts = d2 - d1;\r\n\t\t\t\tif (s < 0) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\treturn true;\r\n\t\t\t},\r\n\t\t\t//服务时间选择\r\n\t\t\tconfirm(e) {\r\n\r\n\t\t\t\t//选中的时间\r\n\t\t\t\tlet hopeTimes = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + ':00:00'\r\n\t\t\t\t//获取当前时间\r\n\t\t\t\tlet tadayDate = this.getNowTime()\r\n\t\t\t\tlet flag = this.dateCompare(tadayDate, hopeTimes)\r\n\t\t\t\tif (flag == true) { //开始时间小于当前时间\r\n\t\t\t\t\tthis.from.hopeTimes = hopeTimes\r\n\t\t\t\t\tthis.from.hopeTime = e.year + '-' + e.month + '-' + e.day + ' ' + e.hour + '时'\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '服务时间必须大于当前时间',\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tcallPay: function(response) {\r\n\t\t\t\tif (typeof WeixinJSBridge === \"undefined\") {\r\n\t\t\t\t\tif (document.addEventListener) {\r\n\t\t\t\t\t\tdocument.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);\r\n\t\t\t\t\t} else if (document.attachEvent) {\r\n\t\t\t\t\t\tdocument.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));\r\n\t\t\t\t\t\tdocument.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.onBridgeReady(response);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonBridgeReady: function(response) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!response.package) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tWeixinJSBridge.invoke(\r\n\t\t\t\t\t'getBrandWCPayRequest', {\r\n\t\t\t\t\t\t\"appId\": response.appid, //公众号名称，由商户传入\r\n\t\t\t\t\t\t\"timeStamp\": response.timestamp, //时间戳，自1970年以来的秒数\r\n\t\t\t\t\t\t\"nonceStr\": response.noncestr, //随机串\r\n\t\t\t\t\t\t\"package\": response.package,\r\n\t\t\t\t\t\t\"signType\": response.signType, //微信签名方式：\r\n\t\t\t\t\t\t\"paySign\": response.sign //微信签名\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\tif (res.err_msg === \"get_brand_wcpay_request:ok\") {\r\n\t\t\t\t\t\t\t// 使用以上方式判断前端返回,微信团队郑重提示：\r\n\t\t\t\t\t\t\t//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\tuni.removeStorageSync('carlist')\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tWeixinJSBridge.log(response.err_msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t\tsetPayment(name, order) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\tprovider: name,\r\n\t\t\t\t\torderInfo: order, //微信、支付宝订单数据\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/order/index'\r\n\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\tuni.removeStorageSync('carlist')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tthat.isPay = true\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tconsole.log(12)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n\r\n\t.img_remarks {\r\n\t\twidth: 100%;\r\n\t\t/* height: 100rpx; */\r\n\t\t/* background-color: red; */\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.img_remarks-uploud {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tborder: 1px dashed grey;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.img_remarks-item {\r\n\t\twidth: 195rpx;\r\n\t\theight: 195rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tposition: relative;\r\n\r\n\t}\r\n\r\n\t.close-circle-close {\r\n\t\tposition: absolute;\r\n\t\tright: -10rpx;\r\n\t\ttop: -10rpx;\r\n\r\n\t}\r\n\r\n\t.img_remarks-item>image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.img_remarks-uploud-icon {\r\n\t\tmargin-top: 25%;\r\n\t}\r\n\r\n\t.img_remarks-uploud-txt {\r\n\t\tcolor: grey;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.zz {\r\n\t\twidth: 100%;\r\n\t\t// height: 200rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.zz-box {\r\n\t\twidth: 686rpx;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.zz-box-title {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tpadding-left: 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t}\r\n\r\n\t.zz-box-con {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding-left: 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999999;\r\n\t\tletter-spacing: 2px;\r\n\t}\r\n\r\n\t.noyouhui {\r\n\t\tcolor: #FD3C44;\r\n\t\tborder: 1rpx solid #FD3C44;\r\n\t\tborder-radius: 15upx;\r\n\t\tdisplay: inline-block;\r\n\t\tpadding: 5rpx 20rpx;\r\n\t}\r\n\r\n\t.listbox {\r\n\t\t/* background: #FFFFFF; */\r\n\t\tbackground: #F5F5F5;\r\n\t\tmargin: 30rpx 30rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 30rpx 0;\r\n\t}\r\n\r\n\t.btnss {\r\n\t\tcolor: #FD3C44;\r\n\t\tborder: 1rpx solid #FD3C44;\r\n\t\tborder-radius: 55upx;\r\n\t\tpadding: 8upx 25upx;\r\n\t}\r\n\r\n\t.active {\r\n\t\tbackground-color: #468EF8 !important;\r\n\t\tcolor: #ffffff !important;\r\n\t}\r\n\r\n\t.bz {\r\n\t\twidth: 100%;\r\n\r\n\t\t.bz-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\tpadding-bottom: 40rpx;\r\n\r\n\t\t\t.bz-box-title {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.bz-box-select {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\theight: auto;\r\n\r\n\t\t\t\t.bz-box-select-c {\r\n\t\t\t\t\twidth: 626rpx;\r\n\t\t\t\t\theight: 100%;\r\n\r\n\t\t\t\t\t.bz-box-select-item {\r\n\t\t\t\t\t\twidth: 200rpx;\r\n\t\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\t\tbackground-color: #e6ebfe;\r\n\t\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.bz-box-btn {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tbackground-color: #468EF8;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.content {\r\n\t\tpadding-bottom: 300rpx;\r\n\t}\r\n\r\n\t.types {\r\n\t\twidth: 100%;\r\n\t\t// height: 120rpx;\r\n\t\tmargin-top: 30rpx;\r\n\r\n\t\t.types-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tbackground-color: #ffffff;\r\n\r\n\t\t\t.types-box-c {\r\n\t\t\t\twidth: 626rpx;\r\n\t\t\t\theight: 120rpx;\r\n\t\t\t\tborder-bottom: 1rpx solid #F2F2F2;\r\n\t\t\t}\r\n\r\n\t\t\t.types-box-c-l {\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\theight: 44rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.types-box-c-r {\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t// font-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.btn {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\r\n\t\t.btn-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\r\n\t\t\t.btn-box-l {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #FF2D01;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 38rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tspan {\r\n\t\t\t\t\tfont-size: 52rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.btn-box-r {\r\n\t\t\t\twidth: 240rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tbackground-color: #468EF8;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeihu.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customPeihu.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627361\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}