{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue?9f2c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue?8a6a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue?c983", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue?6d62", "uni-app:///my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue?9acc", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue?94e1", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-uni/wxs/wxs.wxs?1f0d", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/components/mescroll-uni/components/mescroll-uni/wxs/wxs.wxs?1af6"], "names": ["name", "mixins", "components", "MescrollTop", "props", "down", "up", "i18n", "top", "topbar", "bottom", "safearea", "height", "bottombar", "type", "default", "sticky", "data", "mescroll", "optDown", "optUp", "downHight", "downRate", "downLoadType", "upLoadType", "isShowEmpty", "isShowToTop", "windowHeight", "windowBottom", "statusBarHeight", "computed", "minHeight", "numTop", "padTop", "numBottom", "padBottom", "isDownReset", "transition", "translateY", "isDownLoading", "downRotate", "downText", "isDownEndSuccess", "textInOffset", "methods", "toPx", "num", "emptyClick", "toTopClick", "created", "inOffset", "vm", "outOffset", "onMoving", "showLoading", "beforeEndDownScroll", "endDownScroll", "clearTimeout", "callback", "showNoMore", "hideUpScroll", "empty", "onShow", "toTop", "MeScroll", "setTimeout", "selector", "uni", "scrollTop", "duration", "console", "mescrollI18n", "destroyed"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAge;AAChe;AACiE;AACL;AACa;;;AAGzE;AACsM;AACtM,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,8bAAM;AACR,EAAE,ucAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kcAAU;AACZ;AACA;;AAEA;AACmR;AACnR,WAAW,oSAAM,iBAAiB,4SAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyxB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+E7yB;AAEA;AAEA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA;AAEA;AAEA;AAAA;EAAA;IAAA;EAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,eAqBA;EACAA;EACAC;EACAC;IACAC;EACA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;MACAC;MACAC;IACA;IACAC;EACA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA,qEACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA,0FACAC,kFACAC;QACA;UACA;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACA;QACA;UACA;YACA;YACAC;UACA;YACA;YACAA;UACA;YACA;YACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACA;EACAC;IACA;IAEA;MACA;MACA5C;QACA6C;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACA;UACAF;UACAA;QACA;QACAG;UACAH;UACAA;QACA;QACAI;UACAJ;UACA;QACA;QACAK;UACAL;UACAA;UACA;YACAM;YACAN;UACA;UACAA;YAAA;YACA;UACA;QACA;QACA;QACAO;UACAP;QACA;MACA;MACA;MACA7C;QACA;QACAgD;UACAH;QACA;QACA;QACAQ;UACAR;QACA;QACA;QACAS;UACAT;QACA;QACA;QACAU;UACAC;YACA;YACAX;UACA;QACA;QACA;QACAY;UACAD;YACA;YACAX;UACA;QACA;QACA;QACAO;UACAP;QACA;MACA;IACA;IAEA;IACA;MACArC;IACA;IACAkD;IACAA;IACAA;IACAA;MACA3D;MACAC;IACA;IACA;MACAD;MACAC;IACA;IACA0D;;IAEA;IACAb;IACA;IACAA;IACA;IACAA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACAA;;IAEA;IACAA;MACA;QACA;QACAc;UAAA;UACA;UACA;YACAC;UACA;YACAA;UAMA;UACAC;YACA;cACA;cACA3D;cACA2D;gBACAC;gBACAC;cACA;YACA;cACAC;YACA;UACA;QACA;MACA;QACA;QACAH;UACAC;UACAC;QACA;MACA;IACA;;IAEA;IACA;MACAlB;IACA;;IAEA;IACAgB;MACA;MACA;MACA;QACAhB;QACAoB;QACAP;MACA;MACA;QACA;QACAb;MACA;MACA;QACA;QACAA;MACA;IACA;EACA;EACAqB;IACA;IACAL;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9ZA;AAAA;AAAA;AAAA;AAAknC,CAAgB,88BAAG,EAAC,C;;;;;;;;;;;ACAtoC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAsa,CAAgB,kdAAG,EAAC,C;;;;;;;;;;;;ACA1b;AAAe;AACf;AACA;AACA;AACA;AACA,M", "file": "my/components/mescroll-uni/components/mescroll-body/mescroll-body.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mescroll-body.vue?vue&type=template&id=26a94fdf&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzE5MiwiYXR0cnMiOnsic3JjIjoiLi4vbWVzY3JvbGwtdW5pL3d4cy93eHMud3hzIiwibW9kdWxlIjoid3hzQml6IiwibGFuZyI6Ind4cyJ9LCJlbmQiOjMxOTJ9LCJyZW5kZXJCaXoiOnsidHlwZSI6InJlbmRlcmpzIiwiY29udGVudCI6IiIsInN0YXJ0IjozMzIwLCJhdHRycyI6eyJtb2R1bGUiOiJyZW5kZXJCaXoiLCJsYW5nIjoianMifSwiZW5kIjozNDIyfX0%3D&\"\nvar renderjs\nimport script from \"./mescroll-body.vue?vue&type=script&lang=js&\"\nexport * from \"./mescroll-body.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mescroll-body.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"../mescroll-uni/wxs/wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5Cchenjiayin%5CDesktop%5Ccode%5Cpeizhen%5Cuniapp-peizhen%5Cmy%5Ccomponents%5Cmescroll-uni%5Ccomponents%5Cmescroll-body%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"my/components/mescroll-uni/components/mescroll-body/mescroll-body.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=template&id=26a94fdf&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzE5MiwiYXR0cnMiOnsic3JjIjoiLi4vbWVzY3JvbGwtdW5pL3d4cy93eHMud3hzIiwibW9kdWxlIjoid3hzQml6IiwibGFuZyI6Ind4cyJ9LCJlbmQiOjMxOTJ9LCJyZW5kZXJCaXoiOnsidHlwZSI6InJlbmRlcmpzIiwiY29udGVudCI6IiIsInN0YXJ0IjozMzIwLCJhdHRycyI6eyJtb2R1bGUiOiJyZW5kZXJCaXoiLCJsYW5nIjoianMifSwiZW5kIjozNDIyfX0%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"mescroll-body mescroll-render-touch\" :class=\"{'mescorll-sticky': sticky}\"\n\t\t:style=\"{'minHeight':minHeight, 'padding-top': padTop, 'padding-bottom': padBottom}\"\n\t\t@touchstart=\"wxsBiz.touchstartEvent\" @touchmove=\"wxsBiz.touchmoveEvent\" @touchend=\"wxsBiz.touchendEvent\"\n\t\t@touchcancel=\"wxsBiz.touchendEvent\" :change:prop=\"wxsBiz.propObserver\" :prop=\"wxsProp\">\n\t\t<!-- 状态栏 -->\n\t\t<view v-if=\"topbar&&statusBarHeight\" class=\"mescroll-topbar\"\n\t\t\t:style=\"{height: statusBarHeight+'px', background: topbar}\"></view>\n\n\t\t<view class=\"mescroll-body-content mescroll-wxs-content\"\n\t\t\t:style=\"{ transform: translateY, transition: transition }\" :change:prop=\"wxsBiz.callObserver\"\n\t\t\t:prop=\"callProp\">\n\t\t\t<!-- 下拉加载区域 (支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-down组件实现)-->\n\t\t\t<!-- <mescroll-down :option=\"mescroll.optDown\" :type=\"downLoadType\" :rate=\"downRate\"></mescroll-down> -->\n\t\t\t<view v-if=\"mescroll.optDown.use\" class=\"mescroll-downwarp\"\n\t\t\t\t:style=\"{'background':mescroll.optDown.bgColor,'color':mescroll.optDown.textColor}\">\n\t\t\t\t<view class=\"downwarp-content\">\n\t\t\t\t\t<view class=\"downwarp-progress mescroll-wxs-progress\" :class=\"{'mescroll-rotate': isDownLoading}\"\n\t\t\t\t\t\t:style=\"{'border-color':mescroll.optDown.textColor, 'transform': downRotate}\"></view>\n\t\t\t\t\t<view class=\"downwarp-tip\">{{downText}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 列表内容 -->\n\t\t\t<slot></slot>\n\n\t\t\t<!-- 空布局 -->\n\t\t\t<!-- <mescroll-empty v-if=\"isShowEmpty\" :option=\"mescroll.optUp.empty\" @emptyclick=\"emptyClick\"></mescroll-empty> -->\n\n\t\t\t<!-- 上拉加载区域 (下拉刷新时不显示, 支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-up组件实现)-->\n\t\t\t<!-- <mescroll-up v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==3\" :option=\"mescroll.optUp\" :type=\"upLoadType\"></mescroll-up> -->\n\t\t\t<view v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==3\" class=\"mescroll-upwarp\"\n\t\t\t\t:style=\"{'background':mescroll.optUp.bgColor,'color':mescroll.optUp.textColor}\">\n\t\t\t\t<!-- 加载中 (此处不能用v-if,否则android小程序快速上拉可能会不断触发上拉回调) -->\n\t\t\t\t<view v-show=\"upLoadType===1\">\n\t\t\t\t\t<view class=\"upwarp-progress mescroll-rotate\" :style=\"{'border-color':mescroll.optUp.textColor}\">\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"upwarp-tip\">{{ mescroll.optUp.textLoading }}</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 无数据 -->\n\t\t\t\t<view v-if=\"upLoadType===2\" class=\"upwarp-nodata\">{{ mescroll.optUp.textNoMore }}</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部是否偏移TabBar的高度(默认仅在H5端的tab页生效) -->\n\t\t<!-- #ifdef H5 -->\n\t\t<view v-if=\"bottombar && windowBottom>0\" class=\"mescroll-bottombar\" :style=\"{height: windowBottom+'px'}\"></view>\n\t\t<!-- #endif -->\n\n\t\t<!-- 适配iPhoneX -->\n\t\t<view v-if=\"safearea\" class=\"mescroll-safearea\"></view>\n\n\t\t<!-- 回到顶部按钮 (fixed元素需写在transform外面,防止降级为absolute)-->\n\t\t<!-- <mescroll-top v-model=\"isShowToTop\" :option=\"mescroll.optUp.toTop\" @click=\"toTopClick\"></mescroll-top> -->\n\n\t\t<!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\n\t\t<!-- renderjs的数据载体,不可写在mescroll-downwarp内部,避免use为false时,载体丢失,无法更新数据 -->\n\t\t<view :change:prop=\"renderBiz.propObserver\" :prop=\"wxsProp\"></view>\n\t\t<!-- #endif -->\n\t</view>\n</template>\n\n<!-- 微信小程序, QQ小程序, app, h5使用wxs -->\n<!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\n<script src=\"../mescroll-uni/wxs/wxs.wxs\" module=\"wxsBiz\" lang=\"wxs\"></script>\n<!-- #endif -->\n\n<!-- app, h5使用renderjs -->\n<!-- #ifdef APP-PLUS || H5 -->\n<script module=\"renderBiz\" lang=\"renderjs\">\n\timport renderBiz from \"../mescroll-uni/wxs/renderjs.js\";\n\texport default {\n\t\tmixins: [renderBiz]\n\t}\n</script>\n<!-- #endif -->\n\n<script>\n\t// 引入mescroll-uni.js,处理核心逻辑\n\timport MeScroll from \"../mescroll-uni/mescroll-uni.js\";\n\t// 引入全局配置\n\timport GlobalOption from \"../mescroll-uni/mescroll-uni-option.js\";\n\t// 引入国际化工具类\n\timport mescrollI18n from '../mescroll-uni/mescroll-i18n.js';\n\t// 引入回到顶部组件\n\timport MescrollTop from \"../mescroll-uni/components/mescroll-top.vue\";\n\t// 引入兼容wxs(含renderjs)写法的mixins\n\timport WxsMixin from \"../mescroll-uni/wxs/mixins.js\";\n\n\t/**\n\t * mescroll-body 基于page滚动的下拉刷新和上拉加载组件, 支持嵌套原生组件, 性能好\n\t * @property {Object} down 下拉刷新的参数配置\n\t * @property {Object} up 上拉加载的参数配置\n\t * @property {Object} i18n 国际化的参数配置\n\t * @property {String, Number} top 下拉布局往下的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\n\t * @property {Boolean, String} topbar 偏移量top是否加上状态栏高度, 默认false (使用场景:取消原生导航栏时,配置此项可留出状态栏的占位, 支持传入字符串背景,如色值,背景图,渐变)\n\t * @property {String, Number} bottom 上拉布局往上的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\n\t * @property {Boolean} safearea 偏移量bottom是否加上底部安全区的距离, 默认false (需要适配iPhoneX时使用)\n\t * @property {Boolean} fixed 是否通过fixed固定mescroll的高度, 默认true\n\t * @property {String, Number} height 指定mescroll最小高度,默认windowHeight,使列表不满屏仍可下拉\n\t * @property {Boolean} bottombar 底部是否偏移TabBar的高度 (仅在H5端的tab页生效)\n\t * @property {Boolean} sticky 是否支持sticky,默认false; 当值配置true时,需避免在mescroll-body标签前面加非定位的元素,否则下拉区域无法隐藏\n\t * @event {Function} init 初始化完成的回调 \n\t * @event {Function} down 下拉刷新的回调\n\t * @event {Function} up 上拉加载的回调 \n\t * @event {Function} emptyclick 点击empty配置的btnText按钮回调\n\t * @event {Function} topclick 点击回到顶部的按钮回调\n\t * @event {Function} scroll 滚动监听 (需在 up 配置 onScroll:true 才生效)\n\t * @example <mescroll-body ref=\"mescrollRef\" @init=\"mescrollInit\" @down=\"downCallback\" @up=\"upCallback\"> ... </mescroll-body>\n\t */\n\texport default {\n\t\tname: 'mescroll-body',\n\t\tmixins: [WxsMixin],\n\t\tcomponents: {\n\t\t\tMescrollTop\n\t\t},\n\t\tprops: {\n\t\t\tdown: Object,\n\t\t\tup: Object,\n\t\t\ti18n: Object,\n\t\t\ttop: [String, Number],\n\t\t\ttopbar: [Boolean, String],\n\t\t\tbottom: [String, Number],\n\t\t\tsafearea: Boolean,\n\t\t\theight: [String, Number],\n\t\t\tbottombar: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tsticky: Boolean\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmescroll: {\n\t\t\t\t\toptDown: {},\n\t\t\t\t\toptUp: {}\n\t\t\t\t}, // mescroll实例\n\t\t\t\tdownHight: 0, //下拉刷新: 容器高度\n\t\t\t\tdownRate: 0, // 下拉比率(inOffset: rate<1; outOffset: rate>=1)\n\t\t\t\tdownLoadType: 0, // 下拉刷新状态: 0(loading前), 1(inOffset), 2(outOffset), 3(showLoading), 4(endDownScroll)\n\t\t\t\tupLoadType: 0, // 上拉加载状态：0（loading前），1（loading中），2（没有更多了,显示END文本提示），3（没有更多了,不显示END文本提示）\n\t\t\t\tisShowEmpty: false, // 是否显示空布局\n\t\t\t\tisShowToTop: false, // 是否显示回到顶部按钮\n\t\t\t\twindowHeight: 0, // 可使用窗口的高度\n\t\t\t\twindowBottom: 0, // 可使用窗口的底部位置\n\t\t\t\tstatusBarHeight: 0 // 状态栏高度\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t// mescroll最小高度,默认windowHeight,使列表不满屏仍可下拉\n\t\t\tminHeight() {\n\t\t\t\treturn this.toPx(this.height || '100%') + 'px'\n\t\t\t},\n\t\t\t// 下拉布局往下偏移的距离 (px)\n\t\t\tnumTop() {\n\t\t\t\treturn this.toPx(this.top)\n\t\t\t},\n\t\t\tpadTop() {\n\t\t\t\treturn this.numTop + 'px';\n\t\t\t},\n\t\t\t// 上拉布局往上偏移 (px)\n\t\t\tnumBottom() {\n\t\t\t\treturn this.toPx(this.bottom);\n\t\t\t},\n\t\t\tpadBottom() {\n\t\t\t\treturn this.numBottom + 'px';\n\t\t\t},\n\t\t\t// 是否为重置下拉的状态\n\t\t\tisDownReset() {\n\t\t\t\treturn this.downLoadType === 3 || this.downLoadType === 4;\n\t\t\t},\n\t\t\t// 过渡\n\t\t\ttransition() {\n\t\t\t\treturn this.isDownReset ? 'transform 300ms' : '';\n\t\t\t},\n\t\t\ttranslateY() {\n\t\t\t\treturn this.downHight > 0 ? 'translateY(' + this.downHight + 'px)' :\n\t\t\t\t''; // transform会使fixed失效,需注意把fixed元素写在mescroll之外\n\t\t\t},\n\t\t\t// 是否在加载中\n\t\t\tisDownLoading() {\n\t\t\t\treturn this.downLoadType === 3\n\t\t\t},\n\t\t\t// 旋转的角度\n\t\t\tdownRotate() {\n\t\t\t\treturn 'rotate(' + 360 * this.downRate + 'deg)'\n\t\t\t},\n\t\t\t// 文本提示\n\t\t\tdownText() {\n\t\t\t\tif (!this.mescroll) return \"\"; // 避免头条小程序初始化时报错\n\t\t\t\tswitch (this.downLoadType) {\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\treturn this.mescroll.optDown.textInOffset;\n\t\t\t\t\tcase 2:\n\t\t\t\t\t\treturn this.mescroll.optDown.textOutOffset;\n\t\t\t\t\tcase 3:\n\t\t\t\t\t\treturn this.mescroll.optDown.textLoading;\n\t\t\t\t\tcase 4:\n\t\t\t\t\t\treturn this.mescroll.isDownEndSuccess ? this.mescroll.optDown.textSuccess : this.mescroll\n\t\t\t\t\t\t\t.isDownEndSuccess == false ? this.mescroll.optDown.textErr : this.mescroll.optDown\n\t\t\t\t\t\t\t.textInOffset;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn this.mescroll.optDown.textInOffset;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t//number,rpx,upx,px,% --> px的数值\n\t\t\ttoPx(num) {\n\t\t\t\tif (typeof num === 'string') {\n\t\t\t\t\tif (num.indexOf('px') !== -1) {\n\t\t\t\t\t\tif (num.indexOf('rpx') !== -1) {\n\t\t\t\t\t\t\t// \"10rpx\"\n\t\t\t\t\t\t\tnum = num.replace('rpx', '');\n\t\t\t\t\t\t} else if (num.indexOf('upx') !== -1) {\n\t\t\t\t\t\t\t// \"10upx\"\n\t\t\t\t\t\t\tnum = num.replace('upx', '');\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// \"10px\"\n\t\t\t\t\t\t\treturn Number(num.replace('px', ''));\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (num.indexOf('%') !== -1) {\n\t\t\t\t\t\t// 传百分比,则相对于windowHeight,传\"10%\"则等于windowHeight的10%\n\t\t\t\t\t\tlet rate = Number(num.replace('%', '')) / 100;\n\t\t\t\t\t\treturn this.windowHeight * rate;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn num ? uni.upx2px(Number(num)) : 0;\n\t\t\t},\n\t\t\t// 点击空布局的按钮回调\n\t\t\temptyClick() {\n\t\t\t\tthis.$emit('emptyclick', this.mescroll);\n\t\t\t},\n\t\t\t// 点击回到顶部的按钮回调\n\t\t\ttoTopClick() {\n\t\t\t\tthis.mescroll.scrollTo(0, this.mescroll.optUp.toTop.duration); // 执行回到顶部\n\t\t\t\tthis.$emit('topclick', this.mescroll); // 派发点击回到顶部按钮的回调\n\t\t\t}\n\t\t},\n\t\t// 使用created初始化mescroll对象; 如果用mounted部分css样式编译到H5会失效\n\t\tcreated() {\n\t\t\tlet vm = this;\n\n\t\t\tlet diyOption = {\n\t\t\t\t// 下拉刷新的配置\n\t\t\t\tdown: {\n\t\t\t\t\tinOffset() {\n\t\t\t\t\t\tvm.downLoadType = 1; // 下拉的距离进入offset范围内那一刻的回调 (自定义mescroll组件时,此行不可删)\n\t\t\t\t\t},\n\t\t\t\t\toutOffset() {\n\t\t\t\t\t\tvm.downLoadType = 2; // 下拉的距离大于offset那一刻的回调 (自定义mescroll组件时,此行不可删)\n\t\t\t\t\t},\n\t\t\t\t\tonMoving(mescroll, rate, downHight) {\n\t\t\t\t\t\t// 下拉过程中的回调,滑动过程一直在执行;\n\t\t\t\t\t\tvm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\n\t\t\t\t\t\tvm.downRate = rate; //下拉比率 (inOffset: rate<1; outOffset: rate>=1)\n\t\t\t\t\t},\n\t\t\t\t\tshowLoading(mescroll, downHight) {\n\t\t\t\t\t\tvm.downLoadType = 3; // 显示下拉刷新进度的回调 (自定义mescroll组件时,此行不可删)\n\t\t\t\t\t\tvm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\n\t\t\t\t\t},\n\t\t\t\t\tbeforeEndDownScroll(mescroll) {\n\t\t\t\t\t\tvm.downLoadType = 4;\n\t\t\t\t\t\treturn mescroll.optDown.beforeEndDelay // 延时结束的时长\n\t\t\t\t\t},\n\t\t\t\t\tendDownScroll() {\n\t\t\t\t\t\tvm.downLoadType = 4; // 结束下拉 (自定义mescroll组件时,此行不可删)\n\t\t\t\t\t\tvm.downHight = 0; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\n\t\t\t\t\t\tif (vm.downResetTimer) {\n\t\t\t\t\t\t\tclearTimeout(vm.downResetTimer);\n\t\t\t\t\t\t\tvm.downResetTimer = null\n\t\t\t\t\t\t} // 移除重置倒计时\n\t\t\t\t\t\tvm.downResetTimer = setTimeout(() => { // 过渡动画执行完毕后,需重置为0的状态,避免下次inOffset不及时显示textInOffset\n\t\t\t\t\t\t\tif (vm.downLoadType === 4) vm.downLoadType = 0\n\t\t\t\t\t\t}, 300)\n\t\t\t\t\t},\n\t\t\t\t\t// 派发下拉刷新的回调\n\t\t\t\t\tcallback: function(mescroll) {\n\t\t\t\t\t\tvm.$emit('down', mescroll);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t// 上拉加载的配置\n\t\t\t\tup: {\n\t\t\t\t\t// 显示加载中的回调\n\t\t\t\t\tshowLoading() {\n\t\t\t\t\t\tvm.upLoadType = 1;\n\t\t\t\t\t},\n\t\t\t\t\t// 显示无更多数据的回调\n\t\t\t\t\tshowNoMore() {\n\t\t\t\t\t\tvm.upLoadType = 2;\n\t\t\t\t\t},\n\t\t\t\t\t// 隐藏上拉加载的回调\n\t\t\t\t\thideUpScroll(mescroll) {\n\t\t\t\t\t\tvm.upLoadType = mescroll.optUp.hasNext ? 0 : 3;\n\t\t\t\t\t},\n\t\t\t\t\t// 空布局\n\t\t\t\t\tempty: {\n\t\t\t\t\t\tonShow(isShow) {\n\t\t\t\t\t\t\t// 显示隐藏的回调\n\t\t\t\t\t\t\tvm.isShowEmpty = isShow;\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\t// 回到顶部\n\t\t\t\t\ttoTop: {\n\t\t\t\t\t\tonShow(isShow) {\n\t\t\t\t\t\t\t// 显示隐藏的回调\n\t\t\t\t\t\t\tvm.isShowToTop = isShow;\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\t// 派发上拉加载的回调\n\t\t\t\t\tcallback: function(mescroll) {\n\t\t\t\t\t\tvm.$emit('up', mescroll);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tlet i18nType = mescrollI18n.getType() // 当前语言类型\n\t\t\tlet i18nOption = {\n\t\t\t\ttype: i18nType\n\t\t\t} // 国际化配置\n\t\t\tMeScroll.extend(i18nOption, vm.i18n) // 具体页面的国际化配置\n\t\t\tMeScroll.extend(i18nOption, GlobalOption.i18n) // 全局的国际化配置\n\t\t\tMeScroll.extend(diyOption, i18nOption[i18nType]); // 混入国际化配置\n\t\t\tMeScroll.extend(diyOption, {\n\t\t\t\tdown: GlobalOption.down,\n\t\t\t\tup: GlobalOption.up\n\t\t\t}); // 混入全局的配置\n\t\t\tlet myOption = JSON.parse(JSON.stringify({\n\t\t\t\tdown: vm.down,\n\t\t\t\tup: vm.up\n\t\t\t})); // 深拷贝,避免对props的影响\n\t\t\tMeScroll.extend(myOption, diyOption); // 混入具体界面的配置\n\n\t\t\t// 初始化MeScroll对象\n\t\t\tvm.mescroll = new MeScroll(myOption, true); // 传入true,标记body为滚动区域\n\t\t\t// 挂载语言包\n\t\t\tvm.mescroll.i18n = i18nOption;\n\t\t\t// init回调mescroll对象\n\t\t\tvm.$emit('init', vm.mescroll);\n\n\t\t\t// 设置高度\n\t\t\tconst sys = uni.getSystemInfoSync();\n\t\t\tif (sys.windowHeight) vm.windowHeight = sys.windowHeight;\n\t\t\tif (sys.windowBottom) vm.windowBottom = sys.windowBottom;\n\t\t\tif (sys.statusBarHeight) vm.statusBarHeight = sys.statusBarHeight;\n\t\t\t// 使down的bottomOffset生效\n\t\t\tvm.mescroll.setBodyHeight(sys.windowHeight);\n\n\t\t\t// 因为使用的是page的scroll,这里需自定义scrollTo\n\t\t\tvm.mescroll.resetScrollTo((y, t) => {\n\t\t\t\tif (typeof y === 'string') {\n\t\t\t\t\t// 滚动到指定view (y为css选择器)\n\t\t\t\t\tsetTimeout(() => { // 延时确保view已渲染; 不使用$nextTick\n\t\t\t\t\t\tlet selector;\n\t\t\t\t\t\tif (y.indexOf('#') == -1 && y.indexOf('.') == -1) {\n\t\t\t\t\t\t\tselector = '#' + y // 不带#和. 则默认为id选择器\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tselector = y\n\t\t\t\t\t\t\t// #ifdef APP-PLUS || H5 || MP-ALIPAY || MP-DINGTALK\n\t\t\t\t\t\t\tif (y.indexOf('>>>') != -1) { // 不支持跨自定义组件的后代选择器 (转为普通的选择器即可跨组件查询)\n\t\t\t\t\t\t\t\tselector = y.split('>>>')[1].trim()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.createSelectorQuery().select(selector).boundingClientRect(function(rect) {\n\t\t\t\t\t\t\tif (rect) {\n\t\t\t\t\t\t\t\tlet top = rect.top\n\t\t\t\t\t\t\t\ttop += vm.mescroll.getScrollTop()\n\t\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\t\tscrollTop: top,\n\t\t\t\t\t\t\t\t\tduration: t\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.error(selector + ' does not exist');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}).exec()\n\t\t\t\t\t}, 30)\n\t\t\t\t} else {\n\t\t\t\t\t// 滚动到指定位置 (y必须为数字)\n\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\tscrollTop: y,\n\t\t\t\t\t\tduration: t\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// 具体的界面如果不配置up.toTop.safearea,则取本vue的safearea值\n\t\t\tif (vm.up && vm.up.toTop && vm.up.toTop.safearea != null) {} else {\n\t\t\t\tvm.mescroll.optUp.toTop.safearea = vm.safearea;\n\t\t\t}\n\n\t\t\t// 全局配置监听\n\t\t\tuni.$on(\"setMescrollGlobalOption\", options => {\n\t\t\t\tif (!options) return;\n\t\t\t\tlet i18nType = options.i18n ? options.i18n.type : null\n\t\t\t\tif (i18nType && vm.mescroll.i18n.type != i18nType) {\n\t\t\t\t\tvm.mescroll.i18n.type = i18nType\n\t\t\t\t\tmescrollI18n.setType(i18nType)\n\t\t\t\t\tMeScroll.extend(options, vm.mescroll.i18n[i18nType])\n\t\t\t\t}\n\t\t\t\tif (options.down) {\n\t\t\t\t\tlet down = MeScroll.extend({}, options.down)\n\t\t\t\t\tvm.mescroll.optDown = MeScroll.extend(down, vm.mescroll.optDown)\n\t\t\t\t}\n\t\t\t\tif (options.up) {\n\t\t\t\t\tlet up = MeScroll.extend({}, options.up)\n\t\t\t\t\tvm.mescroll.optUp = MeScroll.extend(up, vm.mescroll.optUp)\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tdestroyed() {\n\t\t\t// 注销全局配置监听\n\t\t\tuni.$off(\"setMescrollGlobalOption\")\n\t\t}\n\t};\n</script>\n\n<style>\n\t@import \"../mescroll-body/mescroll-body.css\";\n\t@import \"../mescroll-uni/components/mescroll-down.css\";\n\t@import \"../mescroll-uni/components/mescroll-up.css\";\n</style>\n", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627715\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5Cchenjiayin%5CDesktop%5Ccode%5Cpeizhen%5Cuniapp-peizhen%5Cmy%5Ccomponents%5Cmescroll-uni%5Ccomponents%5Cmescroll-body%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"; export default mod; export * from \"-!../../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5Cchenjiayin%5CDesktop%5Ccode%5Cpeizhen%5Cuniapp-peizhen%5Cmy%5Ccomponents%5Cmescroll-uni%5Ccomponents%5Cmescroll-body%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       Component.options.wxsCallMethods.push('wxsCall')\n     }"], "sourceRoot": ""}