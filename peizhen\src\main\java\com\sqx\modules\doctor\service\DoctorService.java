package com.sqx.modules.doctor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.doctor.entity.Doctor;

/**
 * <p>
 * 医生管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
public interface DoctorService extends IService<Doctor> {

    /**
     * 分页查询医生列表
     */
    PageUtils queryPage(Integer page, Integer limit, String doctorName, 
                       Long hospitalId, Long departmentId, Integer workStatus, Integer isEnable);

    /**
     * 添加医生
     */
    Result addDoctor(Doctor doctor);

    /**
     * 修改医生
     */
    Result updateDoctor(Doctor doctor);

    /**
     * 删除医生
     */
    Result deleteDoctor(Long[] doctorIds);
}
