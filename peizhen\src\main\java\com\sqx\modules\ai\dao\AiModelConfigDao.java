package com.sqx.modules.ai.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sqx.modules.ai.entity.AiModelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI模型配置DAO
 */
@Mapper
public interface AiModelConfigDao extends BaseMapper<AiModelConfig> {

    /**
     * 查询启用的模型列表
     */
    List<AiModelConfig> selectEnabledModels();

    /**
     * 根据模型代码查询模型配置
     */
    AiModelConfig selectByModelCode(@Param("modelCode") String modelCode);
}
