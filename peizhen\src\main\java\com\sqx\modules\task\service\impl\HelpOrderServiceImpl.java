package com.sqx.modules.task.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.entity.UserMoney;
import com.sqx.modules.app.entity.UserMoneyDetails;
import com.sqx.modules.app.service.UserMoneyDetailsService;
import com.sqx.modules.app.service.UserMoneyService;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.message.entity.MessageInfo;
import com.sqx.modules.message.service.MessageService;
import com.sqx.modules.pay.controller.app.AliPayController;
import com.sqx.modules.pay.service.WxService;
import com.sqx.modules.taking.entity.Game;
import com.sqx.modules.taking.service.GameService;
import com.sqx.modules.task.dao.HelpOrderDao;
import com.sqx.modules.task.entity.HelpOrder;
import com.sqx.modules.task.entity.HelpTake;
import com.sqx.modules.task.service.HelpOrderService;
import com.sqx.modules.task.service.HelpTakeService;
import com.sqx.modules.utils.AmountCalUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 跑腿订单
 */
@Service
public class HelpOrderServiceImpl extends ServiceImpl<HelpOrderDao, HelpOrder> implements HelpOrderService {

    /** 跑腿订单 */
    @Autowired
    private HelpOrderDao helpOrderDao;
    /** 用户金额 */
    @Autowired
    private UserMoneyService userMoneyService;
    /** 用户金额明细 */
    @Autowired
    private UserMoneyDetailsService userMoneyDetailsService;
    @Autowired
    private UserService userService;
    @Autowired
    private HelpTakeService helpTakeService;

    @Autowired
    private MessageService messageService;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private GameService gameService;
    @Autowired
    private WxService wxService;
    @Autowired
    private AliPayController aliPayController;



    @Override
    public Result selectHelpOrder(int page,int limit){
        return Result.success().put("data",helpOrderDao.selectList(new QueryWrapper<>()));
    }

    @Override
    public Result selectCreateHelpOrder(int page,int limit,Integer status,Long userId,Long gameId){
        Page<HelpOrder> pages=new Page<>(page,limit);
        IPage<HelpOrder> helpOrderIPage = helpOrderDao.selectPage(pages, new QueryWrapper<HelpOrder>().eq(status != 0, "status", status).eq("user_id", userId).eq(gameId!=null,"game_id",gameId).orderByDesc("create_time"));
        List<HelpOrder> records = helpOrderIPage.getRecords();
        for(HelpOrder helpOrder:records){
            if(helpOrder.getGameId()!=null){
                Game byId = gameService.getById(helpOrder.getGameId());
                if(byId!=null){
                    helpOrder.setServiceName(byId.getGameName());
                }
            }
            if(helpOrder.getHelpTakeId()!=null){
                HelpTake helpTake = helpTakeService.selectHelpTakeById(helpOrder.getHelpTakeId());
                helpOrder.setHelpTake(helpTake);
            }
            UserEntity userEntity = userService.selectUserById(helpOrder.getUserId());
            helpOrder.setUser(userEntity);
        }
        return Result.success().put("data",new PageUtils(helpOrderIPage));
    }

    @Override
    public Result selectStatusHelpOrder(int page,int limit,String phone,String content,Integer status,Long gameId){
        phone = phone.trim();
        Page<HelpOrder> pages=new Page<>(page,limit);
        IPage<HelpOrder> helpOrderIPage = helpOrderDao.selectStatusHelpOrder(pages,phone,content,status,gameId);
        List<HelpOrder> records = helpOrderIPage.getRecords();
        for(HelpOrder helpOrder:records){
            if(helpOrder.getHelpTakeId()!=null){
                HelpTake helpTake = helpTakeService.selectHelpTakeById(helpOrder.getHelpTakeId());
                helpOrder.setHelpTake(helpTake);
            }
            UserEntity userEntity = userService.selectUserById(helpOrder.getUserId());
            helpOrder.setUser(userEntity);
        }
        return Result.success().put("data",new PageUtils(helpOrderIPage));
    }


    @Override
    public Integer countHelpOrderByCreateTime( String time, Integer flag){
        return helpOrderDao.countHelpOrderByCreateTime(time, flag);
    }


    @Override
    public Double sumPrice( String time, Integer flag){
        return helpOrderDao.sumPrice(time, flag);
    }

    @Override
    public Result selectNewHelpOrderList(int page,int limit,Long gameId,String latitude,String longitude,Integer sort){
        Page<HelpOrder> pages=new Page<>(page,limit);
        IPage<HelpOrder> helpOrderIPage = helpOrderDao.selectNewHelpOrderList(pages,gameId,latitude,longitude,sort);
        return Result.success().put("data",new PageUtils(helpOrderIPage));
    }

    @Override
    public Result selectHelpOrderByClassifyList(int page,int limit,Long classifyId,Long gameId){
        Page<HelpOrder> pages=new Page<>(page,limit);
        IPage<HelpOrder> helpOrderIPage = helpOrderDao.selectHelpOrderByClassifyList(pages,classifyId,gameId);
        return Result.success().put("data",new PageUtils(helpOrderIPage));
    }

    @Override
    public Result updateHelpOrderByStatus(String ids,Integer status,String content){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for(String id:ids.split(",")){
            HelpOrder helpOrder = helpOrderDao.selectById(Long.parseLong(id));
            if(helpOrder!=null && helpOrder.getStatus()==1){
                UserEntity userEntity=userService.selectUserById(helpOrder.getUserId());
                if(status==1){
                    helpOrder.setStatus(2);
                    helpOrderDao.updateById(helpOrder);
                    MessageInfo messageInfo=new MessageInfo();
                    messageInfo.setContent("您好，您的万能任务申请已经通过了！");
                    messageInfo.setTitle("万能任务结果通知！");
                    messageInfo.setState(String.valueOf(5));
                    messageInfo.setUserName(userEntity.getUserName());
                    messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
                    messageInfo.setCreateAt(sdf.format(new Date()));
                    messageService.saveBody(messageInfo);
                }else{
                    helpOrder.setStatus(5);
                    helpOrder.setCause(content);
                    helpOrderDao.updateById(helpOrder);
                    MessageInfo messageInfo=new MessageInfo();
                    messageInfo.setContent("您好，您的万能任务申请被拒绝了！原因："+content);
                    messageInfo.setTitle("万能任务结果通知！");
                    messageInfo.setState(String.valueOf(5));
                    messageInfo.setUserName(userEntity.getUserName());
                    messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
                    messageInfo.setCreateAt(sdf.format(new Date()));
                    messageService.saveBody(messageInfo);
                    userMoneyService.updateMoney(1,helpOrder.getUserId(),helpOrder.getMoney());
                    UserMoneyDetails userMoneyDetails=new UserMoneyDetails();
                    userMoneyDetails.setUserId(helpOrder.getUserId());
                    userMoneyDetails.setTitle("[万能任务退款]："+helpOrder.getContent());
                    userMoneyDetails.setContent("万能任务退款："+helpOrder.getMoney());
                    userMoneyDetails.setType(1);
                    userMoneyDetails.setMoney(helpOrder.getMoney());
                    userMoneyDetails.setCreateTime(sdf.format(new Date()));
                    userMoneyDetailsService.save(userMoneyDetails);
                }
            }
        }
        return Result.success();
    }

    @Override
    public Result selectHelpOrderByContentList(int page,int limit,String content,Long gameId){
        Page<HelpOrder> pages=new Page<>(page,limit);
        IPage<HelpOrder> helpOrderIPage = helpOrderDao.selectHelpOrderByContentList(pages,content,gameId);
        return Result.success().put("data",new PageUtils(helpOrderIPage));
    }


    @Override
    public Result saveBody(HelpOrder helpOrder){
        if(helpOrder.getCommission().doubleValue()<=0){
            return Result.error("金额必须大于0");
        }
        UserEntity userEntity = userService.selectUserById(helpOrder.getUserId());
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date=sdf.format(new Date());
        helpOrder.setStatus(2);
        helpOrder.setCreateTime(date);
        UserMoney userMoney = userMoneyService.selectUserMoneyByUserId(helpOrder.getUserId());
        if(userMoney.getMoney().doubleValue()>=helpOrder.getCommission().doubleValue()){
            helpOrder.setOrderNo(getGeneralOrder());
            helpOrder.setMoney(helpOrder.getCommission());
            CommonInfo one = commonInfoService.findOne(120);
            String value = one.getValue();
            Double mul = AmountCalUtils.mul(helpOrder.getCommission().doubleValue(), Double.parseDouble(value));
            BigDecimal sub = AmountCalUtils.sub(helpOrder.getMoney(), BigDecimal.valueOf(mul));
            helpOrder.setCommission(sub);
            helpOrder.setPayWay(1);
            helpOrderDao.insert(helpOrder);
            userMoneyService.updateMoney(2,helpOrder.getUserId(),helpOrder.getMoney());
            UserMoneyDetails userMoneyDetails=new UserMoneyDetails();
            userMoneyDetails.setUserId(helpOrder.getUserId());
            userMoneyDetails.setTitle("万能任务");
            userMoneyDetails.setContent("万能任务扣款："+helpOrder.getMoney());
            userMoneyDetails.setType(2);
            userMoneyDetails.setMoney(helpOrder.getMoney());
            userMoneyDetails.setCreateTime(date);
            userMoneyDetailsService.save(userMoneyDetails);
            if (userEntity.getClientid() != null) {
                userService.pushToSingle("派发订单","您的订单已经派发成功！" , userEntity.getClientid());
            }
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setContent("您的订单已经派发成功！");
            messageInfo.setTitle("订单通知");
            messageInfo.setState(String.valueOf(5));
            messageInfo.setUserName(userEntity.getUserName());
            messageInfo.setUserId(String.valueOf(userEntity.getUserId()));
            messageService.saveBody(messageInfo);
        }else{
            return Result.error("账户金额不足，请充值！");
        }
        return Result.success();
    }

    @Override
    public Result saveBodys(HelpOrder helpOrder){
        if(helpOrder.getCommission().doubleValue()<=0){
            return Result.error("金额必须大于0");
        }
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date=sdf.format(new Date());
        helpOrder.setStatus(2);
        helpOrder.setCreateTime(date);
        helpOrder.setMoney(helpOrder.getCommission());
        helpOrderDao.insert(helpOrder);
        return Result.success();
    }


    @Override
    public Result updateHelpOrderById(HelpOrder helpOrder){
        HelpOrder helpOrder1 = helpOrderDao.selectById(helpOrder.getId());
        if(!helpOrder1.getCommission().equals(helpOrder.getCommission())){
            return Result.error("已发布任务不能修改金额！");
        }
        if(helpOrder1.getStatus()==3 || helpOrder1.getStatus()==4){
            return Result.error("当前状态不允许修改！");
        }

        helpOrderDao.updateById(helpOrder);
        return Result.success();
    }

    @Override
    public Result updateHelpOrderByIds(HelpOrder helpOrder){
        helpOrderDao.updateById(helpOrder);
        return Result.success();
    }

    @Override
    public Result deleteById(Long id){
        HelpOrder helpOrder = helpOrderDao.selectById(id);
        if(helpOrder.getStatus()!=4 && helpOrder.getStatus()!=5 ){
            return Result.error("当前状态不允许删除！");
        }
        helpOrderDao.deleteById(id);
        return Result.success();
    }


    @Override
    public HelpOrder selectHelpOrderById(Long helpOrderId){
        HelpOrder helpOrder = helpOrderDao.selectById(helpOrderId);
        if(helpOrder.getGameId()!=null){
            Game byId = gameService.getById(helpOrder.getGameId());
            if(byId!=null){
                helpOrder.setServiceName(byId.getGameName());
            }
        }
        UserEntity userEntity = userService.selectUserById(helpOrder.getUserId());
        helpOrder.setUser(userEntity);
        HelpTake helpTake = helpTakeService.selectHelpTakeById(helpOrder.getHelpTakeId());
        if(helpTake!=null){
            helpOrder.setHelpTake(helpTake);
        }
        return helpOrder;
    }


    private String getGeneralOrder(){
        Date date=new Date();
        String newString = String.format("%0"+4+"d", (int)((Math.random()*9+1)*1000));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = sdf.format(date);
        return format+newString;
    }


}
