package com.sqx.modules.ai.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.modules.ai.entity.AiConversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * AI对话会话DAO
 */
@Mapper
public interface AiConversationDao extends BaseMapper<AiConversation> {

    /**
     * 分页查询用户的对话会话列表
     */
    IPage<AiConversation> selectUserConversations(IPage<AiConversation> page, @Param("userId") Long userId);

    /**
     * 管理后台分页查询对话会话列表（包含用户信息）
     */
    IPage<AiConversation> selectConversationListWithUser(IPage<AiConversation> page,
                                                        @Param("title") String title,
                                                        @Param("modelCode") String modelCode,
                                                        @Param("userSearch") String userSearch);

    /**
     * 更新会话的消息统计
     */
    int updateConversationStats(@Param("conversationId") Long conversationId,
                               @Param("messageCount") Integer messageCount,
                               @Param("totalTokens") Integer totalTokens);
}
