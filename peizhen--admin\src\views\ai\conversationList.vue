<template>
  <div>
    <!-- 搜索条件 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="对话标题">
          <el-input v-model="searchForm.title" placeholder="请输入对话标题" clearable></el-input>
        </el-form-item>
        <el-form-item label="模型代码">
          <el-select v-model="searchForm.modelCode" placeholder="请选择模型" clearable>
            <el-option
              v-for="model in modelList"
              :key="model.modelCode"
              :label="model.modelName"
              :value="model.modelCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户信息">
          <el-input v-model="searchForm.userId" placeholder="请输入用户ID/用户名/手机号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="dataListLoading" :data="dataList" border style="width: 100%;">
      <el-table-column prop="id" header-align="center" align="center" label="对话ID" width="100"></el-table-column>
      <el-table-column prop="title" header-align="center" align="center" label="对话标题" min-width="200"></el-table-column>
      <el-table-column header-align="center" align="center" label="用户信息" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.userName || scope.row.phone">
            <div>{{ scope.row.userName || '未设置' }}</div>
            <div style="color: #999; font-size: 12px;">{{ scope.row.phone || '未绑定手机' }}</div>
          </div>
          <div v-else style="color: #999;">
            用户ID: {{ scope.row.userId }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="modelCode" header-align="center" align="center" label="使用模型" width="150">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.modelCode === 'deepseek-chat'" type="primary">DeepSeek</el-tag>
          <el-tag v-else-if="scope.row.modelCode === 'kimi-chat'" type="success">Kimi</el-tag>
          <el-tag v-else type="info">{{ scope.row.modelCode }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="messageCount" header-align="center" align="center" label="消息数量" width="100"></el-table-column>
      <el-table-column prop="totalTokens" header-align="center" align="center" label="Token消耗" width="100"></el-table-column>
      <el-table-column prop="createTime" header-align="center" align="center" label="创建时间" width="160"></el-table-column>
      <el-table-column prop="updateTime" header-align="center" align="center" label="更新时间" width="160"></el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetail(scope.row.id)">查看详情</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 对话详情弹窗 -->
    <el-dialog
      title="对话详情"
      :visible.sync="detailVisible"
      width="80%"
      :close-on-click-modal="false">
      <div v-if="conversationDetail">
        <div class="conversation-info">
          <el-row>
            <el-col :span="8">
              <strong>对话标题：</strong>{{ conversationDetail.title }}
            </el-col>
            <el-col :span="8">
              <strong>用户信息：</strong>
              <span v-if="conversationDetail.userName || conversationDetail.phone">
                {{ conversationDetail.userName || '未设置' }}
                <span v-if="conversationDetail.phone" style="color: #999;">（{{ conversationDetail.phone }}）</span>
              </span>
              <span v-else style="color: #999;">用户ID: {{ conversationDetail.userId }}</span>
            </el-col>
            <el-col :span="8">
              <strong>使用模型：</strong>{{ conversationDetail.modelCode }}
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px;">
            <el-col :span="8">
              <strong>消息数量：</strong>{{ conversationDetail.messageCount }}
            </el-col>
            <el-col :span="8">
              <strong>Token消耗：</strong>{{ conversationDetail.totalTokens }}
            </el-col>
            <el-col :span="8">
              <strong>创建时间：</strong>{{ conversationDetail.createTime }}
            </el-col>
          </el-row>
        </div>
        
        <el-divider>对话消息</el-divider>
        
        <div class="message-list">
          <div
            v-for="message in conversationDetail.messages"
            :key="message.id"
            :class="['message-item', message.role]">
            <div class="message-header">
              <span class="role">{{ message.role === 'user' ? '用户' : 'AI助手' }}</span>
              <span class="time">{{ message.createTime }}</span>
            </div>
            <div class="message-content">{{ message.content }}</div>
            <div v-if="message.role === 'assistant' && message.totalTokens" class="token-info">
              Token消耗: {{ message.totalTokens }} (输入: {{ message.promptTokens }}, 输出: {{ message.completionTokens }})
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        title: '',
        modelCode: '',
        userId: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      modelList: [],
      detailVisible: false,
      conversationDetail: null
    }
  },
  activated() {
    this.getModelList()
    this.getDataList()
  },
  methods: {
    // 获取模型列表
    getModelList() {
      this.$http({
        url: this.$http.adornUrl('/ai/model/enabled'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.modelList = data.data
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/ai/conversation/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          title: this.searchForm.title,
          modelCode: this.searchForm.modelCode,
          userId: this.searchForm.userId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.data.list
          this.totalPage = data.data.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        title: '',
        modelCode: '',
        userId: ''
      }
      this.getDataList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 查看详情
    viewDetail(conversationId) {
      this.$http({
        url: this.$http.adornUrl(`/ai/conversation/${conversationId}`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.conversationDetail = data.data
          this.detailVisible = true
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定要删除该对话记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/ai/conversation/delete/${id}`),
          method: 'post'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.search-form {
  background: #f5f5f5;
  padding: 20px;
  margin-bottom: 20px;
}

.conversation-info {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.message-list {
  max-height: 500px;
  overflow-y: auto;
}

.message-item {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
}

.message-item.user {
  background: #e3f2fd;
  margin-left: 20%;
}

.message-item.assistant {
  background: #f3e5f5;
  margin-right: 20%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.role {
  font-weight: bold;
}

.message-content {
  white-space: pre-wrap;
  line-height: 1.5;
}

.token-info {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}
</style>
