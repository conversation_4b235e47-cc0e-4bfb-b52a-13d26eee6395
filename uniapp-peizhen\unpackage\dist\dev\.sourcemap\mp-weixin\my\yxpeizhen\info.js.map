{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/yxpeizhen/info.vue?da53", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/yxpeizhen/info.vue?450a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/yxpeizhen/info.vue?d8a7", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/yxpeizhen/info.vue?3db4", "uni-app:///my/yxpeizhen/info.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/yxpeizhen/info.vue?5091", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/yxpeizhen/info.vue?b1d2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "info", "current", "content", "content2", "type", "tui<PERSON><PERSON>", "tuiImage", "invitationCode", "modularId", "onShareAppMessage", "path", "title", "imageUrl", "onShareTimeline", "stringify", "onLoad", "that", "console", "methods", "tabs", "goNav", "uni", "url", "hospitalName", "serviceType", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8DvvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC,8CACAH,mEACAP;MAAA;MACAW;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;MACAH,kGACAI;MAAA;MACAH;MACAC;IACA;EACA;EACAG;IACA;IACA;IACA;IACA;IACA;MACAC;IACA;IAEA;MACA;MACAA;IACA;IAEA;MACAA;MACAA;QAAA;QACA;UACAA;QACA;MACA;MACAA;QAAA;QACA;UACAA;QACA;MACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA,2DACA;IACA,wDACA;IACAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACA;UAAA;UACAC;YACAC,kFACAC;UACA;QACA;UAAA;UACA;YACA;cAAA;cACAF;gBACAC,gFACAE;cACA;cACA;YACA;cAAA;cACAH;cACAA;gBACAC,4EACAR;cACA;cACA;YACA;cAAA;cACAO;gBACAC,gFACAE;cACA;cACA;YACA;cACA;UAAA;QAEA;MAEA;QACAH;UACAV;UACAT;UACAuB;YACA;cACAR;cACAI;gBACAC;cACA;YACA;cACAL;YACA;UACA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClMA;AAAA;AAAA;AAAA;AAAs3C,CAAgB,2tCAAG,EAAC,C;;;;;;;;;;;ACA14C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/yxpeizhen/info.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/yxpeizhen/info.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./info.vue?vue&type=template&id=31781ad4&\"\nvar renderjs\nimport script from \"./info.vue?vue&type=script&lang=js&\"\nexport * from \"./info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./info.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/yxpeizhen/info.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=template&id=31781ad4&\"", "var components\ntry {\n  components = {\n    uParse: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-parse/u-parse\" */ \"@/uview-ui/components/u-parse/u-parse.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 200rpx;\">\r\n\t\t<!-- 背景图 -->\r\n\t\t<view class=\"bgImg\">\r\n\t\t\t<image :src=\"info.backgroundImg\" style=\"width: 100%;height: 480rpx;position: absolute;top: 0;left: 0;\"\r\n\t\t\t\tmode=\"\"></image>\r\n\t\t\t<view class=\"bgImg-box\">\r\n\t\t\t\t<view class=\"bgImg-box-title\">\r\n\t\t\t\t\t{{info.serviceName}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bgImg-box-labs flex align-center\">\r\n\t\t\t\t\t<image src=\"../../static/images/ico.png\" style=\"width: 56rpx;height: 34rpx;margin-right: 10rpx;\"\r\n\t\t\t\t\t\tmode=\"\"></image>\r\n\t\t\t\t\t<view class=\"\" style=\"width: 90%;\">\r\n\t\t\t\t\t\t{{info.title}}\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info flex justify-center\">\r\n\t\t\t\t<view class=\"info-box flex justify-center\">\r\n\t\t\t\t\t<view class=\"info-box-c\">\r\n\t\t\t\t\t\t<view class=\"info-box-c-t flex align-center\">\r\n\t\t\t\t\t\t\t{{info.serviceDescribe}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-box-c-b flex align-center\">\r\n\t\t\t\t\t\t\t{{info.money}}<text>元/{{info.company==1?'天':'次'}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"tabs flex justify-center\">\r\n\t\t\t<view class=\"tabs-box flex align-center\">\r\n\t\t\t\t<view :class=\"{active:current==0}\" class=\"tabs-box-l\" @click=\"tabs(0)\">\r\n\t\t\t\t\t服务内容\r\n\t\t\t\t</view>\r\n\t\t\t\t<view :class=\"{active:current==1}\" class=\"tabs-box-r\" @click=\"tabs(1)\">\r\n\t\t\t\t\t服务须知\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 内容 -->\r\n\t\t<view class=\"cont flex justify-center\">\r\n\t\t\t<view class=\"cont-box flex justify-center\">\r\n\t\t\t\t<view class=\"cont-box-c\">\r\n\t\t\t\t\t<u-parse :html=\"current==0?content:content2\"></u-parse>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<!-- 立即预约 -->\r\n\t\t<view class=\"btn flex justify-center align-center\" v-if=\"type==''\">\r\n\t\t\t<view class=\"btn-box flex justify-center align-center\" @tap=\"goNav(info)\">\r\n\t\t\t\t立即预约￥{{info.money}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tinfo: {},\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tcontent: '',\r\n\t\t\t\tcontent2: '',\r\n\t\t\t\ttype: '',\r\n\t\t\t\ttuiName: '',\r\n\t\t\t\ttuiImage: '',\r\n\t\t\t\tinvitationCode: '',\r\n\t\t\t\tmodularId: -1\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/my/yxpeizhen/info?invitation=' + this\r\n\t\t\t\t\t.invitationCode + '&data=' + encodeURIComponent(JSON.stringify(this\r\n\t\t\t\t\t\t.info)), //这是为了传参   onload(data){let id=data.id;} \r\n\t\t\t\ttitle: this.tuiName,\r\n\t\t\t\timageUrl: this.tuiImage\r\n\t\t\t}\r\n\t\t},\r\n\t\t/*\r\n\t\t * uniapp微信小程序分享页面到微信朋友圈\r\n\t\t */\r\n\t\tonShareTimeline(res) {\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/my/yxpeizhen/info?invitation=' + this.invitationCode + '&data=' + encodeURIComponent(JSON\r\n\t\t\t\t\t.stringify(this.info)), //这是为了传参   onload(data){let id=data.id;} \r\n\t\t\t\ttitle: this.tuiName,\r\n\t\t\t\timageUrl: this.tuiImage\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tlet that = this\r\n\t\t\t// 分享\r\n\t\t\tthis.myId = uni.getStorageSync('userId')\r\n\t\t\t// 获取邀请码保存到本地\r\n\t\t\tif (option.invitation) {\r\n\t\t\t\tthat.$queue.setData('inviterCode', option.invitation);\r\n\t\t\t}\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (option.scene) {\r\n\t\t\t\tconst scene = decodeURIComponent(option.scene);\r\n\t\t\t\tthat.$queue.setData('inviterCode', scene.split(',')[0]);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tif (this.myId) {\r\n\t\t\t\tthat.invitationCode = uni.getStorageSync('invitationCode')\r\n\t\t\t\tthat.$Request.getT('/app/common/type/276').then(res => { //分享标题 276\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.tuiName = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthat.$Request.getT('/app/common/type/277').then(res => { //分享图 277\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.tuiImage = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.info = JSON.parse(decodeURIComponent(option.data))\r\n\t\t\tif (option.modularId) {\r\n\t\t\t\tthis.modularId = Number(option.modularId)\r\n\t\t\t} else {\r\n\t\t\t\tthis.modularId = -1\r\n\t\t\t}\r\n\t\t\tthis.content = this.info.serviceInstruction.replace(\"img\",\r\n\t\t\t\t'img style=\"width:100%;height:auto\"');\r\n\t\t\tthis.content2 = this.info.serviceContent.replace(\"img\",\r\n\t\t\t\t'img style=\"width:100%;height:auto\"');\r\n\t\t\tconsole.log(this.content2)\r\n\t\t\tif (option.type) {\r\n\t\t\t\tthis.type = option.type\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttabs(num) {\r\n\t\t\t\tthis.current = num\r\n\t\t\t},\r\n\t\t\tgoNav(e) {\r\n\t\t\t\tif (uni.getStorageSync('token')) {\r\n\t\t\t\t\tif (this.modularId == -1) { //如果没有modularId，那么走原来的分支\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/index/game/orderDet?serviceId=' + e.serviceId + '&hospitalName=' + e\r\n\t\t\t\t\t\t\t\t.hospitalName + '&hospitalId=' + e.hospitalId + '&serviceType=' + e.serviceType\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else { //如果优modularId，那么就根据modularId跳转\r\n\t\t\t\t\t\tswitch (this.modularId) {\r\n\t\t\t\t\t\t\tcase 1: //就医陪诊\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/my/customServer/customPz?serviceId=' + e.serviceId + '&serviceType=' + e\r\n\t\t\t\t\t\t\t\t\t\t.serviceType\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tcase 2: //院内陪护\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('phserviceId', e.serviceId)\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/my/customServer/customPeop?name=护理员&item=' + encodeURIComponent(JSON\r\n\t\t\t\t\t\t\t\t\t\t.stringify(e))\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tcase 3: //优享陪诊\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/my/customServer/customPz?serviceId=' + e.serviceId + '&serviceType=' + e\r\n\t\t\t\t\t\t\t\t\t\t.serviceType\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n\r\n\t.active {\r\n\t\tcolor: #4265F5;\r\n\t\tfont-size: 38rpx;\r\n\t}\r\n\r\n\t.bgImg {\r\n\t\twidth: 100%;\r\n\t\theight: 590rpx;\r\n\t\tposition: relative;\r\n\r\n\t\t.bgImg-box {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 60rpx;\r\n\t\t\ttop: 100rpx;\r\n\r\n\t\t\t.bgImg-box-title {\r\n\t\t\t\tfont-size: 50rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.bgImg-box-labs {\r\n\t\t\t\tmax-width: 635rpx;\r\n\t\t\t\tpadding: 20rpx 30rpx 20rpx 30rpx;\r\n\t\t\t\t// background-color: #4D65FD;\r\n\t\t\t\tbackground-image: linear-gradient(to right, #a5c2fe, #7681ff);\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.info {\r\n\t\twidth: 100%;\r\n\t\theight: 212rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\r\n\t\t.info-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tbackground-color: #ffffff;\r\n\r\n\t\t\t.info-box-c {\r\n\t\t\t\twidth: 566rpx;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.info-box-c-t {\r\n\t\t\t\tcolor: #7F8793;\r\n\t\t\t\tfont-size: 26rpx;\r\n\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 50%;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t-webkit-line-clamp: 3;\r\n\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\tpadding-top: 34rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.info-box-c-b {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 50%;\r\n\t\t\t\tcolor: #4265F5;\r\n\t\t\t\tfont-size: 38rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tabs {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 40rpx;\r\n\r\n\t\t.tabs-box {\r\n\t\t\twidth: 626rpx;\r\n\t\t\tcolor: #252525;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tfont-weight: 800;\r\n\r\n\t\t\t.tabs-box-l {}\r\n\r\n\t\t\t.tabs-box-r {\r\n\t\t\t\tmargin-left: 74rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.cont {\r\n\t\twidth: 100%;\r\n\t\theight: auto;\r\n\t\tmargin-top: 20rpx;\r\n\r\n\t\t.cont-box {\r\n\t\t\twidth: 626rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t\tborder-radius: 16rpx;\r\n\r\n\t\t\t.cont-box-c {\r\n\t\t\t\twidth: 586rpx;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tpadding-top: 20rpx;\r\n\t\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.btn {\r\n\t\twidth: 100%;\r\n\t\theight: 160rpx;\r\n\t\tbackground-color: #F5F5F5;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\r\n\t\t.btn-box {\r\n\t\t\twidth: 533rpx;\r\n\t\t\theight: 76rpx;\r\n\t\t\tborder-radius: 38rpx;\r\n\t\t\tcolor: #ffffff;\r\n\t\t\tbackground-color: #4D65FD;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./info.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627201\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}