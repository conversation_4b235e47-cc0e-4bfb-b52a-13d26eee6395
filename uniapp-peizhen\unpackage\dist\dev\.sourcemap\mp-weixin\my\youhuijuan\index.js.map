{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/youhuijuan/index.vue?f6fb", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/youhuijuan/index.vue?6c9b", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/youhuijuan/index.vue?be7d", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/youhuijuan/index.vue?ab3b", "uni-app:///my/youhuijuan/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/youhuijuan/index.vue?ffb1", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/youhuijuan/index.vue?d30f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "couponId", "showpays", "closeable", "openLists", "openWay", "yhqList", "hdyhqList", "qbyhqList", "onLoad", "image", "text", "id", "methods", "lingqu", "uni", "title", "icon", "shi<PERSON>g", "url", "getHuoDong", "getMyList", "goMyQuan", "selectWay", "showPay", "console", "payYHQ", "that", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "setTimeout", "fail", "callPay", "document", "onBridgeReady", "WeixinJSBridge", "setPayment", "orderInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgIxvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAmBA;MACAC;MACAC;MACAC;IACA;MACAF;MACAC;MACAC;IACA;IACA;IAeA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACAC;YACAC;YACAC;UACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAH;QACAI;MACA;IACA;IACAC;MAAA;MAEA;QACA;UACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;YACA;cACArB;YACA;UACA;UACA;QAEA;MACA;IACA;IACAsB;MACAP;QACAI;MACA;IACA;IACAI;MACA;IACA;IACAC;MACAC;MACA;MACA;IACA;IACAC;MACA;MACAC;MACA;QAEAA,mGACA;UACAA;UACA;YACAZ;cACAa;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;gBACAT;gBACAV;kBACAC;kBACAC;gBACA;gBACAkB;kBACAR;kBACAA;gBACA;cACA;cACAS;gBACAX;gBACAV;kBACAC;kBACAC;gBACA;cACA;YACA;UACA;YACAU;UACA;QACA;MA0BA,+BAgCA;QACAA;UACAZ;UACAY;UACA;YACAZ;cACAC;cACAC;YACA;YACAkB;cACAR;cACAA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IACAU;MACA;QACA;UACAC;QACA;UACAA;UACAA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACAC,sBACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;QAAA;QACA;MACA,GACA;QACA;UACA;UACA;UACAzB;UACAA;YACAC;YACAC;UACA;UACAkB;YACAR;YACAA;UACA;QACA;UACAZ;QACA;QACAyB;MACA,EACA;IACA;IACAC;MACA;MACA1B;QACAa;QACAc;QAAA;QACAR;UACAnB;UACAA;YACAC;YACAC;UACA;UACAkB;YACAR;YACAA;UACA;QACA;QACAS;UACArB;UACAU;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/aA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/youhuijuan/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/youhuijuan/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=545ff55d&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/youhuijuan/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=545ff55d&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.yhqList && _vm.yhqList.length > 0\n  var g1 = _vm.hdyhqList && _vm.hdyhqList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view style=\"padding-bottom: 100rpx;\">\n\t\t<view style=\"margin: 30rpx 30rpx;height: 370rpx;\">\n\t\t\t<view class=\"wdq_view\">\n\t\t\t\t<view class=\"wdq_toptext\">优惠劵商城，您的省钱小帮手</view>\n\t\t\t</view>\n\t\t\t<view class=\"wdq_my\">\n\t\t\t\t<view class=\"flex justify-between align-center\">\n\t\t\t\t\t<view class=\"wdq_mytext\">我的优惠券</view>\n\t\t\t\t\t<view class=\"flex align-center\">\n\t\t\t\t\t\t<view class=\"wdq_mychakan\" @tap=\"goMyQuan\">查看更多</view>\n\t\t\t\t\t\t<image src=\"../../static/images/my/right.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"margin-top\" v-if=\"yhqList && yhqList.length > 0\">\n\t\t\t\t\t<scroll-view class=\"scroll-view_H\" scroll-x=\"true\" scroll-left=\"0\">\n\t\t\t\t\t\t<view class=\"flex\">\n\t\t\t\t\t\t\t<view class=\"wdq_itemview\" v-for=\"(item,index) in yhqList\">\n\t\t\t\t\t\t\t\t<image src=\"../static/yhqbackgroud.png\" class=\"wdq_itemimage\"></image>\n\t\t\t\t\t\t\t\t<view class=\"itemview_item\">\n\t\t\t\t\t\t\t\t\t<view class=\"item_moneyview\">￥<text>{{item.money}}</text> </view>\n\t\t\t\t\t\t\t\t\t<view class=\"item_rightview\">\n\t\t\t\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"item_coupName\">{{item.couponName}}</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"item_coupEndTime\" v-if=\"item.expirationTime\">\n\t\t\t\t\t\t\t\t\t\t\t\t到期：{{item.expirationTime}}</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"item_coupEndTime\" v-else> 永久有效</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"item_coupBtn\" @click=\"shiyong()\">去使用</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"width: 100%;display: flex;justify-content: center;\" v-else>\n\t\t\t\t\t<image src=\"../static/noyhq.png\" style=\"width: 251rpx;height: 178rpx;\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"hd_yhqview\">\n\t\t\t<view class=\"hd_topview\">活动优惠券</view>\n\n\t\t\t<view class=\"margin-top\" v-if=\"hdyhqList && hdyhqList.length > 0\">\n\t\t\t\t<scroll-view class=\"hdscroll-view_H\" scroll-x=\"true\" scroll-left=\"0\">\n\t\t\t\t\t<view class=\"flex\">\n\t\t\t\t\t\t<view class=\"hdwdq_itemview\" v-for=\"(item,index) in hdyhqList\">\n\t\t\t\t\t\t\t<image src=\"../static/hdyhq1.png\" class=\"hdwdq_itemimage\"></image>\n\t\t\t\t\t\t\t<view class=\"hditemview_item\">\n\t\t\t\t\t\t\t\t<view class=\"hd_itemname\">{{item.couponName}}</view>\n\t\t\t\t\t\t\t\t<view class=\"hditem_moneyview\">￥<text>{{item.money}}</text> </view>\n\t\t\t\t\t\t\t\t<view class=\"hd_itemtext\" v-if=\"item.minMoney\">满{{item.minMoney}}元使用</view>\n\t\t\t\t\t\t\t\t<view class=\"hd_itemtext\" v-else>无门槛使用</view>\n\t\t\t\t\t\t\t\t<view class=\"hd_itembtn\" @click=\"lingqu(item)\">去领取</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\t<view style=\"width: 100%;display: flex;justify-content: center;margin-top: 40rpx;\" v-else>\n\t\t\t\t<image src=\"../static/noyhq1.png\" style=\"width: 251rpx;height: 178rpx;\"></image>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- <view class=\"qb_view\">\n\t\t\t<view class=\"hd_topview\">超值券包 <text>购券下单更优惠</text> </view>\n\n\t\t\t<scroll-view class=\"qbscroll-view_H\" scroll-y=\"true\" scroll-left=\"0\"\n\t\t\t\tv-if=\"qbyhqList && qbyhqList.length > 0\">\n\t\t\t\t<view class=\"qb_itemview\" v-for=\"(item,index) in qbyhqList\">\n\t\t\t\t\t<image src=\"../static/wmkquan.png\"></image>\n\t\t\t\t\t<view style=\"margin-left: 20rpx;width: 220rpx;\">\n\t\t\t\t\t\t<view class=\"qb_itemname\">{{item.couponName}}</view>\n\t\t\t\t\t\t<view class=\"flex align-end margin-top-xs\">\n\t\t\t\t\t\t\t<view class=\"qb_money\">￥ <text style=\"font-size: 34rpx;\">{{item.buyMoney}}</text> </view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"qb_btn\" @tap=\"showPay(item.couponId)\">立即抢购</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\n\t\t\t<view style=\"width: 100%;display: flex;justify-content: center;margin-top: 240rpx;\" v-else>\n\t\t\t\t<image src=\"../static/noyhq1.png\" style=\"width: 251rpx;height: 178rpx;\"></image>\n\t\t\t</view>\n\t\t</view>\n -->\n\t\t<!-- 支付方式 -->\n\t\t<u-popup v-model=\"showpays\" mode=\"bottom\" :closeable=\"closeable\">\n\t\t\t<view class=\"popup_pay\">\n\t\t\t\t<view style=\"background-color: #fff;\">\n\t\t\t\t\t<view style=\"padding: 0 20upx;margin-top: 60rpx;margin-bottom: 20rpx;\">\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tstyle=\"display: flex;height: 100upx;align-items: center;padding: 20upx 0;justify-content: center;\"\n\t\t\t\t\t\t\tv-for=\"(item,index) in openLists\" :key='index'>\n\t\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\">\n\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">\n\t\t\t\t\t\t\t\t{{item.text}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 45upx;\" @tap='selectWay(item)'>\n\t\t\t\t\t\t\t\t<label class=\"tui-radio\">\n\t\t\t\t\t\t\t\t\t<radio color=\"#1777FF\" :checked=\"openWay === item.id ? true : false\" />\n\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"pay_btn\" @click=\"payYHQ()\">确认支付</view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<!-- <u-popup v-model=\"showkm\" @close=\"closekm\" mode=\"center\" borderRadius=\"32\">\n\t\t\t<view class=\"popu_view\">\n\t\t\t\t<view class=\"popu_viewtitle\">卡密兑换</view>\n\t\t\t\t<view class=\"popu_kamiview\">\n\t\t\t\t\t<view class=\"kamiview_lefttext\">卡密：</view>\n\t\t\t\t\t<input type=\"text\" class=\"kamiview_righttext\" v-model=\"kamiText\" placeholder=\"请输入卡密信息\"\n\t\t\t\t\t\tplaceholder-class=\"placeholderclass\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"kami_btn\" @tap=\"addKaMi\">立即兑换</view>\n\t\t\t</view>\n\t\t</u-popup>\n -->\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcouponId: 0,\n\t\t\t\tshowpays: false,\n\t\t\t\tcloseable: true,\n\t\t\t\topenLists: [],\n\t\t\t\topenWay: 2,\n\t\t\t\tyhqList: [],\n\t\t\t\thdyhqList: [],\n\t\t\t\tqbyhqList: []\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\tthis.openLists = [{\n\t\t\t\timage: '../../static/images/zhifubao.png',\n\t\t\t\ttext: '支付宝',\n\t\t\t\tid: 2\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/icon_weixin.png',\n\t\t\t\ttext: '微信',\n\t\t\t\tid: 1\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/lingqian.png',\n\t\t\t\ttext: '水贝',\n\t\t\t\tid: 3\n\t\t\t}];\n\t\t\tthis.openWay = 1;\n\t\t\t// #endif\n\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.openLists = [{\n\t\t\t\timage: '../../static/images/icon_weixin.png',\n\t\t\t\ttext: '微信',\n\t\t\t\tid: 1\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/lingqian.png',\n\t\t\t\ttext: '水贝',\n\t\t\t\tid: 3\n\t\t\t}];\n\t\t\tthis.openWay = 1;\n\t\t\t// #endif\n\n\t\t\t// #ifdef H5\n\t\t\tthis.openLists = [{\n\t\t\t\timage: '../../static/images/zhifubao.png',\n\t\t\t\ttext: '支付宝',\n\t\t\t\tid: 2\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/lingqian.png',\n\t\t\t\ttext: '水贝',\n\t\t\t\tid: 3\n\t\t\t}];\n\t\t\tthis.openWay = 2;\n\t\t\t// #endif\n\t\t\tthis.getHuoDong();\n\t\t\tthis.getMyList();\n\t\t},\n\t\tmethods: {\n\t\t\tlingqu(e) {\n\t\t\t\tthis.$Request.getT('/app/couponUser/receiveActivity?couponId=' + e.couponId).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '领取成功',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.getMyList()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$queue.showToast(res.msg);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tshiyong() {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetHuoDong() {\n\n\t\t\t\tthis.$Request.getT('/app/coupon/getCouponPageList?page=1&limit=50&couponType=3').then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.hdyhqList = res.data.records;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// this.$Request.getT('/app/coupon/getCouponPageList?page=1&limit=50&couponType=2').then(res => {\n\t\t\t\t// \tif (res.code == 0) {\n\t\t\t\t// \t\tthis.qbyhqList = res.data.records;\n\t\t\t\t// \t}\n\t\t\t\t// });\n\t\t\t},\n\t\t\tgetMyList() {\n\t\t\t\tthis.$Request.getT('/app/couponUser/getMyCouponList?page=1&limit=10&status=0').then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tlet data = res.data.records\n\t\t\t\t\t\tfor (var i = 0; i < data.length; i++) {\n\t\t\t\t\t\t\tif (data[i].expirationTime) {\n\t\t\t\t\t\t\t\tdata[i].expirationTime = data[i].expirationTime.substring(0, 10)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.yhqList = res.data.records;\n\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoMyQuan() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/my/youhuijuan/myList'\n\t\t\t\t});\n\t\t\t},\n\t\t\tselectWay: function(item) {\n\t\t\t\tthis.openWay = item.id;\n\t\t\t},\n\t\t\tshowPay(couponId) {\n\t\t\t\tconsole.log(couponId)\n\t\t\t\tthis.couponId = couponId;\n\t\t\t\tthis.showpays = true;\n\t\t\t},\n\t\t\tpayYHQ() {\n\t\t\t\tlet that = this;\n\t\t\t\tthat.$queue.showLoading('支付中...');\n\t\t\t\tif (that.openWay == 1) {\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tthat.$Request.postT('/app/wxPay/payCoupon?payType=3&couponId=' + that.couponId + '&buyNum=1').then(\n\t\t\t\t\t\tret => {\n\t\t\t\t\t\t\tthat.showpays = false;\n\t\t\t\t\t\t\tif (ret.code == 0) {\n\t\t\t\t\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\t\t\t\tprovider: 'wxpay',\n\t\t\t\t\t\t\t\t\ttimeStamp: ret.data.timestamp,\n\t\t\t\t\t\t\t\t\tnonceStr: ret.data.noncestr,\n\t\t\t\t\t\t\t\t\tpackage: ret.data.package,\n\t\t\t\t\t\t\t\t\tsignType: ret.data.signType,\n\t\t\t\t\t\t\t\t\tpaySign: ret.data.sign,\n\t\t\t\t\t\t\t\t\tsuccess: function(suc) {\n\t\t\t\t\t\t\t\t\t\tconsole.log('success:' + JSON.stringify(suc));\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\tsetTimeout(d => {\n\t\t\t\t\t\t\t\t\t\t\tthat.getMyList();\n\t\t\t\t\t\t\t\t\t\t\tthat.getHuoDong();\n\t\t\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\t\t\tconsole.log('fail:' + JSON.stringify(err));\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付失败',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthat.$queue.showToast(res.msg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\tthat.$Request.post(\"/app/wxPay/payCoupon?payType=2&couponId=\" + that.couponId + '&buyNum=1').then(\n\t\t\t\t\t\tred => {\n\t\t\t\t\t\t\tthat.showpays = false;\n\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\tthat.callPay(red);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthat.$queue.showToast(red.msg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\tthat.$Request.post(\"/app/wxPay/payCoupon?payType=1&couponId=\" + that.couponId + '&buyNum=1')\n\t\t\t\t\t\t.then(red => {\n\t\t\t\t\t\t\tthat.showpays = false;\n\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\tthat.setPayment('wxpay', JSON.stringify(red\n\t\t\t\t\t\t\t\t\t.data));\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthat.$queue.showToast(red.msg);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t} else if (that.openWay == 2) {\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payCoupon?payType=2&couponId=\" + that.couponId + '&buyNum=1').then(\n\t\t\t\t\t\tred => {\n\t\t\t\t\t\t\tthat.showpays = false;\n\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\tconst div = document.createElement('div')\n\t\t\t\t\t\t\t\tdiv.innerHTML = red.data //此处form就是后台返回接收到的数据\n\t\t\t\t\t\t\t\tdocument.body.appendChild(div)\n\t\t\t\t\t\t\t\tdocument.forms[0].submit()\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: red.msg,\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payCoupon?payType=1&couponId=\" + that.couponId + '&buyNum=1').then(\n\t\t\t\t\t\tred => {\n\t\t\t\t\t\t\tthat.showpays = false;\n\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\tthat.setPayment('alipay', red.data);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: red.msg,\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t} else if (that.openWay == 3) {\n\t\t\t\t\tthat.$Request.postT('/app/couponUser/buyCoupon?couponId=' + that.couponId + '&buyNum=1').then(res => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tthat.showpays = false;\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tsetTimeout(d => {\n\t\t\t\t\t\t\t\tthat.getMyList();\n\t\t\t\t\t\t\t\tthat.getHuoDong();\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthat.$queue.showToast(res.msg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tcallPay: function(response) {\n\t\t\t\tif (typeof WeixinJSBridge === \"undefined\") {\n\t\t\t\t\tif (document.addEventListener) {\n\t\t\t\t\t\tdocument.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);\n\t\t\t\t\t} else if (document.attachEvent) {\n\t\t\t\t\t\tdocument.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));\n\t\t\t\t\t\tdocument.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.onBridgeReady(response);\n\t\t\t\t}\n\t\t\t},\n\t\t\tonBridgeReady: function(response) {\n\t\t\t\tlet that = this;\n\t\t\t\tif (!response.package) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tWeixinJSBridge.invoke(\n\t\t\t\t\t'getBrandWCPayRequest', {\n\t\t\t\t\t\t\"appId\": response.appid, //公众号名称，由商户传入\n\t\t\t\t\t\t\"timeStamp\": response.timestamp, //时间戳，自1970年以来的秒数\n\t\t\t\t\t\t\"nonceStr\": response.noncestr, //随机串\n\t\t\t\t\t\t\"package\": response.package,\n\t\t\t\t\t\t\"signType\": response.signType, //微信签名方式：\n\t\t\t\t\t\t\"paySign\": response.sign //微信签名\n\t\t\t\t\t},\n\t\t\t\t\tfunction(res) {\n\t\t\t\t\t\tif (res.err_msg === \"get_brand_wcpay_request:ok\") {\n\t\t\t\t\t\t\t// 使用以上方式判断前端返回,微信团队郑重提示：\n\t\t\t\t\t\t\t//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tsetTimeout(d => {\n\t\t\t\t\t\t\t\tthat.getMyList();\n\t\t\t\t\t\t\t\tthat.getHuoDong();\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tWeixinJSBridge.log(response.err_msg);\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t},\n\t\t\tsetPayment(name, order) {\n\t\t\t\tlet that = this;\n\t\t\t\tuni.requestPayment({\n\t\t\t\t\tprovider: name,\n\t\t\t\t\torderInfo: order, //微信、支付宝订单数据\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '支付成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tsetTimeout(d => {\n\t\t\t\t\t\t\tthat.getMyList();\n\t\t\t\t\t\t\tthat.getHuoDong();\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tconsole.log(12)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\tpage {\n\t\tbackground: #F2F2F2;\n\t}\n\n\t.popu_view {\n\t\twidth: 690rpx;\n\t\theight: 420rpx;\n\t\tpadding: 40rpx 30rpx;\n\n\t\t.popu_viewtitle {\n\t\t\twidth: 100%;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 34rpx;\n\t\t\tfont-family: PingFang SC;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #333333;\n\t\t}\n\n\t\t.kami_btn {\n\t\t\twidth: 640rpx;\n\t\t\theight: 88rpx;\n\t\t\tbackground: #268CFF;\n\t\t\tborder-radius: 44rpx;\n\t\t\ttext-align: center;\n\t\t\tline-height: 88rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-family: PingFang SC;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #FFFFFF;\n\t\t\tmargin-top: 40rpx;\n\t\t}\n\n\t\t.popu_kamiview {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\twidth: 630rpx;\n\t\t\theight: 100rpx;\n\t\t\tbackground: #F7F5F5;\n\t\t\tborder-radius: 10rpx;\n\t\t\tmargin-top: 60rpx;\n\t\t\tpadding: 0rpx 20rpx;\n\n\t\t\t.kamiview_lefttext {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-family: PingFang SC;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #999999;\n\t\t\t}\n\n\t\t\t.kamiview_righttext {\n\t\t\t\twidth: 480rpx;\n\t\t\t\theight: 100rpx;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\n\t\t}\n\n\t\t.placeholderclass {\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #333333;\n\t\t}\n\t}\n\n\n\t.qb_view {\n\t\twidth: 686rpx;\n\t\theight: 800rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 24rpx;\n\t\tmargin: 20rpx 30rpx;\n\t\tpadding: 30rpx;\n\n\t\t.qbscroll-view_H {\n\t\t\twidth: 100%;\n\t\t\theight: 700rpx;\n\t\t\tpadding: 30rpx 0rpx;\n\t\t}\n\n\t\t.qb_itemview {\n\t\t\twidth: 626rpx;\n\t\t\theight: 160rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder: 1rpx solid #FEE3C0;\n\t\t\tborder-radius: 24rpx;\n\t\t\tpadding: 20rpx;\n\t\t\tmargin-top: 20rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.qb_btn {\n\t\t\t\twidth: 140rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tbackground: linear-gradient(90deg, #ED724B 0%, #FD4136 100%);\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tline-height: 60rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-family: PingFang SC;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tmargin-left: 20rpx;\n\t\t\t}\n\n\t\t\t.qb_itemname {\n\t\t\t\twidth: 200rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-family: PingFang SC;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #1A1A1A;\n\t\t\t}\n\n\t\t\t.qb_money {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-family: PingFang SC;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #F01B1F;\n\t\t\t}\n\n\t\t\t.qb_oldmoney {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-family: PingFang SC;\n\t\t\t\tfont-weight: 500;\n\t\t\t\t// text-decoration: line-through;\n\t\t\t\tcolor: #999999;\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t}\n\n\t\t\timage {\n\t\t\t\twidth: 195rpx;\n\t\t\t\theight: 96rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.hd_topview {\n\t\tfont-size: 32rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: 800;\n\t\tcolor: #050505;\n\n\t\ttext {\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-family: PingFang SC;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #999999;\n\t\t\tmargin-left: 20rpx;\n\t\t}\n\t}\n\n\t.wdq_view {\n\t\twidth: 686rpx;\n\t\theight: 130rpx;\n\t\tbackground: linear-gradient(90deg, #31A1FE 0%, #2589FF 100%);\n\t\tborder-radius: 24rpx;\n\t}\n\n\t.wdq_itemview {\n\t\tmargin-right: 20rpx;\n\t\twidth: 560rpx;\n\t\theight: 140rpx;\n\n\t\t.wdq_itemimage {\n\t\t\twidth: 560rpx;\n\t\t\theight: 140rpx;\n\t\t}\n\n\t\t.itemview_item {\n\t\t\tdisplay: flex;\n\t\t\tposition: absolute;\n\t\t\ttop: 0rpx;\n\t\t\twidth: 560rpx;\n\t\t\theight: 140rpx;\n\t\t\talign-items: center;\n\t\t\tz-index: 99;\n\n\t\t\t.item_moneyview {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-family: PingFang SC;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #84592B;\n\t\t\t\twidth: 140rpx;\n\t\t\t\ttext-align: center;\n\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 48rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.item_rightview {\n\t\t\t\tmargin-left: 40rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\twidth: 360rpx;\n\t\t\t\theight: 140rpx;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.item_coupName {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-family: PingFang SC;\n\t\t\t\t\tfont-weight: 800;\n\t\t\t\t\tcolor: #1A1A1A;\n\t\t\t\t\twidth: 200rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\n\t\t\t\t.item_coupEndTime {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-family: PingFang SC;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t\tmargin-top: 16rpx;\n\t\t\t\t}\n\n\t\t\t\t.item_coupBtn {\n\t\t\t\t\twidth: 130rpx;\n\t\t\t\t\theight: 50rpx;\n\t\t\t\t\tbackground: linear-gradient(90deg, #ED724B 0%, #FD4136 100%);\n\t\t\t\t\tborder-radius: 25rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-family: PingFang SC;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.scroll-view_H {\n\t\twhite-space: nowrap;\n\t\twidth: 100%;\n\t\theight: 150rpx;\n\t\tdisplay: flex;\n\t}\n\n\t.wdq_my {\n\t\twidth: 686rpx;\n\t\theight: 280rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 24rpx;\n\t\tposition: absolute;\n\t\t/* #ifdef H5 */\n\t\ttop: 90rpx;\n\t\t/* #endif */\n\t\t/* #ifndef H5 */\n\t\ttop: 120rpx;\n\t\t/* #endif */\n\t\tz-index: 99;\n\t\tpadding: 30rpx;\n\n\t\t.wdq_mytext {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-family: PingFang SC;\n\t\t\tfont-weight: 800;\n\t\t\tcolor: #050505;\n\t\t}\n\n\t\t.wdq_mychakan {\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-family: PingFang SC;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #999999;\n\t\t\tmargin-right: 10rpx;\n\t\t}\n\n\t\timage {\n\t\t\twidth: 11rpx;\n\t\t\theight: 20rpx;\n\t\t}\n\t}\n\n\t.popup_pay {\n\t\twidth: 100%;\n\t\tposition: relative;\n\t\tpadding-bottom: 45rpx;\n\t}\n\n\t.pay_btn {\n\t\twidth: 90%;\n\t\tmargin: 0 auto;\n\t\ttext-align: center;\n\t\tbackground: #0278FE;\n\t\theight: 80rpx;\n\t\tborder-radius: 16rpx;\n\t\tcolor: #ffffff;\n\t\tline-height: 80rpx;\n\n\t}\n\n\t.wdq_toptext {\n\t\tfont-size: 30rpx;\n\t\tfont-family: PingFang SC;\n\t\tfont-weight: bold;\n\t\tcolor: #FFFFFF;\n\t\tpadding: 30rpx;\n\t}\n\n\t.imag_dw {\n\t\tposition: absolute;\n\t\ttop: 18upx;\n\t\tleft: 15upx;\n\t\tz-index: 99;\n\t\tcolor: #ED724B;\n\t\tfont-size: 32upx;\n\t\tfont-weight: bold;\n\t\tletter-spacing: 1rpx;\n\t}\n\n\t.imag_dw text {\n\t\tfont-size: 24upx;\n\t}\n\n\t.hd_yhqview {\n\t\twidth: 686rpx;\n\t\theight: 380rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 24rpx;\n\t\tmargin: 20rpx 30rpx;\n\t\tpadding: 30rpx;\n\n\t\t.hdscroll-view_H {\n\t\t\twhite-space: nowrap;\n\t\t\twidth: 100%;\n\t\t\theight: 280rpx;\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t.hdwdq_itemview {\n\t\t\tmargin-right: 20rpx;\n\t\t\twidth: 220rpx;\n\t\t\theight: 260rpx;\n\n\t\t\t.hdwdq_itemimage {\n\t\t\t\twidth: 220rpx;\n\t\t\t\theight: 260rpx;\n\t\t\t}\n\n\t\t\t.hditemview_item {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0rpx;\n\t\t\t\twidth: 220rpx;\n\t\t\t\theight: 260rpx;\n\t\t\t\tz-index: 99;\n\t\t\t\ttext-align: center;\n\t\t\t\tpadding: 20rpx 0rpx;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.hd_itemname {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-family: PingFang SC;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tcolor: #1A1A1A;\n\t\t\t\t}\n\n\t\t\t\t.hd_itemtext {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-family: PingFang SC;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\n\t\t\t\t.hd_itembtn {\n\t\t\t\t\twidth: 130rpx;\n\t\t\t\t\theight: 50rpx;\n\t\t\t\t\tbackground: #FEFAEF;\n\t\t\t\t\tborder-radius: 25rpx;\n\t\t\t\t\tmargin-top: 40rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-family: PingFang SC;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tcolor: #F90C14;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\tmargin-left: 44rpx;\n\t\t\t\t}\n\n\t\t\t\t.hditem_moneyview {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-family: PingFang SC;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tcolor: #F01B1F;\n\t\t\t\t\twidth: 220rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tmargin: 10rpx 0rpx;\n\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627731\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}