{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/city-select/city-select.vue?8ef5", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/city-select/city-select.vue?0e40", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/city-select/city-select.vue?aea8", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/city-select/city-select.vue?3a23", "uni-app:///components/city-select/city-select.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/city-select/city-select.vue?d9e1", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/city-select/city-select.vue?bb04"], "names": ["props", "placeholder", "type", "default", "formatName", "activeCity", "hotCity", "obtainCitys", "isSearch", "data", "to<PERSON>ie<PERSON>", "scrollTop", "cityindexs", "activeCityIndex", "handleCity", "serachCity", "cityData", "computed", "sortItems", "cityArr", "searchDatas", "searchData", "oldData", "name", "created", "watch", "methods", "updateCitys", "keyInput", "initializationCity", "isCity", "citys", "forName", "index", "getLetter", "buildCityindexs", "cityName", "unicode", "cityindex", "cityIndexPosition", "cityTrigger"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA0uB,CAAgB,2rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiD9vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QAAA;MAAA;IACA;IACA;IACAG;MACAJ;MACAC;QAAA;MAAA;IACA;IACA;IACAI;MACAL;MACAC;QAAA;MAAA;IACA;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;IACAC;MACA;QACA;UACA;UACAC;YACA;YACA;YACA;UACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAlB;MACA;IACA;EACA;EACAmB;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA,0GACA,iDACA;MACA;QACA;UACAN;UACAO;UAAA;UACAC;UAAA;UACAC,6EACAC;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;QAEA;QACA;UACA;UACA;QACA;QAEA;UACAC;UACAC;UACAf;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAgB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACzPA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/city-select/city-select.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./city-select.vue?vue&type=template&id=c758b27c&\"\nvar renderjs\nimport script from \"./city-select.vue?vue&type=script&lang=js&\"\nexport * from \"./city-select.vue?vue&type=script&lang=js&\"\nimport style0 from \"./city-select.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/city-select/city-select.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city-select.vue?vue&type=template&id=c758b27c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hotCity.length > 0 && !_vm.serachCity\n  var g1 = _vm.hotCity.length > 0 && !_vm.serachCity\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city-select.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city-select.vue?vue&type=script&lang=js&\"", "<template>\n\t<!-- 城市选择-->\n\t<view class=\"city-select\">\n\t\t<scroll-view :scroll-top=\"scrollTop\" scroll-y=\"true\" class=\"city-select-main\" id=\"city-select-main\"\n\t\t\t:scroll-into-view=\"toView\">\n\t\t\t<!-- 预留搜索-->\n\t\t\t<view class=\"city-serach\" v-if=\"isSearch\"><input @input=\"keyInput\" :placeholder=\"placeholder\"\n\t\t\t\t\tclass=\"city-serach-input\" /></view>\n\t\t\t<!-- 当前定位城市 -->\n\t\t\t<view class=\"hot-title\" v-if=\"activeCity && !serachCity\">当前定位城市</view>\n\t\t\t<view class=\"hot-city\" v-if=\"activeCity && !serachCity\">\n\t\t\t\t<view class=\"hot-item\" @click=\"cityTrigger(activeCity)\">{{ activeCity[formatName] }}</view>\n\t\t\t</view>\n\t\t\t<!-- 热门城市 -->\n\t\t\t<view class=\"hot-title\" v-if=\"hotCity.length > 0 && !serachCity\">热门城市</view>\n\t\t\t<view class=\"hot-city\" v-if=\"hotCity.length > 0 && !serachCity\">\n\t\t\t\t<template v-for=\"(item, index) in hotCity\">\n\t\t\t\t\t<view :key=\"index\" @click=\"cityTrigger(item, 'hot')\" class=\"hot-item\">{{ item[formatName] }}</view>\n\t\t\t\t</template>\n\t\t\t</view>\n\t\t\t<!-- 城市列表(搜索前) -->\n\t\t\t<view class=\"citys\" v-if=\"!serachCity\">\n\t\t\t\t<view v-for=\"(city, index) in sortItems\" :key=\"index\" v-show=\"city.isCity\" class=\"citys-row\">\n\t\t\t\t\t<view class=\"citys-item-letter\" :id=\"'city-letter-' + (city.name === '#' ? '0' : city.name)\">\n\t\t\t\t\t\t{{ city.name }}</view>\n\t\t\t\t\t<view class=\"citys-item\" v-for=\"(item, inx) in city.citys\" :key=\"inx\" @click=\"cityTrigger(item)\">\n\t\t\t\t\t\t{{ item.cityName }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 城市列表(搜索后)  -->\n\t\t\t<view class=\"citys\" v-if=\"serachCity\">\n\t\t\t\t<view v-for=\"(item, index) in searchDatas\" :key=\"index\" class=\"citys-row\">\n\t\t\t\t\t<view class=\"citys-item\" :key=\"index\" @click=\"cityTrigger(item)\">{{ item.name }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t\t<!-- 城市选择索引-->\n\t\t<view class=\"city-indexs-view\" v-if=\"!serachCity\">\n\t\t\t<view class=\"city-indexs\">\n\t\t\t\t<view v-for=\"(cityIns, index) in handleCity\" class=\"city-indexs-text\" v-show=\"cityIns.isCity\"\n\t\t\t\t\t:key=\"index\" @click=\"cityindex(cityIns.forName)\">\n\t\t\t\t\t{{ cityIns.name }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport citySelect from './citySelect.js';\n\texport default {\n\t\tprops: {\n\t\t\t//查询提示文字\n\t\t\tplaceholder: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '请输入城市名称'\n\t\t\t},\n\t\t\t//传入要排序的名称\n\t\t\tformatName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'cityName'\n\t\t\t},\n\t\t\t//当前定位城市\n\t\t\tactiveCity: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: () => null\n\t\t\t},\n\t\t\t//热门城市\n\t\t\thotCity: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault: () => []\n\t\t\t},\n\t\t\t//城市数据\n\t\t\tobtainCitys: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault: () => []\n\t\t\t},\n\t\t\t//是否有搜索\n\t\t\tisSearch: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttoView: 'city-letter-Find', //锚链接 初始值\n\t\t\t\tscrollTop: 0, //scroll-view 滑动的距离\n\t\t\t\tcityindexs: [], // 城市索引\n\t\t\t\tactiveCityIndex: '', // 当前所在的城市索引\n\t\t\t\thandleCity: [], // 处理后的城市数据\n\t\t\t\tserachCity: '', // 搜索的城市\n\t\t\t\tcityData: []\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t/**\n\t\t\t * @desc 城市列表排序\n\t\t\t * @return  Array\n\t\t\t */\n\t\t\tsortItems() {\n\t\t\t\tfor (let index = 0; index < this.handleCity.length; index++) {\n\t\t\t\t\tif (this.handleCity[index].isCity) {\n\t\t\t\t\t\tlet cityArr = this.handleCity[index].citys;\n\t\t\t\t\t\tcityArr = cityArr.sort(function(a, b) {\n\t\t\t\t\t\t\tvar value1 = a.unicode;\n\t\t\t\t\t\t\tvar value2 = b.unicode;\n\t\t\t\t\t\t\treturn value1 - value2;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this.handleCity;\n\t\t\t},\n\t\t\t/**\n\t\t\t * @desc 搜索后的城市列表\n\t\t\t * @return Array\n\t\t\t */\n\t\t\tsearchDatas() {\n\t\t\t\tvar searchData = [];\n\t\t\t\tfor (let i = 0; i < this.cityData.length; i++) {\n\t\t\t\t\tif (this.cityData[i][this.formatName].indexOf(this.serachCity) !== -1) {\n\t\t\t\t\t\tsearchData.push({\n\t\t\t\t\t\t\toldData: this.cityData[i],\n\t\t\t\t\t\t\tname: this.cityData[i][this.formatName]\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn searchData;\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 初始化城市数据\n\t\t\tthis.cityData = this.obtainCitys;\n\t\t\tthis.initializationCity();\n\t\t\tthis.buildCityindexs();\n\t\t},\n\t\twatch: {\n\t\t\tobtainCitys(newData) {\n\t\t\t\tthis.updateCitys(newData);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * @desc 初始化\n\t\t\t */\n\t\t\tupdateCitys(data) {\n\t\t\t\tif (data && data.length) {\n\t\t\t\t\tthis.cityData = data;\n\t\t\t\t\tthis.initializationCity();\n\t\t\t\t\tthis.buildCityindexs();\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * @desc 监听输入框的值\n\t\t\t */\n\t\t\tkeyInput(event) {\n\t\t\t\tthis.serachCity = event.detail.value;\n\t\t\t},\n\t\t\t/**\n\t\t\t * @desc 初始化城市数据\n\t\t\t * @return undefind\n\t\t\t */\n\t\t\tinitializationCity() {\n\t\t\t\tthis.handleCity = [];\n\t\t\t\tconst cityLetterArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',\n\t\t\t\t\t'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '#'\n\t\t\t\t];\n\t\t\t\tfor (let index = 0; index < cityLetterArr.length; index++) {\n\t\t\t\t\tthis.handleCity.push({\n\t\t\t\t\t\tname: cityLetterArr[index],\n\t\t\t\t\t\tisCity: false, // 用于区分是否含有当前字母开头的城市\n\t\t\t\t\t\tcitys: [], // 存放城市首字母含是此字母的数组\n\t\t\t\t\t\tforName: 'city-letter-' + (cityLetterArr[index] == '#' ? '0' : cityLetterArr[\n\t\t\t\t\t\t\tindex]) //label的绑定\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * @desc 得到城市的首字母\n\t\t\t * @param str String\n\t\t\t */\n\t\t\tgetLetter(str) {\n\t\t\t\treturn citySelect.getFirstLetter(str[0]);\n\t\t\t},\n\t\t\t/**\n\t\t\t * @desc 构建城市索引\n\t\t\t * @return undefind\n\t\t\t */\n\t\t\tbuildCityindexs() {\n\t\t\t\tthis.cityindexs = [];\n\t\t\t\tfor (let i = 0; i < this.cityData.length; i++) {\n\t\t\t\t\t// 获取首字母\n\t\t\t\t\tconst cityLetter = this.getLetter(this.cityData[i][this.formatName]).firstletter;\n\t\t\t\t\t// 获取当前城市首字母的unicode，用作后续排序\n\t\t\t\t\tconst unicode = this.getLetter(this.cityData[i][this.formatName]).unicode;\n\n\t\t\t\t\tconst index = this.cityIndexPosition(cityLetter);\n\t\t\t\t\tif (this.cityindexs.indexOf(cityLetter) === -1) {\n\t\t\t\t\t\tthis.handleCity[index].isCity = true;\n\t\t\t\t\t\tthis.cityindexs.push(cityLetter);\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.handleCity[index].citys.push({\n\t\t\t\t\t\tcityName: this.cityData[i][this.formatName],\n\t\t\t\t\t\tunicode: unicode,\n\t\t\t\t\t\toldData: this.cityData[i]\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * @desc 滑动到城市索引所在的地方\n\t\t\t * @param id String 城市索引\n\t\t\t */\n\t\t\tcityindex(id) {\n\t\t\t\tthis.toView = id;\n\t\t\t\t// //创建节点查询器\n\t\t\t\t// const query = uni.createSelectorQuery().in(this)\n\t\t\t\t// var that = this\n\t\t\t\t// that.scrollTop = 0\n\t\t\t\t// //滑动到指定位置(解决方法:重置到顶部，重新计算，影响:页面会闪一下)\n\t\t\t\t// setTimeout(() => {\n\t\t\t\t// \tquery\n\t\t\t\t// \t\t.select('#city-letter-' + (id === '#' ? '0' : id))\n\t\t\t\t// \t\t.boundingClientRect(data => {\n\t\t\t\t// \t\t\t// console.log(\"得到布局位置信息\" + JSON.stringify(data));\n\t\t\t\t// \t\t\t// console.log(\"节点离页面顶部的距离为\" + data.top);\n\t\t\t\t// \t\t\tdata ? (that.scrollTop = data.top) : void 0\n\t\t\t\t// \t\t})\n\t\t\t\t// \t\t.exec()\n\t\t\t\t// }, 0)\n\t\t\t},\n\t\t\t/**\n\t\t\t * @desc 获取城市首字母的unicode\n\t\t\t * @param letter String 城市索引\n\t\t\t */\n\t\t\tcityIndexPosition(letter) {\n\t\t\t\tif (!letter) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tconst ACode = 65;\n\t\t\t\treturn letter === '#' ? 26 : letter.charCodeAt(0) - ACode;\n\t\t\t},\n\t\t\t/** @desc 城市列表点击事件\n\t\t\t *  @param Object\n\t\t\t */\n\t\t\tcityTrigger(item) {\n\t\t\t\t// 传值到父组件\n\t\t\t\tthis.$emit('cityClick', item.oldData ? item.oldData : item);\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\">\n\t//宽度转换vw\n\t@function vww($number) {\n\t\t@return ($number / 375) * 750+rpx;\n\t}\n\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\tview {\n\t\tbox-sizing: border-box;\n\t}\n\n\t.city-serach {\n\t\twidth: 100%;\n\t\t// color: #fff;\n\t\tpadding: 0 vww(10);\n\n\t\t&-input {\n\t\t\tmargin: vww(10) 0;\n\t\t\theight: vww(40);\n\t\t\tline-height: vww(40);\n\t\t\tfont-size: vww(14);\n\t\t\tpadding: 0 vww(5);\n\t\t\tborder: 1px solid #e5e5e5;\n\t\t\tborder-radius: 3px;\n\t\t}\n\t}\n\n\t.city-select-main {\n\t\tposition: relative;\n\t\t// overflow: scroll;\n\t\t// -webkit-overflow-scrolling: touch;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\t// background: #f6f5fa;\n\t\t// overflow-y: auto;\n\t}\n\n\t.city-select {\n\t\tposition: relative;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tbackground: #ffffff;\n\n\t\t// 热门城市\n\t\t.hot-title {\n\t\t\tpadding-left: vww(23);\n\t\t\twidth: 100vw;\n\t\t\tfont-size: 14px;\n\t\t\tline-height: vww(40);\n\t\t\tcolor: #9b9b9b;\n\t\t}\n\n\t\t.hot-city {\n\t\t\tpadding-left: vww(23);\n\t\t\tpadding-right: vww(20);\n\t\t\toverflow: hidden;\n\t\t\twidth: 100vw;\n\n\t\t\t.hot-item {\n\t\t\t\tfloat: left;\n\t\t\t\tpadding: 0 vww(5);\n\t\t\t\tmargin-right: vww(16);\n\t\t\t\tmargin-bottom: vww(6);\n\t\t\t\toverflow: hidden;\n\t\t\t\twidth: vww(100);\n\t\t\t\theight: vww(31);\n\t\t\t\tfont-size: 14px;\n\t\t\t\ttext-align: center;\n\n\t\t\t\tdisplay: -webkit-box;\n\t\t\t\t-webkit-box-orient: vertical;\n\t\t\t\t-webkit-line-clamp: 1;\n\n\t\t\t\tline-height: vww(31);\n\t\t\t\tcolor: #4a4a4a;\n\t\t\t\tbackground: #fff;\n\t\t\t\tborder: 1px solid #ebebf0;\n\n\t\t\t\t&:nth-child(3n) {\n\t\t\t\t\tmargin-right: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.hot-hidden {\n\t\t\t\tdisplay: none;\n\t\t\t\tmargin-right: 0;\n\t\t\t}\n\t\t}\n\n\t\t.citys {\n\t\t\t.citys-row {\n\t\t\t\tpadding: 0 vww(18);\n\t\t\t\twidth: 100%;\n\t\t\t\tfont-size: 14px;\n\t\t\t\t// background: #fff;\n\n\t\t\t\t.citys-item-letter {\n\t\t\t\t\tmargin-left: vww(-18);\n\t\t\t\t\tpadding-left: vww(18);\n\t\t\t\t\tmargin-top: -1px;\n\t\t\t\t\twidth: 100vw;\n\t\t\t\t\tline-height: vww(30);\n\t\t\t\t\t// color: #fff;\n\t\t\t\t\t// background: #f6f5fa;\n\t\t\t\t\tborder-top: none;\n\t\t\t\t}\n\n\t\t\t\t.citys-item {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tline-height: vww(50);\n\t\t\t\t\t// color: #FFFFFF;\n\t\t\t\t\tborder-bottom: 1px solid #e5e5e5;\n\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.city-indexs-view {\n\t\t\tposition: absolute;\n\t\t\tright: 0;\n\t\t\ttop: 0;\n\t\t\tz-index: 999;\n\t\t\tdisplay: flex;\n\t\t\twidth: vww(20);\n\t\t\theight: 100%;\n\t\t\ttext-align: center;\n\n\t\t\t.city-indexs {\n\t\t\t\twidth: vww(20);\n\t\t\t\ttext-align: center;\n\t\t\t\tvertical-align: middle;\n\t\t\t\talign-self: center;\n\n\t\t\t\t.city-indexs-text {\n\t\t\t\t\tmargin-bottom: vww(10);\n\t\t\t\t\twidth: vww(20);\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tcolor: #000000;\n\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city-select.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./city-select.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627951\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}