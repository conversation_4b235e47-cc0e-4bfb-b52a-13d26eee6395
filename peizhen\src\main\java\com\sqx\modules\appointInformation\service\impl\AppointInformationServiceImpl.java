package com.sqx.modules.appointInformation.service.impl;

import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.sqx.modules.appointInformation.dao.AppointInformationDao;
import com.sqx.modules.appointInformation.service.AppointInformationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Service
public class AppointInformationServiceImpl extends ServiceImpl<AppointInformationDao, AppointInformation> implements AppointInformationService {

    @Autowired
    private AppointInformationDao informationDao;

    @Override
    public AppointInformation selectByOrdersId(Long ordersId) {
        return informationDao.selectByOrdersId(ordersId);
    }
}
