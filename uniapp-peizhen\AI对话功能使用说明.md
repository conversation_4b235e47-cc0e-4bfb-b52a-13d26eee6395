# AI对话功能使用说明

## 功能概述

基于已完成的AI对话功能后台管理部署，在uniapp-peizhen用户端实现了完整的AI问答功能，支持多轮对话、模型切换、对话历史管理等功能。

## 功能特性

### 1. 核心功能
- ✅ 多轮AI对话，支持上下文记忆
- ✅ 支持多个AI模型切换（DeepSeek/Kimi）
- ✅ 对话历史记录管理
- ✅ 本地缓存和云端同步
- ✅ 实时消息状态显示

### 2. 用户体验
- ✅ 流畅的聊天界面设计
- ✅ 消息发送中的loading状态
- ✅ 错误处理和重试机制
- ✅ 消息复制和重新发送
- ✅ 长按消息显示操作菜单

### 3. 导航集成
- ✅ 首页金刚区AI助手入口
- ✅ "我的"页面推荐工具AI助手入口
- ✅ 完整的页面路由配置

## 页面结构

### 1. AI对话列表页 (`/pages/ai/index`)
- 显示用户的所有对话记录
- 支持创建新对话
- 支持编辑对话标题
- 支持删除对话
- 支持选择AI模型

### 2. AI聊天页面 (`/pages/ai/chat`)
- 实时对话界面
- 消息列表显示
- 输入框和发送功能
- 模型切换功能
- 对话管理操作

### 3. AI模型选择页 (`/pages/ai/models`)
- 显示可用的AI模型
- 模型详细信息展示
- 模型切换功能

## 技术实现

### 1. API封装 (`common/aiRequest.js`)
```javascript
// 主要API方法
- getModels() // 获取可用模型
- createConversation() // 创建对话
- sendMessage() // 发送消息
- getConversationHistory() // 获取历史
```

### 2. 状态管理 (`common/aiStore.js`)
```javascript
// 主要功能
- 本地缓存管理
- 对话历史存储
- 用户设置保存
- 缓存清理机制
```

### 3. UI组件
- 基于uView UI组件库
- 响应式设计
- 支持多端适配

## 使用流程

### 1. 进入AI助手
**方式一：从首页进入**
1. 打开应用首页
2. 点击金刚区的"AI助手"图标
3. 进入AI对话列表页

**方式二：从"我的"页面进入**
1. 进入"我的"页面
2. 在推荐工具区域点击"AI助手"
3. 进入AI对话列表页

### 2. 创建新对话
1. 在AI对话列表页点击"新建对话"
2. 系统自动使用当前选中的AI模型
3. 跳转到聊天页面开始对话

### 3. 选择AI模型
1. 在对话列表页点击"选择模型"
2. 查看可用模型列表
3. 选择合适的模型并确认
4. 新建对话时将使用选中的模型

### 4. 进行对话
1. 在聊天页面输入消息
2. 点击发送按钮
3. 等待AI回复
4. 支持多轮连续对话

### 5. 管理对话
- **编辑标题**：长按对话或点击更多操作
- **删除对话**：在对话列表或聊天页面删除
- **切换模型**：在聊天页面切换当前对话的模型
- **复制消息**：长按消息选择复制
- **重新发送**：长按用户消息选择重新发送

## 配置说明

### 1. 路由配置 (`pages.json`)
```json
{
  "path": "pages/ai/index",
  "style": {
    "navigationBarTitleText": "AI助手"
  }
},
{
  "path": "pages/ai/chat", 
  "style": {
    "navigationBarTitleText": "AI对话"
  }
},
{
  "path": "pages/ai/models",
  "style": {
    "navigationBarTitleText": "选择AI模型"
  }
}
```

### 2. 全局注册 (`main.js`)
```javascript
import aiRequest from './common/aiRequest'
import aiStore from './common/aiStore'

Vue.prototype.$aiRequest = aiRequest;
Vue.prototype.$aiStore = aiStore;
```

## 注意事项

### 1. 用户权限
- 需要用户登录后才能使用AI对话功能
- 未登录用户会提示先登录

### 2. 网络处理
- 支持网络异常时显示缓存消息
- 发送失败时支持重试机制
- 自动处理超时和错误情况

### 3. 缓存管理
- 自动缓存最近50条消息
- 支持清理过期缓存
- 本地缓存与云端数据同步

### 4. 性能优化
- 消息列表虚拟滚动
- 图片懒加载
- 合理的缓存策略

## 扩展功能建议

### 1. 即将支持的功能
- 语音输入和播放
- 图片识别和分析
- 文件上传和处理
- 对话分享功能

### 2. 高级功能
- 对话模板和快捷回复
- 个性化设置
- 使用统计和分析
- 多语言支持

## 故障排查

### 1. 常见问题
- **无法发送消息**：检查网络连接和登录状态
- **AI不回复**：检查后端服务和API密钥
- **页面加载慢**：清理缓存或检查网络
- **消息丢失**：检查本地存储空间

### 2. 调试方法
- 查看浏览器控制台错误信息
- 检查网络请求状态
- 验证API接口返回数据
- 查看本地缓存数据

## 更新日志

### v1.0.0 (2025-08-04)
- ✅ 完成基础AI对话功能
- ✅ 实现多模型支持
- ✅ 添加本地缓存机制
- ✅ 集成导航入口
- ✅ 优化用户体验

---

**开发完成时间：** 2025-08-04  
**版本：** 1.0.0  
**开发者：** AI助手
