package com.sqx.modules.hospitalModular.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
public interface HospitalModularService extends IService<HospitalModular> {

    Result addModular(HospitalModular hospitalModular);

    Result updateModular(HospitalModular hospitalModular);

    Result deleteModular(Long modularId);

    HospitalModular getModularInfo(Long modularId);

    IPage<HospitalModular> getModularList(Integer page, Integer limit, HospitalModular hospitalModular);
}
