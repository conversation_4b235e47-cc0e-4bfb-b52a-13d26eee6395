package com.sqx.modules.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.dao.UserMoneyDetailsDao;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.entity.UserMoneyDetails;
import com.sqx.modules.app.service.UserMoneyDetailsService;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class UserMoneyDetailsServiceImpl extends ServiceImpl<UserMoneyDetailsDao, UserMoneyDetails> implements UserMoneyDetailsService {
    @Autowired
    private UserMoneyDetailsDao userMoneyDetailsDao;
    @Autowired
    private UserService userService;

    @Override
    public Result queryUserMoneyDetails(Integer page, Integer limit, Long userId,Integer classify,Integer type) {
        IPage<UserMoneyDetails> page1 = new Page(page, limit);
        QueryWrapper<UserMoneyDetails> queryWrapper = new QueryWrapper();
        queryWrapper.eq("user_id", userId);
        if(classify!=null){
            queryWrapper.eq("classify", classify);
        }
        if(type!=null){
            queryWrapper.eq("type", type);
        }
        queryWrapper.orderByDesc("create_time");
        return Result.success().put("data", baseMapper.selectPage(page1, queryWrapper));
    }

    @Override
    public Double monthIncome(String date, Long userId) {
        return baseMapper.monthIncome(date,userId);
    }

    @Override
    public IPage<HashMap<String, Object>> getRankingList(Integer page, Integer limit,Integer classify) {
        Page<HashMap<String, Object>> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return userMoneyDetailsDao.getRankingList(pages,classify);



    }

    @Override
    public IPage<HashMap<String,Object>> getAgentProfitList(Long userId, Integer page, Integer limit) {

        Page<HashMap<String,Object>> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return userMoneyDetailsDao.getAgentProfitList(pages,userId);
    }

    @Override
    public IPage<UserMoneyDetails> getBondDetails(Long userId,Integer page, Integer limit,UserMoneyDetails userMoneyDetails) {
        userMoneyDetails.setUserId(userId);
        Page<UserMoneyDetails> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        IPage<UserMoneyDetails> selectPage = baseMapper.selectPage(pages, new QueryWrapper<>(userMoneyDetails).orderByDesc("create_time"));
        for (UserMoneyDetails record : selectPage.getRecords()) {
            UserEntity userEntity = userService.getById(record.getUserId());
            if (userEntity!=null){
                record.setUserName(userEntity.getUserName());
            }

        }
        return selectPage;
    }

}
