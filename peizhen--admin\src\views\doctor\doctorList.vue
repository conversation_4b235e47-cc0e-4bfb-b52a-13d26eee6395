<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.doctorName" placeholder="医生姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.hospitalId" placeholder="选择医院" clearable @change="onHospitalChange">
          <el-option
            v-for="item in hospitalList"
            :key="item.hospitalId"
            :label="item.hospitalName"
            :value="item.hospitalId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.departmentId" placeholder="选择科室" clearable>
          <el-option
            v-for="item in departmentList"
            :key="item.departmentId"
            :label="item.departmentName"
            :value="item.departmentId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.workStatus" placeholder="工作状态" clearable>
          <el-option v-for="item in workStatusList" :key="item.code" :label="item.value" :value="parseInt(item.code)"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.isEnable" placeholder="启用状态" clearable>
          <el-option label="启用" :value="1"></el-option>
          <el-option label="禁用" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="doctorId"
        header-align="center"
        align="center"
        label="ID">
      </el-table-column>
      <el-table-column
        prop="avatar"
        header-align="center"
        align="center"
        label="头像"
        width="80">
        <template slot-scope="scope">
          <img v-if="scope.row.avatar" :src="scope.row.avatar" width="50" height="50" style="border-radius: 50%"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="doctorName"
        header-align="center"
        align="center"
        label="医生姓名">
      </el-table-column>
      <el-table-column
        prop="doctorCode"
        header-align="center"
        align="center"
        label="工号">
      </el-table-column>
      <el-table-column
        prop="hospitalName"
        header-align="center"
        align="center"
        label="医院">
      </el-table-column>
      <el-table-column
        prop="departmentName"
        header-align="center"
        align="center"
        label="科室">
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        label="职称">
      </el-table-column>
      <el-table-column
        prop="phone"
        header-align="center"
        align="center"
        label="联系电话">
      </el-table-column>
      <el-table-column
        prop="gender"
        header-align="center"
        align="center"
        label="性别">
        <template slot-scope="scope">
          <span v-if="scope.row.gender === 1">男</span>
          <span v-else-if="scope.row.gender === 2">女</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="workStatus"
        header-align="center"
        align="center"
        label="工作状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.workStatus === 1" type="success">在职</el-tag>
          <el-tag v-else-if="scope.row.workStatus === 2" type="danger">离职</el-tag>
          <el-tag v-else-if="scope.row.workStatus === 3" type="warning">休假</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="isEnable"
        header-align="center"
        align="center"
        label="状态">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isEnable"
            :active-value="1"
            :inactive-value="0"
            @change="updateStatus(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        header-align="center"
        align="center"
        label="创建时间"
        width="180">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.doctorId)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.doctorId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './doctor-add-or-update'

export default {
  data () {
    return {
      dataForm: {
        doctorName: '',
        hospitalId: '',
        departmentId: '',
        workStatus: '',
        isEnable: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      hospitalList: [],
      departmentList: [],
      // 字典数据
      workStatusList: []     // 工作状态字典
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
    this.getHospitalList()
    this.loadDictData()
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/doctor/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'doctorName': this.dataForm.doctorName,
          'hospitalId': this.dataForm.hospitalId,
          'departmentId': this.dataForm.departmentId,
          'workStatus': this.dataForm.workStatus,
          'isEnable': this.dataForm.isEnable
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取医院列表
    getHospitalList () {
      this.$http({
        url: this.$http.adornUrl('/admin/hospital/selectHospitalList'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.hospitalList = data.data || []
        }
      })
    },
    // 医院改变时获取科室列表
    onHospitalChange (hospitalId) {
      this.dataForm.departmentId = ''
      this.departmentList = []
      if (hospitalId) {
        this.getDepartmentList(hospitalId)
      }
    },
    // 获取科室列表
    getDepartmentList (hospitalId) {
      this.$http({
        url: this.$http.adornUrl('/admin/department/selectDepartmentList'),
        method: 'get',
        params: this.$http.adornParams({
          'hospitalId': hospitalId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.departmentList = data.data || []
        }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.doctorId
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/doctor/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 更新状态
    updateStatus (row) {
      this.$http({
        url: this.$http.adornUrl('/doctor/updateStatus'),
        method: 'post',
        params: this.$http.adornParams({
          'doctorId': row.doctorId,
          'isEnable': row.isEnable
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500
          })
        } else {
          this.$message.error(data.msg)
          // 恢复原状态
          row.isEnable = row.isEnable === 1 ? 0 : 1
        }
      })
    },

    // ========== 字典数据加载方法 ==========
    // 加载字典数据
    loadDictData() {
      // 加载工作状态字典
      this.loadDictByType('工作状态', (data) => {
        this.workStatusList = data
      })
    },

    // 根据类型加载字典数据
    loadDictByType(type, callback) {
      this.$http({
        url: this.$http.adornUrl('sys/dict/selectDictList'),
        method: 'get',
        params: this.$http.adornParams({ 'type': type })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          if (callback && typeof callback === 'function') {
            callback(data.data || [])
          }
        }
      })
    }
  }
}
</script>
