{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/userinfo.vue?97c4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/userinfo.vue?ef5d", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/userinfo.vue?8a97", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/userinfo.vue?5784", "uni-app:///pages/my/userinfo.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/userinfo.vue?6c2b", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/userinfo.vue?33fb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "phone", "avatar", "userName", "nick<PERSON><PERSON>", "userId", "realName", "weChatId", "password", "platform", "createTime", "money", "ji<PERSON>en", "status", "zhiFuBao", "zhiFuBaoName", "sex", "age", "onLoad", "methods", "onChooseAvatar", "console", "uni", "title", "url", "filePath", "header", "token", "name", "success", "that", "go<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadImg", "itemList", "arr", "urls", "count", "sizeType", "sourceType", "config", "info", "getUserInfo", "messagebtn", "icon", "content", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8E3vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;;EACAC;IACA;IACAC;MACAC;MACA;MACA;MACA;MACAC;QACAC;MACA;MACAD;QACA;QACAE;QAAA;QACAC;QACAC;UACAC;QACA;QACAC;QACAC;UACA;UACAC,oBACA,mDACA;YACAR;YACA;cACAQ,sBACA;cACAA;YACA;UACA;QACA;MACA;IAEA;IACAC;MACAT;QACAE;MACA;IACA;IACAQ;MACA;MAEA;QACA;QACA;MACA;MACA;MACA;MACAV;QACA;QACAW;QACAJ;UACA;UACA;YACA;YACA;YACA;YACA;YACAK;YACAZ;cACA;cACAa;YACA;UACA;YACAb;cACAc;cAAA;cACAC;cAAA;cACAC;cAAA;cACAT;gBACAP;kBACAC;gBACA;gBACA;gBACA;gBACAD;kBACA;kBACAE,+BACA;kBAAA;kBACAC;kBACAC;oBACAC;kBACA;kBACAC;kBACAC;oBACAL;oBACAM;oBACAR;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAiB;MACA;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;UACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;QACAnB;MACA;IAGA;IACA;IACAoB;MAAA;MACA;QACA;QACApB;UACAC;UACAoB;QACA;MACA;QACA;QACArB;UACAC;UACAoB;QACA;MACA;QACArB;UACAC;UACAqB;UACAf;YACA;cACA;gBACA1B;gBACAD;gBACAD;gBACAe;gBACAC;cACA;gBACA;kBACAK;oBACAC;oBACAoB;kBACA;kBACAE;oBACAvB;kBACA;gBACA;kBACAA;oBACAC;oBACAoB;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7SA;AAAA;AAAA;AAAA;AAA8iC,CAAgB,y8BAAG,EAAC,C;;;;;;;;;;;ACAlkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/userinfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/userinfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userinfo.vue?vue&type=template&id=2acfebda&\"\nvar renderjs\nimport script from \"./userinfo.vue?vue&type=script&lang=js&\"\nexport * from \"./userinfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userinfo.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/userinfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=template&id=2acfebda&\"", "var components\ntry {\n  components = {\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-radio/u-radio\" */ \"@/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"usermain\">\r\n\t\t\t<view class=\"usermain-item \">\r\n\t\t\t\t<view>头像</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\"\r\n\t\t\t\t\t\tstyle=\"width: 111rpx;height: 111rpx;border-radius: 50%;\">\r\n\t\t\t\t\t\t<image :src=\"avatar?avatar:'../../static/logo.png'\" mode=\"\"\r\n\t\t\t\t\t\t\tstyle=\"width: 111rpx;height: 111rpx;border-radius: 50%;\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t\t\t\t<image @click=\"uploadImg()\" :src=\"avatar?avatar:'../../static/logo.png'\" mode=\"\"\r\n\t\t\t\t\t\tstyle=\"width: 111rpx;height: 111rpx;border-radius: 50%;\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<!-- #endif -->\r\n\r\n\r\n\t\t\t\t\t<!-- <image src=\"../../static/logo.png\" v-if=\"avatar==null\" mode=\"\"\r\n\t\t\t\t\t\tstyle=\"width: 111rpx;height: 111rpx;border-radius: 50%;\"></image>\r\n\t\t\t\t\t<image v-else :src=\"avatar\" mode=\"\" style=\"width: 111rpx;height: 111rpx;border-radius: 50%;\">\r\n\t\t\t\t\t</image> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"usermain-item item-padding \">\r\n\t\t\t\t<view>用户名</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t\t\t<input v-model=\"userName\" type=\"nickname\" placeholder=\"请输入用户名\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"usermain-item item-padding \">\r\n\t\t\t\t<view>年龄</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t\t\t<input v-model=\"age\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"usermain-item item-padding\">\r\n\t\t\t\t<view  >姓名</view>\r\n\t\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t\t<input    v-model=\"realName\" placeholder=\"请填写您的真实姓名\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\r\n\t\t\t<view class=\"usermain-item item-padding \">\r\n\t\t\t\t<view>手机</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<!-- :disabled=\"true\" -->\r\n\t\t\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t\t\t<input :disabled=\"true\" v-model=\"phone\" placeholder=\"请输入联系电话\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"usermain-item item-padding \">\r\n\t\t\t\t<view>性别</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t\t\t<u-radio-group v-model=\"sex\">\r\n\t\t\t\t\t\t\t<u-radio shape=\"circle\" :name=\"1\">男</u-radio>\r\n\t\t\t\t\t\t\t<u-radio shape=\"circle\" active-color=\"red\" :name=\"2\">女</u-radio>\r\n\t\t\t\t\t\t</u-radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"footer-btn\">\r\n\t\t\t<view class=\"usermain-btn\" @click=\"messagebtn()\">保存</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport configdata from '../../common/config.js';\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tphone: '',\r\n\t\t\t\tavatar: '../../static/logo.png',\r\n\t\t\t\tuserName: '',\r\n\t\t\t\tnickName: '',\r\n\t\t\t\tuserId: '',\r\n\t\t\t\trealName: '',\r\n\t\t\t\tweChatId: \"\",\r\n\t\t\t\tpassword: '',\r\n\t\t\t\tplatform: '',\r\n\t\t\t\tcreateTime: '',\r\n\t\t\t\tmoney: '',\r\n\t\t\t\tjiFen: '',\r\n\t\t\t\tstatus: '',\r\n\t\t\t\tzhiFuBao: '',\r\n\t\t\t\tzhiFuBaoName: '',\r\n\t\t\t\tsex: 1,\r\n\t\t\t\tage: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.getUserInfo()\r\n\t\t\t// this.avatar = uni.getStorageSync('avatar')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//微信填写能力获取头像\r\n\t\t\tonChooseAvatar(e) {\r\n\t\t\t\tconsole.log(e.detail.avatarUrl)\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet userId = this.$queue.getData('userId');\r\n\t\t\t\tlet token = uni.getStorageSync('token');\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '上传中...'\r\n\t\t\t\t});\r\n\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t// url: config.APIHOST1 + '/alioss/upload', //仅为示例，非真实的接口地址\r\n\t\t\t\t\turl: that.config(\"APIHOST1\") + '/alioss/upload', //仅为示例，非真实的接口地址\r\n\t\t\t\t\tfilePath: e.detail.avatarUrl,\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\ttoken: token\r\n\t\t\t\t\t},\r\n\t\t\t\t\tname: 'file',\r\n\t\t\t\t\tsuccess: uploadFileRes => {\r\n\t\t\t\t\t\tlet url = JSON.parse(uploadFileRes.data).data;\r\n\t\t\t\t\t\tthat.$Request.postT(\r\n\t\t\t\t\t\t\t'/app/user/updateUserImageUrl?avatar=' + url).then(\r\n\t\t\t\t\t\t\tres => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthat.$queue.showToast(\r\n\t\t\t\t\t\t\t\t\t\t\"更新成功\");\r\n\t\t\t\t\t\t\t\t\tthat.getUserInfo()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\tgoMyAddress() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '../jifen/myaddress'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tuploadImg() {\r\n\t\t\t\tlet token = uni.getStorageSync('token')\r\n\r\n\t\t\t\tif (!token) {\r\n\t\t\t\t\tthis.goLoginInfo();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tvar url = null;\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t// itemList按钮的文字接受的是数组\r\n\t\t\t\t\titemList: [\"查看头像\", \"从相册选择图片\"],\r\n\t\t\t\t\tsuccess(e) {\r\n\t\t\t\t\t\tvar index = e.tapIndex\r\n\t\t\t\t\t\tif (index === 0) {\r\n\t\t\t\t\t\t\t// 用户点击了预览当前图片\r\n\t\t\t\t\t\t\t// 可以自己实现当前头像链接的读取\r\n\t\t\t\t\t\t\tlet url = that.avatar;\r\n\t\t\t\t\t\t\tlet arr = []\r\n\t\t\t\t\t\t\tarr.push(url)\r\n\t\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\t\t// 预览功能图片也必须是数组的\r\n\t\t\t\t\t\t\t\turls: arr\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (index === 1) {\r\n\t\t\t\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\t\t\t\tcount: 1, //默认9\r\n\t\t\t\t\t\t\t\tsizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n\t\t\t\t\t\t\t\tsourceType: ['album'], //从相册选择\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '上传中...'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tlet token = uni.getStorageSync('token');\r\n\t\t\t\t\t\t\t\t\tlet userId = uni.getStorageSync('userId');\r\n\t\t\t\t\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\t\t\t\t\t// url: 'https://peizhen.xianmaxiong.com/sqx_fast/alioss/upload', //真实的接口地址\r\n\t\t\t\t\t\t\t\t\t\turl: that.config(\"APIHOST1\") +\r\n\t\t\t\t\t\t\t\t\t\t'/alioss/upload', //真实的接口地址\r\n\t\t\t\t\t\t\t\t\t\tfilePath: res.tempFilePaths[0],\r\n\t\t\t\t\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t\t\t\t\ttoken: token\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: uploadFileRes => {\r\n\t\t\t\t\t\t\t\t\t\t\turl = JSON.parse(uploadFileRes.data);\r\n\t\t\t\t\t\t\t\t\t\t\tthat.avatar = url.data\r\n\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tconfig: function(name) {\r\n\t\t\t\tvar info = null;\r\n\t\t\t\tif (name) {\r\n\t\t\t\t\tvar name2 = name.split(\".\"); //字符分割\r\n\t\t\t\t\tif (name2.length > 1) {\r\n\t\t\t\t\t\tinfo = configdata[name2[0]][name2[1]] || null;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tinfo = configdata[name] || null;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (info == null) {\r\n\t\t\t\t\t\tlet web_config = cache.get(\"web_config\");\r\n\t\t\t\t\t\tif (web_config) {\r\n\t\t\t\t\t\t\tif (name2.length > 1) {\r\n\t\t\t\t\t\t\t\tinfo = web_config[name2[0]][name2[1]] || null;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tinfo = web_config[name] || null;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn info;\r\n\t\t\t},\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\tlet userId = uni.getStorageSync('userId')\r\n\t\t\t\tthis.$Request.get(\"/app/user/selectUserById\").then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.$queue.setData('avatar', res.data.avatar);\r\n\t\t\t\t\t\tthis.$queue.setData('userId', res.data.userId);\r\n\t\t\t\t\t\tthis.$queue.setData('userName', res.data.userName);\r\n\t\t\t\t\t\tthis.$queue.setData('phone', res.data.phone);\r\n\t\t\t\t\t\tthis.$queue.setData('age', res.data.age);\r\n\t\t\t\t\t\tthis.sex = res.data.sex\r\n\t\t\t\t\t\tthis.age = res.data.age\r\n\t\t\t\t\t\tthis.phone = res.data.phone;\r\n\t\t\t\t\t\tthis.avatar = res.data.avatar;\r\n\t\t\t\t\t\tthis.userName = res.data.userName;\r\n\t\t\t\t\t\tif (this.userName == null) {\r\n\t\t\t\t\t\t\tthis.userName = res.data.nickName;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.userName = res.data.userName;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\r\n\r\n\t\t\t},\r\n\t\t\t// 保存\r\n\t\t\tmessagebtn() {\r\n\t\t\t\tif (!this.userName) {\r\n\t\t\t\t\t// this.$queue.showToast('用户名不能为空');\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"用户名不能为空\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (!this.phone) {\r\n\t\t\t\t\t// this.$queue.showToast('用户名不能为空');\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"联系电话不能为空\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '温馨提示',\r\n\t\t\t\t\t\tcontent: '确定保存信息',\r\n\t\t\t\t\t\tsuccess: e => {\r\n\t\t\t\t\t\t\tif (e.confirm) {\r\n\t\t\t\t\t\t\t\tthis.$Request.postJson(\"/app/user/updateUser\", {\r\n\t\t\t\t\t\t\t\t\tuserName: this.userName,\r\n\t\t\t\t\t\t\t\t\tavatar: this.avatar,\r\n\t\t\t\t\t\t\t\t\tphone: this.phone,\r\n\t\t\t\t\t\t\t\t\tsex: this.sex,\r\n\t\t\t\t\t\t\t\t\tage: this.age\r\n\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// userphone(){\r\n\t\t// \tuni.navigateTo({\r\n\t\t// \t\turl:'/pages/my/userphone'\r\n\t\t// \t})\r\n\t\t// }\r\n\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\t/* background: #1c1b20; */\r\n\t}\r\n\r\n\tbutton {\r\n\t\tpadding: 0 !important;\r\n\t}\r\n\r\n\tbutton::after {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.usermain {\r\n\t\tbackground: #ffffff;\r\n\t\t/* color: #fff; */\r\n\t}\r\n\r\n\t.usermain-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin: 0 40rpx;\r\n\t\tpadding: 10rpx 0;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1rpx solid #e5e5e5;\r\n\t\t/* border-bottom: 2rpx solid #f2f2f2; */\r\n\t}\r\n\r\n\t.usermain-item.item-padding {\r\n\t\t/* padding: 0; */\r\n\t}\r\n\r\n\t.cu-form-group {\r\n\t\tpadding: 0;\r\n\t\tbackground: #ffffff;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.cu-form-group input {\r\n\t\tbackground: #ffffff;\r\n\t\tfont-size: 28rpx;\r\n\t\t/* color: #fff; */\r\n\r\n\t}\r\n\r\n\t.footer-btn {\r\n\t\tmargin-top: 150rpx;\r\n\t}\r\n\r\n\t.footer-btn .usermain-btn {\r\n\t\tcolor: #FFFFFF;\r\n\t\tbackground: #557EFD;\r\n\t\ttext-align: center;\r\n\t\twidth: 450rpx;\r\n\t\theight: 80rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tborder-radius: 40rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userinfo.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447625837\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}