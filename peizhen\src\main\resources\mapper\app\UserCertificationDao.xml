<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.app.dao.UserCertificationDao">
    <update id="updateAuthentication">
        update user_certification
        set status = 2
        where user_id = #{userId}

    </update>

    <select id="queryCertification" resultType="com.sqx.modules.app.entity.UserCertification">
        select *, IFNULL((SELECT GROUP_CONCAT(service_name)
        FROM hospital_employ h
        WHERE find_in_set(h.service_id, u.service_ids)), "暂未填写") AS serviceName from user_certification u where 1=1
        <if test="status!=null and status!=''">
            and status=#{status}
        </if>
        <if test="name!=null and name!=''">
            and name like #{name}
        </if>
        <if test="authentication!=null">
            and authentication = #{authentication}
        </if>
        order by create_time desc
    </select>
    <select id="queryUserCertification" resultType="com.sqx.modules.app.entity.UserEntity">
        SELECT
        t.*
        FROM
        tb_user t
        LEFT JOIN user_certification u ON u.user_id = t.user_id
        WHERE
        1=1
        <if test="name!=null and name !=''">
            and t.user_name like #{name}
        </if>
        <if test="phone!=null and phone!=''">
            and t.phone like #{phone}
        </if>
        <if test="authentication!=null">
            and authentication = #{authentication}
        </if>
        and u.STATUS =1
        order by u.create_time desc
    </select>
    <select id="getUserCertification" resultType="com.sqx.modules.app.entity.UserCertification">
        select `id`,`name`,`user_id`,`create_time`,`update_time`,`sex`,`authentication`,`work_age`,`age`,`province`,`city`,`district`,`avatar`,
               `service_ids`,`details`,`status`,`remek`,
               IFNULL((SELECT GROUP_CONCAT(service_name)
                       FROM hospital_employ h
                       WHERE find_in_set(h.service_id, u.service_ids)), "暂未填写") AS serviceName
        from user_certification u
        where user_id = #{userId}
    </select>


    <select id="selectUserCertificationByUserId" resultType="com.sqx.modules.app.entity.UserCertification">
        select *,IFNULL((SELECT GROUP_CONCAT(service_name)
                       FROM hospital_employ h
                       WHERE find_in_set(h.service_id, u.service_ids)), "暂未填写") AS serviceName
        from user_certification u
        where user_id = #{userId}
    </select>

</mapper>