{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/loginphone.vue?9552", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/loginphone.vue?b6c4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/loginphone.vue?fbbb", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/loginphone.vue?67da", "uni-app:///pages/public/loginphone.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/loginphone.vue?8d76", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/loginphone.vue?7927"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "phone", "password", "banners", "invitation", "loginName", "sending", "sendTime", "count", "onLoad", "methods", "forget", "uni", "url", "register", "inputChange", "navBack", "<PERSON><PERSON><PERSON><PERSON>", "openId", "getIsVip", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiC7vB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IAEAC;MACAC;QACAC;MACA;IACA;IACAC;MACAF;QACAC;MACA;IACA;IACAE;MACA;MACA;IACA;IACAC;MACAJ;IACA;IAEAK;MAAA;MACA;MACA;MACA,IACAhB,QAEA,KAFAA;QACAC,WACA,KADAA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;QACA;UACAA;UACAD;UACAiB;QACA;UACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA;YACAN;YACAA;cACAC;YACA;UACA;YACAD;YACA;UACA;QACA;MACA;IACA;IACAO;MAAA;MACA;QACA;UACA;UACAC;UACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACzHA;AAAA;AAAA;AAAA;AAA43C,CAAgB,iuCAAG,EAAC,C;;;;;;;;;;;ACAh5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/public/loginphone.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/public/loginphone.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./loginphone.vue?vue&type=template&id=20053b3c&\"\nvar renderjs\nimport script from \"./loginphone.vue?vue&type=script&lang=js&\"\nexport * from \"./loginphone.vue?vue&type=script&lang=js&\"\nimport style0 from \"./loginphone.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/public/loginphone.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./loginphone.vue?vue&type=template&id=20053b3c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./loginphone.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./loginphone.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<view class=\"wrapper\">\n\t\t\t<!-- <view style=\"text-align: center;\">\n\t\t\t\t<image src=\"../../static/logo.png\" style=\"border-radius: 64upx;\"></image>\n\t\t\t</view>\n -->\n\t\t\t<view class=\"input-content\">\n\t\t\t\t<view class=\"cu-form-group\"\n\t\t\t\t\tstyle=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\n\t\t\t\t\t<view class=\"title text-black\">账号</view>\n\t\t\t\t\t<input type=\"number\" :value=\"phone\" placeholder=\"请输入手机号\" maxlength=\"11\" data-key=\"phone\"\n\t\t\t\t\t\t@input=\"inputChange\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"cu-form-group\" style=\"border: 2upx solid whitesmoke;border-radius: 30px\">\n\t\t\t\t\t<view class=\"title text-black\">密码</view>\n\t\t\t\t\t<input type=\"password\" placeholder=\"请输入密码\" maxlength=\"20\" :value=\"password\" data-key=\"password\"\n\t\t\t\t\t\t@input=\"inputChange\" @confirm=\"toLogin\" />\n\t\t\t\t\t<text class=\"send-msg\" @click=\"forget\">忘记密码</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<button class=\"confirm-btn\" @click=\"toLogin\">登录</button>\n\n\t\t\t<view style=\"margin-top: 32px;text-align: center\">\n\t\t\t\t<view><text style=\"font-size: 28upx;\">没有账号？</text>\n\t\t\t\t\t<text style=\"color: #557EFD\" @click=\"register()\">立即注册</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tphone: '',\n\t\t\t\tpassword: '',\n\t\t\t\tbanners: [],\n\t\t\t\tinvitation: '',\n\t\t\t\tloginName: '',\n\t\t\t\tsending: false,\n\t\t\t\tsendTime: '获取验证码',\n\t\t\t\tcount: 60,\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\n\t\t},\n\t\tmethods: {\n\n\t\t\tforget() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/public/forgetPwd'\n\t\t\t\t});\n\t\t\t},\n\t\t\tregister() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/public/register'\n\t\t\t\t});\n\t\t\t},\n\t\t\tinputChange(e) {\n\t\t\t\tconst key = e.currentTarget.dataset.key;\n\t\t\t\tthis[key] = e.detail.value;\n\t\t\t},\n\t\t\tnavBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\n\t\t\ttoLogin() {\n\t\t\t\tthis.$queue.loginClear();\n\t\t\t\tlet openid = this.$queue.getData(\"openid\");\n\t\t\t\tconst {\n\t\t\t\t\tphone,\n\t\t\t\t\tpassword\n\t\t\t\t} = this;\n\t\t\t\tif (!phone) {\n\t\t\t\t\tthis.$queue.showToast(\"请输入手机号\");\n\t\t\t\t} else if (phone.length != 11) {\n\t\t\t\t\tthis.$queue.showToast(\"请输入正确的手机号\");\n\t\t\t\t} else if (!password) {\n\t\t\t\t\tthis.$queue.showToast(\"请输入密码\");\n\t\t\t\t} else {\n\t\t\t\t\tthis.$queue.showLoading(\"正在登录中...\");\n\t\t\t\t\tthis.$Request.post(\"/app/Login/loginApp\", {\n\t\t\t\t\t\tpassword: password,\n\t\t\t\t\t\tphone: phone,\n\t\t\t\t\t\topenId: this.$queue.getData('openid')\n\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tthis.$queue.setData(\"userId\", res.user.userId);\n\t\t\t\t\t\t\tthis.$queue.setData(\"token\", res.token);\n\t\t\t\t\t\t\tthis.$queue.setData(\"phone\", res.user.phone);\n\t\t\t\t\t\t\tthis.$queue.setData(\"userName\", res.user.userName);\n\t\t\t\t\t\t\tthis.$queue.setData(\"avatar\", res.user.avatar);\n\t\t\t\t\t\t\tthis.$queue.setData(\"invitationCode\", res.user.invitationCode);\n\t\t\t\t\t\t\tthis.$queue.setData(\"inviterCode\", res.user.inviterCode);\n\n\t\t\t\t\t\t\tthis.getIsVip()\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\turl: '/pages/my/index'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.$queue.showToast(res.msg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetIsVip() {\n\t\t\t\tthis.$Request.get(\"/app/UserVip/isUserVip\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t// this.isVip = res.data\n\t\t\t\t\t\tconsole.log(res.data)\n\t\t\t\t\t\tthis.$queue.setData(\"isVip\", res.data);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t}\n</script>\n\n<style lang='scss'>\n\tpage {\n\t\theight: 100%;\n\t\tbackground: #F5F5F5 !important;\n\t}\n\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.send-msg {\n\t\tborder-radius: 30px;\n\t\tcolor: black;\n\t\tbackground: white;\n\t\theight: 30px;\n\t\tfont-size: 14px;\n\t\tline-height: 30px;\n\t}\n\n\t.container {\n\t\ttop: 0;\n\t\tpadding-top: 32upx;\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: hidden;\n\t\tbackground: #FFFFFF !important;\n\t}\n\n\t.wrapper {\n\t\tposition: relative;\n\t\tz-index: 90;\n\t\tbackground: #FFFFFF;\n\t\tpadding-bottom: 32upx;\n\t}\n\n\n\t.input-content {\n\t\t/* margin-top: 300upx; */\n\t\t/* padding-top: 300upx; */\n\t\tpadding: 32upx 80upx;\n\n\t}\n\n\n\t.confirm-btn {\n\t\twidth: 600upx;\n\t\theight: 80upx;\n\t\tline-height: 80upx;\n\t\tborder-radius: 60upx;\n\t\tmargin-top: 32upx;\n\t\tbackground: #557EFD;\n\t\tcolor: #fff;\n\t\tfont-size: 32upx;\n\n\t\t&:after {\n\t\t\tborder-radius: 60px;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./loginphone.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./loginphone.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627772\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}