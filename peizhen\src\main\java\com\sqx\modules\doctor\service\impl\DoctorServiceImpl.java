package com.sqx.modules.doctor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.service.DepartmentService;
import com.sqx.modules.doctor.dao.DoctorDao;
import com.sqx.modules.doctor.entity.Doctor;
import com.sqx.modules.doctor.service.DoctorService;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.service.HospitalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * <p>
 * 医生管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Service
public class DoctorServiceImpl extends ServiceImpl<DoctorDao, Doctor> implements DoctorService {

    @Autowired
    private DoctorDao doctorDao;

    @Autowired
    private HospitalService hospitalService;

    @Autowired
    private DepartmentService departmentService;

    @Override
    public PageUtils queryPage(Integer page, Integer limit, String doctorName, 
                              Long hospitalId, Long departmentId, Integer workStatus, Integer isEnable) {
        IPage<Doctor> pages = doctorDao.selectDoctorPage(
            new Page<>(page, limit), 
            doctorName, hospitalId, departmentId, workStatus, isEnable
        );
        return new PageUtils(pages);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addDoctor(Doctor doctor) {
        // 验证医院是否存在
        Hospital hospital = hospitalService.getById(doctor.getHospitalId());
        if (hospital == null) {
            return Result.error("选择的医院不存在!");
        }

        // 验证科室是否存在
        Department department = departmentService.getById(doctor.getDepartmentId());
        if (department == null) {
            return Result.error("选择的科室不存在!");
        }

        // 验证科室是否属于该医院
        if (!department.getHospitalId().equals(doctor.getHospitalId())) {
            return Result.error("科室不属于该医院!");
        }

        // 验证医生工号是否重复
        if (doctor.getDoctorCode() != null && !doctor.getDoctorCode().isEmpty()) {
            Integer count = doctorDao.selectCount(
                new QueryWrapper<Doctor>().eq("doctor_code", doctor.getDoctorCode())
            );
            if (count > 0) {
                return Result.error("医生工号已存在!");
            }
        }

        // 设置默认值
        if (doctor.getWorkStatus() == null) {
            doctor.setWorkStatus(1); // 默认在职
        }
        if (doctor.getIsEnable() == null) {
            doctor.setIsEnable(1); // 默认启用
        }
        if (doctor.getSort() == null) {
            doctor.setSort(0);
        }
        
        doctor.setCreateTime(LocalDateTime.now());
        doctor.setUpdateTime(LocalDateTime.now());

        return Result.upStatus(doctorDao.insert(doctor));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateDoctor(Doctor doctor) {
        // 验证医生是否存在
        Doctor existDoctor = doctorDao.selectById(doctor.getDoctorId());
        if (existDoctor == null) {
            return Result.error("医生不存在!");
        }

        // 验证医院是否存在
        Hospital hospital = hospitalService.getById(doctor.getHospitalId());
        if (hospital == null) {
            return Result.error("选择的医院不存在!");
        }

        // 验证科室是否存在
        Department department = departmentService.getById(doctor.getDepartmentId());
        if (department == null) {
            return Result.error("选择的科室不存在!");
        }

        // 验证科室是否属于该医院
        if (!department.getHospitalId().equals(doctor.getHospitalId())) {
            return Result.error("科室不属于该医院!");
        }

        // 验证医生工号是否重复（排除自己）
        if (doctor.getDoctorCode() != null && !doctor.getDoctorCode().isEmpty()) {
            Integer count = doctorDao.selectCount(
                new QueryWrapper<Doctor>()
                    .eq("doctor_code", doctor.getDoctorCode())
                    .ne("doctor_id", doctor.getDoctorId())
            );
            if (count > 0) {
                return Result.error("医生工号已存在!");
            }
        }

        doctor.setUpdateTime(LocalDateTime.now());
        return Result.upStatus(doctorDao.updateById(doctor));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteDoctor(Long[] doctorIds) {
        if (doctorIds == null || doctorIds.length == 0) {
            return Result.error("请选择要删除的医生!");
        }

        int result = doctorDao.deleteBatchIds(Arrays.asList(doctorIds));
        return Result.upStatus(result);
    }
}
