{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/peizhen/peizhen.vue?2353", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/peizhen/peizhen.vue?9f92", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/peizhen/peizhen.vue?4bdc", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/peizhen/peizhen.vue?a81e", "uni-app:///my/peizhen/peizhen.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/peizhen/peizhen.vue?a347", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/peizhen/peizhen.vue?5921"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "classType", "swiperList", "tui<PERSON><PERSON>", "tuiImage", "invitationCode", "orderTakingUserName", "orderTakingUserId", "onShareAppMessage", "path", "title", "imageUrl", "onShareTimeline", "modularId", "onLoad", "that", "methods", "getBannerList", "classify", "goNav", "uni", "url", "serviceType", "content", "success", "console", "getlist", "item"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAsuB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2C1vB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC,+CACAJ;MAAA;MACAK;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;MACAH,qFACAI;MAAA;MACAH;MACAC;IACA;EACA;EACAG;IACA;IACA;IACA;IACA;IACA;MACA;MACA;IACA;IACA;IACA;IACA;IACA;MACAC;IACA;IAEA;MACA;MACAA;IACA;IAEA;MACAA;MACAA;QAAA;QACA;UACAA;QACA;MACA;MACAA;QAAA;QACA;UACAA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACAC;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACAC;UACAC,iFACAC,mEACA;QACA;MACA;QACAF;UACAV;UACAa;UACAC;YACA;cACAC;cACAL;gBACAC;cACA;YACA;cACAI;YACA;UACA;QACA;MACA;IAEA;IACAC;MAAA;MACA;QACAb;MACA;MACA;QACA;UACA;UACA;YACA;cACAc;YACA;cACAA;YACA;UACA;QACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrKA;AAAA;AAAA;AAAA;AAAy3C,CAAgB,8tCAAG,EAAC,C;;;;;;;;;;;ACA74C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/peizhen/peizhen.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/peizhen/peizhen.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./peizhen.vue?vue&type=template&id=7e3e2272&\"\nvar renderjs\nimport script from \"./peizhen.vue?vue&type=script&lang=js&\"\nexport * from \"./peizhen.vue?vue&type=script&lang=js&\"\nimport style0 from \"./peizhen.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/peizhen/peizhen.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peizhen.vue?vue&type=template&id=7e3e2272&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.classType, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.tags.length\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peizhen.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peizhen.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 50rpx;\">\r\n\t\t<!-- 背景图 -->\r\n\t\t<view class=\"bg\">\r\n\t\t\t<swiper :indicator-dots=\"false\" :autoplay=\"true\" style=\"width: 100%;height: 100%;\" :interval=\"1000\"\r\n\t\t\t\t:duration=\"300\">\r\n\t\t\t\t<swiper-item v-for=\"(item,index) in swiperList\" :key=\"index\">\r\n\t\t\t\t\t<image :src=\"item.imageUrl\" mode=\"\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\r\n\t\t</view>\r\n\t\t<!-- 服务列表 -->\r\n\t\t<view style=\"margin-top: -49upx;\">\r\n\t\t\t<view class=\"list flex justify-center margin-bottom-sm\" v-for=\"(item,index) in classType\" :key=\"index\"\r\n\t\t\t\t@click=\"goNav(item)\">\r\n\t\t\t\t<view class=\"list-box flex justify-center align-center\">\r\n\t\t\t\t\t<view class=\"list-box-c flex justify-between align-center\">\r\n\t\t\t\t\t\t<view class=\"list-box-c-l flex align-center\">\r\n\t\t\t\t\t\t\t<image :src=\"item.img?item.img:'../../static/logo.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<view class=\"list-box-c-l-c\">\r\n\t\t\t\t\t\t\t\t<view class=\"list-box-c-l-c-t\">{{item.serviceName}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"list-box-c-l-c-t-b flex-wrap\">\r\n\t\t\t\t\t\t\t\t\t<view v-for=\"(ite,ind) in item.tags\" :key=\"ind\">\r\n\t\t\t\t\t\t\t\t\t\t{{ite}} <text v-if=\"ind!=item.tags.length-1\" class=\"\">|</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"list-box-c-r\">\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#333333\" size=\"32\"></u-icon>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"list-box-c-r\">\r\n\t\t\t\t\t\t\t<span>¥</span><text>{{item.money}}</text>元/{{item.company==1?'天':'次'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tclassType: [],\r\n\t\t\t\tswiperList: [],\r\n\t\t\t\ttuiName: '',\r\n\t\t\t\ttuiImage: '',\r\n\t\t\t\tinvitationCode: '',\r\n\t\t\t\torderTakingUserName: '',\r\n\t\t\t\torderTakingUserId: '',\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShareAppMessage(res) {\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/my/peizhen/peizhen?invitation=' + this\r\n\t\t\t\t\t.invitationCode + '&modularId=' + this.modularId, //这是为了传参   onload(data){let id=data.id;} \r\n\t\t\t\ttitle: this.tuiName,\r\n\t\t\t\timageUrl: this.tuiImage\r\n\t\t\t}\r\n\t\t},\r\n\t\t/*\r\n\t\t * uniapp微信小程序分享页面到微信朋友圈\r\n\t\t */\r\n\t\tonShareTimeline(res) {\r\n\t\t\treturn {\r\n\t\t\t\tpath: '/my/peizhen/peizhen?invitation=' + this.invitationCode + '&modularId=' + this\r\n\t\t\t\t.modularId, //这是为了传参   onload(data){let id=data.id;} \r\n\t\t\t\ttitle: this.tuiName,\r\n\t\t\t\timageUrl: this.tuiImage\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tlet that = this\r\n\t\t\tthis.modularId = e.modularId\r\n\t\t\tthis.getBannerList()\r\n\t\t\tthis.getlist()\r\n\t\t\tif (e.orderTakingUserName && e.orderTakingUserId) {\r\n\t\t\t\tthis.orderTakingUserName = e.orderTakingUserName\r\n\t\t\t\tthis.orderTakingUserId = e.orderTakingUserId\r\n\t\t\t}\r\n\t\t\t// 分享\r\n\t\t\tthis.myId = uni.getStorageSync('userId')\r\n\t\t\t// 获取邀请码保存到本地\r\n\t\t\tif (e.invitation) {\r\n\t\t\t\tthat.$queue.setData('inviterCode', e.invitation);\r\n\t\t\t}\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (e.scene) {\r\n\t\t\t\tconst scene = decodeURIComponent(e.scene);\r\n\t\t\t\tthat.$queue.setData('inviterCode', scene.split(',')[0]);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tif (this.myId) {\r\n\t\t\t\tthat.invitationCode = uni.getStorageSync('invitationCode')\r\n\t\t\t\tthat.$Request.getT('/app/common/type/276').then(res => { //分享标题 276\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.tuiName = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthat.$Request.getT('/app/common/type/277').then(res => { //分享图 277\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthat.tuiImage = res.data.value\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//获取轮播图\r\n\t\t\tgetBannerList() {\r\n\t\t\t\tthis.$Request.get(\"/app/banner/selectBannerList\", {\r\n\t\t\t\t\tclassify: 7\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.swiperList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoNav(e) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (uni.getStorageSync('token')) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/index/game/orderDet?serviceId=' + e.serviceId + '&serviceType=' + e\r\n\t\t\t\t\t\t\t.serviceType + '&orderTakingUserName=' + that.orderTakingUserName +\r\n\t\t\t\t\t\t\t'&orderTakingUserId=' + that.orderTakingUserId\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tgetlist() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tmodularId: this.modularId\r\n\t\t\t\t}\r\n\t\t\t\tthis.$Request.get('/app/hospitalEmploy/getHospitalEmployList', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.classType = res.data.records\r\n\t\t\t\t\t\tthis.classType.map(item => {\r\n\t\t\t\t\t\t\tif (item.tags) {\r\n\t\t\t\t\t\t\t\titem.tags = item.tags.split(',')\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\titem.tag = []\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n\r\n\t.bg {\r\n\t\twidth: 100%;\r\n\t\theight: 280rpx;\r\n\t\tz-index: 1;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t.list {\r\n\t\twidth: 100%;\r\n\t\theight: 192rpx;\r\n\t\tz-index: 2;\r\n\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t.list-box {\r\n\t\t\twidth: 686rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tz-index: 2;\r\n\r\n\t\t\t.list-box-c {\r\n\t\t\t\twidth: 606rpx;\r\n\t\t\t\theight: 85rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.list-box-c-l {\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 85rpx;\r\n\t\t\t\t\theight: 85rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.list-box-c-l-c {\r\n\t\t\t\tmargin-left: 26rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.list-box-c-l-c-t {\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.list-box-c-l-c-t-b {\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.list-box-c-r {\r\n\t\t\t\twidth: 220upx;\r\n\t\t\t\ttext-align: right;\r\n\t\t\t\tcolor: #FF2D01;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tletter-spacing: 5rpx;\r\n\r\n\t\t\t\tspan {\r\n\t\t\t\t\tfont-size: 38rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 48rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tletter-spacing: 0rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peizhen.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./peizhen.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627187\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}