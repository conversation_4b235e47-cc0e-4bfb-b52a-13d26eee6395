/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : localhost:3306
 Source Schema         : peizhen-text

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 25/05/2023 14:36:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for address
-- ----------------------------
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address`
(
    `address_id`      int(11)                                                  NOT NULL AUTO_INCREMENT COMMENT '地址id',
    `name`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '姓名',
    `phone`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '电话',
    `province`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '省',
    `city`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '市',
    `district`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '区',
    `details_address` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '详细地址',
    `is_default`      int(11)                                                  NULL DEFAULT NULL COMMENT '是否是默认地址 0 否 1是',
    `create_time`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '时间',
    `user_id`         int(11)                                                  NULL DEFAULT NULL,
    `longitude`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL,
    `latitude`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL,
    PRIMARY KEY (`address_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 119
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of address
-- ----------------------------

-- ----------------------------
-- Table structure for app
-- ----------------------------
DROP TABLE IF EXISTS `app`;
CREATE TABLE `app`
(
    `id`              bigint(20)                                              NOT NULL AUTO_INCREMENT,
    `android_wgt_url` varchar(600) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
    `create_at`       varchar(600) CHARACTER SET big5 COLLATE big5_chinese_ci NULL DEFAULT NULL,
    `des`             varchar(600) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
    `ios_version`     varchar(255) CHARACTER SET big5 COLLATE big5_chinese_ci NULL DEFAULT NULL,
    `ios_wgt_url`     varchar(600) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
    `method`          varchar(600) CHARACTER SET big5 COLLATE big5_chinese_ci NULL DEFAULT NULL,
    `version`         varchar(600) CHARACTER SET big5 COLLATE big5_chinese_ci NULL DEFAULT NULL,
    `wgt_url`         varchar(600) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6
  CHARACTER SET = big5
  COLLATE = big5_chinese_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of app
-- ----------------------------

-- ----------------------------
-- Table structure for apply
-- ----------------------------
DROP TABLE IF EXISTS `apply`;
CREATE TABLE `apply`
(
    `apply_id`      int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '申请id',
    `apply_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `apply_phone`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
    `apply_age`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '年龄',
    `apply_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
    `classify`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类 1推广员 2代理商',
    `user_id`       int(11)                                                       NULL DEFAULT NULL COMMENT '用户id',
    `status`        int(11)                                                       NULL DEFAULT NULL COMMENT '状态 1待审核 2通过 3拒绝',
    `audit_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核内容',
    `create_time`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`apply_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 41
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of apply
-- ----------------------------

-- ----------------------------
-- Table structure for appoint_information
-- ----------------------------
DROP TABLE IF EXISTS `appoint_information`;
CREATE TABLE `appoint_information`
(
    `appoint_id`           int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '预约信息id',
    `orders_id`            int(11)                                                       NULL DEFAULT NULL COMMENT '订单id',
    `hospital_id`          int(11)                                                       NULL DEFAULT NULL COMMENT '医院id',
    `hospital_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医院名称',
    `type`                 int(1)                                                        NULL DEFAULT NULL COMMENT '类型 1陪诊 2陪护 3助浴 4护理',
    `patient_id`           int(11)                                                       NULL DEFAULT NULL COMMENT '就诊人信息id',
    `hope_time`            datetime(0)                                                   NULL DEFAULT NULL COMMENT '预约时间',
    `department_id`        int(11)                                                       NULL DEFAULT NULL COMMENT '科室id',
    `department_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名称',
    `service_id`           int(11)                                                       NULL DEFAULT NULL COMMENT '服务id',
    `service_name`         varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '服务名称',
    `bad_no`               varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '床号(详:字典)',
    `symptom`              varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '症状(详:字典)',
    `self_ability`         varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '自理能力(详:字典)',
    `nursing_needs`        varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '护理需求(详:字典)',
    `service_num`          int(11)                                                       NULL DEFAULT NULL COMMENT '服务天数',
    `phone`                varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '联系电话',
    `remarks`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '备注',
    `lat`                  double(10, 6)                                                 NULL DEFAULT NULL COMMENT '经度(医院地址)',
    `lng`                  double(10, 6)                                                 NULL DEFAULT NULL COMMENT '纬度(医院地址)',
    `province`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '省(医院地址)',
    `city`                 varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '市(医院地址)',
    `district`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '县(医院地址)',
    `details_address`      varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址(医院地址)',
    `address_id`           int(11)                                                       NULL DEFAULT NULL COMMENT '用户收货地址id',
    `user_province`        varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '省(用户地址)',
    `user_city`            varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '市(用户地址)',
    `user_district`        varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '县(用户地址)',
    `user_name`            varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '收货人姓名',
    `user_phone`           varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '收货人电话',
    `user_details_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址(用户地址)',
    `report_type`          varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '报告类型(详:字典)',
    `exclusive_type`       varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '专享归属(详:字典)',
    `drugs_type`           varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '药物类型(详:字典)',
    `drugs_name`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药物名称',
    `service_type`         int(11)                                                       NULL DEFAULT NULL COMMENT '服务类型',
    `user_lat`             double(10, 6)                                                 NULL DEFAULT NULL COMMENT '经度(用户地址)',
    `user_lng`             double(10, 6)                                                 NULL DEFAULT NULL COMMENT '纬度(用户地址)',
    `img_remarks`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '图片备注',
    PRIMARY KEY (`appoint_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 475
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of appoint_information
-- ----------------------------

-- ----------------------------
-- Table structure for banner
-- ----------------------------
DROP TABLE IF EXISTS `banner`;
CREATE TABLE `banner`
(
    `id`          bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT 'bannerid',
    `create_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `name`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '名称',
    `image_url`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片地址',
    `state`       int(2)                                                        NULL DEFAULT NULL COMMENT '状态1正常2隐藏',
    `classify`    int(2)                                                        NULL DEFAULT NULL COMMENT '分类1banner图2金刚区分类',
    `url`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转地址 ',
    `sort`        int(4)                                                        NULL DEFAULT NULL COMMENT '顺序',
    `describes`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 37
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banner
-- ----------------------------
INSERT INTO `banner`
VALUES (11, '2023-03-31 14:01:23', '诊前咨询',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/78b0d164a34804801989994ce4a80156.png', 1, 2, '联系客服',
        NULL, '诊前咨询');
INSERT INTO `banner`
VALUES (12, '2022-12-08 20:49:43', '诊前约号',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/70a75d1249cc773a41a3d129a59801da.png', 1, 2,
        '/pages/index/game/orderDet?serviceId=6&serviceType=3', NULL, '诊前约号');
INSERT INTO `banner`
VALUES (13, '2022-12-08 20:49:43', '代办问诊',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/d0fa2eb7631784acb9966a4a2681acff.png', 1, 2,
        '/pages/index/game/orderDet?serviceId=9&serviceType=4', NULL, '代办问诊');
INSERT INTO `banner`
VALUES (14, '2022-12-08 20:49:43', '代办买药',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/c4297b9eafd44d8ffc09ddc2d97394ac.png', 1, 2,
        '/pages/index/game/orderDet?serviceId=10&serviceType=7', NULL, '代办买药');
INSERT INTO `banner`
VALUES (15, '2022-12-08 20:49:43', '送取结果',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/ed624d37c91ecdb852cdb2e31b2e40f9.png', 1, 2,
        '/pages/index/game/orderDet?serviceId=7&serviceType=5', NULL, '送取结果');
INSERT INTO `banner`
VALUES (27, '2023-03-31 14:01:23', '轮播',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/a7db3623207eddac936f85d2082e4254.png', 1, 1, '客服',
        NULL, '轮播');
INSERT INTO `banner`
VALUES (28, '2022-08-22 15:16:08', '申领指南',
        'http://tctg.xianmaxiong.com/file/uploadPath/2022/11/08/edd237c4466a413eed103cbd085b68f8.png', 1, 6,
        '/pages/index/game/gameList?gameId=2&name=不知道', NULL, '申领指南');
INSERT INTO `banner`
VALUES (30, '2022-12-02 19:31:28', '邀请背景图',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/12d400db7baedc7e7508740942cbc03a.png', 1, 5,
        'https://xichewap.xianmxkj.com', NULL, '邀请背景图');
INSERT INTO `banner`
VALUES (31, '2022-12-02 20:12:36', '专业陪诊服务',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/ec8f5d518fcff52dde1fb65f64a1403b.png', 1, 7,
        '/pages/index/game/gameList?gameId=2&name=不知道', NULL, '专业陪诊服务');
INSERT INTO `banner`
VALUES (34, '2022-12-02 20:13:37', '院内陪护',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/66ac671912e4afaef187a2088bcfc5f4.png', 1, 8, NULL,
        NULL, '院内陪护');
INSERT INTO `banner`
VALUES (35, '2022-12-02 20:13:58', '优享陪诊',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/c9972046aff3dda2f6120851e9f3d29f.png', 1, 9, NULL,
        NULL, '优享陪诊');
INSERT INTO `banner`
VALUES (36, '2023-05-23 18:29:12', '活动入口图片',
        'https://peizhenv6.xianmaxiong.com/file/uploadPath/2023/05/23/8c2b43b54e759c2b1dbd7ba190a5ec59.png', 1, 10,
        '/pages/my/invitationUser', NULL, '活动入口图片');

-- ----------------------------
-- Table structure for car
-- ----------------------------
DROP TABLE IF EXISTS `car`;
CREATE TABLE `car`
(
    `car_id`       int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '车辆id',
    `car_type`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆型号',
    `car_classify` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆类型',
    `car_no`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '车辆号码',
    `car_color`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆颜色',
    `car_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车主名称',
    `car_phone`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车主电话',
    `user_id`      int(11)                                                       NULL DEFAULT NULL COMMENT '用户id',
    `create_time`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `car_logo`     varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车辆logo',
    PRIMARY KEY (`car_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 29
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of car
-- ----------------------------

-- ----------------------------
-- Table structure for cash_out
-- ----------------------------
DROP TABLE IF EXISTS `cash_out`;
CREATE TABLE `cash_out`
(
    `id`            bigint(20)                                                     NOT NULL AUTO_INCREMENT COMMENT '申请提现id',
    `create_at`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '申请时间',
    `is_out`        int(2)                                                         NULL DEFAULT NULL COMMENT '是否转账',
    `money`         decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '提现金额',
    `out_at`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '转账时间',
    `relation_id`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '会员编号',
    `user_id`       bigint(20)                                                     NULL DEFAULT NULL COMMENT '用户id',
    `zhifubao`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付宝账号',
    `zhifubao_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '支付宝姓名',
    `order_number`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '订单编号',
    `state`         int(11)                                                        NULL DEFAULT NULL COMMENT '状态 0待转账 1成功 -1退款',
    `refund`        varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `classify`      int(11)                                                        NULL DEFAULT NULL COMMENT '提现方式 1支付宝 2微信小程序  3微信公众号',
    `rate`          varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '利息',
    `wx_img`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 130
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of cash_out
-- ----------------------------

-- ----------------------------
-- Table structure for chat_content
-- ----------------------------
DROP TABLE IF EXISTS `chat_content`;
CREATE TABLE `chat_content`
(
    `chat_content_id`      int(11)                                                        NOT NULL AUTO_INCREMENT COMMENT '聊天内容id',
    `chat_conversation_id` int(11)                                                        NULL DEFAULT NULL COMMENT '聊天会话id',
    `content`              varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
    `message_type`         int(11)                                                        NULL DEFAULT NULL COMMENT '类型 1文字 2图片 3语音',
    `width`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '图片宽度',
    `height`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '图片高度',
    `user_id`              int(11)                                                        NULL DEFAULT NULL COMMENT '发送用户id',
    `status`               int(11)                                                        NULL DEFAULT NULL COMMENT '0未读 1已读',
    `create_time`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '发送时间',
    PRIMARY KEY (`chat_content_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 615
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chat_content
-- ----------------------------

-- ----------------------------
-- Table structure for chat_conversation
-- ----------------------------
DROP TABLE IF EXISTS `chat_conversation`;
CREATE TABLE `chat_conversation`
(
    `chat_conversation_id` int(11)                                                        NOT NULL AUTO_INCREMENT COMMENT '聊天会话id',
    `user_id`              int(11)                                                        NULL DEFAULT NULL COMMENT '发送用户id',
    `focused_user_id`      int(11)                                                        NULL DEFAULT NULL COMMENT '接受用户id',
    `status`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '状态',
    `create_time`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '更新时间',
    `remark`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '备注',
    `is_wx_msg`            varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `is_send_msg`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    PRIMARY KEY (`chat_conversation_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 186
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chat_conversation
-- ----------------------------

-- ----------------------------
-- Table structure for chats
-- ----------------------------
DROP TABLE IF EXISTS `chats`;
CREATE TABLE `chats`
(
    `chat_id`     bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '会话id',
    `create_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建时间',
    `user_count`  int(20)                                                       NULL DEFAULT 0 COMMENT '用户未读条数',
    `user_count2` int(20)                                                       NULL DEFAULT 0 COMMENT '好友未读条数',
    `user_head`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户头像',
    `user_head2`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '好友头像',
    `user_id`     bigint(20)                                                    NULL DEFAULT NULL COMMENT '用户id',
    `user_id2`    bigint(20)                                                    NULL DEFAULT NULL COMMENT '好友id',
    `user_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
    `user_name2`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '好友昵称',
    `store_count` int(11)                                                       NULL DEFAULT 0 COMMENT '后台未读条数',
    `store_head`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '后台头像',
    `store_id`    bigint(20)                                                    NULL DEFAULT NULL COMMENT '总后台id(总后台传0',
    `store_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户昵称',
    PRIMARY KEY (`chat_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 157
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '聊天会话'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chats
-- ----------------------------

-- ----------------------------
-- Table structure for chats_content
-- ----------------------------
DROP TABLE IF EXISTS `chats_content`;
CREATE TABLE `chats_content`
(
    `chat_content_id` bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '会话内容id',
    `chat_id`         bigint(20)                                                    NULL DEFAULT NULL COMMENT '会话Id',
    `content`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '聊天内容',
    `create_time`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建时间',
    `send_type`       bigint(20)                                                    NULL DEFAULT NULL COMMENT '消息来源（1用户消息 2好友消息）',
    `status`          int(11)                                                       NULL DEFAULT 1 COMMENT '是否已读(1未读 2已读)',
    `type`            int(1)                                                        NULL DEFAULT NULL COMMENT '类型（1文字 2图片 3链接）',
    `user_id`         bigint(20)                                                    NULL DEFAULT NULL COMMENT '用户id',
    `user_id2`        bigint(20)                                                    NULL DEFAULT NULL COMMENT '好友id',
    `store_id`        bigint(20)                                                    NULL DEFAULT NULL COMMENT '后台id（总后台传0）',
    PRIMARY KEY (`chat_content_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1193
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '聊天会话内容'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chats_content
-- ----------------------------

-- ----------------------------
-- Table structure for city_agency
-- ----------------------------
DROP TABLE IF EXISTS `city_agency`;
CREATE TABLE `city_agency`
(
    `id`          int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '代理id',
    `user_id`     int(11)                                                       NULL DEFAULT NULL COMMENT '用户id',
    `city`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '意向代理城市',
    `user_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `phone`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
    `create_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `classify`    int(11)                                                       NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 26
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of city_agency
-- ----------------------------

-- ----------------------------
-- Table structure for comment_fabulous
-- ----------------------------
DROP TABLE IF EXISTS `comment_fabulous`;
CREATE TABLE `comment_fabulous`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞id',
    `taking_comment_id` bigint(20) NULL DEFAULT NULL COMMENT '接单评论id',
    `user_id`           bigint(20) NULL DEFAULT NULL COMMENT '用户id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of comment_fabulous
-- ----------------------------

-- ----------------------------
-- Table structure for common_info
-- ----------------------------
DROP TABLE IF EXISTS `common_info`;
CREATE TABLE `common_info`
(
    `id`             bigint(20)                                                     NOT NULL AUTO_INCREMENT COMMENT '配置文件id',
    `create_at`      varchar(255) CHARACTER SET big5 COLLATE big5_chinese_ci        NULL DEFAULT NULL COMMENT '创建时间',
    `max`            varchar(255) CHARACTER SET big5 COLLATE big5_chinese_ci        NULL DEFAULT NULL,
    `min`            varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置文件名称',
    `type`           int(11)                                                        NULL DEFAULT NULL COMMENT '类型',
    `value`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin                 NULL COMMENT '值',
    `condition_from` varchar(255) CHARACTER SET big5 COLLATE big5_chinese_ci        NULL DEFAULT NULL COMMENT '分类',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 345
  CHARACTER SET = big5
  COLLATE = big5_chinese_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of common_info
-- ----------------------------
INSERT INTO `common_info`
VALUES (1, '2022-03-28 11:25:23', NULL, '客服二维码', 1, 'https://taobao.xianmxkj.com/custom.jpg', 'tupian');
INSERT INTO `common_info`
VALUES (2, '2020-02-21 21:17:17', NULL, '公众号二维码', 2, 'https://taobao.xianmxkj.com/erweima.jpg', 'tupian');
INSERT INTO `common_info`
VALUES (6, '2023-03-03 11:35:31', NULL, '微信公众号APPID	', 5, '', 'weixin');
INSERT INTO `common_info`
VALUES (7, '2021-03-15 21:16:30', NULL, '短信签名', 6, '省钱兄', 'duanxin');
INSERT INTO `common_info`
VALUES (16, '2023-03-03 11:35:40', NULL, '微信公众号秘钥	', 21, '', 'weixin');
INSERT INTO `common_info`
VALUES (17, '2020-02-25 20:43:59', NULL, '公众号Token', 16, 'maxd', 'weixin');
INSERT INTO `common_info`
VALUES (18, '2020-02-25 20:44:15', NULL, '公众号EncodingAESKey	', 17, 'O8T7NubxpjOd7uNoVV7g01PDpPGUWpiGrLWWIFyaaCH',
        'weixin');
INSERT INTO `common_info`
VALUES (20, '2022-12-02 19:33:45', NULL, '后台管理平台域名配置	', 20, 'https://peizhenadmin.xianmaxiong.com', 'xitong');
INSERT INTO `common_info`
VALUES (21, '2022-12-02 19:33:30', NULL, 'h5服务域名配置	', 19, 'https://peizhen.xianmaxiong.com', 'xitong');
INSERT INTO `common_info`
VALUES (22, '2022-03-28 11:22:48', NULL, '短信服务商（1 腾讯云 2 阿里云 3短信宝）', 79, '3', 'duanxin');
INSERT INTO `common_info`
VALUES (23, '2023-04-13 11:33:18', NULL, '后台服务名称	', 12, '省钱兄', 'xitong');
INSERT INTO `common_info`
VALUES (27, '2020-03-09 20:46:10', NULL, '通用APP下载地址	', 25, 'peizhen.xianmaxiong.com/peizhen-user.apk', 'xitong');
INSERT INTO `common_info`
VALUES (33, '2021-03-15 21:19:27', NULL, '腾讯云短信clientId	', 31, '', 'duanxin');
INSERT INTO `common_info`
VALUES (34, '2020-03-28 00:47', NULL, '腾讯云短信clientSecret	', 32, '', 'duanxin');
INSERT INTO `common_info`
VALUES (47, '2022-08-17 13:15:42', NULL, '微信小程序APPID', 45, '', 'weixin');
INSERT INTO `common_info`
VALUES (48, '2022-08-17 13:15:23', NULL, '微信小程序秘钥', 46, '', 'weixin');
INSERT INTO `common_info`
VALUES (51, '2020-03-29 00:21', NULL, '分享安卓下载地址', 49, 'https://www.pgyer.com/xiansqx', 'xitong');
INSERT INTO `common_info`
VALUES (52, '2020-03-29 00:21', NULL, '分享苹果下载地址', 50, 'https://www.pgyer.com/c11o', 'xitong');
INSERT INTO `common_info`
VALUES (58, '2020-03-29 00:21', NULL, '开启微信登录', 53, '是', 'weixin');
INSERT INTO `common_info`
VALUES (69, '2020-06-04 16:34', NULL, 'APP消息推送PushAppKey', 60, 'uW0zDpss4H9YBco3dgOUM8', 'push');
INSERT INTO `common_info`
VALUES (70, '2020-06-04 16:34', NULL, 'APP消息推送PushAppId', 61, 'ciYgxyP9Xb95ig2yowsIF6', 'push');
INSERT INTO `common_info`
VALUES (71, '2020-06-04 16:34', NULL, 'APP消息推送PushMasterSecret', 62, '7mjw0QAlTD8s7TJWfAuCTA', 'push');
INSERT INTO `common_info`
VALUES (72, '2020-06-04 16:34', NULL, '企业支付宝APPID', 63, '', 'zhifubao');
INSERT INTO `common_info`
VALUES (73, '2020-06-04 16:34', NULL, '企业支付宝公钥', 64, '', 'zhifubao');
INSERT INTO `common_info`
VALUES (74, '2020-06-04 16:34', NULL, '企业支付宝商户秘钥', 65, '+', 'zhifubao');
INSERT INTO `common_info`
VALUES (77, '2021-03-15 21:22:30', NULL, '文件上传阿里云Endpoint', 68, 'https://oss-cn-beijing.aliyuncs.com', 'oss');
INSERT INTO `common_info`
VALUES (78, '2021-03-15 21:22:38', NULL, '文件上传阿里云账号accessKeyId', 69, '', 'oss');
INSERT INTO `common_info`
VALUES (79, '2021-03-15 21:22:46', NULL, '文件上传阿里云账号accessKeySecret', 70, '', 'oss');
INSERT INTO `common_info`
VALUES (80, '2021-03-15 21:22:53', NULL, '文件上传阿里云Bucket名称', 71, 'shegnqx', 'oss');
INSERT INTO `common_info`
VALUES (81, '2021-03-15 21:23:00', NULL, '文件上传阿里云Bucket域名', 72, 'https://shegnqx.oss-cn-beijing.aliyuncs.com', 'oss');
INSERT INTO `common_info`
VALUES (83, '2020-07-27 15:17', NULL, '微信APPappId', 74, '', 'weixin');
INSERT INTO `common_info`
VALUES (84, '2020-07-27 15:17', NULL, '微信商户key', 75, '', 'weixin');
INSERT INTO `common_info`
VALUES (85, '2020-07-27 15:17', NULL, '微信商户号mchId', 76, '', 'weixin');
INSERT INTO `common_info`
VALUES (96, '2020-07-27 15:17', NULL, '阿里云登陆或注册模板code（开启阿里云短信必须配置）', 80, 'SMS_200190994', 'duanxin');
INSERT INTO `common_info`
VALUES (97, '2020-07-27 15:17', NULL, '阿里云找回密码模板code（开启阿里云短信必须配置）', 81, 'SMS_200176048', 'duanxin');
INSERT INTO `common_info`
VALUES (98, '2020-07-27 15:17', NULL, '阿里云绑定手机号模板code（开启阿里云短信必须配置）', 82, 'SMS_200186024', 'duanxin');
INSERT INTO `common_info`
VALUES (99, '2020-07-27 15:17', NULL, '阿里云短信accessKeyId', 83, '', 'duanxin');
INSERT INTO `common_info`
VALUES (100, '2020-07-27 15:17', NULL, '阿里云短信accessSecret', 84, '', 'duanxin');
INSERT INTO `common_info`
VALUES (101, '2021-02-24 18:46:57', NULL, '官方邀请码', 88, '666666', 'xitongs');
INSERT INTO `common_info`
VALUES (102, '2022-04-21 19:39:21', NULL, '转账方式 1支付宝证书  2支付宝秘钥 3手动', 98, '3', 'zhifubao');
INSERT INTO `common_info`
VALUES (108, '2021-11-02 15:26:21', NULL, '公众号是否自动登录', 108, '否', 'weixin');
INSERT INTO `common_info`
VALUES (116, '2023-03-29 19:22:37', NULL, '邀请赚钱推广内容', 116, NULL, 'xitongs');
INSERT INTO `common_info`
VALUES (118, '2020-07-27 15:17', NULL, '邀请赚钱推广内容', 101, '', 'xitongs');
INSERT INTO `common_info`
VALUES (120, '2020-07-27 15:17', NULL, '邀请赚钱微信分享内容', 103, '', 'xitongs');
INSERT INTO `common_info`
VALUES (130, '2021-12-03 17:59:08', NULL, '提现最低额度', 112, '10', 'fuwufei');
INSERT INTO `common_info`
VALUES (131, '2020-11-04 10:31:40', NULL, '万能任务平台抽成', 120, '0.2', 'fuwufeis');
INSERT INTO `common_info`
VALUES (140, '2022-08-22 11:39:17', NULL, '用户端腾讯地图key', 128, '', 'weixin');
INSERT INTO `common_info`
VALUES (154, '2020-11-04 10:31:40', NULL, '是否开启APP微信分享', 136, '否', 'weixins');
INSERT INTO `common_info`
VALUES (159, '2021-11-02 14:53:27', NULL, 'H5推广是否分享APP下载页面', 141, '否', 'xitongs');
INSERT INTO `common_info`
VALUES (170, '2020-11-04 10:31:40', NULL, '提现手续费', 152, '0.01', 'fuwufei');
INSERT INTO `common_info`
VALUES (171, '2021-09-06 16:19:51', NULL, '最高提现金额', 153, '1000', 'fuwufei');
INSERT INTO `common_info`
VALUES (172, '2020-11-04 10:31:40', NULL, '比列(充值的钱比例默认1:1)', 154, '1', 'fuwufeis');
INSERT INTO `common_info`
VALUES (173, '2021-08-14 19:09:54', NULL, '平台费率0.3表示百分之30，', 155, '0.3', 'fuwufeis');
INSERT INTO `common_info`
VALUES (182, '2022-03-28 11:24:34', NULL, '短信宝用户名', 164, '', 'duanxin');
INSERT INTO `common_info`
VALUES (183, '2022-03-28 11:24:47', NULL, '短信宝密码', 165, '', 'duanxin');
INSERT INTO `common_info`
VALUES (200, '2022-08-01 15:09:22', NULL, '帮助中心', 175,
        '<p>本应用尊重并保护所有使用服务用户的个人隐私权。为了给您提供更准确、更有个性化的服务，本应用会按照本隐私权政策的规定使用和披露您的个人信息。但本应用将以高度的勤勉、审慎义务对待这些信息。除本隐私权政策另有规定外，在未征得您事先许可的情况下，本应用不会将这些信息对外披露或向第三方提供。本应用会不时更新本隐私权政策。 您在同意本应用服务使用协议之时，即视为您已经同意本隐私权政策全部内容。本隐私权政策属于本应用服务使用协议不可分割的一部分。</p><p>1. 适用范围</p><p>(a) 在您注册本应用帐号时，您根据本应用要求提供的个人注册信息；</p><p>(b) 在您使用本应用网络服务，或访问本应用平台网页时，本应用自动接收并记录的您的浏览器和计算机上的信息，包括但不限于您的IP地址、浏览器的类型、使用的语言、访问日期和时间、软硬件特征信息及您需求的网页记录等数据；</p><p>(c) 本应用通过合法途径从商业伙伴处取得的用户个人数据。</p><p>您了解并同意，以下信息不适用本隐私权政策：</p><p>(a) 您在使用本应用平台提供的搜索服务时输入的关键字信息；</p><p>(b) 本应用收集到的您在本应用发布的有关信息数据，包括但不限于参与活动、成交信息及评价详情；</p><p>(c) 违反法律规定或违反本应用规则行为及本应用已对您采取的措施。</p><p>2. 信息使用</p><p>(a)本应用不会向任何无关第三方提供、出售、出租、分享或交易您的个人信息，除非事先得到您的许可，或该第三方和本应用（含本应用关联公司）单独或共同为您提供服务，且在该服务结束后，其将被禁止访问包括其以前能够访问的所有这些资料。</p><p>(b) 本应用亦不允许任何第三方以任何手段收集、编辑、出售或者无偿传播您的个人信息。任何本应用平台用户如从事上述活动，一经发现，本应用有权立即终止与该用户的服务协议。</p><p>(c) 为服务用户的目的，本应用可能通过使用您的个人信息，向您提供您感兴趣的信息，包括但不限于向您发出产品和服务信息，或者与本应用合作伙伴共享信息以便他们向您发送有关其产品和服务的信息（后者需要您的事先同意）。</p><p>3. 信息披露</p><p>在如下情况下，本应用将依据您的个人意愿或法律的规定全部或部分的披露您的个人信息：</p><p>(a) 经您事先同意，向第三方披露；</p><p>(b)为提供您所要求的产品和服务，而必须和第三方分享您的个人信息；</p><p>(c) 根据法律的有关规定，或者行政或司法机构的要求，向第三方或者行政、司法机构披露；</p><p>(d) 如您出现违反中国有关法律、法规或者本应用服务协议或相关规则的情况，需要向第三方披露；</p><p>(e) 如您是适格的知识产权投诉人并已提起投诉，应被投诉人要求，向被投诉人披露，以便双方处理可能的权利纠纷；</p><p>(f) 在本应用平台上创建的某一交易中，如交易任何一方履行或部分履行了交易义务并提出信息披露请求的，本应用有权决定向该用户提供其交易对方的联络方式等必要信息，以促成交易的完成或纠纷的解决。</p><p>(g) 其它本应用根据法律、法规或者网站政策认为合适的披露。</p><p>4. 信息存储和交换</p><p>本应用收集的有关您的信息和资料将保存在本应用及（或）其关联公司的服务器上，这些信息和资料可能传送至您所在国家、地区或本应用收集信息和资料所在地的境外并在境外被访问、存储和展示。</p><p>5. Cookie的使用</p><p>(a) 在您未拒绝接受cookies的情况下，本应用会在您的计算机上设定或取用cookies ，以便您能登录或使用依赖于cookies的本应用平台服务或功能。本应用使用cookies可为您提供更加周到的个性化服务，包括推广服务。</p><p>(b) 您有权选择接受或拒绝接受cookies。您可以通过修改浏览器设置的方式拒绝接受cookies。但如果您选择拒绝接受cookies，则您可能无法登录或使用依赖于cookies的本应用网络服务或功能。</p><p>(c) 通过本应用所设cookies所取得的有关信息，将适用本政策。</p><p>6. 信息安全</p><p>(a) 本应用帐号均有安全保护功能，请妥善保管您的用户名及密码信息。本应用将通过对用户密码进行加密等安全措施确保您的信息不丢失，不被滥用和变造。尽管有前述安全措施，但同时也请您注意在信息网络上不存在“完善的安全措施”。</p><p>(b) 在使用本应用网络服务进行网上交易时，您不可避免的要向交易对方或潜在的交易对方披露自己的个人信息，如联络方式或者邮政地址。请您妥善保护自己的个人信息，仅在必要的情形下向他人提供。如您发现自己的个人信息泄密，尤其是本应用用户名及密码发生泄露，请您立即联络本应用客服，以便本应用采取相应措施。</p><p>7.本隐私政策的更改</p><p>(a)如果决定更改隐私政策，我们会在本政策中、本公司网站中以及我们认为适当的位置发布这些更改，以便您了解我们如何收集、使用您的个人信息，哪些人可以访问这些信息，以及在什么情况下我们会透露这些信息。</p><p>(b)本公司保留随时修改本政策的权利，因此请经常查看。如对本政策作出重大更改，本公司会通过网站通知的形式告知。</p>',
        'xieyi');
INSERT INTO `common_info`
VALUES (201, '2021-08-14 19:10:04', NULL, '隐私政策', 176,
        '<p>本应用尊重并保护所有使用服务用户的个人隐私权。为了给您提供更准确、更有个性化的服务，本应用会按照本隐私权政策的规定使用和披露您的个人信息。但本应用将以高度的勤勉、审慎义务对待这些信息。除本隐私权政策另有规定外，在未征得您事先许可的情况下，本应用不会将这些信息对外披露或向第三方提供。本应用会不时更新本隐私权政策。 您在同意本应用服务使用协议之时，即视为您已经同意本隐私权政策全部内容。本隐私权政策属于本应用服务使用协议不可分割的一部分。</p><p>1. 适用范围</p><p>(a) 在您注册本应用帐号时，您根据本应用要求提供的个人注册信息；</p><p>(b) 在您使用本应用网络服务，或访问本应用平台网页时，本应用自动接收并记录的您的浏览器和计算机上的信息，包括但不限于您的IP地址、浏览器的类型、使用的语言、访问日期和时间、软硬件特征信息及您需求的网页记录等数据；</p><p>(c) 本应用通过合法途径从商业伙伴处取得的用户个人数据。</p><p>您了解并同意，以下信息不适用本隐私权政策：</p><p>(a) 您在使用本应用平台提供的搜索服务时输入的关键字信息；</p><p>(b) 本应用收集到的您在本应用发布的有关信息数据，包括但不限于参与活动、成交信息及评价详情；</p><p>(c) 违反法律规定或违反本应用规则行为及本应用已对您采取的措施。</p><p>2. 信息使用</p><p>(a)本应用不会向任何无关第三方提供、出售、出租、分享或交易您的个人信息，除非事先得到您的许可，或该第三方和本应用（含本应用关联公司）单独或共同为您提供服务，且在该服务结束后，其将被禁止访问包括其以前能够访问的所有这些资料。</p><p>(b) 本应用亦不允许任何第三方以任何手段收集、编辑、出售或者无偿传播您的个人信息。任何本应用平台用户如从事上述活动，一经发现，本应用有权立即终止与该用户的服务协议。</p><p>(c) 为服务用户的目的，本应用可能通过使用您的个人信息，向您提供您感兴趣的信息，包括但不限于向您发出产品和服务信息，或者与本应用合作伙伴共享信息以便他们向您发送有关其产品和服务的信息（后者需要您的事先同意）。</p><p>3. 信息披露</p><p>在如下情况下，本应用将依据您的个人意愿或法律的规定全部或部分的披露您的个人信息：</p><p>(a) 经您事先同意，向第三方披露；</p><p>(b)为提供您所要求的产品和服务，而必须和第三方分享您的个人信息；</p><p>(c) 根据法律的有关规定，或者行政或司法机构的要求，向第三方或者行政、司法机构披露；</p><p>(d) 如您出现违反中国有关法律、法规或者本应用服务协议或相关规则的情况，需要向第三方披露；</p><p>(e) 如您是适格的知识产权投诉人并已提起投诉，应被投诉人要求，向被投诉人披露，以便双方处理可能的权利纠纷；</p><p>(f) 在本应用平台上创建的某一交易中，如交易任何一方履行或部分履行了交易义务并提出信息披露请求的，本应用有权决定向该用户提供其交易对方的联络方式等必要信息，以促成交易的完成或纠纷的解决。</p><p>(g) 其它本应用根据法律、法规或者网站政策认为合适的披露。</p><p>4. 信息存储和交换</p><p>本应用收集的有关您的信息和资料将保存在本应用及（或）其关联公司的服务器上，这些信息和资料可能传送至您所在国家、地区或本应用收集信息和资料所在地的境外并在境外被访问、存储和展示。</p><p>5. Cookie的使用</p><p>(a) 在您未拒绝接受cookies的情况下，本应用会在您的计算机上设定或取用cookies ，以便您能登录或使用依赖于cookies的本应用平台服务或功能。本应用使用cookies可为您提供更加周到的个性化服务，包括推广服务。</p><p>(b) 您有权选择接受或拒绝接受cookies。您可以通过修改浏览器设置的方式拒绝接受cookies。但如果您选择拒绝接受cookies，则您可能无法登录或使用依赖于cookies的本应用网络服务或功能。</p><p>(c) 通过本应用所设cookies所取得的有关信息，将适用本政策。</p><p>6. 信息安全</p><p>(a) 本应用帐号均有安全保护功能，请妥善保管您的用户名及密码信息。本应用将通过对用户密码进行加密等安全措施确保您的信息不丢失，不被滥用和变造。尽管有前述安全措施，但同时也请您注意在信息网络上不存在“完善的安全措施”。</p><p>(b) 在使用本应用网络服务进行网上交易时，您不可避免的要向交易对方或潜在的交易对方披露自己的个人信息，如联络方式或者邮政地址。请您妥善保护自己的个人信息，仅在必要的情形下向他人提供。如您发现自己的个人信息泄密，尤其是本应用用户名及密码发生泄露，请您立即联络本应用客服，以便本应用采取相应措施。</p><p>7.本隐私政策的更改</p><p>(a)如果决定更改隐私政策，我们会在本政策中、本公司网站中以及我们认为适当的位置发布这些更改，以便您了解我们如何收集、使用您的个人信息，哪些人可以访问这些信息，以及在什么情况下我们会透露这些信息。</p><p>(b)本公司保留随时修改本政策的权利，因此请经常查看。如对本政策作出重大更改，本公司会通过网站通知的形式告知。</p>',
        'xieyi');
INSERT INTO `common_info`
VALUES (202, '2021-08-14 19:09:54', NULL, '注册协议', 177,
        '<p>尊敬的用户您好：在您使用本服务之前，请您认真阅读本用户协议，更好的了解我们所提供的服务以及您享有的权利义务。您开始使用时，即表示您已经了解并确认接受了本文件中的全部条款，包括我们对本服务条款随时做的任何修改。</p><p>一、协议的效力</p><p>本协议内容包括协议正文及所有已经发布或将来可能发布的各类规则。所有规则为本协议不可分割的组成部分，与协议正文具有同等法律效力。您承诺接受并遵守本协议的约定。如果您不同意本协议的约定，您应立即停止使用本平台服务。</p><p>二、用户行为规范</p><p>用户同意将不会利用本服务进行任何违法或不正当的活动，包括但不限于下列行为∶</p><p>发布或以其它方式传送含有下列内容之一的信息：</p><p>反对宪法所确定的基本原则的；</p><p>危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；</p><p>损害国家荣誉和利益的；</p><p>煽动民族仇恨、民族歧视、破坏民族团结的；</p><p>破坏国家宗教政策，宣扬邪教和封建迷信的；</p><p>散布谣言，扰乱社会秩序，破坏社会稳定的；</p><p>散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；</p><p>侮辱或者诽谤他人，侵害他人合法权利的；</p><p>含有虚假、诈骗、有害、胁迫、侵害他人隐私、骚扰、侵害、中伤、粗俗、猥亵、或其它道德上令人反感的内容；</p><p>含有当地法律、法规、规章、条例以及任何具有法律效力之规范所限制或禁止的其它内容的；</p><p>含有不适合在本平台展示的内容；</p><p>以任何方式危害他人的合法权益；</p><p>冒充其他任何人或机构，或以虚伪不实的方式陈述或谎称与任何人或机构有关；</p><p>将依据任何法律或合约或法定关系（例如由于雇佣关系和依据保密合约所得知或揭露之内部资料、专属及机密资料）知悉但无权传送之任何内容加以发布、发送电子邮件或以其它方式传送；</p><p>将侵害他人著作权、专利权、商标权、商业秘密、或其它专属权利（以下简称“专属权利”）之内容加以发布或以其它方式传送；</p><p>将任何广告信函、促销资料、“垃圾邮件”、““滥发信件”、“连锁信件”、“直销”或其它任何形式的劝诱资料加以发布、发送或以其它方式传送；</p><p>将设计目的在于干扰、破坏或限制任何计算机软件、硬件或通讯设备功能之计算机病毒（包括但不限于木马程序（trojan horses）、蠕虫（worms）、定时炸弹、删除蝇（cancelbots）（以下简称“病毒”）或其它计算机代码、档案和程序之任何资料，加以发布、发送或以其它方式传送；</p><p>干扰或破坏本服务或与本服务相连线之服务器和网络，或违反任何关于本服务连线网络之规定、程序、政策或规范；</p><p>跟踪、人肉搜索或以其它方式骚扰他人；</p><p>故意或非故意地违反任何适用的当地、国家法律，以及任何具有法律效力的规则；</p><p>未经合法授权而截获、篡改、收集、储存或删除他人个人信息、站内邮件或其它数据资料，或将获知的此类资料用于任何非法或不正当目的。</p><p>三、知识产权</p><p>本平台所有设计图样以及其他图样、产品及服务名称。任何人不得使用、复制或用作其他用途。未经我们许可，任何单位和个人不得私自复制、传播、展示、镜像、上载、下载、使用，或者从事任何其他侵犯我们知识产权的行为。否则，我们将追究相关法律责任。</p><p>我们鼓励用户充分利用平台自由地张贴和共享自己的信息，但这些内容必须位于公共领域内，或者用户拥有这些内容的使用权。同时，用户对于其创作并在本平台上发布的合法内容依法享有著作权及其相关权利。</p><p>四、免责声明</p><p>互联网是一个开放平台，用户将照片等个人资料上传到互联网上，有可能会被其他组织或个人复制、转载、擅改或做其它非法用途，用户必须充分意识此类风险的存在。用户明确同意其使用本服务所存在的风险将完全由其自己承担；因其使用本服务而产生的一切后果也由其自己承担，我们对用户不承担任何责任。</p><p>对于用户上传的照片、资料、证件等，已采用相关措施并已尽合理努力进行审核，但不保证其内容的正确性、合法性或可靠性，相关责任由上传上述内容的会员负责。</p><p>尽管已采取相应的技术保障措施 ，但用户仍有可能收到各类的广告信或其他不以招聘/应聘为目的邮件或其它方式传送的任何内容，本平台不承担责任。</p><p>对于各种广告信息、链接、资讯等，不保证其内容的正确性、合法性或可靠性，相关责任由广告商承担；用户通过本服务与广告商进行任何形式的通讯或商业往来，或参与促销活动，包含相关商品或服务之付款及交付，以及达成的其它任何相关条款、条件、保证或声明，完全为用户与广告商之间之行为，与本平台无关。用户因前述任何交易或前述广告商而遭受的任何性质的损失或损害，本平台不承担任何责任。</p><p>本平台不保证其提供的服务一定能满足用户的要求和期望，也不保证服务不会中断，对服务的及时性、安全性、准确性也都不作保证。对于因不可抗力或无法控制的原因造成的网络服务中断或其他缺陷，不承担任何责任。我们不对用户所发布信息的删除或储存失败承担责任。我们有权判断用户的行为是否符合本网站使用协议条款之规定，如果我们认为用户违背了协议条款的规定，我们有终止向其提供服务的权利。</p><p>本平台保留变更、中断或终止部分网络服务的权利。保留根据实际情况随时调整平台提供的服务种类、形式的权利。本平台不承担因业务调整给用户造成的损失。本平台仅提供相关服务，除此之外与本服务有关的设备（如电脑、调制解调器及其他与接入互联网有关的装置）及所需的费用（如为接入互联网而支付的电话费及上网费）均应由用户自行负担。</p>',
        'xieyi');
INSERT INTO `common_info`
VALUES (203, '2021-08-14 19:09:54', NULL, '关于我们', 187, '关于我们，有问题可以在线联系客服哦。', 'xieyi');
INSERT INTO `common_info`
VALUES (204, '2021-08-14 19:09:54', NULL, '登录是否获取手机号', 188, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (205, '2020-10-28 16:57:07', NULL, '邀请赏金(邀请用户可以获得的赏金)', 189, '0', 'fuwufeis');
INSERT INTO `common_info`
VALUES (217, '2022-08-22 11:39:23', NULL, '师傅端微信腾讯地图key', 217, '', 'weixin');
INSERT INTO `common_info`
VALUES (234, '2022-05-18 14:52:58', NULL, '上传方式 1阿里云oss  2本地', 234, '2', 'oss');
INSERT INTO `common_info`
VALUES (235, '2021-12-30 15:40:56', NULL, '腾讯地图key', 235, '', 'weixin');
INSERT INTO `common_info`
VALUES (237, '2022-08-18 14:03:43', NULL, '是否开启公众号', 237, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (238, '2023-03-30 14:55:52', NULL, '小程序上架是否显示1', 238, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (239, '2023-03-03 11:36:07', NULL, '师傅端微信小程序APPID', 239, '', 'weixin');
INSERT INTO `common_info`
VALUES (240, '2023-03-03 11:36:16', NULL, '师傅端微信小程序秘钥', 240, '', 'weixin');
INSERT INTO `common_info`
VALUES (243, '2020-11-04 10:31:40', NULL, '是否开启微信提现', 243, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (244, '2021-12-22 10:11:36', NULL, '微信提现方式 1自动 2手动', 244, '2', 'weixin');
INSERT INTO `common_info`
VALUES (245, '2020-11-04 10:31:40', NULL, '是否开启支付宝提现', 245, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (246, '2020-10-28 16:57:07', NULL, '微信证书地址', 201, '/www/wexixin', 'weixin');
INSERT INTO `common_info`
VALUES (248, '2023-03-31 16:32:13', NULL, '师傅端是否上线', 203, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (250, '2020-11-04 10:31:40', NULL, '超时几分钟短信通知', 250, '5', 'duanxins');
INSERT INTO `common_info`
VALUES (251, '2020-11-04 10:31:40', NULL, '几条未读短信通知', 251, '1', 'duanxins');
INSERT INTO `common_info`
VALUES (252, '2022-04-22 17:41:17', NULL, '推广员佣金', 207, '0.1', 'fuwufei');
INSERT INTO `common_info`
VALUES (253, '2022-04-22 17:41:22', NULL, '代理商佣金', 208, '0.05', 'fuwufei');
INSERT INTO `common_info`
VALUES (254, '2023-03-29 15:15:21', NULL, '是否开启佣金', 209, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (255, '2022-12-05 08:41:27', NULL, '分享提示语', 255, '医院陪诊 陪诊啦', 'xitong');
INSERT INTO `common_info`
VALUES (256, '2022-04-22 17:02:44', NULL, '陪诊师傅初始化佣金', 206, '0.7', 'fuwufei');
INSERT INTO `common_info`
VALUES (257, '2023-03-31 16:43:20', NULL, '小程序上架是否显示2', 257, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (258, '2022-05-06 12:35:13', NULL, '支付宝方式 1证书 2秘钥', 258, '2', 'zhifubao');
INSERT INTO `common_info`
VALUES (259, '2022-05-06 12:35:13', NULL, '支付宝证书方式 证书地址', 259, '1', 'zhifubao');
INSERT INTO `common_info`
VALUES (260, '2023-03-30 10:05:57', NULL, '是否开启推广', 260, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (262, '2020-07-09 17:59:45', NULL, '师傅微信公众号APPID', 262, '', 'weixin');
INSERT INTO `common_info`
VALUES (278, '2020-07-27 15:1', NULL, '师傅端微信商户key', 278, '', 'weixin');
INSERT INTO `common_info`
VALUES (279, '2020-07-27 15:1', NULL, '师傅端微信商户号mchId', 279, '', 'weixin');
INSERT INTO `common_info`
VALUES (298, '2022-06-21 16:47:57', NULL, '聊天关键词过滤', 602,
        '滚 逼 傻 煞 草 泥 马 妈 V v n M m N 干微信 vx VX vX Vx v x X  微 信 sb 草泥马 傻逼 +v + s b S b S B weixin weiXin WeiXin wei xin Wei Xin 牛 bi 笔',
        'xitongs');
INSERT INTO `common_info`
VALUES (301, '2022-08-22 11:54:53', NULL, '保证金金额', 271, '0.01', 'xitong');
INSERT INTO `common_info`
VALUES (302, '2022-12-05 08:41:45', NULL, '保证金缴纳协议', 272, '陪诊认证需要保证金300', 'xitong');
INSERT INTO `common_info`
VALUES (303, '2022-12-27 16:09:29', NULL, '保证金退款协议', 273, '陪诊退款需要确保订单都已经完成才可以申请', 'xitong');
INSERT INTO `common_info`
VALUES (304, '2022-09-01 10:31:04', NULL, '企业微信链接', 274, 'https://work.weixin.qq.com/kfid/kfc59fa3a70b4f7cde1',
        'xitong');
INSERT INTO `common_info`
VALUES (305, '2022-09-01 10:30:52', NULL, '企业微信客服APPID', 275, 'ww16896a4e2489dd2d', 'xitong');
INSERT INTO `common_info`
VALUES (306, '2023-03-29 15:18:30', NULL, '分享标题', 276, '码兄陪诊上线', 'xitong');
INSERT INTO `common_info`
VALUES (307, '2022-12-23 15:40:37', NULL, '分享图', 277,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/12d400db7baedc7e7508740942cbc03a.png', 'tupian');
INSERT INTO `common_info`
VALUES (308, '2022-06-21 16:47:57', NULL, '用户订单状态通知', 308, 'jS_nxHvA5NSpVu_tTPDz6uZIMc-p1Gmx5Tq5qXdTCF0', 'weixin');
INSERT INTO `common_info`
VALUES (309, '2022-12-09 13:54:34', NULL, '新人优惠券(格式:id,数量)', 309, '1,1', 'xitong');
INSERT INTO `common_info`
VALUES (310, '2022-11-26 11:26:50', NULL, '是否开启新人优惠券(1开启 0未开启)', 310, '1', 'xitong');
INSERT INTO `common_info`
VALUES (312, '2022-12-08 11:03:21', NULL, '师傅订单状态通知', 312, 'Ve5LFGU4OuemCCYHWL4RloTXwewMHrtkf9rmOIX-Ngw', 'weixin');
INSERT INTO `common_info`
VALUES (313, '2022-12-08 11:03:21', NULL, '陪诊服务条款同意书', 313,
        '1.为患者提供提前约号服务，本服务由相应的服务人员于\r\n医院公开的挂号渠道，协助用户实名挂号，患者及患者家属应按就诊医院要求提供相应真实有效证件及证明文件。\r\n(不包含加号、佳院陪护、急诊留观陪护等）\r\n2.用户及用户家属应按就诊医院要求提供相应真实有效的证件及证明文件。\r\n3.预约的医生停诊等特殊情况，平台将为您安排同科室其他医生转诊服务，若有其他情况请联系客服。\r\n4.本服务仅为用户提供协助，因各大医院就诊患者数量庞大，并不承诺可以成功按照用户要求的时问内成功提前约\r\n号，如再要求时间内未能完成，会全额退款。\r\n5,支付完成后，如需退款我们会根据实际情况进行相应处理，如您有任何疑问请咨询客服或拨打首页客服热线。\r\n6.优享陪诊的“尊享VIP陪诊”接送服务仅为方便客户轻松前往医院就诊，不具备应对途中出现的身体健康方面的突发状况，请根据自身及家人具体情况判断是否使用优享陪诊\r\n接送服务，并自行承担责任，请知悉！\r\n7.陪诊服务（这里包含老人全程陪诊服务、儿童全程陪诊服务、孕妇全程陪诊服务、全程陪诊以及尊享VIP陪诊服务）服务前，家属或本人需说明任何可发生的一切突发情\r\n況，如不告知，发生任何突发情况，优享陪诊不承担任何责任，请知悉！\r\n8.陪诊服务(这里包含老人全程陪诊服务、儿童全程陪诊服务、孕妇全程陪诊服务、全程陪诊以及尊享VP陪诊服务）仅包含服务代办、跑腿等专业代办业务，如在服务过程中发生任何与服务无关情况或强制陪诊员办理不符合服务内容的情况，优享陪诊有权终止服务并不退还任何费用，请知悉!\r\n9老人全程陪诊仅适用于65岁以上老人，80岁以上老人需\r\n一名家人陪同，因此服务为特殊人群服务，服务内容仅包含服务代办、跑腿、就医协助等一系列代办专业服务，不包含陪护（如：无法自理需要专业护理、特殊严重病情需\r\n特殊照护等）相关服务内容，优享陪诊不承担任何责任，请知悉！\r\n10、孕妇全程陪诊仅适用于孕妇，临产孕妇需一名家人陪同，因此服务为特殊人群服务，服务内容仅包含服务代办、跑腿、就医协助等一系列代办专业服务，不包含陪护\r\n（如：无法自理需专业护理，特殊情况需要特殊照护等）\r\n相关服务内容，如在服务中有任何突发情况，优享陪诊不\r\n承担任何责任，请知悉！\r\n11儿童全程陪诊仅适用于12岁以下儿童，8岁以下儿童需\r\n一名家人陪同，因此服务为特殊人群服务，服务内容仅包含代办、跑腿、就医协助等一系列代办专业服务，不包含陪护（如：无法自理需专业护理、特殊情况需要特殊照护\r\n等）相关服务内容，如在服务中有任何突发情况，优享陪\r\n诊不承担任何责任，请知悉！\r\n12.本平台所有业务均为代办跑腿服务，服务中如您有贵重\r\n物品请自行保管，禁止将贵重物品以及与服务无关的物品\r\n交付于服务人员手中，如在服务中有物品丢失或遗失等一\r\n系列问题，优享陪诊平台以及服务人员不承担任何责任，请知悉',
        'xieyi');
INSERT INTO `common_info`
VALUES (314, '2022-12-08 11:03:21', NULL, '陪护服务条款同意书', 314,
        '1.为患者提供提前约号服务，本服务由相应的服务人员于\r\n医院公开的挂号渠道，协助用户实名挂号，患者及患者家属应按就诊医院要求提供相应真实有效证件及证明文件。\r\n(不包含加号、佳院陪护、急诊留观陪护等）\r\n2.用户及用户家属应按就诊医院要求提供相应真实有效的证件及证明文件。\r\n3.预约的医生停诊等特殊情况，平台将为您安排同科室其他医生转诊服务，若有其他情况请联系客服。\r\n4.本服务仅为用户提供协助，因各大医院就诊患者数量庞大，并不承诺可以成功按照用户要求的时问内成功提前约\r\n号，如再要求时间内未能完成，会全额退款。\r\n5,支付完成后，如需退款我们会根据实际情况进行相应处理，如您有任何疑问请咨询客服或拨打首页客服热线。\r\n6.优享陪诊的“尊享VIP陪诊”接送服务仅为方便客户轻松前往医院就诊，不具备应对途中出现的身体健康方面的突发状况，请根据自身及家人具体情况判断是否使用优享陪诊\r\n接送服务，并自行承担责任，请知悉！\r\n7.陪诊服务（这里包含老人全程陪诊服务、儿童全程陪诊服务、孕妇全程陪诊服务、全程陪诊以及尊享VIP陪诊服务）服务前，家属或本人需说明任何可发生的一切突发情\r\n況，如不告知，发生任何突发情况，优享陪诊不承担任何责任，请知悉！\r\n8.陪诊服务(这里包含老人全程陪诊服务、儿童全程陪诊服务、孕妇全程陪诊服务、全程陪诊以及尊享VP陪诊服务）仅包含服务代办、跑腿等专业代办业务，如在服务过程中发生任何与服务无关情况或强制陪诊员办理不符合服务内容的情况，优享陪诊有权终止服务并不退还任何费用，请知悉!\r\n9老人全程陪诊仅适用于65岁以上老人，80岁以上老人需\r\n一名家人陪同，因此服务为特殊人群服务，服务内容仅包含服务代办、跑腿、就医协助等一系列代办专业服务，不包含陪护（如：无法自理需要专业护理、特殊严重病情需\r\n特殊照护等）相关服务内容，优享陪诊不承担任何责任，请知悉！\r\n10、孕妇全程陪诊仅适用于孕妇，临产孕妇需一名家人陪同，因此服务为特殊人群服务，服务内容仅包含服务代办、跑腿、就医协助等一系列代办专业服务，不包含陪护\r\n（如：无法自理需专业护理，特殊情况需要特殊照护等）\r\n相关服务内容，如在服务中有任何突发情况，优享陪诊不\r\n承担任何责任，请知悉！\r\n11儿童全程陪诊仅适用于12岁以下儿童，8岁以下儿童需\r\n一名家人陪同，因此服务为特殊人群服务，服务内容仅包含代办、跑腿、就医协助等一系列代办专业服务，不包含陪护（如：无法自理需专业护理、特殊情况需要特殊照护\r\n等）相关服务内容，如在服务中有任何突发情况，优享陪\r\n诊不承担任何责任，请知悉！\r\n12.本平台所有业务均为代办跑腿服务，服务中如您有贵重\r\n物品请自行保管，禁止将贵重物品以及与服务无关的物品\r\n交付于服务人员手中，如在服务中有物品丢失或遗失等一\r\n系列问题，优享陪诊平台以及服务人员不承担任何责任，请知悉',
        'xieyi');
INSERT INTO `common_info`
VALUES (315, '2022-12-23 15:53:27', NULL, '招聘岗位描述', 315,
        '<p>招聘医生和护士数名,请符合条件者发送您的简历至**************,如果您通过我们的简历筛选,会及时和您联系,请保持电话畅通.</p>', 'xieyi');
INSERT INTO `common_info`
VALUES (316, '2022-12-27 16:27:08', NULL, '是否自动派单给师傅 （是-自动派单，否-非自动派单）', 316, '否', 'kaiguan');
INSERT INTO `common_info`
VALUES (317, '2022-12-27 16:08:15', NULL, '用户端下单后多长时间开始自动派单（分钟）', 317, '5', 'xitong');
INSERT INTO `common_info`
VALUES (318, '2022-12-14 20:15:21', NULL, '师傅端自动派单距离限制（m）', 318, '40000', 'xitong');
INSERT INTO `common_info`
VALUES (319, '2021-12-30 15:40:56', NULL, '师傅接单所需保证金(元)', 319, '0.01', 'fuwufei');
INSERT INTO `common_info`
VALUES (320, '2022-12-15 18:44:25', NULL, 'app跳转师傅端', 320, 'https://peizhensf.xianmaxiong.com/peizhen-sf.apk',
        'xitong');
INSERT INTO `common_info`
VALUES (321, '2022-12-15 18:12:13', NULL, 'H5跳转师傅端', 321, 'https://peizhensf.xianmaxiong.com', 'xitong');
INSERT INTO `common_info`
VALUES (324, '2023-03-07 15:42:06', NULL, '师傅端是否上线2', 324, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (325, '2023-03-07 15:42:06', NULL, '待付款订单自动取消时间(单位:分钟)', 325, '15', 'xitong');
INSERT INTO `common_info`
VALUES (326, '2023-04-11 19:33:08', NULL, '可提现日期(每月几号)', 326, '7,11,28', 'xitong');
INSERT INTO `common_info`
VALUES (327, '2023-03-07 15:42:06', NULL, '提现限制', 327, '仅限每月7、14、28号提现,其他时间无法提现', 'xitong');
INSERT INTO `common_info`
VALUES (328, '2023-03-30 13:11:41', NULL, '客服类型(1:电话，2:企业微信，3:客服二维码)', 328, '2', 'xitong');
INSERT INTO `common_info`
VALUES (329, '2023-03-29 19:22:55', NULL, '客服电话', 329, '13895585204', 'xitong');
INSERT INTO `common_info`
VALUES (331, '2023-03-29 19:22:37', NULL, '客服微信号', 331, 'maxdlln', 'xitong');
INSERT INTO `common_info`
VALUES (332, '2023-04-06 15:17:31', NULL, '是否开余额支付和钱包充值(是或否)', 332, '是', 'kaiguan');
INSERT INTO `common_info`
VALUES (333, '2023-04-06 11:04:00', NULL, '科室导入模板链接', 333, 'https://peizhen.xianmaxiong.com/科室导入模板.xlsx', 'xitong');
INSERT INTO `common_info`
VALUES (334, '2023-04-10 17:05:11', NULL, '代理商入驻封面图', 334,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/07/794b711e5c097cc02ff32f0d7008a89a.png', 'tupian');
INSERT INTO `common_info`
VALUES (335, '2023-04-10 17:05:11', NULL, '推广员入驻封面图', 335,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/07/cc4b61a23c0b983279d8c2a0f3ecc955.png', 'tupian');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('336', '2023-06-12 09:49:22', NULL, '是否推送新订单通知', '336', '是', 'kaiguan');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('337', '2023-05-23 17:33:58', NULL, '邀请赠送优惠券(格式:id,数量,不填则没有)', '337', '13,2', 'xitong');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('338', '2023-05-15 16:01:22', NULL, '信用分低于多少时无法接单(不支持小数,满分是10分。不填则不限制分数)', '338', '5', 'xitong');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('339', '2023-05-15 16:01:22', NULL, '默认分佣等级(不填则按照默认配置)', '339', '2', 'xitong');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('342', '2023-05-23 19:05:35', NULL, '是否需要缴纳保证金', '343', '是', 'kaiguan');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('343', '2023-05-24 20:20:51', NULL, '是否允许师傅取消订单(是:允许,否:禁止)', '344', '否', 'kaiguan');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('344', '2023-05-24 19:38:21', NULL, '师傅取消订单扣除信用分数量(最大10分,且必须是正整数,0为不扣)', '345', '2', 'xitong');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('345', '2023-06-08 11:33:25', NULL, '师傅端是否显示推广中心(是或否)', '346', '是', 'kaiguan');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`)
values ('347', '2023-05-12 17:05:11', NULL, '订单等级奖励计算周期(1:周 2月,不填则不初始化)', '347', '1', 'xitong');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`) values('348','2023-07-14 16:36:57',NULL,'师傅端隐私政策','348','<p>本应用尊重并保护所有使用服务用户的个人隐私权。为了给您提供更准确、更有的服务，本应用会按照本隐私权政策的规定使用和披露您的个人信息。但本应用将以高度的勤勉、审慎义务对待这些信息。除本隐私权政策另有规定外，在未征得您事先许可的情况下，本应用不会将这些信息对外披露或向第三方提供。本应用会不时更新本隐私权政策。 您在同意本应用服务使用协议之时，即视为您已经同意本隐私权政策全部内容。本隐私权政策属于本应用服务使用协议不可分割的一部分。</p><p>1. 适用范围</p><p>(a) 在您注册本应用帐号时，您根据本应用要求提供的个人注册信息；</p><p>(b) 在您使用本应用网络服务，或访问本应用平台网页时，本应用自动接收并记录的您的浏览器和计算机上的信息，包括但不限于您的IP地址、浏览器的类型、使用的语言、访问日期和时间、软硬件特征信息及您需求的网页记录等数据；</p><p>(c) 本应用通过合法途径从商业伙伴处取得的用户个人数据。</p><p>您了解并同意，以下信息不适用本隐私权政策：</p><p>(a) 您在使用本应用平台提供的搜索服务时输入的关键字信息；</p><p>(b) 本应用收集到的您在本应用发布的有关信息数据，包括但不限于参与活动、成交信息及评价详情；</p><p>(c) 违反法律规定或违反本应用规则行为及本应用已对您采取的措施。</p><p>2. 信息使用</p><p>(a)本应用不会向任何无关第三方提供、出售、出租、分享或交易您的个人信息，除非事先得到您的许可，或该第三方和本应用（含本应用关联公司）单独或共同为您提供服务，且在该服务结束后，其将被禁止访问包括其以前能够访问的所有这些资料。</p><p>(b) 本应用亦不允许任何第三方以任何手段收集、编辑、出售或者无偿传播您的个人信息。任何本应用平台用户如从事上述活动，一经发现，本应用有权立即终止与该用户的服务协议。</p><p>(c) 为服务用户的目的，本应用可能通过使用您的个人信息，向您提供您感兴趣的信息，包括但不限于向您发出产品和服务信息，或者与本应用合作伙伴共享信息以便他们向您发送有关其产品和服务的信息（后者需要您的事先同意）。</p><p>3. 信息披露</p><p>在如下情况下，本应用将依据您的个人意愿或法律的规定全部或部分的披露您的个人信息：</p><p>(a) 经您事先同意，向第三方披露；</p><p>(b)为提供您所要求的产品和服务，而必须和第三方分享您的个人信息；</p><p>(c) 根据法律的有关规定，或者行政或司法机构的要求，向第三方或者行政、司法机构披露；</p><p>(d) 如您出现违反中国有关法律、法规或者本应用服务协议或相关规则的情况，需要向第三方披露；</p><p>(e) 如您是适格的知识产权投诉人并已提起投诉，应被投诉人要求，向被投诉人披露，以便双方处理可能的权利纠纷；</p><p>(f) 在本应用平台上创建的某一交易中，如交易任何一方履行或部分履行了交易义务并提出信息披露请求的，本应用有权决定向该用户提供其交易对方的联络方式等必要信息，以促成交易的完成或纠纷的解决。</p><p>(g) 其它本应用根据法律、法规或者网站政策认为合适的披露。</p><p>4. 信息存储和交换</p><p>本应用收集的有关您的信息和资料将保存在本应用及（或）其关联公司的服务器上，这些信息和资料可能传送至您所在国家、地区或本应用收集信息和资料所在地的境外并在境外被访问、存储和展示。</p><p>5. Cookie的使用</p><p>(a) 在您未拒绝接受cookies的情况下，本应用会在您的计算机上设定或取用cookies ，以便您能登录或使用依赖于cookies的本应用平台服务或功能。本应用使用cookies可为您提供更加周到的个性化服务，包括推广服务。</p><p>(b) 您有权选择接受或拒绝接受cookies。您可以通过修改浏览器设置的方式拒绝接受cookies。但如果您选择拒绝接受cookies，则您可能无法登录或使用依赖于cookies的本应用网络服务或功能。</p><p>(c) 通过本应用所设cookies所取得的有关信息，将适用本政策。</p><p>6. 信息安全</p><p>(a) 本应用帐号均有安全保护功能，请妥善保管您的用户名及密码信息。本应用将通过对用户密码进行加密等安全措施确保您的信息不丢失，不被滥用和变造。尽管有前述安全措施，但同时也请您注意在信息网络上不存在“完善的安全措施”。</p><p>(b) 在使用本应用网络服务进行网上交易时，您不可避免的要向交易对方或潜在的交易对方披露自己的个人信息，如联络方式或者邮政地址。请您妥善保护自己的个人信息，仅在必要的情形下向他人提供。如您发现自己的个人信息泄密，尤其是本应用用户名及密码发生泄露，请您立即联络本应用客服，以便本应用采取相应措施。</p><p>7.本隐私政策的更改</p><p>(a)如果决定更改隐私政策，我们会在本政策中、本公司网站中以及我们认为适当的位置发布这些更改，以便您了解我们如何收集、使用您的个人信息，哪些人可以访问这些信息，以及在什么情况下我们会透露这些信息。</p><p>(b)本公司保留随时修改本政策的权利，因此请经常查看。如对本政策作出重大更改，本公司会通过网站通知的形式告知。</p>','xieyi');
insert into `common_info` (`id`, `create_at`, `max`, `min`, `type`, `value`, `condition_from`) values('349','2021-08-14 19:09:54',NULL,'师傅端注册协议','349','<p>尊敬的用户您好：在您使用本服务之前，请您认真阅读本用户协议，更好的了解我们所提供的服务以及您享有的权利义务。您开始使用时，即表示您已经了解并确认接受了本文件中的全部条款，包括我们对本服务条款随时做的任何修改。</p><p>一、协议的效力</p><p>本协议内容包括协议正文及所有已经发布或将来可能发布的各类规则。所有规则为本协议不可分割的组成部分，与协议正文具有同等法律效力。您承诺接受并遵守本协议的约定。如果您不同意本协议的约定，您应立即停止使用本平台服务。</p><p>二、用户行为规范</p><p>用户同意将不会利用本服务进行任何违法或不正当的活动，包括但不限于下列行为∶</p><p>发布或以其它方式传送含有下列内容之一的信息：</p><p>反对宪法所确定的基本原则的；</p><p>危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；</p><p>损害国家荣誉和利益的；</p><p>煽动民族仇恨、民族歧视、破坏民族团结的；</p><p>破坏国家宗教政策，宣扬邪教和封建迷信的；</p><p>散布谣言，扰乱社会秩序，破坏社会稳定的；</p><p>散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；</p><p>侮辱或者诽谤他人，侵害他人合法权利的；</p><p>含有虚假、诈骗、有害、胁迫、侵害他人隐私、骚扰、侵害、中伤、粗俗、猥亵、或其它道德上令人反感的内容；</p><p>含有当地法律、法规、规章、条例以及任何具有法律效力之规范所限制或禁止的其它内容的；</p><p>含有不适合在本平台展示的内容；</p><p>以任何方式危害他人的合法权益；</p><p>冒充其他任何人或机构，或以虚伪不实的方式陈述或谎称与任何人或机构有关；</p><p>将依据任何法律或合约或法定关系（例如由于雇佣关系和依据保密合约所得知或揭露之内部资料、专属及机密资料）知悉但无权传送之任何内容加以发布、发送电子邮件或以其它方式传送；</p><p>将侵害他人著作权、专利权、商标权、商业秘密、或其它专属权利（以下简称“专属权利”）之内容加以发布或以其它方式传送；</p><p>将任何广告信函、促销资料、“垃圾邮件”、““滥发信件”、“连锁信件”、“直销”或其它任何形式的劝诱资料加以发布、发送或以其它方式传送；</p><p>将设计目的在于干扰、破坏或限制任何计算机软件、硬件或通讯设备功能之计算机病毒（包括但不限于木马程序（trojan horses）、蠕虫（worms）、定时炸弹、删除蝇（cancelbots）（以下简称“病毒”）或其它计算机代码、档案和程序之任何资料，加以发布、发送或以其它方式传送；</p><p>干扰或破坏本服务或与本服务相连线之服务器和网络，或违反任何关于本服务连线网络之规定、程序、政策或规范；</p><p>跟踪、人肉搜索或以其它方式骚扰他人；</p><p>故意或非故意地违反任何适用的当地、国家法律，以及任何具有法律效力的规则；</p><p>未经合法授权而截获、篡改、收集、储存或删除他人个人信息、站内邮件或其它数据资料，或将获知的此类资料用于任何非法或不正当目的。</p><p>三、知识产权</p><p>本平台所有设计图样以及其他图样、产品及服务名称。任何人不得使用、复制或用作其他用途。未经我们许可，任何单位和个人不得私自复制、传播、展示、镜像、上载、下载、使用，或者从事任何其他侵犯我们知识产权的行为。否则，我们将追究相关法律责任。</p><p>我们鼓励用户充分利用平台自由地张贴和共享自己的信息，但这些内容必须位于公共领域内，或者用户拥有这些内容的使用权。同时，用户对于其创作并在本平台上发布的合法内容依法享有著作权及其相关权利。</p><p>四、免责声明</p><p>互联网是一个开放平台，用户将照片等个人资料上传到互联网上，有可能会被其他组织或个人复制、转载、擅改或做其它非法用途，用户必须充分意识此类风险的存在。用户明确同意其使用本服务所存在的风险将完全由其自己承担；因其使用本服务而产生的一切后果也由其自己承担，我们对用户不承担任何责任。</p><p>对于用户上传的照片、资料、证件等，已采用相关措施并已尽合理努力进行审核，但不保证其内容的正确性、合法性或可靠性，相关责任由上传上述内容的会员负责。</p><p>尽管已采取相应的技术保障措施 ，但用户仍有可能收到各类的广告信或其他不以招聘/应聘为目的邮件或其它方式传送的任何内容，本平台不承担责任。</p><p>对于各种广告信息、链接、资讯等，不保证其内容的正确性、合法性或可靠性，相关责任由广告商承担；用户通过本服务与广告商进行任何形式的通讯或商业往来，或参与促销活动，包含相关商品或服务之付款及交付，以及达成的其它任何相关条款、条件、保证或声明，完全为用户与广告商之间之行为，与本平台无关。用户因前述任何交易或前述广告商而遭受的任何性质的损失或损害，本平台不承担任何责任。</p><p>本平台不保证其提供的服务一定能满足用户的要求和期望，也不保证服务不会中断，对服务的及时性、安全性、准确性也都不作保证。对于因不可抗力或无法控制的原因造成的网络服务中断或其他缺陷，不承担任何责任。我们不对用户所发布信息的删除或储存失败承担责任。我们有权判断用户的行为是否符合本网站使用协议条款之规定，如果我们认为用户违背了协议条款的规定，我们有终止向其提供服务的权利。</p><p>本平台保留变更、中断或终止部分网络服务的权利。保留根据实际情况随时调整平台提供的服务种类、形式的权利。本平台不承担因业务调整给用户造成的损失。本平台仅提供相关服务，除此之外与本服务有关的设备（如电脑、调制解调器及其他与接入互联网有关的装置）及所需的费用（如为接入互联网而支付的电话费及上网费）均应由用户自行负担。</p>','xieyi');
-- ----------------------------
-- Table structure for credit_record
-- ----------------------------
DROP TABLE IF EXISTS `credit_record`;
CREATE TABLE `credit_record`
(
    `record_id`         int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '记录id',
    `sys_user_name`     varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '操作人用户',
    `sys_user_id`       int(11)                                                       NULL DEFAULT NULL COMMENT '操作人id',
    `rider_user_id`     int(11)                                                       NULL DEFAULT NULL COMMENT '师傅id',
    `rider_user_name`   varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '师傅昵称',
    `rider_phone`       varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '师傅手机号',
    `last_credit_score` int(11)                                                       NULL DEFAULT NULL COMMENT '修改前的信用分',
    `type`              int(1)                                                        NULL DEFAULT NULL COMMENT '1增加 2减少',
    `num`               int(11)                                                       NULL DEFAULT NULL COMMENT '分数',
    `next_credit_score` int(11)                                                       NULL DEFAULT NULL COMMENT '修改后的信用分',
    `reason`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time`       datetime(0)                                                   NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 31
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of credit_record
-- ----------------------------

-- ----------------------------
-- Table structure for department
-- ----------------------------
DROP TABLE IF EXISTS `department`;
CREATE TABLE `department`
(
    `department_id`   int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT '科室id',
    `hospital_id`     int(11)                                                      NULL DEFAULT NULL COMMENT '医院id',
    `department_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名称',
    `sort`            int(11)                                                      NULL DEFAULT NULL COMMENT '排序',
    `create_time`     datetime(0)                                                  NULL DEFAULT NULL COMMENT '创建时间',
    `is_enable`       int(11)                                                      NULL DEFAULT NULL COMMENT '是否启用(0否 1是)',
    `parent_id`       int(11)                                                      NULL DEFAULT NULL COMMENT '上级id',
    PRIMARY KEY (`department_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 262
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of department
-- ----------------------------
INSERT INTO `department`
VALUES (12, 3, '内科', 1, '2022-12-05 09:54:01', 1, 0);

-- ----------------------------
-- Table structure for doctor
-- ----------------------------
DROP TABLE IF EXISTS `doctor`;
CREATE TABLE `doctor`
(
    `doctor_id`          int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT '医生id',
    `hospital_id`        int(11)                                                      NOT NULL COMMENT '医院id',
    `department_id`      int(11)                                                      NOT NULL COMMENT '科室id',
    `doctor_name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '医生姓名',
    `doctor_code`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医生工号',
    `phone`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
    `email`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
    `gender`             tinyint(1)                                                   NULL DEFAULT NULL COMMENT '性别 1男 2女',
    `birth_date`         date                                                         NULL DEFAULT NULL COMMENT '出生日期',
    `id_card`            varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
    `avatar`             text                                                         NULL COMMENT '头像',
    `title`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职称',
    `professional_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业技术等级',
    `education`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学历',
    `specialty`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业特长',
    `introduction`       text                                                         NULL COMMENT '医生简介',
    `tag`                varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
    `remarks`            text                                                         NULL COMMENT '备注',
    `qualification_cert` text                                                         NULL COMMENT '执业资格证书图片',
    `license_number`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执业证书编号',
    `practice_scope`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执业范围',
    `entry_date`         date                                                         NULL DEFAULT NULL COMMENT '入职日期',
    `work_status`        tinyint(1)                                                   NULL DEFAULT 1 COMMENT '工作状态 1在职 2离职 3休假',
    `sort`               int(11)                                                      NULL DEFAULT 0 COMMENT '排序',
    `is_enable`          tinyint(1)                                                   NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
    `create_time`        datetime(0)                                                  NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime(0)                                                  NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`doctor_id`) USING BTREE,
    UNIQUE INDEX `uk_doctor_code` (`doctor_code`) USING BTREE COMMENT '医生工号唯一索引',
    INDEX `idx_hospital_id` (`hospital_id`) USING BTREE,
    INDEX `idx_department_id` (`department_id`) USING BTREE,
    INDEX `idx_doctor_name` (`doctor_name`) USING BTREE,
    CONSTRAINT `fk_doctor_hospital` FOREIGN KEY (`hospital_id`) REFERENCES `hospital` (`hospital_id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT `fk_doctor_department` FOREIGN KEY (`department_id`) REFERENCES `department` (`department_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  COMMENT = '医生表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of doctor
-- ----------------------------
INSERT INTO `doctor` VALUES (1, 3, 12, '张三', 'DOC001', '13800138001', '<EMAIL>', 1, '1980-05-15', '110101198005151234', NULL, '主任医师', '正高级', '博士', '心血管疾病诊治', '从事心血管内科临床工作20年，擅长冠心病、高血压等疾病的诊治', '专家,心血管', '经验丰富的心血管专家', NULL, 'YS001', '内科执业', '2020-01-01', 1, 1, 1, '2024-08-05 10:00:00', '2024-08-05 10:00:00');
INSERT INTO `doctor` VALUES (2, 3, 12, '李四', 'DOC002', '13800138002', '<EMAIL>', 2, '1985-08-20', '110101198508201234', NULL, '副主任医师', '副高级', '硕士', '消化系统疾病', '消化内科专家，对胃肠疾病有丰富经验', '消化科,专家', '消化内科资深医师', NULL, 'YS002', '内科执业', '2021-03-15', 1, 2, 1, '2024-08-05 10:00:00', '2024-08-05 10:00:00');

-- ----------------------------
-- 医生相关字典数据
-- ----------------------------

-- 性别字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('性别', '性别', NULL, NULL, 1, '性别分类', 0);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('男', NULL, '1', '男', 1, '男性', LAST_INSERT_ID());
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('女', NULL, '2', '女', 2, '女性', LAST_INSERT_ID()-1);

-- 医生职称字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('医生职称', '医生职称', NULL, NULL, 1, '医生职称分类', 0);
SET @title_parent_id = LAST_INSERT_ID();
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('主任医师', NULL, '1', '主任医师', 1, '主任医师职称', @title_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('副主任医师', NULL, '2', '副主任医师', 2, '副主任医师职称', @title_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('主治医师', NULL, '3', '主治医师', 3, '主治医师职称', @title_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('住院医师', NULL, '4', '住院医师', 4, '住院医师职称', @title_parent_id);

-- 学历字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('学历', '学历', NULL, NULL, 1, '学历分类', 0);
SET @education_parent_id = LAST_INSERT_ID();
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('博士', NULL, '1', '博士', 1, '博士学历', @education_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('硕士', NULL, '2', '硕士', 2, '硕士学历', @education_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('本科', NULL, '3', '本科', 3, '本科学历', @education_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('专科', NULL, '4', '专科', 4, '专科学历', @education_parent_id);

-- 工作状态字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('工作状态', '工作状态', NULL, NULL, 1, '医生工作状态分类', 0);
SET @work_status_parent_id = LAST_INSERT_ID();
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('在职', NULL, '1', '在职', 1, '在职状态', @work_status_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('离职', NULL, '2', '离职', 2, '离职状态', @work_status_parent_id);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('休假', NULL, '3', '休假', 3, '休假状态', @work_status_parent_id);
INSERT INTO `department`
VALUES (13, 4, '外科', 2, '2022-12-05 09:54:53', 1, 0);
INSERT INTO `department`
VALUES (14, 5, '妇产科', 3, '2022-12-05 09:55:07', 1, 0);
INSERT INTO `department`
VALUES (15, 4, '儿科', 4, '2022-12-05 09:55:29', 1, 0);
INSERT INTO `department`
VALUES (16, 5, '五官科', 5, '2022-12-05 09:55:37', 1, 0);
INSERT INTO `department`
VALUES (17, 5, '其他科室', 6, '2022-12-05 09:55:47', 1, 0);
INSERT INTO `department`
VALUES (18, 4, '专科护理门诊', 7, '2022-12-05 09:56:05', 1, 0);
INSERT INTO `department`
VALUES (19, 3, '特需门诊', 8, '2022-12-05 09:56:15', 1, 0);
INSERT INTO `department`
VALUES (20, 3, '神经一科', 1, '2022-12-05 09:56:52', 1, 12);
INSERT INTO `department`
VALUES (21, 3, '神经二科', 2, '2022-12-05 09:57:02', 1, 12);
INSERT INTO `department`
VALUES (22, 3, '神经三科', 3, '2022-12-05 09:57:06', 1, 12);
INSERT INTO `department`
VALUES (23, 3, '眩晕门诊', 4, '2022-12-05 09:57:16', 1, 12);
INSERT INTO `department`
VALUES (24, 3, '消化内一科', 5, '2022-12-05 09:57:24', 1, 12);
INSERT INTO `department`
VALUES (25, 3, '消化内二科', 6, '2022-12-05 09:57:29', 1, 12);
INSERT INTO `department`
VALUES (26, 3, '心血管内一科', 7, '2022-12-05 09:57:56', 1, 12);
INSERT INTO `department`
VALUES (27, 3, '心血管内二科', 8, '2022-12-05 09:58:00', 1, 12);
INSERT INTO `department`
VALUES (28, 3, '呼吸内一科', 9, '2022-12-05 09:58:09', 1, 12);
INSERT INTO `department`
VALUES (29, 3, '呼吸内二科', 10, '2022-12-05 09:58:14', 1, 12);
INSERT INTO `department`
VALUES (30, 4, '肠胃肝胆胰肿瘤', 1, '2022-12-05 09:58:50', 1, 13);
INSERT INTO `department`
VALUES (31, 4, '食管肺肿瘤门诊', 2, '2022-12-05 09:59:17', 1, 13);
INSERT INTO `department`
VALUES (32, 4, '泌尿外科', 3, '2022-12-05 09:59:29', 1, 13);
INSERT INTO `department`
VALUES (33, 4, '女性泌尿外科', 4, '2022-12-05 09:59:38', 1, 13);
INSERT INTO `department`
VALUES (34, 4, 'xx二科', 5, '2022-12-05 09:59:51', 1, 13);
INSERT INTO `department`
VALUES (35, 4, '胸外科', 6, '2022-12-05 10:00:01', 1, 13);
INSERT INTO `department`
VALUES (36, 4, '皮肤科', 7, '2022-12-05 10:00:08', 1, 13);
INSERT INTO `department`
VALUES (37, 5, '妇科', 1, '2022-12-05 10:00:29', 1, 14);
INSERT INTO `department`
VALUES (38, 5, '产科', 2, '2022-12-05 10:00:35', 1, 14);
INSERT INTO `department`
VALUES (39, 5, '产科孕产心里保健门诊', 3, '2022-12-05 10:00:49', 1, 14);
INSERT INTO `department`
VALUES (40, 18, '神经内科', 1, '2023-03-07 14:21:57', 1, 0);
INSERT INTO `department`
VALUES (41, 18, '神经内科1科', 0, '2023-03-07 14:22:35', 1, 40);
INSERT INTO `department`
VALUES (42, 16, 'rtyu', 0, '2023-03-31 13:37:16', 1, 0);
INSERT INTO `department`
VALUES (44, 16, 'yyyy', 0, '2023-03-31 13:37:39', 1, 42);
INSERT INTO `department`
VALUES (45, 9, '肾内科', 0, '2023-04-03 16:05:28', 1, 0);
INSERT INTO `department`
VALUES (46, 9, '肾内一科', 0, '2023-04-03 16:05:54', 1, 45);
INSERT INTO `department`
VALUES (47, 9, '肾内二科', 1, '2023-04-03 16:06:11', 1, 45);
INSERT INTO `department`
VALUES (48, 9, '肾内三科', 2, '2023-04-03 16:07:07', 1, 45);
INSERT INTO `department`
VALUES (49, 9, '耳鼻喉科', 1, '2023-04-03 16:11:33', 1, 0);
INSERT INTO `department`
VALUES (50, 9, '肾内四科', 3, '2023-04-03 16:11:52', 1, 45);
INSERT INTO `department`
VALUES (51, 9, '肾病综合科', 4, '2023-04-03 16:12:54', 1, 45);
INSERT INTO `department`
VALUES (52, 9, '肾内六科', 5, '2023-04-03 16:16:49', 1, 45);
INSERT INTO `department`
VALUES (53, 9, '肾内七科', 6, '2023-04-03 16:17:09', 1, 45);
INSERT INTO `department`
VALUES (54, 9, '肾内八科', 7, '2023-04-03 16:17:24', 1, 45);
INSERT INTO `department`
VALUES (55, 9, '肾内九科', 8, '2023-04-03 16:17:36', 1, 45);
INSERT INTO `department`
VALUES (56, 9, '肾内十科', 9, '2023-04-03 16:17:56', 1, 45);
INSERT INTO `department`
VALUES (57, 9, '肾内十一科', 10, '2023-04-03 16:18:19', 1, 45);
INSERT INTO `department`
VALUES (58, 9, '肾内十二', 11, '2023-04-03 16:29:23', 1, 45);
INSERT INTO `department`
VALUES (59, 9, '肾内13', 12, '2023-04-03 16:29:34', 1, 45);
INSERT INTO `department`
VALUES (60, 9, '肾内14', 13, '2023-04-03 16:29:44', 1, 45);
INSERT INTO `department`
VALUES (61, 9, '肾内15', 14, '2023-04-03 16:29:54', 1, 45);
INSERT INTO `department`
VALUES (62, 9, '肾内16', 15, '2023-04-03 16:30:06', 1, 45);
INSERT INTO `department`
VALUES (63, 3, '传参', 3, '2023-04-03 17:33:14', 1, 12);
INSERT INTO `department`
VALUES (64, 3, '阿斯6', 0, '2023-04-03 17:33:50', 1, 12);
INSERT INTO `department`
VALUES (65, 9, '肾内17', 16, '2023-04-03 17:52:32', 1, 45);
INSERT INTO `department`
VALUES (66, 9, '肾内19', 18, '2023-04-03 17:52:49', 1, 45);
INSERT INTO `department`
VALUES (67, 9, '肾内20', 19, '2023-04-03 17:52:58', 1, 45);
INSERT INTO `department`
VALUES (68, 9, '肾内21', 20, '2023-04-03 17:53:07', 1, 45);
INSERT INTO `department`
VALUES (69, 9, '肾内23', 22, '2023-04-03 18:23:57', 1, 45);
INSERT INTO `department`
VALUES (184, 3, '呼吸内二科', 0, '2023-04-04 19:01:02', 1, 14);
INSERT INTO `department`
VALUES (185, 3, '特需门诊', 0, '2023-04-04 19:01:03', 1, 14);
INSERT INTO `department`
VALUES (186, 3, '心血管内二科', 0, '2023-04-04 19:01:03', 1, 18);
INSERT INTO `department`
VALUES (187, 3, '心血管内一科', 0, '2023-04-04 19:01:03', 1, 18);
INSERT INTO `department`
VALUES (188, 3, '消化内二科', 0, '2023-04-04 19:01:03', 1, 18);
INSERT INTO `department`
VALUES (191, 7, '1', 0, '2023-04-11 16:58:16', 1, 0);
INSERT INTO `department`
VALUES (192, 7, '2', 0, '2023-04-11 16:58:23', 1, 0);
INSERT INTO `department`
VALUES (193, 7, '3', 0, '2023-04-11 16:58:29', 1, 0);
INSERT INTO `department`
VALUES (194, 7, '31', 0, '2023-04-11 16:58:48', 1, 193);
INSERT INTO `department`
VALUES (195, 7, '32', 0, '2023-04-11 16:59:06', 1, 193);
INSERT INTO `department`
VALUES (196, 7, '21', 0, '2023-04-11 16:59:19', 1, 192);
INSERT INTO `department`
VALUES (197, 7, '4', 0, '2023-04-11 16:59:56', 1, 0);
INSERT INTO `department`
VALUES (221, 19, '02', 0, '2023-04-11 18:23:37', 1, 0);
INSERT INTO `department`
VALUES (227, 19, '01', 0, '2023-04-11 18:26:37', 1, 0);
INSERT INTO `department`
VALUES (228, 19, '03', 0, '2023-04-11 18:26:45', 1, 0);
INSERT INTO `department`
VALUES (234, 5, '测试', 0, '2023-04-11 18:28:06', 1, 16);
INSERT INTO `department`
VALUES (235, 3, '妇产科', 0, '2023-04-11 18:28:47', 1, 0);
INSERT INTO `department`
VALUES (237, 3, '科室一', 0, '2023-04-11 18:29:01', 1, 235);
INSERT INTO `department`
VALUES (238, 3, '科室二', 0, '2023-04-11 18:29:10', 1, 235);
INSERT INTO `department`
VALUES (239, 3, '科室三', 0, '2023-04-11 18:29:20', 1, 235);
INSERT INTO `department`
VALUES (240, 3, '耳鼻喉科', 0, '2023-04-11 18:32:26', 1, 0);
INSERT INTO `department`
VALUES (241, 3, '耳鼻喉一科', 0, '2023-04-11 18:32:37', 1, 240);
INSERT INTO `department`
VALUES (242, 3, '耳鼻喉二科', 0, '2023-04-11 18:32:44', 1, 240);
INSERT INTO `department`
VALUES (243, 3, '耳鼻喉三科', 0, '2023-04-11 18:32:51', 1, 240);
INSERT INTO `department`
VALUES (248, 3, '特许门诊一', 0, '2023-04-11 18:41:56', 1, 19);
INSERT INTO `department`
VALUES (249, 3, '特许门诊二', 0, '2023-04-11 18:42:03', 1, 19);
INSERT INTO `department`
VALUES (250, 3, '特许门诊4', 0, '2023-04-11 18:49:54', 1, 240);
INSERT INTO `department`
VALUES (251, 3, '特许门诊5', 0, '2023-04-11 18:50:08', 1, 240);
INSERT INTO `department`
VALUES (252, 3, '内科', 1, '2023-04-14 18:46:39', 1, 49);
INSERT INTO `department`
VALUES (287, 3, '内科', 1, '2023-04-14 18:59:44', 1, 12);
INSERT INTO `department`
VALUES (288, 3, '内科1', 1, '2023-04-14 19:01:31', 1, 12);
INSERT INTO `department`
VALUES (289, 3, '神经一科2', 1, '2023-04-14 19:01:31', 1, 12);
INSERT INTO `department`
VALUES (290, 3, '特需门诊3', 2, '2023-04-14 19:01:31', 1, 0);
INSERT INTO `department`
VALUES (291, 3, '神经二科4', 2, '2023-04-14 19:01:31', 1, 12);
INSERT INTO `department`
VALUES (292, 3, '神经三科5', 3, '2023-04-14 19:01:31', 1, 12);
INSERT INTO `department`
VALUES (293, 3, '眩晕门诊6', 4, '2023-04-14 19:01:31', 1, 12);
INSERT INTO `department`
VALUES (294, 20, '血液科', 1, '2023-04-17 14:40:04', 1, 0);
INSERT INTO `department`
VALUES (295, 20, '血液一科', 0, '2023-04-17 14:40:31', 1, 294);
INSERT INTO `department`
VALUES (296, 20, '血液二科', 0, '2023-04-17 14:40:46', 1, 294);
INSERT INTO `department`
VALUES (297, 20, '耳鼻喉科', 0, '2023-04-17 14:41:08', 1, 0);
INSERT INTO `department`
VALUES (298, 20, '耳鼻喉一科', 1, '2023-04-17 14:41:25', 1, 297);
INSERT INTO `department`
VALUES (299, 20, '耳鼻喉二科', 0, '2023-04-17 14:41:46', 1, 297);
INSERT INTO `department`
VALUES (300, 21, '肾内科', 0, '2023-04-17 17:45:09', 1, 0);
INSERT INTO `department`
VALUES (301, 21, '肾内一科', 0, '2023-04-17 17:45:25', 1, 300);
-- ----------------------------
-- Table structure for game
-- ----------------------------
DROP TABLE IF EXISTS `game`;
CREATE TABLE `game`
(
    `id`          bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '游戏id',
    `game_name`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '游戏名称',
    `status`      int(2)                                                        NULL DEFAULT NULL COMMENT '0启用1删除\r\n',
    `game_img`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '游戏图分类图片',
    `create_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 19
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game
-- ----------------------------

-- ----------------------------
-- Table structure for help_classify
-- ----------------------------
DROP TABLE IF EXISTS `help_classify`;
CREATE TABLE `help_classify`
(
    `help_classify_id`   int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '帮助中心分类',
    `help_classify_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
    `sort`               int(11)                                                       NULL DEFAULT NULL COMMENT '排序',
    `parent_id`          int(11)                                                       NULL DEFAULT NULL COMMENT '上级id',
    `create_time`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `types`              int(11)                                                       NULL DEFAULT NULL,
    PRIMARY KEY (`help_classify_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 11
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of help_classify
-- ----------------------------

-- ----------------------------
-- Table structure for help_word
-- ----------------------------
DROP TABLE IF EXISTS `help_word`;
CREATE TABLE `help_word`
(
    `help_word_id`      int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '帮助文档id',
    `help_word_title`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '帮助标题',
    `help_word_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NULL COMMENT '帮助文档内容',
    `help_classify_id`  int(11)                                                       NULL DEFAULT NULL COMMENT '帮助分类id',
    `sort`              int(11)                                                       NULL DEFAULT NULL COMMENT '排序',
    `create_time`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`help_word_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 11
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of help_word
-- ----------------------------
INSERT INTO `help_word`
VALUES (1, '为什么提示不支持打开非业务域名', '因为所以科学道理', 2, 1, '2020-12-12 12:12:12');
INSERT INTO `help_word`
VALUES (2, '文件类课间如何保存', '不知道', 2, 2, '2020-12-12 12:12:12');
INSERT INTO `help_word`
VALUES (3, '已报名的活动可以取消吗', '可以，需要扣除百分之20', 3, 1, '2020-12-12 12:12:12');
INSERT INTO `help_word`
VALUES (7, '报名活动呀呀呀呀', '<p>报名活动呀呀呀呀报名活动呀呀呀呀报名活动呀呀呀呀报名活动呀呀呀呀报名活动呀呀呀呀报名活动呀呀呀呀报名活动呀呀呀呀报名活动呀呀呀呀报名活动呀呀呀呀</p>', 6, 0,
        '2022-07-05');
INSERT INTO `help_word`
VALUES (8, '怎么登录呢？',
        '<p>微信授权即可登录<img src=\"https://jiazheng.xianmxkj.com/file/uploadPath/2022/07/06/0e9d093abb26f9bc85be36e3a206bff5.png\"></p>',
        7, 0, '2022-07-06');
INSERT INTO `help_word`
VALUES (9, '商户怎么入驻呢？', '<p>登录首页 点击：我要入驻</p>', 8, 0, '2022-07-06');
INSERT INTO `help_word`
VALUES (10, '支付延迟', '<p>如果支付发生延迟，请稍后再重试</p>', 10, 1, '2023-04-17 16:12:54');
-- ----------------------------
-- Table structure for hospital
-- ----------------------------
DROP TABLE IF EXISTS `hospital`;
CREATE TABLE `hospital`
(
    `hospital_id`        int(11) NOT NULL AUTO_INCREMENT COMMENT '医院id',
    `hospital_name`      varchar(255)   DEFAULT NULL COMMENT '医院名称',
    `hospital_img`       text COMMENT '医院图片',
    `hospital_level`     varchar(25)    DEFAULT NULL COMMENT '医院等级(1:三甲)',
    `hospital_type`      varchar(25)    DEFAULT NULL COMMENT '医院类型 (1:综合医院)',
    `hospital_lng`       decimal(10, 6) DEFAULT NULL COMMENT '医院经度',
    `hospital_lat`       decimal(10, 6) DEFAULT NULL COMMENT '医院纬度',
    `hospital_details`   text COMMENT '医院简介',
    `province`           varchar(100)   DEFAULT NULL COMMENT '省',
    `city`               varchar(100)   DEFAULT NULL COMMENT '市',
    `district`           varchar(100)   DEFAULT NULL COMMENT '区',
    `address_details`    varchar(255)   DEFAULT NULL COMMENT '详细地址',
    `department_details` text COMMENT '科室简介',
    `is_enable`          int(1)         DEFAULT NULL COMMENT '是否启用 (0:否 1:是)',
    `icon`               text COMMENT '医院logo',
    `city_initial`       varchar(255)   DEFAULT NULL COMMENT '城市首字母',
    `sort`               int(11)        DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`hospital_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 28
  DEFAULT CHARSET = utf8mb4 COMMENT ='医院表';

-- ----------------------------
-- Records of hospital
-- ----------------------------
INSERT INTO `hospital`
VALUES (3, '西京医院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/03/07/4e067896f920bc552eeb25847a828d4e.png',
        '三甲医院', '综合医院', 108.987999, 34.271500,
        '西京医院坐落在美丽的古都西安，为第四军医大学第一附属医院，前身是1939年延安抗战岁月里诞生的中央医院，1954年与原第五军医大学附属医院合并。2017年转隶空军。医院始终秉承践行宗旨、救死扶伤的“使命文化”，勇于争先、不断超越的“创新文化”，精益求精、追求卓越的“精品文化”，公平竞争、和谐发展的“阳光文化”，锐意进取、创新发展，先后获全国百佳医院、全国百姓放心示范医院、全国拥政爱民模范单位、全国抗震救灾英雄集体、全军医院建设先进单位、全军为部队服务先进医院、总后\n先进师旅团单位和总后先进党委等殊荣。在国内权威部门发布的全国医院综合排名中，医院综合实力跻身全国五强，院名被国家工商总局认定为中国驰名商标。\n\n',
        '浙江省', '杭州市', '下城区', '西京医院',
        '重点科室:医院现有国家级重点学科9个,国家临床重点专科(军队) 7个,全军医学专科研究所12个,国家、军队重点实验室S个，建成消化病、科、脑科、心血管病、皮肤病、形等6个“院中院”。\n\n', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/03/07/71b5d906edfa10a54a18cbfb43085ddc.png', 'H', 1);
INSERT INTO `hospital`
VALUES (4, '交大一附院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/d15e38d8aac0b1cd9f0f9f6092727933.png',
        '三甲医院', '综合医院', 108.936829, 34.219009,
        '交大位于西安高新技术产业开发区团结南路16号，成立于2002年6月9日，是一所集医疗、教学、科研、预防、保健、康复为一体的股份制综合医院，2009年8月通过陕西省卫生厅三级甲等综合医院评审，2017年6月顺利通过三甲复审。2021年12月，与妙佑医疗国际（Mayo Clinic）签署合作协议，成为中国第二家妙佑医疗联盟成员医院。',
        '陕西省', '西安市', '新城区', '交大一附院',
        '重点科室:医院现有国家级重点学科9个,国家临床重点专科(军队) 7个,全军医学专科研究所12个,国家、军队重点实验室S个，建成消化病、科、脑科、心血管病、皮肤病、形等6个“院中院”。\n', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/5fa80dd7a2996cbbeed002ad7c2aee7a.png', 'X', 2);
INSERT INTO `hospital`
VALUES (5, '唐都医院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/1903b84a42e0cbabcf2d7470271f6795.png',
        '三甲医院', '综合医院', 109.063110, 34.277145,
        '空军军医大学第二附属医院(第四军医大学唐都医院)创建于1939年，前身为延安中央医院，1951年定点西安市灞桥区，1955年归建第四军医大学，1958年正式命名为第四军医大学第二附属医院，1985年对外称“唐都医院”。2017年转隶空军后，称空军军医大学第二附属医院，是一所集医疗、教学、科研、预防、保健、康复为一体的现代化综合型三级甲等医院。',
        '陕西省', '西安市', '灞桥区', '新医路口', '神经内科', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/f65fd4a07eecc8fb247400f9668914ed.png', 'X', 3);
INSERT INTO `hospital`
VALUES (6, '西安高新医院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/27/4d9a2adf59d0d48b17ae8cf222284e20.png',
        '二甲医院', '综合医院', 108.879900, 34.230590,
        '西安高新医院位于西安高新技术产业开发区团结南路16号，成立于2002年6月9日，是一所集医疗、教学、科研、预防、保健、康复为一体的股份制综合医院，2009年8月通过陕西省卫生厅三级甲等综合医院评审，2017年6月顺利通过三甲复审。2021年12月，与妙佑医疗国际（Mayo Clinic）签署合作协议，成为中国第二家妙佑医疗联盟成员医院。',
        '陕西省', '西安市', '雁塔区', '西安市团结南路16号',
        '医院现有国家级重点学科9个,国家临床重点专科(军队) 7个,全军医学专科研究所12个,国家、军队重点实验室S个，建成消化病、科、脑科、心血管病、皮肤病、形等6个“院中院”。\n', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/22/e235734e2be8162511e56d495eadcedd.png', 'X', 1);
INSERT INTO `hospital`
VALUES (7, '河南省第三人民医院',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/02/11/adf3272f7c7f5c26d288f9018104c32a.png', '二甲医院',
        '综合医院', 113.750611, 34.767313, '河南省第三人民医院', '河南省', '郑州市', '金水区', '河南省郑州市金水区正光路9号', '内科', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/02/11/35a21abd726d11487a357109ba89fd1b.png', 'Z', 1);
INSERT INTO `hospital`
VALUES (8, '人民医院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/02/21/ea7622dc891aa4ac72e748a33a3f81c5.jpg',
        '三甲医院', '综合医院', 114.228187, 22.724941, '人民医院', '广东省', '深圳市', '龙岗区', '广东省深圳市龙岗区秋收路', '消化内科', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/02/21/c4717d328a117b9345bc2c4755b0addf.jpg', 'S', 1);
INSERT INTO `hospital`
VALUES (9, '郑州第一附属医院',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/03/07/2f7e97bef37f93e6f2ad9ce717ffb203.jpg', '三甲医院',
        '综合医院', 113.613320, 34.748210, '医院简介', '河南省', '郑州市', '中原区', '中原区人民路', '肾内科,心内科等', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/03/07/bdfab2c13396d105819e21eb9800cca6.png', 'Z', 1);
INSERT INTO `hospital`
VALUES (19, '暨南大学附属第一医院',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/03/08/4dc596823f0cb3669f8e0a1e34eeca55.jpg', '三甲医院',
        '综合医院', 113.350480, 23.126709,
        '暨南大学附属第一医院（又名广州华侨医院、暨南大学第一临床医学院），是 “华侨最高学府”、中央统战部、教育部、广东省共建高校暨南大学的直属附属医院。医院是一所集医疗、教学、科研、预防、保健和康复于一体的综合性三级甲等医院，也是广东省高水平医院重点建设医院，综合实力排行华南地区综合医院前十（复旦华南区排行榜）；2019年国家三级公立医院绩效考核中，医院在无年报综合组评级为最高级A级，位居全国第二；获评为“爱婴医院”、“国际SOS合作医院”、“广东省文明医院”、“广州十佳三甲医院”、“全国侨办系统先进集体”、 “全国优质护理服务考核优秀医院”、 “广州最受欢迎三甲医院”。',
        '广东省', '广州市', '天河区', '广东省广州市天河区黄埔大道西', '内科', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/03/08/bdbeadafb4ee110c5d290550b5383e74.jpg', 'G', 1);
INSERT INTO `hospital`
VALUES (20, '福州协和医院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/17/30c3de6413d5aea4ad979963bb50f6d9.jpg',
        '三甲医院', '综合医院', 119.303892, 26.077836, '非常厉害', '福建省', '福州市', '鼓楼区', '福建省福州市鼓楼区新权路29-1号', '没有科室', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/17/d59f230d71b6af98b6bb6f885e7c4466.jpg', 'F', 1);
INSERT INTO `hospital`
VALUES (21, '中南大学第一附属医院',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/17/c11fe4845e61631a979338d154e0b61d.png', '三甲医院',
        '综合医院', 113.120972, 36.157440, '医院简介', '山西省', '长治市', '潞州区', '长冶市人民医院', '妇产科', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/17/a24b782f58fb0300222152a7243f7ffe.png', 'C', 1);
INSERT INTO `hospital`
VALUES (22, '北京医院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/99fd3cc2118fcbf48df81e918010eb3c.jpeg',
        '三甲医院', '综合医院', 116.373909, 39.914730, '5367', '北京市', '北京市', '东城区', '灵境胡同', 'ert', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/1ed677084782467256dfcd82686ba227.jpeg', 'B', 1);
INSERT INTO `hospital`
VALUES (25, '华西医院', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/08/d5226e1b448b98007c7b50f69013db87.jpg',
        '三甲医院', '综合医院', 104.062220, 30.643440,
        '四川大学华西医院（West China Hospital，Sichuan University）是中国西部疑难危急重症诊疗的国家级中心，也是世界规模第一的综合性单点医院，拥有中国规模最大、最早整体通过美国病理家学会（CAP）检查认可的医学检验中心。',
        '四川省', '成都市', '武侯区', '成都市武侯区国学巷37号', '以诊治康复、肿瘤及慢性疾病为主；', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/08/b0f738c0061a9ed33feb3d350db5c178.png', 'C', 1);
INSERT INTO `hospital`
VALUES (27, '厦门大学附属厦门眼科中心医院',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/10/8bbdd768fb9f890ddb7c18fb12e706d0.jpg', '三甲医院',
        '综合医院', 118.097427, 24.436790, '厦门大学附属厦门眼科中心医院', '福建省', '厦门市', '市辖区', '厦门大学附属厦门眼科中心医院', '呼吸科', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/10/4acacebdbe62f2dfa6f916e3c0a741fe.jpg', 'X', 1);

-- ----------------------------
-- Table structure for hospital_employ
-- ----------------------------
DROP TABLE IF EXISTS `hospital_employ`;
CREATE TABLE `hospital_employ`
(
    `service_id`          int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '服务id',
    `service_name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务名称',
    `modular_id`          int(1)                                                  NULL DEFAULT NULL COMMENT '分类id',
    `money`               decimal(10, 2)                                          NULL DEFAULT NULL COMMENT '价格',
    `company`             int(11)                                                 NULL DEFAULT NULL COMMENT '单位(1:天 2:次)',
    `tags`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签',
    `service_describe`    text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '描述',
    `img`                 text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '图标',
    `is_enable`           int(1)                                                  NULL DEFAULT 1 COMMENT '是否启用 0否 1是',
    `service_content`     text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '服务内容',
    `service_instruction` text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '服务须知',
    `title`               text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '标题',
    `route`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路由',
    `background_img`      text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '背景图',
    `service_type`        int(5)                                                  NULL DEFAULT NULL COMMENT '模板类型(1:尊享VIP陪诊 2:全程陪诊 3:诊前约号 4:代办问诊 5:送取结果 6:专享陪诊 7:代办买药 8院内陪护)',
    `icon`                varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '特殊标志',
    `matters_thing`       text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '注意事项',
    `sort`                int(11)                                                 NULL DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`service_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 20
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '服务类型表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hospital_employ
-- ----------------------------
INSERT INTO `hospital_employ`
VALUES (2, '全程陪诊', 1, 588.00, 1, '引流管,picc,造漏,全程陪诊', '高级护理,适合需要专业辅助患者术后恢复的人群',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/7131f5371cc4a0cd5956c303b5e0611b.png', 1,
        '可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本',
        '哒哒哒付<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2023/02/21/b8facf1347b355f7d64c885a87cd5860.jpg\" />',
        '代替患者在医院内跑腿。例如，带患者排队，楼上楼下缴费，提取化验报告等。诊前指导，全程陪\r\n诊，代取结果，车接车送。\r\n', '/pages/index/game/orderDet',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/df427ff5846c4be8bd67b4e7ba784615.png', 2, NULL,
        '可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框可能是富文本框',
        1);
INSERT INTO `hospital_employ`
VALUES (3, '尊享VIP陪诊', 3, 10.01, 2, '陪检,看护输液,晨晚间护理,1111', '123123基础护理，12小时陪护，适合具备完全可以自理入群123123',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/6ace878f61b9d7c8ffe4051b5d1f5400.png', 1,
        '尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊',
        '尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊尊享VIP陪诊<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/d90725086a3a948302e95f58c6cc02fa.png\" />',
        '尊享VIP体验，车接车送', '/pages/index/game/orderDet',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/6a7dcab58a251667c5a746ac5f4f287e.png', 1, NULL,
        '测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试', 1);
INSERT INTO `hospital_employ`
VALUES (4, '专业陪护', 2, 0.01, 2, '就医代办,陪护就医', '基础护理，12小时陪护，适合具备完全可以自理入群',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/2cab4d1367c4656af91e630eb7fb9240.png', 1,
        '专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护',
        '专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/06421f89293893e489d4d06bbd5c56f9.png\" />',
        '专业陪护', '/pages/index/game/orderDet',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/3a9e7d3ffb7bf67b0fa08599739f8264.png', 7, NULL,
        '专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护专业陪护', 3);
INSERT INTO `hospital_employ`
VALUES (5, '12小时陪护', 2, 500.00, 1, '全市接送,代排队挂号,省时高效', '基础护理，12小时陪护，适合具备完全可以自理入群',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/16b7fc8b23c65d5d7188e2a99d5647c7.png', 1,
        '12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护',
        '12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/bfe0124415b74692f675cd5cf8671219.png\" />',
        '12小时陪护', '/pages/index/game/orderDet',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/0ab1b0df391ea69a5329b093dd6dff22.png', 7, NULL,
        '12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护12小时陪护', 4);
INSERT INTO `hospital_employ`
VALUES (6, '诊前约号', 3, 400.00, 2, '就医陪护,陪护就医,安心放心', '基础护理，12小时陪护，适合具备完全可以自理入群',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/07/fb988e77ba580b38b8f934566c40f3d0.png', 1,
        '1、服务内容服务内容服务内容服务内容2、服务内容服务内容服务内容服务内容服务内容3、服务内容服务内容服务内容服务内容服务内容服务内容',
        '1、服务须知服务须知服务须知服务须知2、服务须知服务须知服务须知服务须知服务须知3、服务须知服务须知服务须知服务须知服务须知服务须知<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/07/434fcfd9df561d56c42c8b152db83df0.png\" />',
        '老人、儿童、孕妇优惠专享陪同就医服务', '/pages/index/game/orderDet',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/07/5bb5ba5a57a38ce8a1b5095a1b09f9fe.png', 3,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/cbc65039657ce5fae03f484f71cc3868.png',
        '测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试', 5);
INSERT INTO `hospital_employ`
VALUES (7, '送取结果', 3, 400.00, 2, '就医陪护,陪护就医,安心放心', '基础护理，12小时陪护，适合具备完全可以自理入群',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/ab117125bd4ac3b26ea9ba35916c47ec.png', 1,
        '送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果',
        '送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/16b8953a9249175b2c34a71ce06b0c5d.png\" />',
        '送取结果', '/pages/index/game/orderDet',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/00fd6cd843ba0579ef49be5f1c0c2a48.png', 5,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/4766f849eb4f4b60ba584254d2e6c1ff.png',
        '送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果送取结果', 6);
INSERT INTO `hospital_employ`
VALUES (9, '代办问诊', 3, 288.00, 2, '跑腿,排队,问诊', '代办问诊',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/3e9cf40bea176a861930696550cd3f16.png', 0,
        '代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊',
        '代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/3223ef77ad30d6dbc339a1410dce3f93.png\" />',
        '代办问诊', NULL, 'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/b656c51beceeaf44cccc079d1de92b85.png',
        4, NULL, '代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊代办问诊', 7);
INSERT INTO `hospital_employ`
VALUES (10, '代办买药', 3, 188.00, 2, '买药,排队,跑腿', '代办买药',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/ad1928112324331c74d1786e0bdb6dee.png', 1,
        '代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药',
        '代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/38e533ad4191a378a991978acbca93a3.png\" />',
        '代办买药', NULL, 'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/d37ed4db4d65c2a394a9eaa73b32d5fc.png',
        7, NULL, '代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药代办买药', 8);
INSERT INTO `hospital_employ`
VALUES (11, '专享陪诊', 1, 200.00, 1, '全天陪诊,一条龙服务', '专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊专享陪诊',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/4f1248e670253f83a7d770c8b5fb62c3.png', 1,
        '服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容',
        '服务须知服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/84b19ab37bcaffbf48aa8081b7e6f4af.png\" />',
        '专享陪诊', NULL, 'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/773354e9d2f2fbcbc6fadc21a2c096de.png',
        6, NULL, '服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容服务内容', 9);
INSERT INTO `hospital_employ`
VALUES (12, '全程陪诊', 3, 388.00, 2, '全程陪诊,安心放心', '全程陪诊全程陪诊全程陪诊全程陪诊',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/2caf7dfe1f2cf2343a41df8ebad5736e.png', 1,
        '全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊',
        '全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/e522188f487025506b5551d91a2e9c97.png\" />',
        '全程陪诊', NULL, 'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/08/d1bf5fc4f76168acfed5f1e2e6a7ce10.png',
        2, NULL, '全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊全程陪诊', 10);
INSERT INTO `hospital_employ`
VALUES (15, '半天陪诊', 1, 0.01, 2, '专业,周到,体贴', '半天的陪诊，半天的价格',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/17/91a5d865e0baa49799f855d717b525a1.jpg', 1,
        '半天陪诊半天价格的优惠', '服务周到，价格', '没有服务', NULL,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/17/58f71a00e0a276257b126857bdd8f6bb.jpg', 2, NULL,
        '没有什么可以注意的', 1);
INSERT INTO `hospital_employ`
VALUES (16, '测试测试', 1, 0.03, 2, '测试测试', '测试测试测试测试测试测试',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/5755681a680255062fc9242de2df4e93.jpg', 1,
        '测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试', '测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试', '测试测试测试测试测试测试',
        NULL, 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/04bc74efc252ce9c47f0bfad6db85fac.jpg', 1,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/f81602b58ec2e554e0a35396dfcb5d8a.jpg',
        '测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试', 1);
INSERT INTO `hospital_employ`
VALUES (17, '医院内测试测试测试', 3, 0.01, 2, '测试', '医院内测试测试测试医院内测试测试测试',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/cdb3b5c35009e1630a8bb8916d891619.jpg', 1,
        '医院内测试测试测试医院内测试测试测试', '医院内测试测试测试医院内测试测试测试医院内测试测试测试医院内测试测试测试', '医院内测试测试测试2', NULL,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/4f0db72ba66bf2ab6b8260a7246f5621.jpg', 2,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/20/a37d5523cdd3a90def8be4030f158179.jpg',
        '医院内测试测试测试医院内测试测试测试', 1);
INSERT INTO `hospital_employ`
VALUES (18, '测试', 1, 0.25, 1, '测试', '测试',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/24/fd23fa3ef2dcf03c803a50f1569563ed.jpg', 1, '测试测试',
        '测试', '测试测试测试测试', NULL,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/24/c0d49227565d704a529ee737483e6193.jpg', 7,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/24/f738f52cf25502ec774c5d8f333477d2.jpg', '测试', 1);
INSERT INTO `hospital_employ`
VALUES (19, '3333333', 1, 333.00, 1, '非的故事,发发发',
        '33333333333333333333333333333333333333dgdgghdfg是啥撒旦法撒旦法撒旦法感受到分公司东方故事东方故事电饭锅森岛帆高山东分公司分公司的风格森岛帆高山东分公司山东分公司的分公司的分公司的发给山东分公司东方故事对方公司的个森岛帆高sfg毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/1605a25df529c6b1e911cb3033cb25f2.jpeg', 0,
        '<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/f137e0492f88c496ac1e570bb3accb17.jpeg\" /><img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/0e1d60b65b55f9cb15bf2371fa025701.jpg\" /><img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/003b4bb6abb1efc2184237f2ad969776.png\" />毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付',
        '森岛帆高<img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/0d25c3449ac1f11f887d6aca685a52ba.png\" /><img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/120941838bfb829d87895d6ee134e983.jpg\" /><img src=\"https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/5b30c96e7fdcfaf796bcb8da7baa582b.jpeg\" />毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付毒贩夫妇付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付发发发发发发付付付付付付付付付付付付付付付付付付付付付付付付付付付付付付',
        '3444444444444', NULL,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/ac922c6f90880c1d636b727357fb2448.jpeg', 7,
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/12/b7e09f0d3b27d2f9bea928904e6ba0fe.png',
        '撒旦法规格是的分公司的分公司的发森岛帆高森岛帆高是电饭锅森岛帆高森岛帆高森岛帆高撒旦法感受到分公司的防护服的就房规划局法国红酒改好规划局法国红酒法国红酒风格好就规范化就我突然有人他以为儿童一万二玩儿台湾儿童沃尔特感受到分公司非的故事的分公司答复干啥的风格森岛帆高山东分公司的分公司的分公司的发个导入台湾儿童玩儿他玩儿台湾儿童问题问题问题',
        1);

-- ----------------------------
-- Table structure for hospital_modular
-- ----------------------------
DROP TABLE IF EXISTS `hospital_modular`;
CREATE TABLE `hospital_modular`
(
    `modular_id`   int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '模块id',
    `modular_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名称',
    `tags`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
    `image`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '图片',
    `is_enable`    int(1)                                                        NULL DEFAULT NULL COMMENT '是否启用(0否 1是)',
    `type`         int(11)                                                       NULL DEFAULT NULL COMMENT '1banner位置 2列表',
    PRIMARY KEY (`modular_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '模块表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hospital_modular
-- ----------------------------
INSERT INTO `hospital_modular`
VALUES (1, '就医陪诊', '/my/peizhen/peizhen',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/57732961abcbb92c90622f6bb4ee12b9.png', 1, 1);
INSERT INTO `hospital_modular`
VALUES (2, '院内陪护', '/my/ynpeihu/ynpeihu',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2022/12/02/4528dc062753cf1ecfc1f8a4e07397cd.png', 1, 1);
INSERT INTO `hospital_modular`
VALUES (3, '优享陪诊', '医院专属,家人般温暖',
        'https://xichewap.xianmxkj.com/file/uploadPath/2022/11/11/e1bb1c57291e19780994e8ca82398461.png', 1, 2);

-- ----------------------------
-- Table structure for invite
-- ----------------------------
DROP TABLE IF EXISTS `invite`;
CREATE TABLE `invite`
(
    `id`              int(11)                                                NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id`         int(11)                                                NULL DEFAULT NULL COMMENT '邀请者id',
    `invitee_user_id` int(11)                                                NULL DEFAULT NULL COMMENT '被邀请者id',
    `money`           decimal(10, 2)                                         NULL DEFAULT NULL COMMENT '收益',
    `create_time`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
    `money_type`      int(1)                                                 NULL DEFAULT NULL COMMENT '1会员2陪玩',
    `state`           int(11)                                                NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1245
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '邀请信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of invite
-- ----------------------------

-- ----------------------------
-- Table structure for invite_money
-- ----------------------------
DROP TABLE IF EXISTS `invite_money`;
CREATE TABLE `invite_money`
(
    `id`        int(11)        NOT NULL AUTO_INCREMENT COMMENT '收益钱包id',
    `user_id`   int(11)        NULL DEFAULT NULL COMMENT '用户id',
    `money_sum` decimal(10, 2) NULL DEFAULT NULL COMMENT '总获取收益',
    `money`     decimal(10, 2) NULL DEFAULT NULL COMMENT '当前金额',
    `cash_out`  decimal(10, 2) NULL DEFAULT NULL COMMENT '累计提现',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 135
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of invite_money
-- ----------------------------

-- ----------------------------
-- Table structure for member
-- ----------------------------
DROP TABLE IF EXISTS `member`;
CREATE TABLE `member`
(
    `member_id`   int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '会员特权id',
    `member_img`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '特权图标',
    `member_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '特权名称',
    `sort`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`member_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of member
-- ----------------------------

-- ----------------------------
-- Table structure for message_info
-- ----------------------------
DROP TABLE IF EXISTS `message_info`;
CREATE TABLE `message_info`
(
    `id`            bigint(20)                                                     NOT NULL AUTO_INCREMENT COMMENT '消息id',
    `content`       varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
    `create_at`     varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `image`         varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '图片',
    `is_see`        varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    `send_state`    varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    `send_time`     varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL DEFAULT NULL,
    `state`         varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '分类',
    `title`         varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '标题',
    `url`           varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '地址',
    `type`          varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    `platform`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    `user_id`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '用户id',
    `user_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '用户名',
    `audit_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    `status`        int(11)                                                        NULL DEFAULT NULL,
    `by_user_id`    int(11)                                                        NULL DEFAULT NULL,
    `platform_id`   int(11)                                                        NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7583
  CHARACTER SET = big5
  COLLATE = big5_chinese_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of message_info
-- ----------------------------

-- ----------------------------
-- Table structure for ai_model_config
-- ----------------------------
DROP TABLE IF EXISTS `ai_model_config`;
CREATE TABLE `ai_model_config`
(
    `id`                bigint(20)                                              NOT NULL AUTO_INCREMENT COMMENT '模型配置ID',
    `model_name`        varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模型名称',
    `model_code`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '模型代码',
    `api_url`           varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'API地址',
    `api_key`           varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'API密钥',
    `model_type`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '模型类型(deepseek,kimi)',
    `max_tokens`        int(11)                                                 NULL DEFAULT 4000 COMMENT '最大token数',
    `temperature`       decimal(3, 2)                                           NULL DEFAULT 0.70 COMMENT '温度参数',
    `top_p`             decimal(3, 2)                                           NULL DEFAULT 1.00 COMMENT 'top_p参数',
    `frequency_penalty` decimal(3, 2)                                           NULL DEFAULT 0.00 COMMENT '频率惩罚',
    `presence_penalty`  decimal(3, 2)                                           NULL DEFAULT 0.00 COMMENT '存在惩罚',
    `is_enabled`        tinyint(1)                                              NULL DEFAULT 1 COMMENT '是否启用(0否1是)',
    `sort_order`        int(11)                                                 NULL DEFAULT 0 COMMENT '排序',
    `create_time`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`            varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_model_code` (`model_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  COMMENT = 'AI模型配置表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_model_config
-- ----------------------------
INSERT INTO `ai_model_config` VALUES (1, 'DeepSeek Chat', 'deepseek-chat', 'https://api.deepseek.com/v1/chat/completions', '', 'deepseek', 4000, 0.70, 1.00, 0.00, 0.00, 1, 1, NOW(), NOW(), 'DeepSeek官方模型');
INSERT INTO `ai_model_config` VALUES (2, 'Kimi Chat', 'kimi-chat', 'https://api.moonshot.cn/v1/chat/completions', '', 'kimi', 4000, 0.70, 1.00, 0.00, 0.00, 1, 2, NOW(), NOW(), 'Kimi官方模型');

-- ----------------------------
-- Table structure for ai_conversation
-- ----------------------------
DROP TABLE IF EXISTS `ai_conversation`;
CREATE TABLE `ai_conversation`
(
    `id`              bigint(20)                                              NOT NULL AUTO_INCREMENT COMMENT '对话会话ID',
    `user_id`         bigint(20)                                              NOT NULL COMMENT '用户ID',
    `title`           varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对话标题',
    `model_id`        bigint(20)                                              NOT NULL COMMENT '使用的模型ID',
    `model_code`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '模型代码',
    `message_count`   int(11)                                                 NULL DEFAULT 0 COMMENT '消息数量',
    `total_tokens`    int(11)                                                 NULL DEFAULT 0 COMMENT '总token消耗',
    `status`          tinyint(1)                                              NULL DEFAULT 1 COMMENT '状态(0删除1正常)',
    `create_time`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_user_id` (`user_id`) USING BTREE,
    INDEX `idx_model_id` (`model_id`) USING BTREE,
    INDEX `idx_create_time` (`create_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  COMMENT = 'AI对话会话表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_message
-- ----------------------------
DROP TABLE IF EXISTS `ai_message`;
CREATE TABLE `ai_message`
(
    `id`              bigint(20)                                               NOT NULL AUTO_INCREMENT COMMENT '消息ID',
    `conversation_id` bigint(20)                                               NOT NULL COMMENT '对话会话ID',
    `user_id`         bigint(20)                                               NOT NULL COMMENT '用户ID',
    `role`            varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL COMMENT '角色(user,assistant,system)',
    `content`         text CHARACTER SET utf8 COLLATE utf8_general_ci          NOT NULL COMMENT '消息内容',
    `model_code`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '模型代码',
    `prompt_tokens`   int(11)                                                  NULL DEFAULT 0 COMMENT '输入token数',
    `completion_tokens` int(11)                                                NULL DEFAULT 0 COMMENT '输出token数',
    `total_tokens`    int(11)                                                  NULL DEFAULT 0 COMMENT '总token数',
    `response_time`   int(11)                                                  NULL DEFAULT 0 COMMENT '响应时间(毫秒)',
    `error_message`   varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '错误信息',
    `create_time`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_conversation_id` (`conversation_id`) USING BTREE,
    INDEX `idx_user_id` (`user_id`) USING BTREE,
    INDEX `idx_create_time` (`create_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  COMMENT = 'AI对话消息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for msg
-- ----------------------------
DROP TABLE IF EXISTS `msg`;
CREATE TABLE `msg`
(
    `id`    bigint(20)                                              NOT NULL AUTO_INCREMENT COMMENT 'id',
    `code`  varchar(255) CHARACTER SET big5 COLLATE big5_chinese_ci NULL DEFAULT NULL COMMENT '短信验证码',
    `phone` varchar(255) CHARACTER SET big5 COLLATE big5_chinese_ci NULL DEFAULT NULL COMMENT '电话',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `index_name` (`code`, `phone`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3359
  CHARACTER SET = big5
  COLLATE = big5_chinese_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of msg
-- ----------------------------

-- ----------------------------
-- Table structure for order_taking
-- ----------------------------
DROP TABLE IF EXISTS `order_taking`;
CREATE TABLE `order_taking`
(
    `id`                bigint(20)                                                     NOT NULL AUTO_INCREMENT COMMENT '接单id',
    `game_id`           varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '游戏id类型',
    `my_level`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '我的段位',
    `order_level`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '接单段位',
    `order_taking_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '接单时间',
    `order_taking_area` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '接单大区',
    `money`             decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '价格',
    `member_money`      decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '会员价格',
    `old_money`         decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '原价 发布价格',
    `unit_type`         varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '接单类型单位',
    `voice_introduce`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '语音介绍',
    `homepage_img`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '主页图片',
    `create_time`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '创建时间',
    `status`            varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci    NULL DEFAULT NULL COMMENT '接单状态0进行中1待审核2已下架3拒绝',
    `update_time`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '修改时间',
    `is_recommend`      int(2)                                                         NULL DEFAULT NULL COMMENT '是否是推荐接单0是1不是',
    `user_id`           bigint(20)                                                     NULL DEFAULT NULL COMMENT '发布人',
    `city`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '城市',
    `count`             int(64)                                                        NULL DEFAULT NULL COMMENT '服务人数',
    `order_score`       double(10, 2)                                                  NULL DEFAULT NULL COMMENT '评分',
    `longitude`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '精度',
    `latitude`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '维度',
    `isdelete`          int(2)                                                         NULL DEFAULT NULL COMMENT '假删除0显示1删除',
    `content`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核理由',
    `sec`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '时长',
    `classify`          int(11)                                                        NULL DEFAULT NULL COMMENT '1 线上 2线下',
    `unit`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '单位',
    `details_img`       varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情图',
    `sales_num`         int(11)                                                        NULL DEFAULT NULL COMMENT '销量',
    `authentication`    int(11)                                                        NULL DEFAULT NULL COMMENT '审核',
    `min_num`           int(11)                                                        NULL DEFAULT NULL COMMENT '最低数量',
    `region`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '地区',
    `detailadd`         varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情地址',
    `service_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '服务项目',
    `car_type`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '适用车型',
    `safeguard`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '保障',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of order_taking
-- ----------------------------

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders`
(
    `orders_id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单id',
    `orders_no`            varchar(32)    DEFAULT NULL COMMENT '订单编号',
    `user_id`              bigint(20)     DEFAULT NULL COMMENT '用户id',
    `order_taking_id`      bigint(20)     DEFAULT NULL COMMENT '接单id',
    `pay_money`            decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `order_money`          decimal(10, 2) DEFAULT NULL COMMENT '订单金额(不计算任何优惠或减免)',
    `state`                int(4)         DEFAULT NULL COMMENT '订单状态0待支付1进行中2已完成3已退款 4待接单 5待服务',
    `create_time`          varchar(64)    DEFAULT NULL COMMENT '创建时间',
    `refund_content`       varchar(255)   DEFAULT NULL COMMENT '退款原因',
    `orders_type`          int(2)         DEFAULT '1' COMMENT '订单类型 1陪诊 2陪护',
    `remarks`              varchar(255)   DEFAULT NULL COMMENT '备注',
    `order_number`         int(10)        DEFAULT NULL COMMENT '服务天数',
    `update_time`          varchar(64)    DEFAULT NULL COMMENT '更新时间',
    `order_score`          double(10, 2)  DEFAULT NULL COMMENT '评分',
    `vip_details_id`       bigint(255)    DEFAULT NULL COMMENT '会员类型id',
    `type`                 int(2)         DEFAULT NULL COMMENT '1会员2非会员',
    `isdelete`             int(2)         DEFAULT NULL COMMENT '假删除0显示1删除',
    `province`             varchar(255)   DEFAULT NULL COMMENT '省',
    `city`                 varchar(255)   DEFAULT NULL COMMENT '市',
    `district`             varchar(255)   DEFAULT NULL COMMENT '区',
    `details_address`      varchar(2000)  DEFAULT NULL COMMENT '详细地址',
    `name`                 varchar(255)   DEFAULT NULL COMMENT '姓名',
    `phone`                varchar(255)   DEFAULT NULL COMMENT '电话',
    `start_time`           varchar(500)   DEFAULT NULL COMMENT '上门时间',
    `latitude`             varchar(255)   DEFAULT NULL COMMENT '纬度',
    `longitude`            varchar(255)   DEFAULT NULL COMMENT '经度',
    `is_remind`            int(11)        DEFAULT NULL COMMENT '是否推荐',
    `rate`                 decimal(10, 2) DEFAULT NULL COMMENT '佣金',
    `zhi_rate`             decimal(10, 2) DEFAULT NULL COMMENT '直属佣金',
    `zhi_user_id`          int(11)        DEFAULT NULL COMMENT '直属用户id',
    `fei_rate`             decimal(10, 2) DEFAULT NULL COMMENT '非直属佣金',
    `fei_user_id`          int(11)        DEFAULT NULL COMMENT '非直属用户id',
    `ping_rate`            decimal(10, 2) DEFAULT NULL COMMENT '平台佣金',
    `order_taking_user_id` int(11)        DEFAULT NULL COMMENT '接单用户id',
    `code`                 varchar(255)   DEFAULT NULL COMMENT '验收码',
    `pay_way`              int(11)        DEFAULT NULL COMMENT '支付方式 1零钱 2微信 3支付宝',
    `car_no`               varchar(64)    DEFAULT NULL,
    `car_type`             varchar(255)   DEFAULT NULL,
    `car_color`            varchar(255)   DEFAULT NULL,
    `car_name`             varchar(255)   DEFAULT NULL,
    `car_phone`            varchar(255)   DEFAULT NULL,
    `start_img`            varchar(2000)  DEFAULT NULL,
    `end_img`              varchar(2000)  DEFAULT NULL,
    `is_transfer`          int(11)        DEFAULT NULL,
    `end_time`             varchar(64)    DEFAULT NULL,
    `department_id`        int(11)        DEFAULT NULL COMMENT '科室id',
    `service_id`           int(11)        DEFAULT NULL COMMENT '服务id',
    `hospital_id`          int(11)        DEFAULT NULL COMMENT '医院id',
    `coupon_id`            int(11)        DEFAULT NULL COMMENT '优惠券id',
    `is_pay`               int(1)         DEFAULT '0' COMMENT '是否已支付 0未支付 1已支付',
    `rate_proportion`      decimal(10, 2) DEFAULT NULL COMMENT '师傅分佣比例',
    `sf_zhi_user_id`       int(11)        DEFAULT NULL COMMENT '师傅上级id',
    `sf_zhi_rate`          decimal(10, 2) DEFAULT NULL COMMENT '师傅直属佣金',
    PRIMARY KEY (`orders_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 537
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Records of orders
-- ----------------------------

-- ----------------------------
-- Table structure for patient_info
-- ----------------------------
DROP TABLE IF EXISTS `patient_info`;
CREATE TABLE `patient_info`
(
    `patient_id`      int(11) NOT NULL AUTO_INCREMENT COMMENT '信息id',
    `user_id`         int(11)      DEFAULT NULL COMMENT '用户id',
    `is_under_age`    int(1)       DEFAULT NULL COMMENT '是否已满18岁(1是 0否)',
    `sex`             int(1)       DEFAULT NULL COMMENT '性别 1男 2女',
    `real_name`       varchar(100) DEFAULT NULL COMMENT '姓名',
    `phone`           varchar(20)  DEFAULT NULL COMMENT '电话号码',
    `id_number`       varchar(50)  DEFAULT NULL COMMENT '身份证号码',
    `relationship`    varchar(255) DEFAULT NULL COMMENT '就诊人关系',
    `is_delete`       int(1)       DEFAULT '0' COMMENT '0未删除 1删除',
    `emergency_phone` varchar(255) DEFAULT NULL COMMENT '紧急联系人',
    PRIMARY KEY (`patient_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 79
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Records of patient_info
-- ----------------------------

-- ----------------------------
-- Table structure for pay_classify
-- ----------------------------
DROP TABLE IF EXISTS `pay_classify`;
CREATE TABLE `pay_classify`
(
    `pay_classify_id` int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT '充值分类id',
    `price`           decimal(10, 2)                                               NULL DEFAULT NULL COMMENT '售价',
    `coupon_id`       bigint(10)                                                   NULL DEFAULT NULL COMMENT '优惠券id',
    `give_num`        bigint(10)                                                   NULL DEFAULT NULL COMMENT '数量',
    `sort`            int(11)                                                      NULL DEFAULT NULL COMMENT '排序',
    `create_time`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时间',
    PRIMARY KEY (`pay_classify_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 20
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pay_classify
-- ----------------------------
INSERT INTO `pay_classify`
VALUES (2, 0.02, 1, 1, 2, '2022-11-25 18:54:10');
INSERT INTO `pay_classify`
VALUES (3, 0.03, NULL, NULL, 3, '2022-11-25 18:54:10');
INSERT INTO `pay_classify`
VALUES (4, 1000.00, NULL, NULL, 4, '2022-11-25 18:54:10');
INSERT INTO `pay_classify`
VALUES (5, 3000.00, 6, 4, 5, '2022-11-25 18:54:10');
INSERT INTO `pay_classify`
VALUES (18, 100.00, 6, 6, 7, '2022-11-25 18:55:00');
INSERT INTO `pay_classify`
VALUES (19, 0.01, NULL, NULL, NULL, '2022-12-14 19:02:10');

-- ----------------------------
-- Table structure for pay_details
-- ----------------------------
DROP TABLE IF EXISTS `pay_details`;
CREATE TABLE `pay_details`
(
    `id`          bigint(20)                                                   NOT NULL AUTO_INCREMENT COMMENT '充值id',
    `classify`    varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '分类（ 1app微信 2微信公众号 3微信小程序 4支付宝）',
    `order_id`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
    `trade_no`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付宝支付单号',
    `money`       decimal(10, 2)                                               NULL DEFAULT NULL COMMENT '充值金额',
    `user_id`     bigint(20)                                                   NULL DEFAULT NULL COMMENT '用户id',
    `state`       int(4)                                                       NULL DEFAULT NULL COMMENT '0待支付 1支付成功 2失败',
    `create_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建时间',
    `pay_time`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付时间',
    `type`        int(4)                                                       NULL DEFAULT NULL COMMENT '支付类型 1.充值 2订单支付 3万能任务 4缴纳保证金',
    `remark`      longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci    NULL,
    `parent_id`   int(11)                                                      NULL DEFAULT NULL COMMENT '关联id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 306
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pay_details
-- ----------------------------

-- ----------------------------
-- Table structure for pay_order
-- ----------------------------
DROP TABLE IF EXISTS `pay_order`;
CREATE TABLE `pay_order`
(
    `id`             bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT 'id',
    `orders_no`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '订单编号',
    `trade_no`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付宝支付单号',
    `money`          decimal(11, 2)                                                NULL DEFAULT NULL COMMENT '金币金额',
    `pay_money`      decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '支付金额',
    `pay_way`        int(11)                                                       NULL DEFAULT NULL COMMENT '支付方式 1微信小程序  2微信公众号 3微信App 4支付宝',
    `state`          int(11)                                                       NULL DEFAULT NULL COMMENT '状态 0待支付  1已支付  2已退款',
    `create_time`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `refund_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款原因',
    `update_time`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '更新时间',
    `user_id`        bigint(20)                                                    NULL DEFAULT NULL COMMENT '用户id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 181
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pay_order
-- ----------------------------

-- ----------------------------
-- Table structure for reward_level
-- ----------------------------
DROP TABLE IF EXISTS `reward_level`;
CREATE TABLE `reward_level`
(
    `reward_id`   int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '奖励id',
    `level_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '等级名称',
    `icon_img`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '等级图标',
    `scale`       decimal(3, 2)                                                 NULL DEFAULT NULL COMMENT '分佣比例',
    `order_count` int(11)                                                       NULL DEFAULT NULL COMMENT '需完成订单数',
    `is_enable`   int(1)                                                        NULL DEFAULT NULL COMMENT '是否开启 1是 0否',
    `create_time` datetime(0)                                                   NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`reward_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '分佣比例奖励表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of reward_level
-- ----------------------------
INSERT INTO `reward_level`
VALUES (2, '铜牌', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/18/99b66980483a9bbc7cafe8a1f7840aa6.png',
        0.70, 1, 1, '2023-05-17 16:03:07');
INSERT INTO `reward_level`
VALUES (3, '银牌', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/18/15bd99e19c772c11c75f9e36f0ad5f66.png',
        0.80, 2, 1, '2023-05-17 15:55:23');
INSERT INTO `reward_level`
VALUES (4, '金牌', 'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/18/0f75768214385c99c58a95de14630537.png',
        0.90, 3, 1, '2023-05-17 15:55:39');

-- ----------------------------
-- Table structure for rider_location
-- ----------------------------
DROP TABLE IF EXISTS `rider_location`;
CREATE TABLE `rider_location`
(
    `id`              int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `user_id`         int(11)                                                       NULL DEFAULT NULL COMMENT '骑手id',
    `lng`             double(20, 10)                                                NULL DEFAULT NULL COMMENT '骑手当前经度',
    `lat`             double(20, 10)                                                NULL DEFAULT NULL COMMENT '骑手当前纬度',
    `province`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '骑手当前所在省',
    `city`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '骑手当前所在市',
    `district`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '骑手当前所在区',
    `address_details` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '骑手当前详细地址',
    `update_time`     datetime(0)                                                   NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2013
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of rider_location
-- ----------------------------

-- ----------------------------
-- Table structure for search
-- ----------------------------
DROP TABLE IF EXISTS `search`;
CREATE TABLE `search`
(
    `search_id`   bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '搜索id',
    `search_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '搜索名称',
    `user_id`     bigint(20)                                                    NULL DEFAULT NULL COMMENT '用户id',
    `update_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`search_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 135
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of search
-- ----------------------------

-- ----------------------------
-- Table structure for service_time
-- ----------------------------
DROP TABLE IF EXISTS `service_time`;
CREATE TABLE `service_time`
(
    `service_time_id` int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT '接单时间id',
    `service_time`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接单时间',
    `company_id`      int(11)                                                      NULL DEFAULT NULL COMMENT '公司id',
    `sort`            int(1)                                                       NULL DEFAULT NULL COMMENT '排序',
    `create_time`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时间',
    `num`             int(11)                                                      NULL DEFAULT NULL COMMENT '数量',
    PRIMARY KEY (`service_time_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 51
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of service_time
-- ----------------------------

-- ----------------------------
-- Table structure for sys_captcha
-- ----------------------------
DROP TABLE IF EXISTS `sys_captcha`;
CREATE TABLE `sys_captcha`
(
    `uuid`        char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT 'uuid',
    `code`        varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '验证码',
    `expire_time` datetime(0)                                                 NULL DEFAULT NULL COMMENT '过期时间',
    PRIMARY KEY (`uuid`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '系统验证码'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_captcha
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`
(
    `id`          bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `param_key`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT 'key',
    `param_value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'value',
    `status`      tinyint(4)                                                     NULL DEFAULT 1 COMMENT '状态   0：隐藏   1：显示',
    `remark`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `param_key` (`param_key`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '系统配置信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config`
VALUES (1, 'CLOUD_STORAGE_CONFIG_KEY',
        '{\"aliyunAccessKeyId\":\"\",\"aliyunAccessKeySecret\":\"\",\"aliyunBucketName\":\"\",\"aliyunDomain\":\"\",\"aliyunEndPoint\":\"\",\"aliyunPrefix\":\"\",\"qcloudBucketName\":\"\",\"qcloudDomain\":\"\",\"qcloudPrefix\":\"\",\"qcloudSecretId\":\"\",\"qcloudSecretKey\":\"\",\"qiniuAccessKey\":\"NrgMfABZxWLo5B-YYSjoE8-AZ1EISdi1Z3ubLOeZ\",\"qiniuBucketName\":\"ios-app\",\"qiniuDomain\":\"http://7xqbwh.dl1.z0.glb.clouddn.com\",\"qiniuPrefix\":\"upload\",\"qiniuSecretKey\":\"uIwJHevMRWU0VLxFvgy0tAcOdGqasdtVlJkdy6vV\",\"type\":1}',
        0, '云存储配置信息');

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`
(
    `id`        bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '字典名称',
    `type`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '字典类型',
    `code`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '字典码',
    `value`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典值',
    `order_num` int(11)                                                        NULL DEFAULT 0 COMMENT '排序',
    `remark`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '备注',
    `parent_id` int(11)                                                        NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 197
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '数据字典表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict`
VALUES (138, '就诊人关系', '就诊人关系', NULL, NULL, 8, '就诊人关系', 0);
INSERT INTO `sys_dict`
VALUES (139, '', NULL, '父母', '父母', 0, NULL, 138);
INSERT INTO `sys_dict`
VALUES (140, NULL, NULL, '子女', '子女', 0, NULL, 138);
INSERT INTO `sys_dict`
VALUES (141, NULL, NULL, '兄弟姐妹', '兄弟姐妹', 0, NULL, 138);
INSERT INTO `sys_dict`
VALUES (142, NULL, NULL, '本人', '本人', 0, NULL, 138);
INSERT INTO `sys_dict`
VALUES (143, NULL, NULL, '夫妻', '夫妻', 0, NULL, 138);
INSERT INTO `sys_dict`
VALUES (144, NULL, NULL, '其他', '其他', 0, NULL, 138);
INSERT INTO `sys_dict`
VALUES (145, '医院类型', '医院类型', NULL, NULL, 10, '医院类型', 0);
INSERT INTO `sys_dict`
VALUES (146, '医院等级', '医院等级', NULL, NULL, 9, '医院等级', 0);
INSERT INTO `sys_dict`
VALUES (147, NULL, NULL, '一甲医院', '一甲医院', 1, NULL, 146);
INSERT INTO `sys_dict`
VALUES (148, NULL, NULL, '二甲医院', '二甲医院', 2, NULL, 146);
INSERT INTO `sys_dict`
VALUES (149, NULL, NULL, '三甲医院', '三甲医院', 3, NULL, 146);
INSERT INTO `sys_dict`
VALUES (150, NULL, NULL, '综合医院', '综合医院', 0, NULL, 145);
INSERT INTO `sys_dict`
VALUES (151, NULL, NULL, '专科医院', '专科医院', 0, NULL, 145);
INSERT INTO `sys_dict`
VALUES (152, '护龄', '护龄', NULL, NULL, 2, '护龄', 0);
INSERT INTO `sys_dict`
VALUES (153, NULL, NULL, '不限', '不限', 0, '不限', 152);
INSERT INTO `sys_dict`
VALUES (154, NULL, NULL, '2-5年', '2-5年', 0, '2-5年', 152);
INSERT INTO `sys_dict`
VALUES (155, NULL, NULL, '5-10年', '5-10年', 0, '5-10年', 152);
INSERT INTO `sys_dict`
VALUES (156, NULL, NULL, '10年以上', '10年以上', 0, '10年以上', 152);
INSERT INTO `sys_dict`
VALUES (157, '年龄', '年龄', NULL, NULL, 3, '年龄', 0);
INSERT INTO `sys_dict`
VALUES (158, NULL, NULL, '不限', '不限', 0, '不限', 157);
INSERT INTO `sys_dict`
VALUES (159, NULL, NULL, '20-29岁', '20-29岁', 0, '20-29岁', 157);
INSERT INTO `sys_dict`
VALUES (160, NULL, NULL, '30-39岁', '30-39岁', 0, '30-39岁', 157);
INSERT INTO `sys_dict`
VALUES (161, NULL, NULL, '40-49岁', '40-49岁', 0, '40-49岁', 157);
INSERT INTO `sys_dict`
VALUES (162, NULL, NULL, '50-59岁', '50-59岁', 0, '50-59岁', 157);
INSERT INTO `sys_dict`
VALUES (163, '自理能力', '自理能力', NULL, NULL, 4, '自理能力', 0);
INSERT INTO `sys_dict`
VALUES (164, NULL, NULL, '不能自理', '不能自理', 0, '不能自理', 163);
INSERT INTO `sys_dict`
VALUES (165, NULL, NULL, '半自理', '半自理', 0, '半自理', 163);
INSERT INTO `sys_dict`
VALUES (166, NULL, NULL, '自理', '自理', 0, '自理', 163);
INSERT INTO `sys_dict`
VALUES (167, '病症', '病症', NULL, NULL, 5, '病症', 0);
INSERT INTO `sys_dict`
VALUES (168, NULL, NULL, '糖尿病', '糖尿病', 0, '糖尿病', 167);
INSERT INTO `sys_dict`
VALUES (169, NULL, NULL, '慢阻肺', '慢阻肺', 0, '慢阻肺', 167);
INSERT INTO `sys_dict`
VALUES (170, NULL, NULL, '冠心病', '冠心病', 0, '冠心病', 167);
INSERT INTO `sys_dict`
VALUES (171, NULL, NULL, '高血压', '高血压', 0, '高血压', 167);
INSERT INTO `sys_dict`
VALUES (172, NULL, NULL, '高血脂', '高血脂', 0, '高血脂', 167);
INSERT INTO `sys_dict`
VALUES (173, NULL, NULL, '癌症', '癌症', 0, '癌症', 167);
INSERT INTO `sys_dict`
VALUES (174, NULL, NULL, '帕金森', '帕金森', 0, '帕金森', 167);
INSERT INTO `sys_dict`
VALUES (175, NULL, NULL, '记忆力紊乱', '记忆力紊乱', 0, '记忆力紊乱', 167);
INSERT INTO `sys_dict`
VALUES (176, NULL, NULL, '其他', '其他', 0, '其他', 167);
INSERT INTO `sys_dict`
VALUES (177, '护理需求', '护理需求', NULL, NULL, 6, '护理需求', 0);
INSERT INTO `sys_dict`
VALUES (178, NULL, NULL, '胃管', '胃管', 0, '胃管', 177);
INSERT INTO `sys_dict`
VALUES (179, NULL, NULL, '引流管', '引流管', 0, '引流管', 177);
INSERT INTO `sys_dict`
VALUES (180, NULL, NULL, '尿管', '尿管', 0, '尿管', 177);
INSERT INTO `sys_dict`
VALUES (181, NULL, NULL, '吸痰', '吸痰', 0, '吸痰', 177);
INSERT INTO `sys_dict`
VALUES (182, NULL, NULL, '人工肛门', '人工肛门', 0, '人工肛门', 177);
INSERT INTO `sys_dict`
VALUES (183, NULL, NULL, '骨折术后', '骨折术后', 0, '骨折术后', 177);
INSERT INTO `sys_dict`
VALUES (184, NULL, NULL, '其它', '其它', 0, '其它', 177);
INSERT INTO `sys_dict`
VALUES (185, '报告类型', '报告类型', NULL, NULL, 7, '报告类型', 0);
INSERT INTO `sys_dict`
VALUES (186, NULL, NULL, 'CT结果', 'CT结果', 1, NULL, 185);
INSERT INTO `sys_dict`
VALUES (187, NULL, NULL, '检测报告', '检测报告', 2, NULL, 185);
INSERT INTO `sys_dict`
VALUES (188, NULL, NULL, '验血报告', '验血报告', 3, NULL, 185);
INSERT INTO `sys_dict`
VALUES (189, NULL, NULL, '其他', '其他', 4, NULL, 185);
INSERT INTO `sys_dict`
VALUES (190, '专享归属', '专享归属', NULL, NULL, 11, '专享归属', 0);
INSERT INTO `sys_dict`
VALUES (191, NULL, NULL, '老人专享', '老人专享', 1, NULL, 190);
INSERT INTO `sys_dict`
VALUES (192, NULL, NULL, '儿童专享', '儿童专享', 2, NULL, 190);
INSERT INTO `sys_dict`
VALUES (193, NULL, NULL, '孕妇专享', '孕妇专享', 3, NULL, 190);
INSERT INTO `sys_dict`
VALUES (194, '药物类型', '药物类型', NULL, NULL, 12, '药物类型', 0);
INSERT INTO `sys_dict`
VALUES (195, NULL, NULL, '处方药', '处方药', 1, NULL, 194);
INSERT INTO `sys_dict`
VALUES (196, NULL, NULL, '非处方药', '非处方药', 2, NULL, 194);

-- ----------------------------
-- Table structure for sys_evaluate
-- ----------------------------
DROP TABLE IF EXISTS `sys_evaluate`;
CREATE TABLE `sys_evaluate`
(
    `evaluate_id`       int(11)                                                   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `user_id`           int(11)                                                   NULL DEFAULT NULL COMMENT '用户id(评分人)',
    `rider_user_id`     int(11)                                                   NULL DEFAULT NULL COMMENT '接单人id(被评分人)',
    `indent_number`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci   NULL DEFAULT NULL COMMENT '订单号',
    `evaluate_message`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评价内容',
    `satisfaction_flag` int(1)                                                    NULL DEFAULT NULL COMMENT '评级 1:非常差 2:差 3:一般 4:满意 5:非常满意',
    `create_time`       datetime(0)                                               NULL DEFAULT NULL COMMENT '评价时间',
    `evaluate_img`      text CHARACTER SET utf8 COLLATE utf8_general_ci           NULL COMMENT '评价图片',
    `is_delete`         int(1)                                                    NULL DEFAULT 0 COMMENT '是否已删除0否 -1是',
    PRIMARY KEY (`evaluate_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3221
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_evaluate
-- ----------------------------
INSERT INTO `sys_evaluate`
VALUES (3215, 116052, 115612, '1000000575984015', '测试', 3, '2023-05-17 20:38:38',
        'https://peizhen.xianmaxiong.com/file/upload/2023/05/17/53c44c41cfa3178e31817a8df4552354.png', -1);
INSERT INTO `sys_evaluate`
VALUES (3216, 116052, 115612, '1000001902200862', '测试测试测试', 3, '2023-05-18 17:16:27',
        'https://peizhen.xianmaxiong.com/file/upload/2023/05/18/ff169ce3081694fe0df0f4741533044c.png', -1);
INSERT INTO `sys_evaluate`
VALUES (3217, 116054, 116057, '1000001457912080', '把睡吧睡吧很好很好', 4, '2023-05-22 19:05:08', NULL, 0);
INSERT INTO `sys_evaluate`
VALUES (3218, 116050, 116052, '1000001958860567', '哈哈哈', 5, '2023-05-23 14:43:53', NULL, 0);
INSERT INTO `sys_evaluate`
VALUES (3219, 116054, 116059, '1000001923438091', '这个医生真的很好，4分吧', 5, '2023-05-23 15:35:22', ',', -1);
INSERT INTO `sys_evaluate`
VALUES (3220, 116054, 116059, '1000001299813366', '四分四分', 4, '2023-05-23 15:45:49', NULL, -1);

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`
(
    `id`          bigint(20)                                                     NOT NULL AUTO_INCREMENT,
    `username`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '用户名',
    `operation`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '用户操作',
    `method`      varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '请求方法',
    `params`      varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求参数',
    `time`        bigint(20)                                                     NOT NULL COMMENT '执行时长(毫秒)',
    `ip`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT 'IP地址',
    `create_date` datetime(0)                                                    NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 377
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '系统日志'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_log
-- ----------------------------
INSERT INTO `sys_log`
VALUES (1, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":32,\"parentId\":0,\"name\":\"用户中心\",\"url\":\"userList\",\"perms\":\"\",\"type\":1,\"icon\":\"geren\",\"orderNum\":0}]',
        67, '*************', '2020-09-27 14:33:26');
INSERT INTO `sys_log`
VALUES (2, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":1,\"parentId\":0,\"name\":\"系统管理\",\"type\":0,\"icon\":\"system\",\"orderNum\":20}]', 67,
        '*************', '2020-09-27 14:33:46');
INSERT INTO `sys_log`
VALUES (3, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":32,\"parentId\":0,\"name\":\"用户中心\",\"url\":\"userList\",\"perms\":\"\",\"type\":1,\"icon\":\"yonghul\",\"orderNum\":0}]',
        109, '*************', '2020-09-27 14:34:21');
INSERT INTO `sys_log`
VALUES (4, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":33,\"parentId\":0,\"name\":\"数据中心\",\"url\":\"home\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        56, '192.168.1.3', '2021-06-04 17:08:29');
INSERT INTO `sys_log`
VALUES (5, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":34,\"parentId\":0,\"name\":\"财务中心\",\"url\":\"financeList\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        52, '192.168.1.3', '2021-06-04 17:09:47');
INSERT INTO `sys_log`
VALUES (6, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":35,\"parentId\":34,\"name\":\"查看\",\"url\":\"\",\"perms\":\"financeList:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        82, '192.168.1.3', '2021-06-04 17:10:45');
INSERT INTO `sys_log`
VALUES (7, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":36,\"parentId\":34,\"name\":\"转账\",\"url\":\"\",\"perms\":\"financeList:transfer\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '192.168.1.3', '2021-06-04 17:11:08');
INSERT INTO `sys_log`
VALUES (8, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":37,\"parentId\":34,\"name\":\"退款\",\"url\":\"\",\"perms\":\"financeList:refund\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        84, '192.168.1.3', '2021-06-04 17:11:31');
INSERT INTO `sys_log`
VALUES (9, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":38,\"parentId\":34,\"name\":\"退款\",\"url\":\"\",\"perms\":\"financeList:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        94, '192.168.1.3', '2021-06-04 17:11:54');
INSERT INTO `sys_log`
VALUES (10, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":39,\"parentId\":34,\"name\":\"修改\",\"url\":\"\",\"perms\":\"financeList:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        115, '192.168.1.3', '2021-06-04 17:22:32');
INSERT INTO `sys_log`
VALUES (11, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":40,\"parentId\":34,\"name\":\"删除\",\"url\":\"\",\"perms\":\"financeList:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        81, '192.168.1.3', '2021-06-04 17:22:54');
INSERT INTO `sys_log`
VALUES (12, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":41,\"parentId\":0,\"name\":\"消息中心\",\"url\":\"message\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        53, '192.168.1.3', '2021-06-04 17:23:33');
INSERT INTO `sys_log`
VALUES (13, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":42,\"parentId\":41,\"name\":\"查看\",\"url\":\"\",\"perms\":\"message:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        75, '192.168.1.3', '2021-06-04 17:24:03');
INSERT INTO `sys_log`
VALUES (14, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":43,\"parentId\":41,\"name\":\"消息推送\",\"url\":\"\",\"perms\":\"message:push\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '192.168.1.3', '2021-06-04 17:24:30');
INSERT INTO `sys_log`
VALUES (15, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":44,\"parentId\":0,\"name\":\"商家中心\",\"url\":\"mission\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        53, '192.168.1.3', '2021-06-04 17:28:58');
INSERT INTO `sys_log`
VALUES (16, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":45,\"parentId\":44,\"name\":\"查看\",\"url\":\"\",\"perms\":\"mission:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        85, '192.168.1.3', '2021-06-04 17:29:27');
INSERT INTO `sys_log`
VALUES (17, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":46,\"parentId\":44,\"name\":\"添加\",\"url\":\"\",\"perms\":\"mission:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '192.168.1.3', '2021-06-04 17:29:46');
INSERT INTO `sys_log`
VALUES (18, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":47,\"parentId\":44,\"name\":\"修改\",\"url\":\"\",\"perms\":\"mission:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        75, '192.168.1.3', '2021-06-04 17:30:05');
INSERT INTO `sys_log`
VALUES (19, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":48,\"parentId\":44,\"name\":\"删除\",\"url\":\"\",\"perms\":\"mission:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '192.168.1.3', '2021-06-04 17:30:21');
INSERT INTO `sys_log`
VALUES (20, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":49,\"parentId\":44,\"name\":\"下架\",\"url\":\"\",\"perms\":\"mission:sold\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '192.168.1.3', '2021-06-04 17:30:40');
INSERT INTO `sys_log`
VALUES (21, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":50,\"parentId\":0,\"name\":\"首页装修\",\"url\":\"bannerList\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        53, '192.168.1.3', '2021-06-04 17:31:11');
INSERT INTO `sys_log`
VALUES (22, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":51,\"parentId\":50,\"name\":\"查看\",\"url\":\"\",\"perms\":\"bannerList:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '192.168.1.3', '2021-06-04 17:31:46');
INSERT INTO `sys_log`
VALUES (23, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":52,\"parentId\":50,\"name\":\"添加\",\"url\":\"\",\"perms\":\"bannerList:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        82, '192.168.1.3', '2021-06-04 17:32:02');
INSERT INTO `sys_log`
VALUES (24, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":53,\"parentId\":50,\"name\":\"修改\",\"url\":\"\",\"perms\":\"bannerList:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        82, '192.168.1.3', '2021-06-04 17:32:19');
INSERT INTO `sys_log`
VALUES (25, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":54,\"parentId\":50,\"name\":\"删除\",\"url\":\"\",\"perms\":\"bannerList:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        81, '192.168.1.3', '2021-06-04 17:32:33');
INSERT INTO `sys_log`
VALUES (26, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":55,\"parentId\":0,\"name\":\"商户合作\",\"url\":\"merchant\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        53, '192.168.1.3', '2021-06-04 17:33:15');
INSERT INTO `sys_log`
VALUES (27, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":56,\"parentId\":55,\"name\":\"查看\",\"url\":\"\",\"perms\":\"\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '192.168.1.3', '2021-06-04 17:34:59');
INSERT INTO `sys_log`
VALUES (28, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":57,\"parentId\":0,\"name\":\"系统配置\",\"url\":\"allocationList\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        82, '192.168.1.3', '2021-06-04 17:35:41');
INSERT INTO `sys_log`
VALUES (29, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":58,\"parentId\":57,\"name\":\"查看\",\"url\":\"\",\"perms\":\"allocationList:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '192.168.1.3', '2021-06-04 17:36:01');
INSERT INTO `sys_log`
VALUES (30, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":59,\"parentId\":57,\"name\":\"修改\",\"url\":\"\",\"perms\":\"allocationList:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        81, '192.168.1.3', '2021-06-04 17:36:16');
INSERT INTO `sys_log`
VALUES (31, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":60,\"parentId\":32,\"name\":\"查看\",\"url\":\"\",\"perms\":\"userList:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        79, '192.168.1.3', '2021-06-04 17:37:11');
INSERT INTO `sys_log`
VALUES (32, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":61,\"parentId\":32,\"name\":\"删除\",\"url\":\"\",\"perms\":\"userList:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        415, '192.168.1.3', '2021-06-04 17:37:27');
INSERT INTO `sys_log`
VALUES (33, 'admin', '保存角色', 'com.sqx.modules.sys.controller.SysRoleController.save()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":1,\"menuIdList\":[32,60,61,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,1,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666],\"createTime\":\"Jun 4, 2021 5:38:27 PM\"}]',
        3643, '192.168.1.3', '2021-06-04 17:38:31');
INSERT INTO `sys_log`
VALUES (34, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":44,\"parentId\":0,\"name\":\"课程中心\",\"url\":\"mission\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        58, '192.168.1.3', '2021-06-05 09:58:53');
INSERT INTO `sys_log`
VALUES (35, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":62,\"parentId\":0,\"name\":\"订单中心\",\"url\":\"orderCenter\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":0}]',
        68, '***********', '2021-06-16 17:01:54');
INSERT INTO `sys_log`
VALUES (36, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":63,\"parentId\":62,\"name\":\"查看\",\"url\":\"\",\"perms\":\"\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '***********', '2021-06-16 17:02:43');
INSERT INTO `sys_log`
VALUES (37, 'admin', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":64,\"parentId\":62,\"name\":\"删除\",\"url\":\"\",\"perms\":\"\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        79, '***********', '2021-06-16 17:03:04');
INSERT INTO `sys_log`
VALUES (38, 'admin', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":1,\"menuIdList\":[32,60,61,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,63,64,1,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        3518, '***********', '2021-06-16 17:03:35');
INSERT INTO `sys_log`
VALUES (39, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":33,\"parentId\":0,\"name\":\"数据中心\",\"url\":\"home\",\"perms\":\"\",\"type\":1,\"icon\":\"shuju\",\"orderNum\":0}]',
        62, '***********', '2021-06-23 14:59:00');
INSERT INTO `sys_log`
VALUES (40, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":34,\"parentId\":0,\"name\":\"财务中心\",\"url\":\"financeList\",\"perms\":\"\",\"type\":1,\"icon\":\"caiwu\",\"orderNum\":0}]',
        65, '***********', '2021-06-23 14:59:22');
INSERT INTO `sys_log`
VALUES (41, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":41,\"parentId\":0,\"name\":\"消息中心\",\"url\":\"message\",\"perms\":\"\",\"type\":1,\"icon\":\"xiaoxi\",\"orderNum\":0}]',
        53, '***********', '2021-06-23 14:59:37');
INSERT INTO `sys_log`
VALUES (42, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":44,\"parentId\":0,\"name\":\"课程中心\",\"url\":\"mission\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":0}]',
        54, '***********', '2021-06-23 15:00:19');
INSERT INTO `sys_log`
VALUES (43, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":50,\"parentId\":0,\"name\":\"首页装修\",\"url\":\"bannerList\",\"perms\":\"\",\"type\":1,\"icon\":\"shangpin\",\"orderNum\":0}]',
        55, '***********', '2021-06-23 15:00:43');
INSERT INTO `sys_log`
VALUES (44, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":55,\"parentId\":0,\"name\":\"商户合作\",\"url\":\"merchant\",\"perms\":\"\",\"type\":1,\"icon\":\"role\",\"orderNum\":0}]',
        57, '***********', '2021-06-23 15:00:58');
INSERT INTO `sys_log`
VALUES (45, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":57,\"parentId\":0,\"name\":\"系统配置\",\"url\":\"allocationList\",\"perms\":\"\",\"type\":1,\"icon\":\"menu\",\"orderNum\":0}]',
        53, '***********', '2021-06-23 15:01:21');
INSERT INTO `sys_log`
VALUES (46, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":62,\"parentId\":0,\"name\":\"订单中心\",\"url\":\"orderCenter\",\"perms\":\"\",\"type\":1,\"icon\":\"log\",\"orderNum\":0}]',
        60, '***********', '2021-06-23 15:01:44');
INSERT INTO `sys_log`
VALUES (47, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":32,\"parentId\":0,\"name\":\"用户中心\",\"url\":\"userList\",\"perms\":\"\",\"type\":1,\"icon\":\"yonghul\",\"orderNum\":9}]',
        58, '***********', '2021-06-30 10:33:41');
INSERT INTO `sys_log`
VALUES (48, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[29]', 1, '***********',
        '2021-06-30 10:34:17');
INSERT INTO `sys_log`
VALUES (49, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[30]', 0, '***********',
        '2021-06-30 10:34:38');
INSERT INTO `sys_log`
VALUES (50, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[31]', 0, '***********',
        '2021-06-30 10:34:54');
INSERT INTO `sys_log`
VALUES (51, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[27]', 0, '***********',
        '2021-06-30 10:35:01');
INSERT INTO `sys_log`
VALUES (52, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[5]', 0, '***********',
        '2021-06-30 10:35:11');
INSERT INTO `sys_log`
VALUES (53, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":5,\"parentId\":0,\"name\":\"SQL监控\",\"url\":\"http://localhost:8080/sqx_fast/druid/sql.html\",\"type\":1,\"icon\":\"sql\",\"orderNum\":4}]',
        59, '***********', '2021-06-30 10:35:30');
INSERT INTO `sys_log`
VALUES (54, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[5]', 0, '***********',
        '2021-06-30 10:35:44');
INSERT INTO `sys_log`
VALUES (55, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":5,\"parentId\":0,\"name\":\"SQL监控\",\"url\":\"11\",\"type\":1,\"icon\":\"sql\",\"orderNum\":4}]',
        50, '***********', '2021-06-30 10:36:25');
INSERT INTO `sys_log`
VALUES (56, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[5]', 1, '***********',
        '2021-06-30 10:36:34');
INSERT INTO `sys_log`
VALUES (57, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":5,\"parentId\":0,\"name\":\"SQL监控\",\"url\":\"11\",\"type\":0,\"icon\":\"sql\",\"orderNum\":4}]',
        55, '***********', '2021-06-30 10:36:58');
INSERT INTO `sys_log`
VALUES (58, 'admin', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[5]', 0, '***********',
        '2021-06-30 10:37:06');
INSERT INTO `sys_log`
VALUES (59, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":5,\"parentId\":0,\"name\":\"SQL监控\",\"url\":\"http://localhost:8080/sqx_fast/druid/sql.html\",\"type\":1,\"icon\":\"sql\",\"orderNum\":4}]',
        54, '***********', '2021-06-30 10:38:37');
INSERT INTO `sys_log`
VALUES (60, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":5,\"parentId\":1,\"name\":\"SQL监控\",\"url\":\"http://localhost:8080/sqx_fast/druid/sql.html\",\"type\":1,\"icon\":\"sql\",\"orderNum\":4}]',
        328, '***********', '2021-06-30 10:38:51');
INSERT INTO `sys_log`
VALUES (61, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":2,\"parentId\":0,\"name\":\"管理员列表\",\"url\":\"sys/user\",\"type\":1,\"icon\":\"admin\",\"orderNum\":10}]',
        56, '***********', '2021-06-30 10:39:40');
INSERT INTO `sys_log`
VALUES (62, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":2,\"parentId\":0,\"name\":\"管理员列表\",\"url\":\"sys/user\",\"type\":1,\"icon\":\"admin\",\"orderNum\":10}]',
        56, '***********', '2021-06-30 10:39:40');
INSERT INTO `sys_log`
VALUES (63, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":3,\"parentId\":0,\"name\":\"角色管理\",\"url\":\"sys/role\",\"type\":1,\"icon\":\"role\",\"orderNum\":11}]',
        56, '***********', '2021-06-30 10:40:05');
INSERT INTO `sys_log`
VALUES (64, 'admin', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":4,\"parentId\":0,\"name\":\"菜单管理\",\"url\":\"sys/menu\",\"type\":1,\"icon\":\"menu\",\"orderNum\":12}]',
        54, '***********', '2021-06-30 10:40:27');
INSERT INTO `sys_log`
VALUES (65, 'admin', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":2,\"username\":\"nyy\",\"password\":\"b1672b04628a52e4a3d7a472c691564c7b9d92e3ee38abd96f78bab92446b9a2\",\"salt\":\"gOQpg687tI4FgraTuw2v\",\"email\":\"<EMAIL>\",\"mobile\":\"13211111111\",\"status\":1,\"roleIdList\":[1],\"createUserId\":1,\"createTime\":\"Jul 22, 2021 9:56:51 AM\"}]',
        565, '***********', '2021-07-22 09:56:52');
INSERT INTO `sys_log`
VALUES (66, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":32,\"parentId\":0,\"name\":\"用户中心\",\"url\":\"userList\",\"perms\":\"\",\"type\":1,\"icon\":\"yonghul\",\"orderNum\":1}]',
        56, '***********', '2021-08-03 16:53:28');
INSERT INTO `sys_log`
VALUES (67, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":62,\"parentId\":0,\"name\":\"订单中心\",\"url\":\"orderCenter\",\"perms\":\"\",\"type\":1,\"icon\":\"log\",\"orderNum\":4}]',
        55, '***********', '2021-08-03 16:53:48');
INSERT INTO `sys_log`
VALUES (68, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":57,\"parentId\":0,\"name\":\"系统配置\",\"url\":\"allocationList\",\"perms\":\"\",\"type\":1,\"icon\":\"menu\",\"orderNum\":9}]',
        55, '***********', '2021-08-03 16:53:58');
INSERT INTO `sys_log`
VALUES (69, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":55,\"parentId\":0,\"name\":\"商户合作\",\"url\":\"merchant\",\"perms\":\"\",\"type\":1,\"icon\":\"role\",\"orderNum\":9}]',
        55, '***********', '2021-08-03 16:54:08');
INSERT INTO `sys_log`
VALUES (70, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":50,\"parentId\":0,\"name\":\"首页装修\",\"url\":\"bannerList\",\"perms\":\"\",\"type\":1,\"icon\":\"shangpin\",\"orderNum\":2}]',
        54, '***********', '2021-08-03 16:54:22');
INSERT INTO `sys_log`
VALUES (71, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":44,\"parentId\":0,\"name\":\"课程中心\",\"url\":\"mission\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":3}]',
        54, '***********', '2021-08-03 16:54:35');
INSERT INTO `sys_log`
VALUES (72, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":41,\"parentId\":0,\"name\":\"消息中心\",\"url\":\"message\",\"perms\":\"\",\"type\":1,\"icon\":\"xiaoxi\",\"orderNum\":1}]',
        53, '***********', '2021-08-03 16:54:50');
INSERT INTO `sys_log`
VALUES (73, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":34,\"parentId\":0,\"name\":\"财务中心\",\"url\":\"financeList\",\"perms\":\"\",\"type\":1,\"icon\":\"caiwu\",\"orderNum\":1}]',
        54, '***********', '2021-08-03 16:55:00');
INSERT INTO `sys_log`
VALUES (74, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":65,\"parentId\":0,\"name\":\"会员列表\",\"url\":\"viplist\",\"perms\":\"\",\"type\":1,\"icon\":\"fenleilist\",\"orderNum\":5}]',
        69, '***********', '2021-08-04 13:54:55');
INSERT INTO `sys_log`
VALUES (75, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":66,\"parentId\":0,\"name\":\"会员列表\",\"url\":\"viplist\",\"perms\":\"\",\"type\":1,\"icon\":\"fenleilist\",\"orderNum\":5}]',
        51, '***********', '2021-08-04 13:54:55');
INSERT INTO `sys_log`
VALUES (76, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[65]', 142, '***********',
        '2021-08-04 13:55:19');
INSERT INTO `sys_log`
VALUES (77, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":67,\"parentId\":66,\"name\":\"查看\",\"url\":\"\",\"perms\":\"viplist:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        72, '***********', '2021-08-04 13:55:48');
INSERT INTO `sys_log`
VALUES (78, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":68,\"parentId\":66,\"name\":\"查看\",\"url\":\"\",\"perms\":\"viplist:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '***********', '2021-08-04 13:55:48');
INSERT INTO `sys_log`
VALUES (79, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":4,\"username\":\"fang\",\"password\":\"d278cd6ca4d02bd7b637b0a31c69a6f0a6b02926a3ca084cf3b015db26c0b963\",\"salt\":\"45SeS76urO3OC3xmdFmB\",\"email\":\"<EMAIL>\",\"mobile\":\"15289385023\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Aug 4, 2021 2:04:00 PM\"}]',
        500, '************', '2021-08-04 14:04:00');
INSERT INTO `sys_log`
VALUES (80, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,50,51,52,53,54,44,45,46,47,48,49,62,63,64,66,67,68,55,56,57,58,59,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        3852, '***********', '2021-08-04 14:06:25');
INSERT INTO `sys_log`
VALUES (81, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,50,51,52,53,54,44,45,46,47,48,49,62,63,64,66,67,68,55,56,57,58,59,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        3604, '***********', '2021-08-04 14:06:30');
INSERT INTO `sys_log`
VALUES (82, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,50,51,52,53,54,44,45,46,47,48,49,62,63,64,66,67,68,55,56,57,58,59,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        7283, '***********', '2021-08-04 14:06:34');
INSERT INTO `sys_log`
VALUES (83, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[68]', 139, '***********',
        '2021-08-04 14:07:51');
INSERT INTO `sys_log`
VALUES (84, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":5,\"username\":\"wp\",\"password\":\"e2fe5869ef8232fb6e9a153476157e58f495c202334c2afcb4526c4348fb7e53\",\"salt\":\"biUJKwRIENuM1vdmf5om\",\"email\":\"<EMAIL>\",\"mobile\":\"13200000000\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Aug 5, 2021 10:27:13 AM\"}]',
        478, '192.168.1.9', '2021-08-05 10:27:14');
INSERT INTO `sys_log`
VALUES (85, 'fang', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[56]', 142, '************',
        '2021-08-05 10:31:39');
INSERT INTO `sys_log`
VALUES (86, 'fang', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[55]', 123, '************',
        '2021-08-05 10:31:46');
INSERT INTO `sys_log`
VALUES (87, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":69,\"parentId\":41,\"name\":\"添加\",\"url\":\"\",\"perms\":\"message:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        84, '192.168.1.9', '2021-08-05 11:11:53');
INSERT INTO `sys_log`
VALUES (88, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":70,\"parentId\":41,\"name\":\"修改\",\"url\":\"\",\"perms\":\"message:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '192.168.1.9', '2021-08-05 11:12:14');
INSERT INTO `sys_log`
VALUES (89, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":71,\"parentId\":41,\"name\":\"删除\",\"url\":\"\",\"perms\":\"message:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '192.168.1.9', '2021-08-05 11:12:28');
INSERT INTO `sys_log`
VALUES (90, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,44,45,46,47,48,49,62,63,64,66,67,57,58,59,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        3678, '192.168.1.9', '2021-08-05 11:14:21');
INSERT INTO `sys_log`
VALUES (91, 'wp', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":44,\"parentId\":0,\"name\":\"任务中心\",\"url\":\"mission\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":3}]',
        56, '***********', '2021-08-16 11:19:40');
INSERT INTO `sys_log`
VALUES (92, 'wp', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":44,\"parentId\":0,\"name\":\"课程中心\",\"url\":\"mission\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":3}]',
        52, '***********', '2021-08-16 11:28:12');
INSERT INTO `sys_log`
VALUES (93, 'wp', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"任务中心\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":5}]',
        57, '***********', '2021-08-16 11:28:34');
INSERT INTO `sys_log`
VALUES (94, 'wp', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":73,\"parentId\":72,\"name\":\"添加\",\"url\":\"\",\"perms\":\"locality:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        81, '***********', '2021-08-16 11:29:06');
INSERT INTO `sys_log`
VALUES (95, 'wp', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":74,\"parentId\":72,\"name\":\"查看\",\"url\":\"\",\"perms\":\"locality:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        78, '***********', '2021-08-16 11:29:27');
INSERT INTO `sys_log`
VALUES (96, 'wp', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":75,\"parentId\":72,\"name\":\"修改\",\"url\":\"\",\"perms\":\"locality:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        78, '***********', '2021-08-16 11:29:51');
INSERT INTO `sys_log`
VALUES (97, 'wp', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":76,\"parentId\":72,\"name\":\"删除\",\"url\":\"\",\"perms\":\"locality:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        88, '***********', '2021-08-16 11:30:11');
INSERT INTO `sys_log`
VALUES (98, 'wp', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":5,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,44,45,46,47,48,49,62,63,64,66,67,72,73,74,75,76,57,58,59,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        4410, '***********', '2021-08-16 11:30:27');
INSERT INTO `sys_log`
VALUES (99, 'wp', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"任务中心\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"\",\"orderNum\":6}]',
        66, '***********', '2021-08-16 13:13:19');
INSERT INTO `sys_log`
VALUES (100, 'wp', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"任务中心\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"order\",\"orderNum\":6}]',
        63, '***********', '2021-08-16 13:14:41');
INSERT INTO `sys_log`
VALUES (101, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[66]', 25, '***********',
        '2021-08-16 16:09:55');
INSERT INTO `sys_log`
VALUES (102, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[67]', 145, '***********',
        '2021-08-16 16:17:26');
INSERT INTO `sys_log`
VALUES (103, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[66]', 131, '***********',
        '2021-08-16 16:17:34');
INSERT INTO `sys_log`
VALUES (104, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":77,\"parentId\":0,\"name\":\"实名认证\",\"url\":\"autonym\",\"perms\":\"\",\"type\":1,\"icon\":\"shangpin\",\"orderNum\":6}]',
        83, '***********', '2021-08-16 16:45:17');
INSERT INTO `sys_log`
VALUES (105, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":78,\"parentId\":77,\"name\":\"列表\",\"url\":\"\",\"perms\":\"autonym:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        84, '***********', '2021-08-16 16:45:44');
INSERT INTO `sys_log`
VALUES (106, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,44,45,46,47,48,49,62,63,64,72,73,74,75,76,77,78,57,58,59,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        5186, '***********', '2021-08-16 16:46:19');
INSERT INTO `sys_log`
VALUES (107, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,57,58,59,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        3837, '***********', '2021-08-23 14:52:00');
INSERT INTO `sys_log`
VALUES (108, 'nyy', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[4]]', 80, '***********',
        '2021-08-24 17:13:44');
INSERT INTO `sys_log`
VALUES (109, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":41,\"parentId\":0,\"name\":\"消息中心\",\"url\":\"message\",\"perms\":\"\",\"type\":1,\"icon\":\"xiangqufill\",\"orderNum\":1}]',
        56, '***********', '2021-08-24 17:15:41');
INSERT INTO `sys_log`
VALUES (110, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":41,\"parentId\":0,\"name\":\"消息中心\",\"url\":\"message\",\"perms\":\"\",\"type\":1,\"icon\":\"xiaoxi\",\"orderNum\":1}]',
        50, '***********', '2021-08-24 17:15:53');
INSERT INTO `sys_log`
VALUES (111, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":79,\"parentId\":0,\"name\":\"聊天室\",\"url\":\"vueMchat\",\"perms\":\"\",\"type\":1,\"icon\":\"xiaoxi\",\"orderNum\":9}]',
        68, '***********', '2021-08-27 15:13:11');
INSERT INTO `sys_log`
VALUES (112, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        4835, '***********', '2021-08-27 15:13:45');
INSERT INTO `sys_log`
VALUES (113, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[49]', 151, '***********',
        '2021-08-28 15:57:01');
INSERT INTO `sys_log`
VALUES (114, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[48]', 149, '***********',
        '2021-08-28 15:57:13');
INSERT INTO `sys_log`
VALUES (115, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[47]', 479, '***********',
        '2021-08-28 15:57:23');
INSERT INTO `sys_log`
VALUES (116, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[46]', 414, '***********',
        '2021-08-28 15:57:33');
INSERT INTO `sys_log`
VALUES (117, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[45]', 126, '***********',
        '2021-08-28 15:57:41');
INSERT INTO `sys_log`
VALUES (118, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[44]', 135, '***********',
        '2021-08-28 15:57:54');
INSERT INTO `sys_log`
VALUES (119, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":80,\"parentId\":0,\"name\":\"会员配置\",\"url\":\"memberDetails\",\"perms\":\"\",\"type\":1,\"icon\":\"fenleilist\",\"orderNum\":8}]',
        61, '***********', '2021-08-28 16:19:39');
INSERT INTO `sys_log`
VALUES (120, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":81,\"parentId\":80,\"name\":\"查看\",\"url\":\"\",\"perms\":\"memberDetails:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        84, '***********', '2021-08-28 16:20:09');
INSERT INTO `sys_log`
VALUES (121, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":82,\"parentId\":80,\"name\":\"添加\",\"url\":\"\",\"perms\":\"memberDetails:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '***********', '2021-08-28 16:20:22');
INSERT INTO `sys_log`
VALUES (122, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":83,\"parentId\":80,\"name\":\"修改\",\"url\":\"\",\"perms\":\"memberDetails:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        73, '***********', '2021-08-28 16:20:38');
INSERT INTO `sys_log`
VALUES (123, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":84,\"parentId\":80,\"name\":\"删除\",\"url\":\"\",\"perms\":\"memberDetails:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        116, '***********', '2021-08-28 16:20:53');
INSERT INTO `sys_log`
VALUES (124, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,1,5,6,7,8,9,10,11,12,13,14,27,31,30,29,-666666]}]',
        4540, '***********', '2021-08-28 16:21:16');
INSERT INTO `sys_log`
VALUES (125, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":85,\"parentId\":0,\"name\":\"会员特权\",\"url\":\"vipPrivilege\",\"perms\":\"\",\"type\":1,\"icon\":\"geren\",\"orderNum\":8}]',
        55, '***********', '2021-08-30 11:41:51');
INSERT INTO `sys_log`
VALUES (126, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":86,\"parentId\":85,\"name\":\"列表\",\"url\":\"\",\"perms\":\"vipPrivilege:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '***********', '2021-08-30 11:42:10');
INSERT INTO `sys_log`
VALUES (127, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":87,\"parentId\":85,\"name\":\"添加\",\"url\":\"\",\"perms\":\"vipPrivilege:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '***********', '2021-08-30 11:42:24');
INSERT INTO `sys_log`
VALUES (128, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":88,\"parentId\":85,\"name\":\"修改\",\"url\":\"\",\"perms\":\"vipPrivilege:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        76, '***********', '2021-08-30 11:42:41');
INSERT INTO `sys_log`
VALUES (129, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":89,\"parentId\":85,\"name\":\"删除\",\"url\":\"\",\"perms\":\"vipPrivilege:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        97, '***********', '2021-08-30 11:42:59');
INSERT INTO `sys_log`
VALUES (130, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3512, '***********', '2021-08-30 13:08:08');
INSERT INTO `sys_log`
VALUES (131, 'wp', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":6,\"username\":\"tk\",\"password\":\"b1352b5e9537a23ad48885bcbe9a4beb73facb55cdccfc93a7999f6f8454a2dd\",\"salt\":\"Mk9i64L19lHyv4T8v5KG\",\"email\":\"<EMAIL>\",\"mobile\":\"13259456001\",\"status\":1,\"roleIdList\":[1],\"createUserId\":5,\"createTime\":\"Aug 30, 2021, 5:57:46 PM\"}]',
        399, '192.168.1.16', '2021-08-30 17:57:47');
INSERT INTO `sys_log`
VALUES (132, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":7,\"username\":\"ceshi\",\"password\":\"149713a1930838737a2b710c8c13af800610740d322780da025c00c5428823ad\",\"salt\":\"PLn6LlBQYz6RJhnkbYBc\",\"email\":\"<EMAIL>\",\"mobile\":\"15500000000\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Sep 1, 2021, 10:00:56 AM\"}]',
        680, '***********', '2021-09-01 10:00:57');
INSERT INTO `sys_log`
VALUES (133, 'ceshi', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":8,\"username\":\"11\",\"password\":\"3c42c09c2f1d62ab71528cc5f56dec63ebdf26f53c7f9ff44487dfd2ee718012\",\"salt\":\"pF4RvdoiVlHVwXbZumP7\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688001\",\"status\":1,\"roleIdList\":[1],\"createUserId\":7,\"createTime\":\"Sep 10, 2021 2:11:13 PM\"}]',
        419, '0:0:0:0:0:0:0:1', '2021-09-10 14:11:14');
INSERT INTO `sys_log`
VALUES (134, 'ceshi', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":9,\"username\":\"22\",\"password\":\"bb66a0e986626a558c12853a481b63c818ce42cdcd45287e9b3b4f5cfad917a4\",\"salt\":\"bjQ8IfyiT2yKgqtF5GWr\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688500\",\"status\":1,\"roleIdList\":[1],\"createUserId\":7,\"createTime\":\"Sep 10, 2021 2:11:34 PM\"}]',
        326, '127.0.0.1', '2021-09-10 14:11:35');
INSERT INTO `sys_log`
VALUES (135, 'ceshi', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":8,\"username\":\"12\",\"salt\":\"pF4RvdoiVlHVwXbZumP7\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688001\",\"status\":1,\"roleIdList\":[1],\"createUserId\":7}]',
        336, '127.0.0.1', '2021-09-10 14:11:48');
INSERT INTO `sys_log`
VALUES (136, 'ceshi', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[8,9]]', 77,
        '0:0:0:0:0:0:0:1', '2021-09-10 14:11:58');
INSERT INTO `sys_log`
VALUES (137, 'ceshi', '保存角色', 'com.sqx.modules.sys.controller.SysRoleController.save()',
        '[{\"roleId\":2,\"roleName\":\"11\",\"remark\":\"11\",\"createUserId\":7,\"menuIdList\":[33,-666666],\"createTime\":\"Sep 10, 2021 2:12:27 PM\"}]',
        405, '0:0:0:0:0:0:0:1', '2021-09-10 14:12:28');
INSERT INTO `sys_log`
VALUES (138, 'ceshi', '保存角色', 'com.sqx.modules.sys.controller.SysRoleController.save()',
        '[{\"roleId\":3,\"roleName\":\"22\",\"remark\":\"\",\"createUserId\":7,\"menuIdList\":[-666666],\"createTime\":\"Sep 10, 2021 2:12:40 PM\"}]',
        320, '127.0.0.1', '2021-09-10 14:12:41');
INSERT INTO `sys_log`
VALUES (139, 'ceshi', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":2,\"roleName\":\"12\",\"remark\":\"12\",\"createUserId\":7,\"menuIdList\":[33,4,23,24,25,26,-666666]}]',
        787, '0:0:0:0:0:0:0:1', '2021-09-10 14:12:58');
INSERT INTO `sys_log`
VALUES (140, 'ceshi', '删除角色', 'com.sqx.modules.sys.controller.SysRoleController.delete()', '[[2,3]]', 320,
        '0:0:0:0:0:0:0:1', '2021-09-10 14:13:13');
INSERT INTO `sys_log`
VALUES (141, 'ceshi', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":10,\"username\":\"zww\",\"password\":\"2820111e5914360c1d365ce3f3d5602c3447ec53b2bb12c6df5f9f074832dd33\",\"salt\":\"0X8LGAUlW6Q76gOvZngL\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688510\",\"status\":1,\"roleIdList\":[1],\"createUserId\":7,\"createTime\":\"Sep 10, 2021 2:43:53 PM\"}]',
        441, '127.0.0.1', '2021-09-10 14:43:53');
INSERT INTO `sys_log`
VALUES (142, 'ceshi', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":7,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4832, '0:0:0:0:0:0:0:1', '2021-09-10 14:44:29');
INSERT INTO `sys_log`
VALUES (143, 'ceshi', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":7,\"menuIdList\":[33,32,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4804, '127.0.0.1', '2021-09-10 14:44:34');
INSERT INTO `sys_log`
VALUES (144, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":11,\"username\":\"ckl\",\"password\":\"8fac3aa210756547c02b43c384f04406e04de9f0b1c3a58def1364e87fa81849\",\"salt\":\"aZhZeVQAGeWt3dB1RTL8\",\"email\":\"<EMAIL>\",\"mobile\":\"13200000000\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Sep 10, 2021 2:47:00 PM\"}]',
        319, '0:0:0:0:0:0:0:1', '2021-09-10 14:47:00');
INSERT INTO `sys_log`
VALUES (145, 'ceshi', '保存角色', 'com.sqx.modules.sys.controller.SysRoleController.save()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":7,\"menuIdList\":[33,60,35,42,51,63,74,81,86,58,79,15,19,23,-666666,32,34,41,50,62,72,80,85,57,2,3,4],\"createTime\":\"Sep 10, 2021 3:33:46 PM\"}]',
        2361, '0:0:0:0:0:0:0:1', '2021-09-10 15:33:49');
INSERT INTO `sys_log`
VALUES (146, 'ceshi', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":12,\"username\":\"sqx\",\"password\":\"3dc80ee60c663ef7a5c3bbc73da92d250529c74aae89cd1e8f43ad4ed60ac171\",\"salt\":\"eVL7KZbYgMqVZvd6j74Y\",\"email\":\"<EMAIL>\",\"mobile\":\"18791000399\",\"status\":1,\"roleIdList\":[4],\"createUserId\":7,\"createTime\":\"Sep 10, 2021 3:37:01 PM\"}]',
        350, '127.0.0.1', '2021-09-10 15:37:02');
INSERT INTO `sys_log`
VALUES (147, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":90,\"parentId\":32,\"name\":\"修改积分\",\"url\":\"\",\"perms\":\"userList:updatejf\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        108, '0:0:0:0:0:0:0:1', '2021-09-10 15:45:40');
INSERT INTO `sys_log`
VALUES (148, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":91,\"parentId\":32,\"name\":\"修改用户状态\",\"url\":\"\",\"perms\":\"userList:updateStatus\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        112, '0:0:0:0:0:0:0:1', '2021-09-10 15:46:18');
INSERT INTO `sys_log`
VALUES (149, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,60,61,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666,32]}]',
        4861, '0:0:0:0:0:0:0:1', '2021-09-10 15:48:49');
INSERT INTO `sys_log`
VALUES (150, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"陪玩列表\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"order\",\"orderNum\":6}]',
        76, '0:0:0:0:0:0:0:1', '2021-09-10 15:51:35');
INSERT INTO `sys_log`
VALUES (151, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666,32]}]',
        4831, '0:0:0:0:0:0:0:1', '2021-09-10 16:00:22');
INSERT INTO `sys_log`
VALUES (152, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,60,61,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666,32]}]',
        4751, '127.0.0.1', '2021-09-10 16:02:39');
INSERT INTO `sys_log`
VALUES (153, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,60,61,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666,32]}]',
        4806, '0:0:0:0:0:0:0:1', '2021-09-10 16:08:13');
INSERT INTO `sys_log`
VALUES (154, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,80,81,82,83,84,85,86,87,88,89,57,58,59,79,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        5039, '0:0:0:0:0:0:0:1', '2021-09-10 16:09:02');
INSERT INTO `sys_log`
VALUES (155, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":64,\"parentId\":62,\"name\":\"删除\",\"url\":\"\",\"perms\":\"orderCenter:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        110, '0:0:0:0:0:0:0:1', '2021-09-10 16:13:46');
INSERT INTO `sys_log`
VALUES (156, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":92,\"parentId\":77,\"name\":\"通过\",\"url\":\"\",\"perms\":\"autonym:tongguo\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        115, '0:0:0:0:0:0:0:1', '2021-09-10 16:15:35');
INSERT INTO `sys_log`
VALUES (157, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":93,\"parentId\":77,\"name\":\"拒绝\",\"url\":\"\",\"perms\":\"autonym:jujue\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        114, '127.0.0.1', '2021-09-10 16:15:53');
INSERT INTO `sys_log`
VALUES (158, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":2,\"username\":\"nyy\",\"salt\":\"gOQpg687tI4FgraTuw2v\",\"email\":\"<EMAIL>\",\"mobile\":\"13211111111\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2}]',
        408, '0:0:0:0:0:0:0:1', '2021-09-10 18:01:23');
INSERT INTO `sys_log`
VALUES (159, 'zww', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":10,\"username\":\"zww\",\"password\":\"2820111e5914360c1d365ce3f3d5602c3447ec53b2bb12c6df5f9f074832dd33\",\"salt\":\"0X8LGAUlW6Q76gOvZngL\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688510\",\"status\":1,\"roleIdList\":[4],\"createUserId\":10}]',
        328, '127.0.0.1', '2021-09-10 18:06:57');
INSERT INTO `sys_log`
VALUES (160, 'ckl', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":94,\"parentId\":0,\"name\":\"升级配置\",\"url\":\"app\",\"perms\":\"\",\"type\":1,\"icon\":\"sql\",\"orderNum\":9}]',
        73, '127.0.0.1', '2021-09-10 18:34:03');
INSERT INTO `sys_log`
VALUES (161, 'ckl', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":95,\"parentId\":94,\"name\":\"查看\",\"url\":\"\",\"perms\":\"app:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        108, '127.0.0.1', '2021-09-10 18:34:50');
INSERT INTO `sys_log`
VALUES (162, 'ckl', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":96,\"parentId\":94,\"name\":\"添加\",\"url\":\"\",\"perms\":\"app:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        109, '127.0.0.1', '2021-09-10 18:35:03');
INSERT INTO `sys_log`
VALUES (163, 'ckl', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":97,\"parentId\":94,\"name\":\"修改\",\"url\":\"\",\"perms\":\"app:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        105, '0:0:0:0:0:0:0:1', '2021-09-10 18:35:16');
INSERT INTO `sys_log`
VALUES (164, 'ckl', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":98,\"parentId\":94,\"name\":\"删除\",\"url\":\"\",\"perms\":\"app:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        106, '0:0:0:0:0:0:0:1', '2021-09-10 18:35:29');
INSERT INTO `sys_log`
VALUES (165, 'ckl', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":11,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        5439, '0:0:0:0:0:0:0:1', '2021-09-10 18:35:55');
INSERT INTO `sys_log`
VALUES (166, 'ckl', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":11,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,73,74,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666,72]}]',
        5284, '0:0:0:0:0:0:0:1', '2021-09-10 18:39:28');
INSERT INTO `sys_log`
VALUES (167, 'ceshi', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":10,\"username\":\"zww\",\"password\":\"2820111e5914360c1d365ce3f3d5602c3447ec53b2bb12c6df5f9f074832dd33\",\"salt\":\"0X8LGAUlW6Q76gOvZngL\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688510\",\"status\":1,\"roleIdList\":[1],\"createUserId\":7}]',
        337, '127.0.0.1', '2021-09-10 19:21:07');
INSERT INTO `sys_log`
VALUES (168, 'zww', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":10,\"username\":\"zww\",\"password\":\"2820111e5914360c1d365ce3f3d5602c3447ec53b2bb12c6df5f9f074832dd33\",\"salt\":\"0X8LGAUlW6Q76gOvZngL\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688510\",\"status\":1,\"roleIdList\":[4],\"createUserId\":10}]',
        449, '0:0:0:0:0:0:0:1', '2021-09-10 19:35:20');
INSERT INTO `sys_log`
VALUES (169, 'ceshi', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":7,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        5590, '0:0:0:0:0:0:0:1', '2021-09-10 20:10:04');
INSERT INTO `sys_log`
VALUES (170, 'tk', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":6,\"menuIdList\":[33,60,35,42,51,63,74,77,78,92,93,81,86,58,79,15,19,23,-666666,32,34,41,50,62,72,80,85,57,2,3,4]}]',
        3336, '0:0:0:0:0:0:0:1', '2021-10-27 14:04:50');
INSERT INTO `sys_log`
VALUES (171, 'tk', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":2,\"username\":\"nyy\",\"salt\":\"gOQpg687tI4FgraTuw2v\",\"email\":\"<EMAIL>\",\"mobile\":\"13211111111\",\"status\":1,\"roleIdList\":[1],\"createUserId\":6}]',
        392, '192.168.1.12', '2021-11-04 15:38:40');
INSERT INTO `sys_log`
VALUES (172, 'tk', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":13,\"username\":\"xh\",\"password\":\"18e867050b23a24afc05c9b0153148824bf74d23d8ea779682a9a9993635bdc1\",\"salt\":\"JwC8bc56iyiLjLet4PnL\",\"email\":\"<EMAIL>\",\"mobile\":\"18740331034\",\"status\":1,\"roleIdList\":[1],\"createUserId\":6,\"createTime\":\"Nov 5, 2021 3:04:36 PM\"}]',
        440, '127.0.0.1', '2021-11-05 15:04:36');
INSERT INTO `sys_log`
VALUES (173, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,35,42,51,63,74,77,78,92,93,81,86,79,95,15,19,23,-666666,32,34,41,50,62,72,80,85,94,2,3,4]}]',
        2648, '0:0:0:0:0:0:0:1', '2021-11-06 13:10:23');
INSERT INTO `sys_log`
VALUES (174, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":12,\"username\":\"sqx\",\"password\":\"360515c889683cc7149027e7af4fc79eed82886b88851d71d37463bbcfa38006\",\"salt\":\"eVL7KZbYgMqVZvd6j74Y\",\"email\":\"<EMAIL>\",\"mobile\":\"18791000399\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2}]',
        347, '0:0:0:0:0:0:0:1', '2021-11-06 13:10:51');
INSERT INTO `sys_log`
VALUES (175, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"服务列表\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"order\",\"orderNum\":6}]',
        48, '192.168.0.105', '2021-11-11 11:27:34');
INSERT INTO `sys_log`
VALUES (176, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":99,\"parentId\":0,\"name\":\"聊天记录\",\"url\":\"chatRecord\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":9}]',
        42, '192.168.0.107', '2021-11-12 17:47:02');
INSERT INTO `sys_log`
VALUES (177, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":100,\"parentId\":99,\"name\":\"查看\",\"url\":\"\",\"perms\":\"chatRecord:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        64, '192.168.0.107', '2021-11-12 17:47:21');
INSERT INTO `sys_log`
VALUES (178, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":101,\"parentId\":99,\"name\":\"删除\",\"url\":\"\",\"perms\":\"chatRecord:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        52, '192.168.0.107', '2021-11-12 17:47:37');
INSERT INTO `sys_log`
VALUES (179, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,72,73,74,75,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,99,100,101,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        2530, '192.168.0.107', '2021-11-12 17:47:52');
INSERT INTO `sys_log`
VALUES (180, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":12,\"username\":\"sqx\",\"password\":\"4be3ab0c3732b9619112be72e9af54b9dc39b39196d404dc8d79e87493521053\",\"salt\":\"eVL7KZbYgMqVZvd6j74Y\",\"email\":\"<EMAIL>\",\"mobile\":\"18791000399\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2}]',
        659, '127.0.0.1', '2021-12-10 17:55:39');
INSERT INTO `sys_log`
VALUES (181, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,35,42,51,63,74,77,78,92,93,81,86,79,95,100,15,19,23,-666666,32,34,41,50,62,72,80,85,94,99,2,3,4]}]',
        3006, '0:0:0:0:0:0:0:1', '2021-12-10 17:58:18');
INSERT INTO `sys_log`
VALUES (182, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":10,\"username\":\"zww\",\"password\":\"2820111e5914360c1d365ce3f3d5602c3447ec53b2bb12c6df5f9f074832dd33\",\"salt\":\"0X8LGAUlW6Q76gOvZngL\",\"email\":\"<EMAIL>\",\"mobile\":\"18329688510\",\"status\":1,\"roleIdList\":[4,1],\"createUserId\":2}]',
        565, '127.0.0.1', '2021-12-28 11:36:42');
INSERT INTO `sys_log`
VALUES (183, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":103,\"parentId\":0,\"name\":\"万能任务\",\"url\":\"universalMission\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":5}]',
        60, '192.168.0.104', '2022-03-22 10:21:52');
INSERT INTO `sys_log`
VALUES (184, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":104,\"parentId\":103,\"name\":\"查看\",\"url\":\"\",\"perms\":\"universalMission:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        59, '192.168.0.104', '2022-03-22 10:22:10');
INSERT INTO `sys_log`
VALUES (185, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":105,\"parentId\":103,\"name\":\"添加\",\"url\":\"\",\"perms\":\"universalMission:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        63, '192.168.0.104', '2022-03-22 10:22:22');
INSERT INTO `sys_log`
VALUES (186, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":106,\"parentId\":103,\"name\":\"修改\",\"url\":\"\",\"perms\":\"universalMission:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        60, '192.168.0.104', '2022-03-22 10:22:37');
INSERT INTO `sys_log`
VALUES (187, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":107,\"parentId\":103,\"name\":\"删除\",\"url\":\"\",\"perms\":\"universalMission:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        60, '192.168.0.104', '2022-03-22 10:23:06');
INSERT INTO `sys_log`
VALUES (188, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,103,104,105,106,107,72,73,74,75,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,99,100,101,102,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3075, '192.168.0.104', '2022-03-22 10:24:22');
INSERT INTO `sys_log`
VALUES (189, 'zww', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":108,\"parentId\":62,\"name\":\"退款\",\"url\":\"\",\"perms\":\"orderCenter:tuikuan\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        61, '192.168.0.105', '2022-03-28 13:48:53');
INSERT INTO `sys_log`
VALUES (190, 'zww', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":10,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,38,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,103,104,105,106,107,72,73,74,75,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,99,100,101,102,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3760, '192.168.0.105', '2022-03-28 13:49:55');
INSERT INTO `sys_log`
VALUES (191, 'zww', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[38]', 125, '192.168.0.105',
        '2022-03-28 13:50:15');
INSERT INTO `sys_log`
VALUES (192, 'zww', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":109,\"parentId\":62,\"name\":\"完成\",\"url\":\"\",\"perms\":\"orderCenter:wancheng\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        58, '192.168.0.105', '2022-03-28 13:54:23');
INSERT INTO `sys_log`
VALUES (193, 'zww', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":10,\"menuIdList\":[33,32,60,61,90,91,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,99,100,101,102,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3608, '192.168.0.105', '2022-03-28 13:54:40');
INSERT INTO `sys_log`
VALUES (194, 'zww', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":10,\"menuIdList\":[33,60,90,91,35,42,51,62,63,64,108,109,103,104,105,106,107,74,77,78,92,93,81,86,58,79,95,100,15,19,23,-666666,32,34,41,50,72,80,85,57,94,99,102,2,3,4]}]',
        3777, '0:0:0:0:0:0:0:1', '2022-03-28 19:14:04');
INSERT INTO `sys_log`
VALUES (195, 'zww', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":77,\"parentId\":0,\"name\":\"入驻审核\",\"url\":\"autonym\",\"perms\":\"\",\"type\":1,\"icon\":\"shangpin\",\"orderNum\":6}]',
        325, '0:0:0:0:0:0:0:1', '2022-03-28 19:21:18');
INSERT INTO `sys_log`
VALUES (196, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":110,\"parentId\":32,\"name\":\"修改佣金\",\"url\":\"\",\"perms\":\"userList:updatebl\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        102, '192.168.0.104', '2022-04-22 14:48:13');
INSERT INTO `sys_log`
VALUES (197, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,77,78,92,93,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,99,100,101,102,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4000, '192.168.0.104', '2022-04-22 14:48:35');
INSERT INTO `sys_log`
VALUES (198, 'zww', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":10,\"menuIdList\":[33,60,90,91,35,42,51,62,63,64,108,109,103,104,105,106,107,73,74,75,77,78,92,93,81,86,58,79,95,100,15,19,23,-666666,32,34,41,50,72,80,85,57,94,99,102,2,3,4]}]',
        4204, '0:0:0:0:0:0:0:1', '2022-06-24 18:54:15');
INSERT INTO `sys_log`
VALUES (199, 'tk', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":14,\"username\":\"xh\",\"password\":\"2b3ae8cb629550102f37060005ccada74ec514b14f84460b1c5450dfd3547cfe\",\"salt\":\"ShbNL2PG9DY8Q0WOU0zD\",\"email\":\"<EMAIL>\",\"mobile\":\"18740331034\",\"status\":1,\"roleIdList\":[1],\"createUserId\":6,\"createTime\":\"Jun 30, 2022 10:11:05 AM\"}]',
        387, '0:0:0:0:0:0:0:1', '2022-06-30 10:11:05');
INSERT INTO `sys_log`
VALUES (200, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":111,\"parentId\":0,\"name\":\"帮助中心\",\"url\":\"materialsList\",\"perms\":\"\",\"type\":1,\"icon\":\"menu\",\"orderNum\":6}]',
        63, '*************', '2022-07-05 13:56:27');
INSERT INTO `sys_log`
VALUES (201, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":112,\"parentId\":111,\"name\":\"查看\",\"url\":\"\",\"perms\":\"materialsList:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        65, '*************', '2022-07-05 13:56:43');
INSERT INTO `sys_log`
VALUES (202, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":113,\"parentId\":111,\"name\":\"添加\",\"url\":\"\",\"perms\":\"materialsList:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        61, '*************', '2022-07-05 13:56:56');
INSERT INTO `sys_log`
VALUES (203, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":114,\"parentId\":111,\"name\":\"修改\",\"url\":\"\",\"perms\":\"materialsList:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        64, '*************', '2022-07-05 13:57:11');
INSERT INTO `sys_log`
VALUES (204, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":115,\"parentId\":111,\"name\":\"删除\",\"url\":\"\",\"perms\":\"materialsList:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        65, '*************', '2022-07-05 13:57:31');
INSERT INTO `sys_log`
VALUES (205, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,77,78,92,93,111,112,113,114,115,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,99,100,101,102,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3282, '*************', '2022-07-05 13:57:46');
INSERT INTO `sys_log`
VALUES (206, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,72,73,74,75,76,77,78,92,93,111,112,113,114,115,80,81,82,83,84,85,86,87,88,89,57,58,59,79,94,95,96,97,98,99,100,101,102,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3614, '*************', '2022-07-25 14:05:00');
INSERT INTO `sys_log`
VALUES (207, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":94,\"parentId\":102,\"name\":\"升级配置\",\"url\":\"app\",\"perms\":\"\",\"type\":1,\"icon\":\"sql\",\"orderNum\":9}]',
        81, '*************', '2022-07-25 14:11:28');
INSERT INTO `sys_log`
VALUES (208, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":116,\"parentId\":0,\"name\":\"会员配置\",\"url\":\"\",\"perms\":\"\",\"type\":0,\"icon\":\"yonghu\",\"orderNum\":8}]',
        48, '*************', '2022-07-25 14:12:48');
INSERT INTO `sys_log`
VALUES (209, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":117,\"parentId\":0,\"name\":\"聊天管理\",\"url\":\"\",\"perms\":\"\",\"type\":0,\"icon\":\"xiaoxi\",\"orderNum\":9}]',
        39, '*************', '2022-07-25 14:13:56');
INSERT INTO `sys_log`
VALUES (210, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":79,\"parentId\":117,\"name\":\"聊天室\",\"url\":\"vueMchat\",\"perms\":\"\",\"type\":1,\"icon\":\"xiaoxi\",\"orderNum\":1}]',
        121, '*************', '2022-07-25 14:14:16');
INSERT INTO `sys_log`
VALUES (211, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":99,\"parentId\":117,\"name\":\"聊天记录\",\"url\":\"chatRecord\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":2}]',
        62, '*************', '2022-07-25 14:14:32');
INSERT INTO `sys_log`
VALUES (212, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":80,\"parentId\":116,\"name\":\"会员配置\",\"url\":\"memberDetails\",\"perms\":\"\",\"type\":1,\"icon\":\"fenleilist\",\"orderNum\":1}]',
        56, '*************', '2022-07-25 14:14:58');
INSERT INTO `sys_log`
VALUES (213, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":85,\"parentId\":116,\"name\":\"会员特权\",\"url\":\"vipPrivilege\",\"perms\":\"\",\"type\":1,\"icon\":\"geren\",\"orderNum\":2}]',
        59, '*************', '2022-07-25 14:15:16');
INSERT INTO `sys_log`
VALUES (214, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":117,\"parentId\":0,\"name\":\"聊天管理\",\"url\":\"\",\"perms\":\"\",\"type\":0,\"icon\":\"xiaoxi\",\"orderNum\":8}]',
        46, '*************', '2022-07-25 14:15:32');
INSERT INTO `sys_log`
VALUES (215, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":118,\"parentId\":0,\"name\":\"车辆管理\",\"url\":\"carBrandList\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhilb\",\"orderNum\":7}]',
        44, '*************', '2022-07-25 14:18:59');
INSERT INTO `sys_log`
VALUES (216, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":119,\"parentId\":118,\"name\":\"查看\",\"url\":\"\",\"perms\":\"carBrandList:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        65, '*************', '2022-07-25 14:19:17');
INSERT INTO `sys_log`
VALUES (217, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":120,\"parentId\":118,\"name\":\"添加\",\"url\":\"\",\"perms\":\"carBrandList:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        58, '*************', '2022-07-25 14:19:28');
INSERT INTO `sys_log`
VALUES (218, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":121,\"parentId\":118,\"name\":\"修改\",\"url\":\"\",\"perms\":\"carBrandList|:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        55, '*************', '2022-07-25 14:19:43');
INSERT INTO `sys_log`
VALUES (219, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":122,\"parentId\":118,\"name\":\"删除\",\"url\":\"\",\"perms\":\"carBrandList:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        53, '*************', '2022-07-25 14:20:00');
INSERT INTO `sys_log`
VALUES (220, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":121,\"parentId\":118,\"name\":\"修改\",\"url\":\"\",\"perms\":\"carBrandList:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '*************', '2022-07-25 14:20:18');
INSERT INTO `sys_log`
VALUES (221, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,72,73,74,75,76,77,78,92,93,111,112,113,114,115,118,119,120,121,122,116,80,81,82,83,84,85,86,87,88,89,117,79,99,100,101,57,58,59,102,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        2906, '*************', '2022-07-25 14:20:43');
INSERT INTO `sys_log`
VALUES (222, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":123,\"parentId\":0,\"name\":\"招商审核\",\"url\":\"recruitList\",\"perms\":\"\",\"type\":1,\"icon\":\"mudedi\",\"orderNum\":7}]',
        62, '192.168.0.104', '2022-07-27 11:00:08');
INSERT INTO `sys_log`
VALUES (223, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":124,\"parentId\":123,\"name\":\"查看\",\"url\":\"\",\"perms\":\"recruitList:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        77, '192.168.0.104', '2022-07-27 11:00:35');
INSERT INTO `sys_log`
VALUES (224, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,72,73,74,75,76,77,78,92,93,111,112,113,114,115,118,119,120,121,122,123,124,116,80,81,82,83,84,85,86,87,88,89,117,79,99,100,101,57,58,59,102,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4860, '192.168.0.104', '2022-07-27 11:01:18');
INSERT INTO `sys_log`
VALUES (225, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":123,\"parentId\":0,\"name\":\"职务招商\",\"url\":\"recruitList\",\"perms\":\"\",\"type\":1,\"icon\":\"mudedi\",\"orderNum\":7}]',
        65, '192.168.0.104', '2022-07-27 11:02:29');
INSERT INTO `sys_log`
VALUES (226, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":118,\"parentId\":0,\"name\":\"车辆列表\",\"url\":\"carBrandList\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhilb\",\"orderNum\":7}]',
        66, '192.168.0.104', '2022-07-27 14:54:10');
INSERT INTO `sys_log`
VALUES (227, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":90,\"parentId\":32,\"name\":\"修改余额\",\"url\":\"\",\"perms\":\"userList:updatejf\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        86, '192.168.0.104', '2022-07-27 17:41:28');
INSERT INTO `sys_log`
VALUES (228, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":125,\"parentId\":32,\"name\":\"修改保证金\",\"url\":\"\",\"perms\":\"userList:updatebzj\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        85, '192.168.0.104', '2022-07-27 17:42:09');
INSERT INTO `sys_log`
VALUES (229, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,72,73,74,75,76,77,78,92,93,111,112,113,114,115,118,119,120,121,122,123,124,116,80,81,82,83,84,85,86,87,88,89,117,79,99,100,101,57,58,59,102,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4324, '192.168.0.104', '2022-07-27 17:42:28');
INSERT INTO `sys_log`
VALUES (230, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":118,\"parentId\":0,\"name\":\"简历管理\",\"url\":\"carBrandList\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhilb\",\"orderNum\":7}]',
        52, '*************', '2022-08-02 10:45:12');
INSERT INTO `sys_log`
VALUES (231, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":123,\"parentId\":0,\"name\":\"技能提升\",\"url\":\"recruitList\",\"perms\":\"\",\"type\":1,\"icon\":\"mudedi\",\"orderNum\":7}]',
        52, '*************', '2022-08-02 10:45:30');
INSERT INTO `sys_log`
VALUES (232, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":103,\"parentId\":0,\"name\":\"定制化列表\",\"url\":\"universalMission\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":5}]',
        41, '*************', '2022-08-02 11:08:04');
INSERT INTO `sys_log`
VALUES (233, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"精准项目列表\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"order\",\"orderNum\":6}]',
        37, '*************', '2022-08-02 11:08:53');
INSERT INTO `sys_log`
VALUES (234, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,77,78,92,93,111,112,113,114,115,118,119,120,121,122,123,124,116,80,81,82,83,84,85,86,87,88,89,117,79,99,100,101,57,58,59,102,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3339, '*************', '2022-08-02 11:09:16');
INSERT INTO `sys_log`
VALUES (235, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":77,\"parentId\":0,\"name\":\"入驻审核\",\"url\":\"autonym\",\"perms\":\"\",\"type\":1,\"icon\":\"shangpin\",\"orderNum\":7}]',
        57, '*************', '2022-08-02 14:40:27');
INSERT INTO `sys_log`
VALUES (236, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":111,\"parentId\":0,\"name\":\"帮助中心\",\"url\":\"materialsList\",\"perms\":\"\",\"type\":1,\"icon\":\"menu\",\"orderNum\":7}]',
        48, '*************', '2022-08-02 14:40:38');
INSERT INTO `sys_log`
VALUES (237, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":118,\"parentId\":0,\"name\":\"简历管理\",\"url\":\"carBrandList\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhilb\",\"orderNum\":6}]',
        40, '*************', '2022-08-02 14:40:49');
INSERT INTO `sys_log`
VALUES (238, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":118,\"parentId\":0,\"name\":\"简历管理\",\"url\":\"carBrandList\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhilb\",\"orderNum\":6}]',
        50, '*************', '2022-08-02 14:40:50');
INSERT INTO `sys_log`
VALUES (239, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":123,\"parentId\":0,\"name\":\"技能提升\",\"url\":\"recruitList\",\"perms\":\"\",\"type\":1,\"icon\":\"mudedi\",\"orderNum\":6}]',
        48, '*************', '2022-08-02 14:41:00');
INSERT INTO `sys_log`
VALUES (240, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"服务列表\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"order\",\"orderNum\":6}]',
        44, '*************', '2022-08-02 17:22:57');
INSERT INTO `sys_log`
VALUES (241, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":123,\"parentId\":0,\"name\":\"职务招商\",\"url\":\"recruitList\",\"perms\":\"\",\"type\":1,\"icon\":\"mudedi\",\"orderNum\":6}]',
        58, '*************', '2022-08-02 17:23:41');
INSERT INTO `sys_log`
VALUES (242, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":118,\"parentId\":0,\"name\":\"车辆管理\",\"url\":\"carBrandList\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhilb\",\"orderNum\":6}]',
        44, '*************', '2022-08-02 17:24:12');
INSERT INTO `sys_log`
VALUES (243, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,72,73,74,75,76,118,119,120,121,122,123,124,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,117,79,99,100,101,57,58,59,102,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3376, '*************', '2022-08-02 17:24:30');
INSERT INTO `sys_log`
VALUES (244, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[79]', 219, '127.0.0.1',
        '2022-08-17 13:42:30');
INSERT INTO `sys_log`
VALUES (245, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[100]', 243, '127.0.0.1',
        '2022-08-17 13:42:45');
INSERT INTO `sys_log`
VALUES (246, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[101]', 213,
        '0:0:0:0:0:0:0:1', '2022-08-17 13:42:58');
INSERT INTO `sys_log`
VALUES (247, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[99]', 214, '127.0.0.1',
        '2022-08-17 13:43:11');
INSERT INTO `sys_log`
VALUES (248, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[117]', 212,
        '0:0:0:0:0:0:0:1', '2022-08-17 13:43:21');
INSERT INTO `sys_log`
VALUES (249, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":12,\"username\":\"sqx\",\"password\":\"360515c889683cc7149027e7af4fc79eed82886b88851d71d37463bbcfa38006\",\"salt\":\"eVL7KZbYgMqVZvd6j74Y\",\"email\":\"<EMAIL>\",\"mobile\":\"18791000399\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2}]',
        443, '127.0.0.1', '2022-08-22 11:55:50');
INSERT INTO `sys_log`
VALUES (250, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,35,36,37,42,51,62,63,64,108,109,103,104,105,106,107,73,74,75,118,119,120,121,122,123,124,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,58,95,15,19,23,-666666,32,34,41,50,72,57,102,94,2,3,4]}]',
        5815, '0:0:0:0:0:0:0:1', '2022-08-22 11:56:33');
INSERT INTO `sys_log`
VALUES (251, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":126,\"parentId\":0,\"name\":\"推广代理\",\"url\":\"recruitList\",\"perms\":\"\",\"type\":1,\"icon\":\"xinxi\",\"orderNum\":6}]',
        51, '*************', '2022-08-25 17:17:08');
INSERT INTO `sys_log`
VALUES (252, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":126,\"parentId\":0,\"name\":\"推广代理\",\"url\":\"agent\",\"perms\":\"\",\"type\":1,\"icon\":\"xinxi\",\"orderNum\":6}]',
        52, '*************', '2022-08-25 17:20:43');
INSERT INTO `sys_log`
VALUES (253, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":127,\"parentId\":126,\"name\":\"查看\",\"url\":\"\",\"perms\":\"agent:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        58, '*************', '2022-08-25 17:20:59');
INSERT INTO `sys_log`
VALUES (254, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":128,\"parentId\":126,\"name\":\"添加\",\"url\":\"\",\"perms\":\"agent:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        60, '*************', '2022-08-25 17:21:09');
INSERT INTO `sys_log`
VALUES (255, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":129,\"parentId\":126,\"name\":\"修改\",\"url\":\"\",\"perms\":\"agent:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        64, '*************', '2022-08-25 17:21:21');
INSERT INTO `sys_log`
VALUES (256, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":130,\"parentId\":126,\"name\":\"删除\",\"url\":\"\",\"perms\":\"agent:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        60, '*************', '2022-08-25 17:21:35');
INSERT INTO `sys_log`
VALUES (257, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,57,58,59,102,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        3473, '*************', '2022-08-25 17:21:53');
INSERT INTO `sys_log`
VALUES (258, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,57,58,59,102,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        294, '192.168.0.110', '2022-11-09 16:13:04');
INSERT INTO `sys_log`
VALUES (259, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":103,\"parentId\":0,\"name\":\"医院管理\",\"url\":\"universalMission\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":5}]',
        8, '192.168.0.110', '2022-11-09 16:14:17');
INSERT INTO `sys_log`
VALUES (260, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":72,\"parentId\":0,\"name\":\"服务管理\",\"url\":\"locality\",\"perms\":\"\",\"type\":1,\"icon\":\"order\",\"orderNum\":6}]',
        4, '192.168.0.110', '2022-11-09 17:10:39');
INSERT INTO `sys_log`
VALUES (261, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":131,\"parentId\":102,\"name\":\"字典\",\"url\":\"sys/dict\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhi\",\"orderNum\":0}]',
        8, '192.168.0.110', '2022-11-09 17:23:49');
INSERT INTO `sys_log`
VALUES (262, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":132,\"parentId\":131,\"name\":\"查看\",\"perms\":\"sys:dict:list,sys:dict:info\",\"type\":2,\"orderNum\":0}]',
        8, '192.168.0.110', '2022-11-09 17:26:09');
INSERT INTO `sys_log`
VALUES (263, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":133,\"parentId\":131,\"name\":\"添加\",\"perms\":\"sys:dict:save\",\"type\":2,\"orderNum\":0}]', 6,
        '192.168.0.110', '2022-11-09 17:26:54');
INSERT INTO `sys_log`
VALUES (264, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":134,\"parentId\":131,\"name\":\"修改\",\"perms\":\"sys:dict:update\",\"type\":2,\"orderNum\":0}]',
        5, '192.168.0.110', '2022-11-09 17:28:04');
INSERT INTO `sys_log`
VALUES (265, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":135,\"parentId\":131,\"name\":\"删除\",\"perms\":\"sys:dict:delete\",\"type\":2,\"orderNum\":0}]',
        6, '192.168.0.110', '2022-11-09 17:28:44');
INSERT INTO `sys_log`
VALUES (266, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        164, '192.168.0.110', '2022-11-09 17:28:59');
INSERT INTO `sys_log`
VALUES (267, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":77,\"parentId\":0,\"name\":\"认证审核\",\"url\":\"autonym\",\"perms\":\"\",\"type\":1,\"icon\":\"shangpin\",\"orderNum\":7}]',
        8, '192.168.0.113', '2022-11-18 14:15:56');
INSERT INTO `sys_log`
VALUES (268, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":118,\"parentId\":0,\"name\":\"就诊人管理\",\"url\":\"carBrandList\",\"perms\":\"\",\"type\":1,\"icon\":\"peizhilb\",\"orderNum\":6}]',
        8, '192.168.0.113', '2022-11-18 15:47:52');
INSERT INTO `sys_log`
VALUES (269, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":136,\"parentId\":0,\"name\":\"排行榜管理\",\"url\":\"top\",\"perms\":\"\",\"type\":1,\"icon\":\"tubiao\",\"orderNum\":6}]',
        9, '192.168.0.104', '2022-11-21 14:04:45');
INSERT INTO `sys_log`
VALUES (270, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":137,\"parentId\":136,\"name\":\"查看\",\"url\":\"\",\"perms\":\"top:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        8, '192.168.0.104', '2022-11-21 14:04:57');
INSERT INTO `sys_log`
VALUES (271, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        285, '192.168.0.104', '2022-11-21 14:05:10');
INSERT INTO `sys_log`
VALUES (272, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,35,36,37,39,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666,34]}]',
        347, '192.168.0.108', '2022-11-28 09:54:39');
INSERT INTO `sys_log`
VALUES (273, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        315, '192.168.0.108', '2022-11-28 09:56:17');
INSERT INTO `sys_log`
VALUES (274, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":138,\"parentId\":116,\"name\":\"测试菜单\",\"url\":\"aaa\",\"perms\":\"\",\"type\":1,\"icon\":\"pdd\",\"orderNum\":0}]',
        9, '192.168.0.108', '2022-11-28 09:57:21');
INSERT INTO `sys_log`
VALUES (275, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":139,\"parentId\":138,\"name\":\"查看\",\"url\":\"\",\"perms\":\"ddd\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        6, '192.168.0.108', '2022-11-28 09:57:41');
INSERT INTO `sys_log`
VALUES (276, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,138,139,80,81,82,83,84,85,86,87,88,89,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666,32]}]',
        280, '192.168.0.108', '2022-11-28 10:10:28');
INSERT INTO `sys_log`
VALUES (277, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,138,139,80,81,82,83,84,85,86,87,88,89,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        153, '192.168.0.108', '2022-11-28 10:11:56');
INSERT INTO `sys_log`
VALUES (278, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":15,\"username\":\"yang\",\"password\":\"c1ce784b9cb14c276347c32517443370fe19da2d39e60185ce0af22a56d781e8\",\"salt\":\"OUTatcHfGRLNN8gPYBZ9\",\"email\":\"<EMAIL>\",\"mobile\":\"17866668888\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Nov 29, 2022 1:49:10 PM\"}]',
        136, '192.168.0.110', '2022-11-29 13:49:11');
INSERT INTO `sys_log`
VALUES (279, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":131,\"parentId\":102,\"name\":\"数据字典\",\"url\":\"sys/dict\",\"type\":1,\"icon\":\"peizhi\",\"orderNum\":0}]',
        111, '127.0.0.1', '2022-12-05 08:42:26');
INSERT INTO `sys_log`
VALUES (280, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[139]', 206,
        '0:0:0:0:0:0:0:1', '2022-12-05 08:42:44');
INSERT INTO `sys_log`
VALUES (281, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[138]', 187, '127.0.0.1',
        '2022-12-05 08:42:47');
INSERT INTO `sys_log`
VALUES (282, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[138]', 180, '127.0.0.1',
        '2022-12-05 08:42:55');
INSERT INTO `sys_log`
VALUES (283, 'nyy', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[15]]', 112,
        '0:0:0:0:0:0:0:1', '2022-12-05 08:43:31');
INSERT INTO `sys_log`
VALUES (284, 'nyy', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[11]]', 77, '127.0.0.1',
        '2022-12-05 08:43:37');
INSERT INTO `sys_log`
VALUES (285, 'nyy', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[5]]', 75, '0:0:0:0:0:0:0:1',
        '2022-12-05 08:43:41');
INSERT INTO `sys_log`
VALUES (286, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":12,\"username\":\"sqx\",\"password\":\"cdbc7b2de23445336a8ac9302d8aea75d608ebd58f190260381792e8778f58db\",\"salt\":\"eVL7KZbYgMqVZvd6j74Y\",\"email\":\"<EMAIL>\",\"mobile\":\"18791000399\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2}]',
        811, '127.0.0.1', '2022-12-05 08:44:11');
INSERT INTO `sys_log`
VALUES (287, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,35,36,37,42,51,62,63,64,108,109,104,105,106,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,112,113,114,81,82,83,86,87,88,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,116,80,85,57,102,131,94,2,3,4]}]',
        5212, '127.0.0.1', '2022-12-05 08:44:59');
INSERT INTO `sys_log`
VALUES (288, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":140,\"parentId\":0,\"name\":\"优惠券管理\",\"url\":\"\",\"perms\":\"\",\"type\":0,\"icon\":\"peizhi\",\"orderNum\":9}]',
        43, '192.168.0.113', '2022-12-06 17:23:16');
INSERT INTO `sys_log`
VALUES (289, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":141,\"parentId\":140,\"name\":\"优惠券\",\"url\":\"couponYhq\",\"type\":1,\"icon\":\"peizhi\",\"orderNum\":0}]',
        65, '192.168.0.113', '2022-12-06 17:32:56');
INSERT INTO `sys_log`
VALUES (290, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":140,\"parentId\":0,\"name\":\"优惠券管理\",\"type\":0,\"icon\":\"peizhi\",\"orderNum\":8}]', 50,
        '192.168.0.113', '2022-12-06 17:34:20');
INSERT INTO `sys_log`
VALUES (291, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,140,141,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4600, '192.168.0.113', '2022-12-06 17:35:38');
INSERT INTO `sys_log`
VALUES (292, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":142,\"parentId\":141,\"name\":\"查看\",\"url\":\"\",\"perms\":\"couponYhq:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        83, '192.168.0.113', '2022-12-06 17:49:48');
INSERT INTO `sys_log`
VALUES (293, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":143,\"parentId\":141,\"name\":\"查看\",\"url\":\"\",\"perms\":\"couponYhq:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        67, '192.168.0.113', '2022-12-06 17:50:01');
INSERT INTO `sys_log`
VALUES (294, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":144,\"parentId\":141,\"name\":\"添加\",\"url\":\"\",\"perms\":\"couponYhq:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        65, '192.168.0.113', '2022-12-06 17:50:13');
INSERT INTO `sys_log`
VALUES (295, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":145,\"parentId\":141,\"name\":\"修改\",\"url\":\"\",\"perms\":\"couponYhq:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        73, '192.168.0.113', '2022-12-06 17:50:24');
INSERT INTO `sys_log`
VALUES (296, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":146,\"parentId\":141,\"name\":\"删除\",\"url\":\"\",\"perms\":\"couponYhq:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        79, '192.168.0.113', '2022-12-06 17:50:39');
INSERT INTO `sys_log`
VALUES (297, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":147,\"parentId\":141,\"name\":\"赠送\",\"url\":\"\",\"perms\":\"couponYhq:zengsong\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        60, '192.168.0.113', '2022-12-06 17:50:55');
INSERT INTO `sys_log`
VALUES (298, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":148,\"parentId\":140,\"name\":\"已发布优惠券\",\"url\":\"couponissue\",\"perms\":\"\",\"type\":1,\"icon\":\"ordercenter\",\"orderNum\":0}]',
        66, '192.168.0.113', '2022-12-06 17:52:08');
INSERT INTO `sys_log`
VALUES (299, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":149,\"parentId\":148,\"name\":\"查看\",\"url\":\"\",\"perms\":\"couponissue:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        75, '192.168.0.113', '2022-12-06 17:52:31');
INSERT INTO `sys_log`
VALUES (300, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":150,\"parentId\":148,\"name\":\"添加\",\"url\":\"\",\"perms\":\"couponissue:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        69, '192.168.0.113', '2022-12-06 17:52:46');
INSERT INTO `sys_log`
VALUES (301, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":151,\"parentId\":148,\"name\":\"修改\",\"url\":\"\",\"perms\":\"couponissue:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        64, '192.168.0.113', '2022-12-06 17:52:59');
INSERT INTO `sys_log`
VALUES (302, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":152,\"parentId\":148,\"name\":\"删除\",\"url\":\"\",\"perms\":\"couponissue:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        69, '192.168.0.113', '2022-12-06 17:53:17');
INSERT INTO `sys_log`
VALUES (303, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":153,\"parentId\":140,\"name\":\"优惠券领取记录\",\"url\":\"couponuser\",\"perms\":\"\",\"type\":1,\"icon\":\"renwu\",\"orderNum\":0}]',
        57, '192.168.0.113', '2022-12-06 17:53:58');
INSERT INTO `sys_log`
VALUES (304, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":154,\"parentId\":153,\"name\":\"查看\",\"url\":\"\",\"perms\":\"couponuser:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        55, '192.168.0.113', '2022-12-06 17:54:19');
INSERT INTO `sys_log`
VALUES (305, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4800, '192.168.0.113', '2022-12-06 17:54:43');
INSERT INTO `sys_log`
VALUES (306, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[148]', 28, '192.168.0.113',
        '2022-12-06 18:35:31');
INSERT INTO `sys_log`
VALUES (307, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[148]', 25, '192.168.0.113',
        '2022-12-06 18:35:38');
INSERT INTO `sys_log`
VALUES (308, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[152]', 110, '192.168.0.113',
        '2022-12-06 18:35:44');
INSERT INTO `sys_log`
VALUES (309, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[151]', 106, '192.168.0.113',
        '2022-12-06 18:35:56');
INSERT INTO `sys_log`
VALUES (310, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[150]', 106, '192.168.0.113',
        '2022-12-06 18:36:06');
INSERT INTO `sys_log`
VALUES (311, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[149]', 114, '192.168.0.113',
        '2022-12-06 18:36:22');
INSERT INTO `sys_log`
VALUES (312, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[148]', 106, '192.168.0.113',
        '2022-12-06 18:37:06');
INSERT INTO `sys_log`
VALUES (313, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":155,\"parentId\":62,\"name\":\"取消接单\",\"url\":\"\",\"perms\":\"orderCenter:nojorder\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        70, '192.168.0.113', '2022-12-07 17:31:00');
INSERT INTO `sys_log`
VALUES (314, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":156,\"parentId\":62,\"name\":\"取消订单\",\"url\":\"\",\"perms\":\"orderCenter:noorder\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        59, '192.168.0.113', '2022-12-07 17:31:25');
INSERT INTO `sys_log`
VALUES (315, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,116,80,81,82,83,84,85,86,87,88,89,140,141,142,143,144,145,146,147,153,154,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4490, '192.168.0.113', '2022-12-07 17:32:03');
INSERT INTO `sys_log`
VALUES (316, 'nyy', '删除菜单', 'com.sqx.modules.sys.controller.SysMenuController.delete()', '[116]', 21, '192.168.0.113',
        '2022-12-07 20:14:03');
INSERT INTO `sys_log`
VALUES (317, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,103,104,105,106,107,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,140,141,142,143,144,145,146,147,153,154,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        4842, '192.168.0.113', '2022-12-07 20:14:39');
INSERT INTO `sys_log`
VALUES (318, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,35,36,37,42,51,62,63,64,108,109,155,156,104,105,106,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,112,113,114,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,57,102,131,94,2,3,4]}]',
        3058, '192.168.0.113', '2022-12-07 20:16:25');
INSERT INTO `sys_log`
VALUES (319, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":157,\"parentId\":140,\"name\":\"充值分类\",\"url\":\"IntegralGoods\",\"perms\":\"\",\"type\":1,\"icon\":\"shuju\",\"orderNum\":3}]',
        123, '0:0:0:0:0:0:0:1', '2022-12-13 15:04:24');
INSERT INTO `sys_log`
VALUES (320, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":158,\"parentId\":153,\"name\":\"查看\",\"url\":\"\",\"perms\":\"IntegralGoods:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        118, '0:0:0:0:0:0:0:1', '2022-12-13 15:04:46');
INSERT INTO `sys_log`
VALUES (321, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":159,\"parentId\":157,\"name\":\"添加\",\"url\":\"\",\"perms\":\"IntegralGoods:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        111, '127.0.0.1', '2022-12-13 15:05:11');
INSERT INTO `sys_log`
VALUES (322, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":158,\"parentId\":157,\"name\":\"查看\",\"perms\":\"IntegralGoods:list\",\"type\":2,\"orderNum\":0}]',
        111, '0:0:0:0:0:0:0:1', '2022-12-13 15:05:31');
INSERT INTO `sys_log`
VALUES (323, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":160,\"parentId\":157,\"name\":\"修改\",\"perms\":\"IntegralGoods:update\",\"type\":2,\"orderNum\":0}]',
        110, '127.0.0.1', '2022-12-13 15:05:44');
INSERT INTO `sys_log`
VALUES (324, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":161,\"parentId\":157,\"name\":\"删除\",\"perms\":\"IntegralGoods:delete\",\"type\":2,\"orderNum\":0}]',
        119, '127.0.0.1', '2022-12-13 15:06:00');
INSERT INTO `sys_log`
VALUES (325, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":162,\"parentId\":0,\"name\":\"医院地图\",\"url\":\"shifuScheduling\",\"perms\":\"\",\"type\":1,\"icon\":\"dangdifill\",\"orderNum\":5}]',
        82, '0:0:0:0:0:0:0:1', '2022-12-13 15:09:05');
INSERT INTO `sys_log`
VALUES (326, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":163,\"parentId\":162,\"name\":\"查看\",\"url\":\"\",\"perms\":\"shifuScheduling:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        117, '127.0.0.1', '2022-12-13 15:09:25');
INSERT INTO `sys_log`
VALUES (327, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,103,104,105,106,107,162,163,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,140,141,142,143,144,145,146,147,153,154,157,158,159,160,161,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        8734, '127.0.0.1', '2022-12-13 15:09:55');
INSERT INTO `sys_log`
VALUES (328, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":162,\"parentId\":0,\"name\":\"医院地图\",\"url\":\"riderScheduling\",\"type\":1,\"icon\":\"dangdifill\",\"orderNum\":5}]',
        57, '192.168.0.101', '2022-12-14 15:47:54');
INSERT INTO `sys_log`
VALUES (329, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":163,\"parentId\":162,\"name\":\"查看\",\"perms\":\"riderScheduling:list\",\"type\":2,\"orderNum\":0}]',
        82, '192.168.0.101', '2022-12-14 15:48:08');
INSERT INTO `sys_log`
VALUES (330, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":162,\"parentId\":0,\"name\":\"医院地图\",\"url\":\"shifuScheduling\",\"type\":1,\"icon\":\"dangdifill\",\"orderNum\":5}]',
        60, '192.168.0.101', '2022-12-14 20:21:58');
INSERT INTO `sys_log`
VALUES (331, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":163,\"parentId\":162,\"name\":\"查看\",\"perms\":\"shifuScheduling:list\",\"type\":2,\"orderNum\":0}]',
        81, '192.168.0.101', '2022-12-14 20:22:11');
INSERT INTO `sys_log`
VALUES (332, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":15,\"username\":\"lhl\",\"password\":\"32e5c5a367fdedd05f7d6e4f85b84801ba7cd54c16a7a469bb39bd83a1f11eef\",\"salt\":\"na4mmU5Hwuhsrb0Tvg9y\",\"email\":\"<EMAIL>\",\"mobile\":\"17693623556\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Dec 27, 2022 6:32:33 PM\"}]',
        424, '0:0:0:0:0:0:0:1', '2022-12-27 18:32:33');
INSERT INTO `sys_log`
VALUES (333, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,35,36,37,42,51,62,63,64,108,109,155,156,104,105,106,162,163,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,112,113,114,141,142,143,144,145,146,147,153,154,158,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,140,157,57,102,131,94,2,3,4]}]',
        5093, '0:0:0:0:0:0:0:1', '2022-12-27 19:49:36');
INSERT INTO `sys_log`
VALUES (334, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":16,\"username\":\"yy\",\"password\":\"6bc1d3b54c53a1c51ea4a64d6b7dede074db861730a4f3bd4f79b65831c40b66\",\"salt\":\"PpJNBEnRcj0ORHCIXm0e\",\"email\":\"<EMAIL>\",\"mobile\":\"18888888888\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Jan 11, 2023 9:53:57 AM\"}]',
        659, '127.0.0.1', '2023-01-11 09:53:58');
INSERT INTO `sys_log`
VALUES (335, 'admin', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":17,\"username\":\"shengqianxiong\",\"password\":\"1e1f10e5c03ceda51a92843873c27d3f4616fcbb1b4edd88b4087d45d598be66\",\"salt\":\"eWwML1XyDhMY0C0fLirh\",\"email\":\"<EMAIL>\",\"mobile\":\"13312345678\",\"status\":1,\"roleIdList\":[1],\"createUserId\":1,\"createTime\":\"Mar 5, 2023 10:45:44 PM\"}]',
        908, '127.0.0.1', '2023-03-05 22:45:44');
INSERT INTO `sys_log`
VALUES (336, 'admin', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":1,\"username\":\"admin\",\"password\":\"24603812741b3903454dc2c4c56878a9961c009871a521370f1022f4cb46f2a6\",\"salt\":\"YKJpTRPDurwTYVsIhd0P\",\"email\":\"<EMAIL>\",\"mobile\":\"13612345678\",\"status\":1,\"roleIdList\":[1],\"createUserId\":1}]',
        330, '0:0:0:0:0:0:0:1', '2023-03-07 11:27:33');
INSERT INTO `sys_log`
VALUES (337, 'admin', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[7]]', 125,
        '0:0:0:0:0:0:0:1', '2023-03-07 11:42:05');
INSERT INTO `sys_log`
VALUES (338, 'admin', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[17]]', 80,
        '0:0:0:0:0:0:0:1', '2023-03-07 11:42:12');
INSERT INTO `sys_log`
VALUES (339, 'admin', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[16]]', 76, '127.0.0.1',
        '2023-03-07 11:42:17');
INSERT INTO `sys_log`
VALUES (340, 'admin', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[15]]', 80, '127.0.0.1',
        '2023-03-07 11:42:23');
INSERT INTO `sys_log`
VALUES (341, 'admin', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[6]]', 80,
        '0:0:0:0:0:0:0:1', '2023-03-07 11:42:30');
INSERT INTO `sys_log`
VALUES (342, 'admin', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":1,\"username\":\"admin\",\"password\":\"484613ca721ca5e1945ae50fb75e400ec09d85f5cbd1c56505ebd2771f74aa8d\",\"salt\":\"YKJpTRPDurwTYVsIhd0P\",\"email\":\"<EMAIL>\",\"mobile\":\"13612345678\",\"status\":1,\"roleIdList\":[1],\"createUserId\":1}]',
        339, '0:0:0:0:0:0:0:1', '2023-03-07 11:43:05');
INSERT INTO `sys_log`
VALUES (343, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":164,\"parentId\":32,\"name\":\"修改信息\",\"url\":\"\",\"perms\":\"userList:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        118, '127.0.0.1', '2023-03-07 14:35:58');
INSERT INTO `sys_log`
VALUES (344, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,164,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,103,104,105,106,107,162,163,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,111,112,113,114,115,140,141,142,143,144,145,146,147,153,154,157,158,159,160,161,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        8647, '0:0:0:0:0:0:0:1', '2023-03-07 14:36:38');
INSERT INTO `sys_log`
VALUES (345, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,164,35,36,37,42,43,51,62,63,64,108,109,155,156,104,105,106,162,163,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,112,113,114,141,142,143,144,145,146,147,153,154,158,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,140,157,57,102,131,94,2,3,4]}]',
        6318, '127.0.0.1', '2023-03-15 11:30:57');
INSERT INTO `sys_log`
VALUES (346, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,164,35,36,37,42,43,51,62,63,64,108,109,155,156,104,105,106,162,163,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,112,113,114,141,142,143,144,145,146,147,153,154,158,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,140,157,57,102,131,94,2,3,4]}]',
        5951, '0:0:0:0:0:0:0:1', '2023-03-15 11:31:04');
INSERT INTO `sys_log`
VALUES (347, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":165,\"parentId\":77,\"name\":\"修改\",\"url\":\"\",\"perms\":\"autonym:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        116, '0:0:0:0:0:0:0:1', '2023-04-11 19:47:10');
INSERT INTO `sys_log`
VALUES (348, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,164,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,103,104,105,106,107,162,163,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,165,111,112,113,114,115,140,141,142,143,144,145,146,147,153,154,157,158,159,160,161,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        7934, '127.0.0.1', '2023-04-11 19:47:41');
INSERT INTO `sys_log`
VALUES (349, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":57,\"parentId\":0,\"name\":\"系统配置\",\"url\":\"allocationList\",\"perms\":\"\",\"type\":1,\"icon\":\"menu\",\"orderNum\":19}]',
        50, '192.168.0.114', '2023-04-17 10:25:33');
INSERT INTO `sys_log`
VALUES (350, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":166,\"parentId\":0,\"name\":\"师傅位置记录\",\"url\":\"masterPosition\",\"perms\":\"\",\"type\":1,\"icon\":\"daohang\",\"orderNum\":9}]',
        60, '192.168.0.114', '2023-04-17 10:27:17');
INSERT INTO `sys_log`
VALUES (351, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":167,\"parentId\":166,\"name\":\"查看\",\"url\":\"\",\"perms\":\"masterPosition:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        68, '192.168.0.114', '2023-04-17 10:27:36');
INSERT INTO `sys_log`
VALUES (352, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":168,\"parentId\":62,\"name\":\"修改\",\"url\":\"\",\"perms\":\"orderCenter:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        66, '192.168.0.114', '2023-04-17 10:28:19');
INSERT INTO `sys_log`
VALUES (353, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,164,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,168,103,104,105,106,107,162,163,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,165,111,112,113,114,115,140,141,142,143,144,145,146,147,153,154,157,158,159,160,161,166,167,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        5426, '192.168.0.114', '2023-04-17 10:28:47');
INSERT INTO `sys_log`
VALUES (354, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":15,\"username\":\"feng\",\"password\":\"36819ba155928b4ef4f1e6ce79f2fbb319959b7ab4b254375e3695b3442f934d\",\"salt\":\"MqTq3U4y4Tq0lA7GrjmW\",\"email\":\"<EMAIL>\",\"mobile\":\"18049660620\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2,\"createTime\":\"Apr 19, 2023 1:29:44 PM\"}]',
        496, '127.0.0.1', '2023-04-19 13:29:45');
INSERT INTO `sys_log`
VALUES (355, 'nyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":16,\"username\":\"jiyy\",\"password\":\"73cdfbc2e2db4d841905838fb8fd3ce68a0613571d65edbb90e6ee8984391f68\",\"salt\":\"EsITP0U5BGKCZwrdNXCu\",\"email\":\"<EMAIL>\",\"mobile\":\"18691847635\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2,\"createTime\":\"Apr 24, 2023 10:27:00 AM\"}]',
        452, '127.0.0.1', '2023-04-24 10:27:00');
INSERT INTO `sys_log`
VALUES (356, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":12,\"username\":\"sqx\",\"password\":\"360515c889683cc7149027e7af4fc79eed82886b88851d71d37463bbcfa38006\",\"salt\":\"eVL7KZbYgMqVZvd6j74Y\",\"email\":\"<EMAIL>\",\"mobile\":\"18791000399\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2}]',
        304, '127.0.0.1', '2023-04-24 10:28:56');
INSERT INTO `sys_log`
VALUES (357, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,164,35,36,37,42,43,51,62,63,64,108,109,155,156,165,104,105,106,162,163,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,172,112,113,114,141,142,143,144,145,146,147,153,154,158,167,170,171,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,140,157,166,57,102,131,94,2,3,4]}]',
        6600, '0:0:0:0:0:0:0:1', '2023-04-24 17:39:18');
INSERT INTO `sys_log`
VALUES (358, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,164,35,36,37,42,43,51,62,63,64,108,109,155,156,165,104,105,106,162,163,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,172,112,113,114,141,142,143,144,145,146,147,153,154,158,167,170,171,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,140,157,166,57,102,131,94,2,3,4]}]',
        6275, '127.0.0.1', '2023-04-24 17:39:24');
INSERT INTO `sys_log`
VALUES (359, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":16,\"username\":\"jiyy\",\"password\":\"51db37b8ef4a30cce1185f6b5a02d022b6d7fd0453a1e31fdfb48d1665e31f6e\",\"salt\":\"EsITP0U5BGKCZwrdNXCu\",\"email\":\"<EMAIL>\",\"mobile\":\"18691847635\",\"status\":1,\"roleIdList\":[1],\"createUserId\":2}]',
        318, '0:0:0:0:0:0:0:1', '2023-04-26 15:46:12');
INSERT INTO `sys_log`
VALUES (360, 'xh', '删除科室', 'com.sqx.modules.department.controller.AdminDepartmentController.deleteDepartment()',
        '[252]', 67, '127.0.0.1', '2023-05-05 14:49:37');
INSERT INTO `sys_log`
VALUES (361, 'xh', '删除科室', 'com.sqx.modules.department.controller.AdminDepartmentController.deleteDepartment()',
        '[253]', 118, '0:0:0:0:0:0:0:1', '2023-05-05 14:49:43');
INSERT INTO `sys_log`
VALUES (362, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":4,\"roleName\":\"普通用户\",\"remark\":\"普通用户\",\"createUserId\":2,\"menuIdList\":[33,60,90,91,110,125,164,35,36,37,42,43,51,62,63,64,108,109,155,156,165,104,105,106,162,163,73,74,75,119,120,121,123,124,127,128,129,136,137,77,78,92,93,172,112,113,114,141,142,143,144,145,146,147,153,154,158,167,170,171,58,132,95,15,19,23,-666666,32,34,41,50,103,72,118,126,111,140,157,166,57,102,131,94,2,3,4]}]',
        5696, '127.0.0.1', '2023-05-05 15:11:04');
INSERT INTO `sys_log`
VALUES (363, 'nyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":12,\"username\":\"sqx\",\"password\":\"cdbc7b2de23445336a8ac9302d8aea75d608ebd58f190260381792e8778f58db\",\"salt\":\"eVL7KZbYgMqVZvd6j74Y\",\"email\":\"<EMAIL>\",\"mobile\":\"18791000399\",\"status\":1,\"roleIdList\":[4],\"createUserId\":2}]',
        300, '0:0:0:0:0:0:0:1', '2023-05-05 15:11:19');
INSERT INTO `sys_log`
VALUES (364, 'jiyy', '保存用户', 'com.sqx.modules.sys.controller.SysUserController.save()',
        '[{\"userId\":18,\"username\":\"ty\",\"password\":\"7212d6b8012e7ef9dba56ea159e1651f46374fc2c1721409440b4fd33c413c3c\",\"salt\":\"zGRKbHNy47z0Cfr6vONM\",\"email\":\"<EMAIL>\",\"mobile\":\"18691847635\",\"status\":1,\"roleIdList\":[4],\"createUserId\":16,\"createTime\":\"May 9, 2023 2:38:54 PM\"}]',
        414, '127.0.0.1', '2023-05-09 14:38:55');
INSERT INTO `sys_log`
VALUES (365, 'jiyy', '修改用户', 'com.sqx.modules.sys.controller.SysUserController.update()',
        '[{\"userId\":18,\"username\":\"ty\",\"password\":\"845b111bc28ffdd5740a06e807e6812939a28d31ba734111e395f85d259ab2e1\",\"salt\":\"zGRKbHNy47z0Cfr6vONM\",\"email\":\"<EMAIL>\",\"mobile\":\"18691847635\",\"status\":1,\"roleIdList\":[4],\"createUserId\":16}]',
        328, '0:0:0:0:0:0:0:1', '2023-05-10 16:39:51');
INSERT INTO `sys_log`
VALUES (366, 'jiyy', '删除用户', 'com.sqx.modules.sys.controller.SysUserController.delete()', '[[18]]', 85,
        '0:0:0:0:0:0:0:1', '2023-05-10 16:43:41');
INSERT INTO `sys_log`
VALUES (367, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":173,\"parentId\":140,\"name\":\"信用分配置\",\"url\":\"creditScore\",\"perms\":\"\",\"type\":1,\"icon\":\"sql\",\"orderNum\":0}]',
        11, '192.168.0.109', '2023-05-16 09:31:27');
INSERT INTO `sys_log`
VALUES (368, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":174,\"parentId\":173,\"name\":\"查看\",\"url\":\"\",\"perms\":\"creditScore:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        5, '192.168.0.109', '2023-05-16 09:31:46');
INSERT INTO `sys_log`
VALUES (369, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":175,\"parentId\":173,\"name\":\"添加\",\"url\":\"\",\"perms\":\"creditScore:add\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        6, '192.168.0.109', '2023-05-16 09:32:01');
INSERT INTO `sys_log`
VALUES (370, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":176,\"parentId\":173,\"name\":\"修改\",\"url\":\"\",\"perms\":\"creditScore:update\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        6, '192.168.0.109', '2023-05-16 09:32:15');
INSERT INTO `sys_log`
VALUES (371, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":177,\"parentId\":173,\"name\":\"删除\",\"url\":\"\",\"perms\":\"creditScore:delete\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        6, '192.168.0.109', '2023-05-16 09:32:30');
INSERT INTO `sys_log`
VALUES (372, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,164,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,165,103,104,105,106,107,162,163,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,172,111,112,113,114,115,140,141,142,143,144,145,146,147,153,154,173,174,175,176,177,157,158,159,160,161,166,167,168,169,170,171,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        305, '192.168.0.109', '2023-05-16 09:32:54');
INSERT INTO `sys_log`
VALUES (373, 'nyy', '修改菜单', 'com.sqx.modules.sys.controller.SysMenuController.update()',
        '[{\"menuId\":173,\"parentId\":140,\"name\":\"分佣配置\",\"url\":\"creditScore\",\"type\":1,\"icon\":\"sql\",\"orderNum\":0}]',
        8, '192.168.0.109', '2023-05-16 10:06:36');
INSERT INTO `sys_log`
VALUES (374, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":178,\"parentId\":0,\"name\":\"信用分明细\",\"url\":\"credit\",\"perms\":\"\",\"type\":1,\"icon\":\"log\",\"orderNum\":9}]',
        4, '192.168.0.109', '2023-05-16 15:22:49');
INSERT INTO `sys_log`
VALUES (375, 'nyy', '保存菜单', 'com.sqx.modules.sys.controller.SysMenuController.save()',
        '[{\"menuId\":179,\"parentId\":178,\"name\":\"查看\",\"url\":\"\",\"perms\":\"credit:list\",\"type\":2,\"icon\":\"\",\"orderNum\":0}]',
        6, '192.168.0.109', '2023-05-16 15:23:03');
INSERT INTO `sys_log`
VALUES (376, 'nyy', '修改角色', 'com.sqx.modules.sys.controller.SysRoleController.update()',
        '[{\"roleId\":1,\"roleName\":\"超级管理员\",\"remark\":\"super\",\"createUserId\":2,\"menuIdList\":[33,32,60,61,90,91,110,125,164,34,35,36,37,39,40,41,42,43,69,70,71,50,51,52,53,54,62,63,64,108,109,155,156,165,103,104,105,106,107,162,163,72,73,74,75,76,118,119,120,121,122,123,124,126,127,128,129,130,136,137,77,78,92,93,172,111,112,113,114,115,140,141,142,143,144,145,146,147,153,154,173,174,175,176,177,157,158,159,160,161,178,179,166,167,168,169,170,171,57,58,59,102,131,132,133,134,135,94,95,96,97,98,2,15,16,17,18,3,19,20,21,22,4,23,24,25,26,-666666]}]',
        234, '192.168.0.109', '2023-05-16 15:23:18');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`
(
    `menu_id`   bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `parent_id` bigint(20)                                                    NULL DEFAULT NULL COMMENT '父菜单ID，一级菜单为0',
    `name`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '菜单名称',
    `url`       varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单URL',
    `perms`     varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授权(多个用逗号分隔，如：user:list,user:create)',
    `type`      int(11)                                                       NULL DEFAULT NULL COMMENT '类型   0：目录   1：菜单   2：按钮',
    `icon`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '菜单图标',
    `order_num` int(11)                                                       NULL DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 180
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '菜单管理'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('2', '102', '管理员列表', 'sys/user', NULL, '1', 'admin', '10');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('3', '102', '角色管理', 'sys/role', NULL, '1', 'role', '11');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('4', '102', '菜单管理', 'sys/menu', NULL, '1', 'menu', '12');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('15', '2', '查看', NULL, 'sys:user:list,sys:user:info', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('16', '2', '新增', NULL, 'sys:user:save,sys:role:select', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('17', '2', '修改', NULL, 'sys:user:update,sys:role:select', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('18', '2', '删除', NULL, 'sys:user:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('19', '3', '查看', NULL, 'sys:role:list,sys:role:info', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('20', '3', '新增', NULL, 'sys:role:save,sys:menu:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('21', '3', '修改', NULL, 'sys:role:update,sys:menu:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('22', '3', '删除', NULL, 'sys:role:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('23', '4', '查看', NULL, 'sys:menu:list,sys:menu:info', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('24', '4', '新增', NULL, 'sys:menu:save,sys:menu:select', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('25', '4', '修改', NULL, 'sys:menu:update,sys:menu:select', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('26', '4', '删除', NULL, 'sys:menu:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('32', '0', '用户中心', 'userList', '', '1', 'yonghul', '1');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('33', '0', '数据中心', 'home', '', '1', 'shuju', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('34', '0', '财务中心', 'financeList', '', '1', 'caiwu', '1');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('35', '34', '查看', '', 'financeList:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('36', '34', '转账', '', 'financeList:transfer', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('37', '34', '退款', '', 'financeList:refund', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('39', '34', '修改', '', 'financeList:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('40', '34', '删除', '', 'financeList:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('41', '0', '消息中心', 'message', '', '1', 'xiaoxi', '1');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('42', '41', '查看', '', 'message:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('43', '41', '消息推送', '', 'message:push', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('50', '0', '首页装修', 'bannerList', '', '1', 'shangpin', '2');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('51', '50', '查看', '', 'bannerList:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('52', '50', '添加', '', 'bannerList:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('53', '50', '修改', '', 'bannerList:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('54', '50', '删除', '', 'bannerList:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('57', '0', '系统配置', 'allocationList', '', '1', 'menu', '19');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('58', '57', '查看', '', 'allocationList:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('59', '57', '修改', '', 'allocationList:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('60', '32', '查看', '', 'userList:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('61', '32', '删除', '', 'userList:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('62', '0', '订单中心', 'orderCenter', '', '1', 'log', '4');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('63', '62', '查看', '', '', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('64', '62', '删除', '', 'orderCenter:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('69', '41', '添加', '', 'message:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('70', '41', '修改', '', 'message:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('71', '41', '删除', '', 'message:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('72', '0', '服务管理', 'locality', '', '1', 'order', '6');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('73', '72', '添加', '', 'locality:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('74', '72', '查看', '', 'locality:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('75', '72', '修改', '', 'locality:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('76', '72', '删除', '', 'locality:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('77', '0', '认证审核', 'autonym', '', '1', 'shangpin', '7');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('78', '77', '列表', '', 'autonym:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('80', '116', '会员配置', 'memberDetails', '', '1', 'fenleilist', '1');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('81', '80', '查看', '', 'memberDetails:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('82', '80', '添加', '', 'memberDetails:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('83', '80', '修改', '', 'memberDetails:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('84', '80', '删除', '', 'memberDetails:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('85', '116', '会员特权', 'vipPrivilege', '', '1', 'geren', '2');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('86', '85', '列表', '', 'vipPrivilege:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('87', '85', '添加', '', 'vipPrivilege:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('88', '85', '修改', '', 'vipPrivilege:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('89', '85', '删除', '', 'vipPrivilege:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('90', '32', '修改余额', '', 'userList:updatejf', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('91', '32', '修改用户状态', '', 'userList:updateStatus', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('92', '77', '通过', '', 'autonym:tongguo', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('93', '77', '拒绝', '', 'autonym:jujue', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('94', '102', '升级配置', 'app', '', '1', 'sql', '9');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('95', '94', '查看', '', 'app:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('96', '94', '添加', '', 'app:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('97', '94', '修改', '', 'app:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('98', '94', '删除', '', 'app:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('102', '0', '系统管理', '', '', '0', 'menu', '20');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('103', '0', '医院管理', 'universalMission', '', '1', 'renwu', '5');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('104', '103', '查看', '', 'universalMission:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('105', '103', '添加', '', 'universalMission:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('106', '103', '修改', '', 'universalMission:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('107', '103', '删除', '', 'universalMission:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('108', '62', '退款', '', 'orderCenter:tuikuan', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('109', '62', '完成', '', 'orderCenter:wancheng', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('110', '32', '修改佣金', '', 'userList:updatebl', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('111', '0', '帮助中心', 'materialsList', '', '1', 'menu', '7');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('112', '111', '查看', '', 'materialsList:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('113', '111', '添加', '', 'materialsList:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('114', '111', '修改', '', 'materialsList:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('115', '111', '删除', '', 'materialsList:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('116', '0', '会员配置', '', '', '0', 'yonghu', '8');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('118', '0', '就诊人管理', 'carBrandList', '', '1', 'peizhilb', '6');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('119', '118', '查看', '', 'carBrandList:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('120', '118', '添加', '', 'carBrandList:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('121', '118', '修改', '', 'carBrandList:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('122', '118', '删除', '', 'carBrandList:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('123', '0', '职务招商', 'recruitList', '', '1', 'mudedi', '6');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('124', '123', '查看', '', 'recruitList:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('125', '32', '修改保证金', '', 'userList:updatebzj', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('126', '0', '推广代理', 'agent', '', '1', 'xinxi', '6');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('127', '126', '查看', '', 'agent:list', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('128', '126', '添加', '', 'agent:add', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('129', '126', '修改', '', 'agent:update', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('130', '126', '删除', '', 'agent:delete', '2', '', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('131', '102', '数据字典', 'sys/dict', NULL, '1', 'peizhi', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('132', '131', '查看', NULL, 'sys:dict:list,sys:dict:info', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('133', '131', '添加', NULL, 'sys:dict:save', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('134', '131', '修改', NULL, 'sys:dict:update', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('135', '131', '删除', NULL, 'sys:dict:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('136', '0', '排行榜管理', 'top', NULL, '1', 'tubiao', '6');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('137', '136', '查看', NULL, 'top:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('140', '0', '优惠券管理', NULL, NULL, '0', 'peizhi', '8');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('141', '140', '优惠券', 'couponYhq', NULL, '1', 'peizhi', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('142', '141', '查看', NULL, 'couponYhq:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('143', '141', '查看', NULL, 'couponYhq:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('144', '141', '添加', NULL, 'couponYhq:add', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('145', '141', '修改', NULL, 'couponYhq:update', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('146', '141', '删除', NULL, 'couponYhq:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('147', '141', '赠送', NULL, 'couponYhq:zengsong', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('153', '140', '优惠券领取记录', 'couponuser', NULL, '1', 'renwu', '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('154', '153', '查看', NULL, 'couponuser:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('155', '62', '取消接单', NULL, 'orderCenter:nojorder', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('156', '62', '取消订单', NULL, 'orderCenter:noorder', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('157', '140', '充值分类', 'IntegralGoods', NULL, '1', 'shuju', '3');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('158', '157', '查看', NULL, 'IntegralGoods:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('159', '157', '添加', NULL, 'IntegralGoods:add', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('160', '157', '修改', NULL, 'IntegralGoods:update', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('161', '157', '删除', NULL, 'IntegralGoods:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('162', '0', '医院地图', 'shifuScheduling', NULL, '1', 'dangdifill', '5');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('163', '162', '查看', NULL, 'shifuScheduling:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('164', '32', '修改信息', NULL, 'userList:update', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('165', '62', '修改', NULL, 'orderCenter:update', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('166', '0', '评价中心', 'comment', NULL, '1', 'xiaoxi', '10');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('167', '166', '查看', NULL, 'comment:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('168', '166', '审核', NULL, 'comment:check', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('169', '166', '删除', NULL, 'comment:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('170', '0', '师傅位置记录', 'masterPosition', NULL, '1', 'dangdifill', '11');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('171', '170', '查看', NULL, 'masterPosition:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('172', '77', '修改', NULL, 'autonym:update', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('173', '0', '陪诊师等级', 'creditScore', NULL, '1', 'sql', '7');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('174', '173', '查看', NULL, 'creditScore:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('175', '173', '添加', NULL, 'creditScore:add', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('176', '173', '修改', NULL, 'creditScore:update', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('177', '173', '删除', NULL, 'creditScore:delete', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('178', '0', '信用分明细', 'credit', NULL, '1', 'log', '9');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('179', '178', '查看', NULL, 'credit:list', '2', NULL, '0');
insert into `sys_menu` (`menu_id`, `parent_id`, `name`, `url`, `perms`, `type`, `icon`, `order_num`)
values ('180', '32', '查看信息详情', NULL, 'userList:details', '2', NULL, '0');
-- ----------------------------
-- Table structure for sys_oss
-- ----------------------------
DROP TABLE IF EXISTS `sys_oss`;
CREATE TABLE `sys_oss`
(
    `id`          bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `url`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'URL地址',
    `create_date` datetime(0)                                                   NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '文件上传'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oss
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`
(
    `role_id`        bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `role_name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '角色名称',
    `remark`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_user_id` bigint(20)                                                    NULL DEFAULT NULL COMMENT '创建者ID',
    `create_time`    datetime(0)                                                   NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '角色'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role`
VALUES (1, '超级管理员', 'super', 2, '2021-06-04 17:38:28');
INSERT INTO `sys_role`
VALUES (4, '普通用户', '普通用户', 2, '2021-09-10 15:33:47');

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`
(
    `id`      bigint(20) NOT NULL AUTO_INCREMENT,
    `role_id` bigint(20) NULL DEFAULT NULL COMMENT '角色ID',
    `menu_id` bigint(20) NULL DEFAULT NULL COMMENT '菜单ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5773
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '角色与菜单对应关系'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu`
VALUES (5433, 4, 33);
INSERT INTO `sys_role_menu`
VALUES (5434, 4, 60);
INSERT INTO `sys_role_menu`
VALUES (5435, 4, 90);
INSERT INTO `sys_role_menu`
VALUES (5436, 4, 91);
INSERT INTO `sys_role_menu`
VALUES (5437, 4, 110);
INSERT INTO `sys_role_menu`
VALUES (5438, 4, 125);
INSERT INTO `sys_role_menu`
VALUES (5439, 4, 164);
INSERT INTO `sys_role_menu`
VALUES (5440, 4, 35);
INSERT INTO `sys_role_menu`
VALUES (5441, 4, 36);
INSERT INTO `sys_role_menu`
VALUES (5442, 4, 37);
INSERT INTO `sys_role_menu`
VALUES (5443, 4, 42);
INSERT INTO `sys_role_menu`
VALUES (5444, 4, 43);
INSERT INTO `sys_role_menu`
VALUES (5445, 4, 51);
INSERT INTO `sys_role_menu`
VALUES (5446, 4, 62);
INSERT INTO `sys_role_menu`
VALUES (5447, 4, 63);
INSERT INTO `sys_role_menu`
VALUES (5448, 4, 64);
INSERT INTO `sys_role_menu`
VALUES (5449, 4, 108);
INSERT INTO `sys_role_menu`
VALUES (5450, 4, 109);
INSERT INTO `sys_role_menu`
VALUES (5451, 4, 155);
INSERT INTO `sys_role_menu`
VALUES (5452, 4, 156);
INSERT INTO `sys_role_menu`
VALUES (5453, 4, 165);
INSERT INTO `sys_role_menu`
VALUES (5454, 4, 104);
INSERT INTO `sys_role_menu`
VALUES (5455, 4, 105);
INSERT INTO `sys_role_menu`
VALUES (5456, 4, 106);
INSERT INTO `sys_role_menu`
VALUES (5457, 4, 162);
INSERT INTO `sys_role_menu`
VALUES (5458, 4, 163);
INSERT INTO `sys_role_menu`
VALUES (5459, 4, 73);
INSERT INTO `sys_role_menu`
VALUES (5460, 4, 74);
INSERT INTO `sys_role_menu`
VALUES (5461, 4, 75);
INSERT INTO `sys_role_menu`
VALUES (5462, 4, 119);
INSERT INTO `sys_role_menu`
VALUES (5463, 4, 120);
INSERT INTO `sys_role_menu`
VALUES (5464, 4, 121);
INSERT INTO `sys_role_menu`
VALUES (5465, 4, 123);
INSERT INTO `sys_role_menu`
VALUES (5466, 4, 124);
INSERT INTO `sys_role_menu`
VALUES (5467, 4, 127);
INSERT INTO `sys_role_menu`
VALUES (5468, 4, 128);
INSERT INTO `sys_role_menu`
VALUES (5469, 4, 129);
INSERT INTO `sys_role_menu`
VALUES (5470, 4, 136);
INSERT INTO `sys_role_menu`
VALUES (5471, 4, 137);
INSERT INTO `sys_role_menu`
VALUES (5472, 4, 77);
INSERT INTO `sys_role_menu`
VALUES (5473, 4, 78);
INSERT INTO `sys_role_menu`
VALUES (5474, 4, 92);
INSERT INTO `sys_role_menu`
VALUES (5475, 4, 93);
INSERT INTO `sys_role_menu`
VALUES (5476, 4, 172);
INSERT INTO `sys_role_menu`
VALUES (5477, 4, 112);
INSERT INTO `sys_role_menu`
VALUES (5478, 4, 113);
INSERT INTO `sys_role_menu`
VALUES (5479, 4, 114);
INSERT INTO `sys_role_menu`
VALUES (5480, 4, 141);
INSERT INTO `sys_role_menu`
VALUES (5481, 4, 142);
INSERT INTO `sys_role_menu`
VALUES (5482, 4, 143);
INSERT INTO `sys_role_menu`
VALUES (5483, 4, 144);
INSERT INTO `sys_role_menu`
VALUES (5484, 4, 145);
INSERT INTO `sys_role_menu`
VALUES (5485, 4, 146);
INSERT INTO `sys_role_menu`
VALUES (5486, 4, 147);
INSERT INTO `sys_role_menu`
VALUES (5487, 4, 153);
INSERT INTO `sys_role_menu`
VALUES (5488, 4, 154);
INSERT INTO `sys_role_menu`
VALUES (5489, 4, 158);
INSERT INTO `sys_role_menu`
VALUES (5490, 4, 167);
INSERT INTO `sys_role_menu`
VALUES (5491, 4, 170);
INSERT INTO `sys_role_menu`
VALUES (5492, 4, 171);
INSERT INTO `sys_role_menu`
VALUES (5493, 4, 58);
INSERT INTO `sys_role_menu`
VALUES (5494, 4, 132);
INSERT INTO `sys_role_menu`
VALUES (5495, 4, 95);
INSERT INTO `sys_role_menu`
VALUES (5496, 4, 15);
INSERT INTO `sys_role_menu`
VALUES (5497, 4, 19);
INSERT INTO `sys_role_menu`
VALUES (5498, 4, 23);
INSERT INTO `sys_role_menu`
VALUES (5499, 4, -666666);
INSERT INTO `sys_role_menu`
VALUES (5500, 4, 32);
INSERT INTO `sys_role_menu`
VALUES (5501, 4, 34);
INSERT INTO `sys_role_menu`
VALUES (5502, 4, 41);
INSERT INTO `sys_role_menu`
VALUES (5503, 4, 50);
INSERT INTO `sys_role_menu`
VALUES (5504, 4, 103);
INSERT INTO `sys_role_menu`
VALUES (5505, 4, 72);
INSERT INTO `sys_role_menu`
VALUES (5506, 4, 118);
INSERT INTO `sys_role_menu`
VALUES (5507, 4, 126);
INSERT INTO `sys_role_menu`
VALUES (5508, 4, 111);
INSERT INTO `sys_role_menu`
VALUES (5509, 4, 140);
INSERT INTO `sys_role_menu`
VALUES (5510, 4, 157);
INSERT INTO `sys_role_menu`
VALUES (5511, 4, 166);
INSERT INTO `sys_role_menu`
VALUES (5512, 4, 57);
INSERT INTO `sys_role_menu`
VALUES (5513, 4, 102);
INSERT INTO `sys_role_menu`
VALUES (5514, 4, 131);
INSERT INTO `sys_role_menu`
VALUES (5515, 4, 94);
INSERT INTO `sys_role_menu`
VALUES (5516, 4, 2);
INSERT INTO `sys_role_menu`
VALUES (5517, 4, 3);
INSERT INTO `sys_role_menu`
VALUES (5518, 4, 4);
INSERT INTO `sys_role_menu`
VALUES (5645, 1, 33);
INSERT INTO `sys_role_menu`
VALUES (5646, 1, 32);
INSERT INTO `sys_role_menu`
VALUES (5647, 1, 60);
INSERT INTO `sys_role_menu`
VALUES (5648, 1, 61);
INSERT INTO `sys_role_menu`
VALUES (5649, 1, 90);
INSERT INTO `sys_role_menu`
VALUES (5650, 1, 91);
INSERT INTO `sys_role_menu`
VALUES (5651, 1, 110);
INSERT INTO `sys_role_menu`
VALUES (5652, 1, 125);
INSERT INTO `sys_role_menu`
VALUES (5653, 1, 164);
INSERT INTO `sys_role_menu`
VALUES (5654, 1, 34);
INSERT INTO `sys_role_menu`
VALUES (5655, 1, 35);
INSERT INTO `sys_role_menu`
VALUES (5656, 1, 36);
INSERT INTO `sys_role_menu`
VALUES (5657, 1, 37);
INSERT INTO `sys_role_menu`
VALUES (5658, 1, 39);
INSERT INTO `sys_role_menu`
VALUES (5659, 1, 40);
INSERT INTO `sys_role_menu`
VALUES (5660, 1, 41);
INSERT INTO `sys_role_menu`
VALUES (5661, 1, 42);
INSERT INTO `sys_role_menu`
VALUES (5662, 1, 43);
INSERT INTO `sys_role_menu`
VALUES (5663, 1, 69);
INSERT INTO `sys_role_menu`
VALUES (5664, 1, 70);
INSERT INTO `sys_role_menu`
VALUES (5665, 1, 71);
INSERT INTO `sys_role_menu`
VALUES (5666, 1, 50);
INSERT INTO `sys_role_menu`
VALUES (5667, 1, 51);
INSERT INTO `sys_role_menu`
VALUES (5668, 1, 52);
INSERT INTO `sys_role_menu`
VALUES (5669, 1, 53);
INSERT INTO `sys_role_menu`
VALUES (5670, 1, 54);
INSERT INTO `sys_role_menu`
VALUES (5671, 1, 62);
INSERT INTO `sys_role_menu`
VALUES (5672, 1, 63);
INSERT INTO `sys_role_menu`
VALUES (5673, 1, 64);
INSERT INTO `sys_role_menu`
VALUES (5674, 1, 108);
INSERT INTO `sys_role_menu`
VALUES (5675, 1, 109);
INSERT INTO `sys_role_menu`
VALUES (5676, 1, 155);
INSERT INTO `sys_role_menu`
VALUES (5677, 1, 156);
INSERT INTO `sys_role_menu`
VALUES (5678, 1, 165);
INSERT INTO `sys_role_menu`
VALUES (5679, 1, 103);
INSERT INTO `sys_role_menu`
VALUES (5680, 1, 104);
INSERT INTO `sys_role_menu`
VALUES (5681, 1, 105);
INSERT INTO `sys_role_menu`
VALUES (5682, 1, 106);
INSERT INTO `sys_role_menu`
VALUES (5683, 1, 107);
INSERT INTO `sys_role_menu`
VALUES (5684, 1, 162);
INSERT INTO `sys_role_menu`
VALUES (5685, 1, 163);
INSERT INTO `sys_role_menu`
VALUES (5686, 1, 72);
INSERT INTO `sys_role_menu`
VALUES (5687, 1, 73);
INSERT INTO `sys_role_menu`
VALUES (5688, 1, 74);
INSERT INTO `sys_role_menu`
VALUES (5689, 1, 75);
INSERT INTO `sys_role_menu`
VALUES (5690, 1, 76);
INSERT INTO `sys_role_menu`
VALUES (5691, 1, 118);
INSERT INTO `sys_role_menu`
VALUES (5692, 1, 119);
INSERT INTO `sys_role_menu`
VALUES (5693, 1, 120);
INSERT INTO `sys_role_menu`
VALUES (5694, 1, 121);
INSERT INTO `sys_role_menu`
VALUES (5695, 1, 122);
INSERT INTO `sys_role_menu`
VALUES (5696, 1, 123);
INSERT INTO `sys_role_menu`
VALUES (5697, 1, 124);
INSERT INTO `sys_role_menu`
VALUES (5698, 1, 126);
INSERT INTO `sys_role_menu`
VALUES (5699, 1, 127);
INSERT INTO `sys_role_menu`
VALUES (5700, 1, 128);
INSERT INTO `sys_role_menu`
VALUES (5701, 1, 129);
INSERT INTO `sys_role_menu`
VALUES (5702, 1, 130);
INSERT INTO `sys_role_menu`
VALUES (5703, 1, 136);
INSERT INTO `sys_role_menu`
VALUES (5704, 1, 137);
INSERT INTO `sys_role_menu`
VALUES (5705, 1, 77);
INSERT INTO `sys_role_menu`
VALUES (5706, 1, 78);
INSERT INTO `sys_role_menu`
VALUES (5707, 1, 92);
INSERT INTO `sys_role_menu`
VALUES (5708, 1, 93);
INSERT INTO `sys_role_menu`
VALUES (5709, 1, 172);
INSERT INTO `sys_role_menu`
VALUES (5710, 1, 111);
INSERT INTO `sys_role_menu`
VALUES (5711, 1, 112);
INSERT INTO `sys_role_menu`
VALUES (5712, 1, 113);
INSERT INTO `sys_role_menu`
VALUES (5713, 1, 114);
INSERT INTO `sys_role_menu`
VALUES (5714, 1, 115);
INSERT INTO `sys_role_menu`
VALUES (5715, 1, 140);
INSERT INTO `sys_role_menu`
VALUES (5716, 1, 141);
INSERT INTO `sys_role_menu`
VALUES (5717, 1, 142);
INSERT INTO `sys_role_menu`
VALUES (5718, 1, 143);
INSERT INTO `sys_role_menu`
VALUES (5719, 1, 144);
INSERT INTO `sys_role_menu`
VALUES (5720, 1, 145);
INSERT INTO `sys_role_menu`
VALUES (5721, 1, 146);
INSERT INTO `sys_role_menu`
VALUES (5722, 1, 147);
INSERT INTO `sys_role_menu`
VALUES (5723, 1, 153);
INSERT INTO `sys_role_menu`
VALUES (5724, 1, 154);
INSERT INTO `sys_role_menu`
VALUES (5725, 1, 173);
INSERT INTO `sys_role_menu`
VALUES (5726, 1, 174);
INSERT INTO `sys_role_menu`
VALUES (5727, 1, 175);
INSERT INTO `sys_role_menu`
VALUES (5728, 1, 176);
INSERT INTO `sys_role_menu`
VALUES (5729, 1, 177);
INSERT INTO `sys_role_menu`
VALUES (5730, 1, 157);
INSERT INTO `sys_role_menu`
VALUES (5731, 1, 158);
INSERT INTO `sys_role_menu`
VALUES (5732, 1, 159);
INSERT INTO `sys_role_menu`
VALUES (5733, 1, 160);
INSERT INTO `sys_role_menu`
VALUES (5734, 1, 161);
INSERT INTO `sys_role_menu`
VALUES (5735, 1, 178);
INSERT INTO `sys_role_menu`
VALUES (5736, 1, 179);
INSERT INTO `sys_role_menu`
VALUES (5737, 1, 166);
INSERT INTO `sys_role_menu`
VALUES (5738, 1, 167);
INSERT INTO `sys_role_menu`
VALUES (5739, 1, 168);
INSERT INTO `sys_role_menu`
VALUES (5740, 1, 169);
INSERT INTO `sys_role_menu`
VALUES (5741, 1, 170);
INSERT INTO `sys_role_menu`
VALUES (5742, 1, 171);
INSERT INTO `sys_role_menu`
VALUES (5743, 1, 57);
INSERT INTO `sys_role_menu`
VALUES (5744, 1, 58);
INSERT INTO `sys_role_menu`
VALUES (5745, 1, 59);
INSERT INTO `sys_role_menu`
VALUES (5746, 1, 102);
INSERT INTO `sys_role_menu`
VALUES (5747, 1, 131);
INSERT INTO `sys_role_menu`
VALUES (5748, 1, 132);
INSERT INTO `sys_role_menu`
VALUES (5749, 1, 133);
INSERT INTO `sys_role_menu`
VALUES (5750, 1, 134);
INSERT INTO `sys_role_menu`
VALUES (5751, 1, 135);
INSERT INTO `sys_role_menu`
VALUES (5752, 1, 94);
INSERT INTO `sys_role_menu`
VALUES (5753, 1, 95);
INSERT INTO `sys_role_menu`
VALUES (5754, 1, 96);
INSERT INTO `sys_role_menu`
VALUES (5755, 1, 97);
INSERT INTO `sys_role_menu`
VALUES (5756, 1, 98);
INSERT INTO `sys_role_menu`
VALUES (5757, 1, 2);
INSERT INTO `sys_role_menu`
VALUES (5758, 1, 15);
INSERT INTO `sys_role_menu`
VALUES (5759, 1, 16);
INSERT INTO `sys_role_menu`
VALUES (5760, 1, 17);
INSERT INTO `sys_role_menu`
VALUES (5761, 1, 18);
INSERT INTO `sys_role_menu`
VALUES (5762, 1, 3);
INSERT INTO `sys_role_menu`
VALUES (5763, 1, 19);
INSERT INTO `sys_role_menu`
VALUES (5764, 1, 20);
INSERT INTO `sys_role_menu`
VALUES (5765, 1, 21);
INSERT INTO `sys_role_menu`
VALUES (5766, 1, 22);
INSERT INTO `sys_role_menu`
VALUES (5767, 1, 4);
INSERT INTO `sys_role_menu`
VALUES (5768, 1, 23);
INSERT INTO `sys_role_menu`
VALUES (5769, 1, 24);
INSERT INTO `sys_role_menu`
VALUES (5770, 1, 25);
INSERT INTO `sys_role_menu`
VALUES (5771, 1, 26);
INSERT INTO `sys_role_menu`
VALUES (5772, 1, -666666);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`
(
    `user_id`        bigint(20)                                                    NOT NULL AUTO_INCREMENT,
    `username`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户名',
    `password`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
    `salt`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '盐',
    `email`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
    `mobile`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `status`         tinyint(4)                                                    NULL DEFAULT NULL COMMENT '状态  0：禁用   1：正常',
    `create_user_id` bigint(20)                                                    NULL DEFAULT NULL COMMENT '创建者ID',
    `create_time`    datetime(0)                                                   NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`user_id`) USING BTREE,
    UNIQUE INDEX `username` (`username`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 17
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '系统用户'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user`
VALUES (1, 'admin', 'b1672b04628a52e4a3d7a472c691564c7b9d92e3ee38abd96f78bab92446b9a2', 'gOQpg687tI4FgraTuw2v',
        '<EMAIL>', '13612345678', 1, 1, '2016-11-11 11:11:11');
-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`
(
    `id`      bigint(20) NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
    `role_id` bigint(20) NULL DEFAULT NULL COMMENT '角色ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 38
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户与角色对应关系'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role`
VALUES (2, 4, 1);
INSERT INTO `sys_user_role`
VALUES (3, 5, 1);
INSERT INTO `sys_user_role`
VALUES (4, 6, 1);
INSERT INTO `sys_user_role`
VALUES (5, 7, 1);
INSERT INTO `sys_user_role`
VALUES (7, 9, 1);
INSERT INTO `sys_user_role`
VALUES (8, 8, 1);
INSERT INTO `sys_user_role`
VALUES (10, 11, 1);
INSERT INTO `sys_user_role`
VALUES (16, 2, 1);
INSERT INTO `sys_user_role`
VALUES (17, 13, 1);
INSERT INTO `sys_user_role`
VALUES (20, 10, 4);
INSERT INTO `sys_user_role`
VALUES (21, 10, 1);
INSERT INTO `sys_user_role`
VALUES (22, 14, 1);
INSERT INTO `sys_user_role`
VALUES (28, 17, 1);
INSERT INTO `sys_user_role`
VALUES (30, 1, 1);
INSERT INTO `sys_user_role`
VALUES (31, 15, 1);
INSERT INTO `sys_user_role`
VALUES (34, 16, 1);
INSERT INTO `sys_user_role`
VALUES (35, 12, 4);
INSERT INTO `sys_user_role`
VALUES (37, 18, 4);

-- ----------------------------
-- Table structure for sys_user_token
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_token`;
CREATE TABLE `sys_user_token`
(
    `user_id`     bigint(20)                                                    NOT NULL,
    `token`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'token',
    `expire_time` datetime(0)                                                   NULL DEFAULT NULL COMMENT '过期时间',
    `update_time` datetime(0)                                                   NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`user_id`) USING BTREE,
    UNIQUE INDEX `token` (`token`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '系统用户Token'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_token
-- ----------------------------
INSERT INTO `sys_user_token`
VALUES (1, '8742bdb155ffa6a2795920bea8180cb9', '2023-03-07 23:26:27', '2023-03-07 11:26:27');
INSERT INTO `sys_user_token`
VALUES (2, '39193451ef535947ba59b4459b4e793d', '2023-05-25 22:16:30', '2023-05-25 10:16:30');
INSERT INTO `sys_user_token`
VALUES (4, 'ee9b7ede1bc6795d0055dd8673a8c6f0', '2021-08-06 21:37:45', '2021-08-06 09:37:45');
INSERT INTO `sys_user_token`
VALUES (5, 'e73de6f65899660ff9c5a32953a0cca4', '2022-11-24 04:22:47', '2022-11-23 16:22:47');
INSERT INTO `sys_user_token`
VALUES (6, 'cc8f74a1c68f034af323803a23466c6b', '2022-12-28 09:14:29', '2022-12-27 21:14:29');
INSERT INTO `sys_user_token`
VALUES (7, 'cf8e426dc620b18fb242bc68db5e96c1', '2021-11-08 22:57:14', '2021-11-08 10:57:14');
INSERT INTO `sys_user_token`
VALUES (10, '0d9d643fd3c2c51c08185b22a315cbb9', '2023-05-25 21:44:46', '2023-05-25 09:44:46');
INSERT INTO `sys_user_token`
VALUES (11, 'f63a8c6a75dcaae94575ae4233231cd8', '2021-09-22 23:27:52', '2021-09-22 11:27:52');
INSERT INTO `sys_user_token`
VALUES (12, 'dcfe90fb0e9048e4c2da6754e2395063', '2023-05-12 23:42:29', '2023-05-12 11:42:29');
INSERT INTO `sys_user_token`
VALUES (13, '6cbefcce39136c07d4eacc5eb02b5f3d', '2022-03-30 21:06:11', '2022-03-30 09:06:11');
INSERT INTO `sys_user_token`
VALUES (14, '716b71dc344da620c4d3d9679ab9c2cb', '2023-05-18 22:35:14', '2023-05-18 10:35:14');
INSERT INTO `sys_user_token`
VALUES (15, '5b6d0a2498e22b9c7bdbc3ef37bed5c3', '2023-04-27 04:36:19', '2023-04-26 16:36:19');
INSERT INTO `sys_user_token`
VALUES (16, 'de6fc9816a2d794d9a73d0713439deec', '2023-05-24 21:52:59', '2023-05-24 09:52:59');
INSERT INTO `sys_user_token`
VALUES (17, 'f7fbcffd5a1fc11d0051f912751f15c1', '2023-03-06 10:45:58', '2023-03-05 22:45:58');
INSERT INTO `sys_user_token`
VALUES (18, '659d144923af398586563656aec12e0e', '2023-05-11 02:46:36', '2023-05-10 14:46:36');

-- ----------------------------
-- Table structure for taking_commnt
-- ----------------------------
DROP TABLE IF EXISTS `taking_commnt`;
CREATE TABLE `taking_commnt`
(
    `id`              bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '接单评论id',
    `order_taking_id` bigint(20)                                                    NULL DEFAULT NULL COMMENT '接单id',
    `user_id`         bigint(20)                                                    NULL DEFAULT NULL COMMENT '用户id',
    `content`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评论内容',
    `create_time`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `mail`            varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '联系方式',
    `score`           int(11)                                                       NULL DEFAULT NULL COMMENT '评分',
    `orders_id`       int(11)                                                       NULL DEFAULT NULL COMMENT '评论id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 95
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of taking_commnt
-- ----------------------------

-- ----------------------------
-- Table structure for tb_coupon
-- ----------------------------
DROP TABLE IF EXISTS `tb_coupon`;
CREATE TABLE `tb_coupon`
(
    `coupon_id`      int(11)                                                       NOT NULL AUTO_INCREMENT,
    `coupon_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券名称',
    `coupon_picture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券图片',
    `valid_days`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '有效期天数',
    `min_money`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券可使用订单最低金额',
    `money`          decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '优惠券抵扣金额',
    `delete_flag`    int(1)                                                        NULL DEFAULT NULL COMMENT '是否删除 0未删除 1已删除',
    `is_enable`      int(1)                                                        NULL DEFAULT NULL COMMENT '是否启用 0未启用 1已启用',
    `coupon_type`    int(1)                                                        NULL DEFAULT NULL COMMENT '1新手赠送  3免费领取',
    `max_receive`    int(11)                                                       NULL DEFAULT NULL COMMENT '最多领取或购买数量(0为不限制数量)',
    `coupon_num`     int(11)                                                       NULL DEFAULT NULL COMMENT '优惠券数量',
    PRIMARY KEY (`coupon_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 14
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_coupon
-- ----------------------------
INSERT INTO `tb_coupon`
VALUES (1, '新人注册送优惠券',
        'https://img1.baidu.com/it/u=3009731526,373851691&amp;fm=253&amp;fmt=auto&amp;app=138&amp;f=JPEG?w=800&amp;h=500',
        '7', '10.01', 10.00, 0, 1, 1, 1, 1);
INSERT INTO `tb_coupon`
VALUES (6, '活动优惠券一', 'https://kuaidi.xianmaxiong.com/file/uploadPath/2022/11/23/d72cf5127bb5663d188d45af1eb052af.png',
        '1', '8', 5.00, 0, 1, 3, 2, 1);
INSERT INTO `tb_coupon`
VALUES (8, '测试优惠券', 'https://kuaidi.xianmaxiong.com/file/uploadPath/2022/12/06/065a5b9dd00329fd7a3355f017a0717e.png',
        '1', '11', 10.00, 0, 1, 3, 2, 1);
INSERT INTO `tb_coupon`
VALUES (9, '活动优惠券二', 'https://kuaidi.xianmaxiong.com/file/uploadPath/2022/11/23/d72cf5127bb5663d188d45af1eb052af.png',
        '0', '0', 10.00, 0, 1, 3, 2, 1);
INSERT INTO `tb_coupon`
VALUES (10, '活动优惠券三', 'https://kuaidi.xianmaxiong.com/file/uploadPath/2022/11/23/d72cf5127bb5663d188d45af1eb052af.png',
        '0', '0', 10.00, 0, 1, 3, 0, 1);
INSERT INTO `tb_coupon`
VALUES (11, '活动优惠券四', 'https://kuaidi.xianmaxiong.com/file/uploadPath/2022/11/23/d72cf5127bb5663d188d45af1eb052af.png',
        '0', '0', 10.00, 0, 1, 3, 0, 1);
INSERT INTO `tb_coupon`
VALUES (12, '活动优惠券五', 'https://kuaidi.xianmaxiong.com/file/uploadPath/2022/11/23/d72cf5127bb5663d188d45af1eb052af.png',
        '0', '0', 10.00, 0, 1, 3, 0, 1);
INSERT INTO `tb_coupon`
VALUES (13, '普通用户邀请赠送优惠券',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/04/18/f1adc5c74232ac0b4bf12f66a505918a.jpg', '7', '5.01',
        5.00, 0, 1, 3, 1, 1);

-- ----------------------------
-- Table structure for tb_coupon_user
-- ----------------------------
DROP TABLE IF EXISTS `tb_coupon_user`;
CREATE TABLE `tb_coupon_user`
(
    `id`              int(11)                                                       NOT NULL AUTO_INCREMENT,
    `user_id`         int(11)                                                       NULL DEFAULT NULL COMMENT '用户id',
    `coupon_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券名称',
    `coupon_picture`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券图片',
    `create_time`     datetime(0)                                                   NULL DEFAULT NULL COMMENT '优惠券领取时间',
    `employ_time`     datetime(0)                                                   NULL DEFAULT NULL COMMENT '优惠券使用时间',
    `expiration_time` datetime(0)                                                   NULL DEFAULT NULL COMMENT '优惠券过期时间',
    `min_money`       decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '优惠券可使用订单最低金额',
    `money`           decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '优惠券金额',
    `status`          int(11)                                                       NULL DEFAULT NULL COMMENT '优惠券状态 0正常  1已使用  2已失效',
    `valid_days`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '有效天数',
    `coupon_id`       int(11)                                                       NULL DEFAULT NULL COMMENT '绑定的优惠券id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2561
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_coupon_user
-- ----------------------------

-- ----------------------------
-- Table structure for tb_user
-- ----------------------------
DROP TABLE IF EXISTS `tb_user`;
CREATE TABLE `tb_user`
(
    `user_id`           bigint(20)                                                     NOT NULL AUTO_INCREMENT COMMENT '用户id',
    `user_name`         varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '用户名',
    `phone`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '手机号',
    `avatar`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '头像',
    `sex`               int(11)                                                        NULL DEFAULT NULL COMMENT '性别 1男 2女',
    `age`               int(4)                                                         NULL DEFAULT NULL COMMENT '年龄',
    `open_id`           varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '微信小程序openId',
    `wx_open_id`        varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '微信App  openId',
    `password`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '密码',
    `create_time`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '更新时间',
    `apple_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '苹果id',
    `sys_phone`         int(11)                                                        NULL DEFAULT NULL COMMENT '手机类型 1安卓 2ios',
    `status`            int(11)                                                        NULL DEFAULT NULL COMMENT '状态 1正常 2禁用',
    `platform`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '来源 APP 小程序  公众号',
    `jifen`             int(11)                                                        NULL DEFAULT NULL COMMENT '积分',
    `invitation_code`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '邀请码',
    `inviter_code`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '邀请人邀请码',
    `clientid`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT 'app消息推送',
    `zhi_fu_bao_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付宝名称',
    `zhi_fu_bao`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '支付宝账号',
    `is_authentication` int(11)                                                        NULL DEFAULT NULL COMMENT '是否认证',
    `shop_open_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '师傅小程序openId',
    `details`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '简介',
    `details_img`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '主页轮播图',
    `certification_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '资质',
    `wx_img`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '微信二维码',
    `shop_img`          varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商铺轮播图',
    `shop_name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '商铺名称',
    `address_img`       varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商铺地址图',
    `start_time`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '开始时间',
    `end_time`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '结束时间',
    `shop_type`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '商铺类型',
    `longitude`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '经度',
    `latitude`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '纬度',
    `address`           varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
    `shop_phone`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '店铺手机号',
    `is_send_msg`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL,
    `rate`              decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '佣金',
    `zhi_rate`          decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '直属佣金',
    `fei_rate`          decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '非直属佣金',
    `is_safety_money`   int(11)                                                        NULL DEFAULT NULL COMMENT '是否缴纳保证金 1是',
    `is_promotion`      int(255)                                                       NULL DEFAULT NULL COMMENT '是否是推广员 1是',
    `agent_rate`        decimal(10, 2)                                                 NULL DEFAULT NULL COMMENT '代理商佣金',
    `is_agent`          int(255)                                                       NULL DEFAULT NULL COMMENT '是否是代理 1是',
    `province`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '省',
    `city`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '市',
    `district`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '区',
    `final_score`       double(10, 1)                                                  NULL DEFAULT 5.0 COMMENT '评分',
    `is_new_people`     int(11)                                                        NULL DEFAULT 1 COMMENT '是否是新人 1是 0否',
    `credit_score`      int(3) UNSIGNED                                                NULL DEFAULT 10 COMMENT '信用分',
    `reward_id`         int(11)                                                        NULL DEFAULT NULL COMMENT '等级id',
    `icon_img`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci          NULL COMMENT '等级图片',
    `level_name`        varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL DEFAULT NULL COMMENT '等级名称',
    `shop_state`        int(1)                                                         NULL DEFAULT 0 COMMENT '接单状态:0下线 1上线',
    `account_open_id`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '公众号openId',
    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 116061
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户'
  ROW_FORMAT = Dynamic;

INSERT INTO `tb_user`
VALUES (1, '官方', '***********',
        'https://zp.xianmaxiong.com/file/uploadPath/2023/03/07/eeca5df417f4b1ac2f5e6ffd748fe36f.jpg', 1, 24,
        'otlsY5fd7feZKE1irYL8rMLQ8NMg', 'o9sjm57zb2wYT9X4qrOSqThH6wF0',
        '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92', '2021-08-28 15:38:05',
        '2022-08-16 14:14:26', NULL, NULL, 1, 'H5', NULL, '666666', '666666', NULL, '', '', 1, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.70, 0.10, 0.05, 1, 1, NULL, 0, NULL,
        NULL, NULL, 5.0, 1, '10', '2',
        'https://peizhen.xianmaxiong.com/file/uploadPath/2023/05/18/99b66980483a9bbc7cafe8a1f7840aa6.png', '铜牌', '0',
        NULL);

-- ----------------------------
-- Records of tb_user
-- ----------------------------

-- ----------------------------
-- Table structure for user_browse
-- ----------------------------
DROP TABLE IF EXISTS `user_browse`;
CREATE TABLE `user_browse`
(
    `id`           bigint(20)                                                   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id`      bigint(20)                                                   NULL DEFAULT NULL COMMENT '用户id',
    `by_browse_id` bigint(20)                                                   NULL DEFAULT NULL COMMENT '浏览用户id',
    `update_time`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新时间',
    `taking_id`    bigint(20)                                                   NULL DEFAULT NULL COMMENT '接单id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1813
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_browse
-- ----------------------------

-- ----------------------------
-- Table structure for user_certification
-- ----------------------------
DROP TABLE IF EXISTS `user_certification`;
CREATE TABLE `user_certification`
(
    `id`             bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '用户实名认证id',
    `name`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '真实姓名',
    `id_number`      varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '身份证号码',
    `user_id`        bigint(20)                                                    NULL DEFAULT NULL COMMENT '用户id',
    `create_time`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '创建时间',
    `front`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '正面照',
    `back`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '反面照',
    `status`         int(255)                                                      NULL DEFAULT NULL COMMENT '0审核中1审核成功2拒绝',
    `remek`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
    `update_time`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '修改时间',
    `phone`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
    `birth`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出生日期',
    `sex`            int(1)                                                        NULL DEFAULT NULL COMMENT '性别',
    `authentication` int(1)                                                        NULL DEFAULT NULL COMMENT '0普通用户 1陪诊认证 2陪护认证',
    `work_age`       int(11)                                                       NULL DEFAULT NULL COMMENT '护龄时长',
    `age`            int(11)                                                       NULL DEFAULT NULL COMMENT '年龄',
    `province`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '省',
    `city`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '市',
    `district`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '区/县',
    `aptitude`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '其他资质',
    `avatar`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '头像',
    `service_ids`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '服务项目id',
    `details`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '个人简介',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 151
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_certification
-- ----------------------------

-- ----------------------------
-- Table structure for user_follow
-- ----------------------------
DROP TABLE IF EXISTS `user_follow`;
CREATE TABLE `user_follow`
(
    `follow_id`      bigint(20)                                                   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id`        bigint(20)                                                   NULL DEFAULT NULL COMMENT '用户id',
    `follow_user_id` bigint(20)                                                   NULL DEFAULT NULL COMMENT '关注用户id',
    `create_time`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`follow_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 167
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_follow
-- ----------------------------

-- ----------------------------
-- Table structure for user_money
-- ----------------------------
DROP TABLE IF EXISTS `user_money`;
CREATE TABLE `user_money`
(
    `id`               bigint(20)                                                   NOT NULL AUTO_INCREMENT COMMENT '钱包id',
    `money`            decimal(10, 2) UNSIGNED                                      NULL DEFAULT 0.00 COMMENT '钱包金额',
    `user_id`          bigint(11)                                                   NULL DEFAULT NULL COMMENT '用户id',
    `safety_money`     decimal(10, 2)                                               NULL DEFAULT NULL COMMENT '保证金',
    `order_no`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号',
    `safety_money_way` int(11)                                                      NULL DEFAULT NULL COMMENT '支付方式 1微信 2支付宝',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1037
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_money
-- ----------------------------

-- ----------------------------
-- Table structure for user_money_details
-- ----------------------------
DROP TABLE IF EXISTS `user_money_details`;
CREATE TABLE `user_money_details`
(
    `id`          int(11)                                                       NOT NULL AUTO_INCREMENT COMMENT '钱包详情id',
    `user_id`     int(11)                                                       NULL DEFAULT NULL COMMENT '用户id',
    `by_user_id`  int(11)                                                       NULL DEFAULT NULL COMMENT '邀请用户id',
    `title`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
    `classify`    int(11)                                                       NULL DEFAULT NULL COMMENT '1注册   2购买 3提现',
    `type`        int(11)                                                       NULL DEFAULT NULL COMMENT '类型（1充值 2.提现）',
    `state`       int(11)                                                       NULL DEFAULT 1 COMMENT '状态 1待支付 2已到账 3取消',
    `money`       decimal(10, 2)                                                NULL DEFAULT NULL COMMENT '金额',
    `content`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
    `create_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         NULL DEFAULT NULL COMMENT '创建时间',
    `parent_id`   int(11)                                                       NULL DEFAULT NULL COMMENT '关联id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 681
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_money_details
-- ----------------------------

-- ----------------------------
-- Table structure for user_vip
-- ----------------------------
DROP TABLE IF EXISTS `user_vip`;
CREATE TABLE `user_vip`
(
    `vip_id`        bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '会员id',
    `vip_name_type` int(2)                                                        NULL DEFAULT NULL COMMENT '会员类型0月1季2年',
    `user_id`       bigint(20)                                                    NULL DEFAULT NULL COMMENT '用户id',
    `create_time`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '购买时间',
    `end_time`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '到期时间',
    `is_vip`        int(255)                                                      NULL DEFAULT NULL COMMENT '1是会员2不是',
    PRIMARY KEY (`vip_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 31
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_vip
-- ----------------------------

-- ----------------------------
-- Table structure for user_visitor
-- ----------------------------
DROP TABLE IF EXISTS `user_visitor`;
CREATE TABLE `user_visitor`
(
    `id`          bigint(32)                                                   NOT NULL AUTO_INCREMENT COMMENT '访客id',
    `user_id`     bigint(32)                                                   NULL DEFAULT NULL COMMENT '用户id',
    `by_user_id`  bigint(32)                                                   NULL DEFAULT NULL COMMENT '访问用户id',
    `update_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 706
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_visitor
-- ----------------------------

-- ----------------------------
-- Table structure for vip_details
-- ----------------------------
DROP TABLE IF EXISTS `vip_details`;
CREATE TABLE `vip_details`
(
    `id`            bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT 'id',
    `vip_name_type` int(2)                                                        NULL DEFAULT NULL COMMENT '会员类型0月1季2年',
    `money`         decimal(10, 0)                                                NULL DEFAULT NULL COMMENT '会员价格',
    `vip_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会员名称',
    `rate`          decimal(10, 2)                                                NULL DEFAULT NULL,
    `award`         decimal(10, 2)                                                NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 9
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vip_details
-- ----------------------------

-- ----------------------------
-- Table structure for vip_discount
-- ----------------------------
DROP TABLE IF EXISTS `vip_discount`;
CREATE TABLE `vip_discount`
(
    `id`       int(11)        NOT NULL AUTO_INCREMENT COMMENT 'id',
    `discount` decimal(10, 0) NULL DEFAULT NULL COMMENT '优惠金币',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vip_discount
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
