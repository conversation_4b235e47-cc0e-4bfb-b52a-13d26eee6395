{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/chat.vue?1c75", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/chat.vue?e07c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/chat.vue?eeb8", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/chat.vue?1ffa", "uni-app:///pages/ai/chat.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/chat.vue?3da3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/chat.vue?a2ee"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "conversationId", "conversationTitle", "currentModelCode", "currentModelName", "messageList", "inputMessage", "sending", "aiTyping", "scrollTop", "scrollIntoView", "userAvatar", "historyPage", "historyLimit", "hasMoreHistory", "loadingHistory", "availableModels", "showModelSelector", "showMoreActionSheet", "moreActionList", "text", "value", "color", "showMessageActionSheet", "currentMessage", "messageActionList", "streamTaskId", "streamPollingTimer", "streamScrollTimer", "streamCurrentIndex", "streamMessage", "onLoad", "id", "role", "content", "createTime", "onReady", "setTimeout", "console", "onShow", "onUnload", "methods", "initChat", "loadAvailableModels", "loadConversationDetail", "loadChatHistory", "page", "limit", "loadMoreHistory", "sendMessage", "createConversationAndSend", "modelCode", "title", "handleSendError", "status", "sendMessageStream", "message", "isStreaming", "startStreamPolling", "clearInterval", "pollStreamChunks", "fromIndex", "chunks", "finishStreamOutput", "handleStreamError", "stopStreamPolling", "updateConversationTitle", "switchModel", "changeModel", "updateCurrentModelName", "showInputMenu", "showMoreActions", "handleMoreAction", "editTitle", "uni", "editable", "placeholderText", "success", "clearChat", "deleteChat", "scrollToBottom", "scrollToLatestMessage", "formatTime", "processedTimeStr", "hour", "minute", "showMessageActions", "handleMessageAction", "copyMessage", "fail", "resendMessage", "saveConversationState", "scrollPosition", "lastViewTime", "parseMarkdown", "type", "htmlToNodes", "html", "nodes", "name", "attrs", "style", "children", "parseInlineElements", "remaining", "elements", "result"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,yNAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpFA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2JvvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC,iBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;QAAAC;MAAA,EACA;MAEA;MACAC;MACAC;MACAC,oBACA;QAAAL;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MAEA;MACAK;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;;IAEA;IACA;MACA,oBACA;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;IACA;IAEA;EACA;EAEAC;IAAA;IACA;IACAC;MACAC;MACA;IACA;EACA;EAEAC;IAAA;IACA;IACAF;MACAC;MACA;IACA;EACA;EAEAE;IACA;IACA;;IAEA;IACA;MACA;QACAF;MACA;IACA;;IAEA;IACA;EACA;EACAG;IACA;IACAC;MAAA;MACA;MACA;MACA;;MAEA;MACAL;QACAC;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACA;QACAL;QACA;UACA;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAM;MAAA;MACA;QACA;QACA;MACA;MAEA;QACAN;QACA;UACA;UACA;UACA;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAO;MAAA;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;MAEA;;MAEA;MACA;QACA;QACA;UACA;UACAP;UACA;YACAD;cACA;YACA;UACA;QACA;MACA;MAEA;QACAS;QACAC;MACA;MAEA;QACAT;QACA;UACA;UAEA;YAAA;YACA;YACA;UACA;YACA;YACA;cACA;;cAEA;cACAA;cACA;gBACAD;kBACA;gBACA;cACA;YACA;YACA;YACA;cACA;YACA;UACA;;UAEA;UACA;UACA;YACA;UACA;QACA;UACAC;QACA;MACA;QACAA;QACA;QACA;UACA;UACA;YACA;YACA;;YAEA;YACA;cACAD;gBACA;cACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAW;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAX;QACA;MACA;MAEAA;;MAEA;MACA;QACAN;QACAC;QACAC;QACAC;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;QACAG;QACA;MACA;IACA;IAEA;IACAY;MAAA;MACA;QACAC;QACAC;MACA;MAEA;QACAd;QACA;UACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAIA;IACAe;MACA;QACArB;QACAC;QACAC;QACAC;QACAmB;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAjB;MACA;QACAkB;QACAL;MACA;MAEA;QACAb;QACA;UACA;UACA;UACAA;;UAEA;UACA;YACAN;YACAC;YACAC;YACAC;YACAsB;UACA;UACA;UACAnB;;UAEA;UACA;YACA;UACA;;UAEA;UACA;;UAEA;UACA;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAoB;MAAA;MACA;QACAC;MACA;MACA;QACAA;MACA;MAEArB;MACA;QACA;MACA;;MAEA;MACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAsB;MAAA;MACA;QACAtB;QACA;MACA;MAEA;QAAAuB;MAAA;MACAvB;MAEA;QACAA;QACA;UACA;UACAA;;UAEA;UACAwB;YACAxB;;YAEA;YACA;cACA;cACAA;;cAEA;cACA;gBACA;cACA;YACA;YACA;;YAEA;YACA;cACAA;cACA;YACA;UACA;QACA;UACAA;UACA;UACA;QACA;MACA;QACAA;QACA;QACA;MACA;IACA;IAEA;IACAyB;MACA;MAEA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAN;QACA;MACA;MACA;QACAA;QACA;MACA;IACA;IAIA;IACAO;MAAA;MACA;QACA;MACA;;MAEA;MACA;MACA;QAAAd;MAAA;MAEA;QACAd;QACA;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACA6B;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;QAAAjB;MAAA;MAEA;QACAb;QACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACA+B;MAAA;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;MACA,uBACA;QAAAlD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;QAAAC;MAAA,EACA;MACA;IACA;IAEA;IACAiD;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MAAA;IAEA;IAEA;IACAC;MAAA;MACAC;QACAtB;QACAuB;QACAC;QACAC;UACA;YACA;cACA;cACA;cACA;cACA;YACA;YAEA;cAAAzB;YAAA;YACA;cACAd;cACA;gBACA;gBACA;cACA;gBACA;cACA;YACA;cACAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAwC;MAAA;MACAJ;QACAtB;QACAlB;QACA2C;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACAL;QACAtB;QACAlB;QACA2C;UACA;YACA;cACA;cACA;cACAxC;gBACAqC;cACA;cACA;YACA;YAEA;cACApC;cACA;gBACA;gBACAD;kBACAqC;gBACA;cACA;gBACA;cACA;YACA;cACApC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACA0C;MAAA;MACA;QACA;QACA;QACA1C;;QAEA;QACAD;UACA;QACA;MACA;IACA;IAEA;IACA4C;MAAA;MACA;QACA;QACA3C;QAEA;UACA;UACA;;UAEA;UACAD;YACA;YACA;UACA;;UAEA;UACAA;YACA;UACA;;UAEA;UACAA;YACA;UACA;QACA;MACA;QACAC;QACA;MACA;IACA;IAEA;IACA4C;MACA;;MAEA;MACA;MACA;QACA;QACAC;MACA;MAEA;;MAEA;MACA;QACA7C;QACA;MACA;MAEA;QACA8C;QACAC;MACA;IACA;IAIA;IACAC;MACA;MACA;MACA;QACA,0BACA;UAAAlE;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,EACA;MACA;QACA,0BACA;UAAAD;UAAAC;QAAA,EACA;MACA;MACA;IACA;IAEA;IACAkE;MACA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;QACAd;UACA1E;UACA6E;YACA;UACA;UACAY;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAjB;QACAkB;QACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;;QAEA;QACA;MACA;QACAxD;QACA;QACA;UACAyD;UACA3E;QACA;MACA;IACA;IAEA;IACA4E;MAAA;MACA;MACA;MACA;;MAEA;MACAC;QACAC;UACAC;UACAC;YACAC;UACA;UACAC;QACA;QACA;MACA;;MAEA;MACAL;QACA;QACAC;UACAC;UACAC;YACAC;UACA;UACAC;YACAP;YACA3E;UACA;QACA;QACA;MACA;;MAEA;MACA6E;QACAC;UACAC;UACAC;YACAC;UACA;UACAC;YACAP;YACA3E;UACA;QACA;QACA;MACA;;MAEA;MACA6E;QACA;QACA;UACA;UACA;YACAE;YACAC;cACAC;YACA;YACAC;cACAP;cACA3E;YACA;UACA;QACA;QAEA8E;UACAC;UACAC;YACAC;UACA;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAJ;UACAC;UACAG;QACA;MACA;MAEA;QACAP;QACA3E;MACA;IACA;IAEA;IACAmF;MACA;MACA;;MAEA;MACAC;QACAC;UACAN;UACAC;YACAC;UACA;UACAC;YACAP;YACA3E;UACA;QACA;QACA;MACA;;MAEA;MACAoF;QACAC;UACAN;UACAC;YACAC;UACA;UACAC;YACAP;YACA3E;UACA;QACA;QACA;MACA;;MAEA;MACAoF;QACAC;UACAN;UACAC;YACAC;UACA;UACAC;YACAP;YACA3E;UACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACA2E;UACA3E;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;UACAsF;YACAX;YACA3E;UACA;QACA;QACA;UACAsF;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1oCA;AAAA;AAAA;AAAA;AAA84C,CAAgB,mvCAAG,EAAC,C;;;;;;;;;;;ACAl6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ai/chat.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/ai/chat.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./chat.vue?vue&type=template&id=1edb40f6&scoped=true&\"\nvar renderjs\nimport script from \"./chat.vue?vue&type=script&lang=js&\"\nexport * from \"./chat.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chat.vue?vue&type=style&index=0&id=1edb40f6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1edb40f6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ai/chat.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=template&id=1edb40f6&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-loading/u-loading\" */ \"@/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-avatar/u-avatar\" */ \"@/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uActionSheet: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-action-sheet/u-action-sheet\" */ \"@/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.messageList, function (message, index) {\n    var $orig = _vm.__get_orig(message)\n    var m0 = !(message.role === \"user\")\n      ? _vm.formatTime(message.createTime)\n      : null\n    var m1 = !(message.role === \"user\")\n      ? _vm.parseMarkdown(message.content)\n      : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.inputMessage.trim() && !_vm.sending\n  var g1 = !_vm.sending ? _vm.inputMessage.trim() : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showModelSelector = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"chat-container\">\n\t\t<!-- 顶部工具栏 -->\n\t\t<view class=\"chat-header\">\n\t\t\t<text class=\"conversation-title\">{{ conversationTitle }}</text>\n\t\t</view>\n\n\t\t<!-- 消息列表 -->\n\t\t<scroll-view\n\t\t\tclass=\"message-list\"\n\t\t\tscroll-y\n\t\t\t:scroll-top=\"scrollTop\"\n\t\t\t:scroll-into-view=\"scrollIntoView\"\n\t\t\t:scroll-with-animation=\"true\"\n\t\t\t@scrolltoupper=\"loadMoreHistory\"\n\t\t>\n\t\t\t<!-- 加载更多历史消息 -->\n\t\t\t<view v-if=\"hasMoreHistory\" class=\"load-more-history\">\n\t\t\t\t<view v-if=\"loadingHistory\" class=\"loading-indicator\">\n\t\t\t\t\t<u-loading :show=\"true\" size=\"20\"></u-loading>\n\t\t\t\t\t<text>加载中...</text>\n\t\t\t\t</view>\n\t\t\t\t<text v-else @click=\"loadMoreHistory\">点击加载更多历史消息</text>\n\t\t\t</view>\n\n\t\t\t<!-- 消息项 -->\n\t\t\t<view v-for=\"(message, index) in messageList\" :key=\"message.id\" :id=\"'msg-' + message.id\" class=\"message-item\">\n\t\t\t\t<!-- 用户消息 -->\n\t\t\t\t<view v-if=\"message.role === 'user'\" class=\"message-wrapper user-message\">\n\t\t\t\t\t<view class=\"message-content user-content\" @longpress=\"showMessageActions(message)\">\n\t\t\t\t\t\t<text class=\"message-text\">{{ message.content }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"message-avatar\">\n\t\t\t\t\t\t<u-avatar :src=\"userAvatar\" size=\"32\"></u-avatar>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- AI消息 -->\n\t\t\t\t<view v-else class=\"message-wrapper ai-message\">\n\t\t\t\t\t<view class=\"message-avatar\">\n\t\t\t\t\t\t<u-avatar src=\"/static/ai-avatar.png\" size=\"32\"></u-avatar>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"message-content ai-content\" @longpress=\"showMessageActions(message)\">\n\t\t\t\t\t\t<view class=\"ai-header\">\n\t\t\t\t\t\t\t<text class=\"ai-name\">{{ currentModelName }}</text>\n\t\t\t\t\t\t\t<text class=\"message-time\">{{ formatTime(message.createTime) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"message-text-container\">\n\t\t\t\t\t\t\t<!-- 使用rich-text组件渲染markdown -->\n\t\t\t\t\t\t\t<rich-text\n\t\t\t\t\t\t\t\tclass=\"message-text markdown-content\"\n\t\t\t\t\t\t\t\t:nodes=\"parseMarkdown(message.content)\"\n\t\t\t\t\t\t\t></rich-text>\n\t\t\t\t\t\t\t<!-- 流式输出光标 -->\n\t\t\t\t\t\t\t<text v-if=\"message.isStreaming\" class=\"stream-cursor\">|</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 消息状态 -->\n\t\t\t\t\t\t<view v-if=\"message.status\" class=\"message-status\">\n\t\t\t\t\t\t\t<view v-if=\"message.status === 'sending'\" class=\"sending-indicator\">\n\t\t\t\t\t\t\t\t<u-loading :show=\"true\" size=\"14\"></u-loading>\n\t\t\t\t\t\t\t\t<text>发送中...</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text v-else-if=\"message.status === 'error'\" class=\"error-text\" @click=\"resendMessage(message)\">发送失败，点击重试</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 正在输入提示 -->\n\t\t\t<!-- <view v-if=\"aiTyping\" class=\"message-wrapper ai-message\">\n\t\t\t\t<view class=\"message-avatar\">\n\t\t\t\t\t<u-avatar src=\"/static/ai-avatar.png\" size=\"32\"></u-avatar>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"message-content ai-content\">\n\t\t\t\t\t<view class=\"typing-indicator\">\n\t\t\t\t\t\t<u-loading :show=\"true\" size=\"16\"></u-loading>\n\t\t\t\t\t\t<text>AI正在思考中...</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view> -->\n\n\t\t\t<!-- 底部锚点，用于滚动到底部 -->\n\t\t\t<view id=\"bottom-anchor\" style=\"height: 1px;\"></view>\n\t\t</scroll-view>\n\n\t\t<!-- 输入区域 -->\n\t\t<view class=\"input-area\">\n\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t<!-- 左侧菜单按钮 -->\n\t\t\t\t<view class=\"menu-btn\" @click=\"showInputMenu\">\n\t\t\t\t\t<u-icon name=\"plus\" size=\"22\" color=\"#666\"></u-icon>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 输入框 -->\n\t\t\t\t<textarea\n\t\t\t\t\tv-model=\"inputMessage\"\n\t\t\t\t\tplaceholder=\"输入消息...\"\n\t\t\t\t\tclass=\"message-input\"\n\t\t\t\t\tmaxlength=\"1000\"\n\t\t\t\t\t:disabled=\"sending\"\n\t\t\t\t\t@confirm=\"sendMessage\"\n\t\t\t\t\tauto-height\n\t\t\t\t></textarea>\n\n\t\t\t\t<!-- 发送按钮 -->\n\t\t\t\t<view\n\t\t\t\t\tclass=\"send-btn\"\n\t\t\t\t\t:class=\"{ 'send-btn-active': inputMessage.trim() && !sending }\"\n\t\t\t\t\t@click=\"sendMessage\"\n\t\t\t\t>\n\t\t\t\t\t<u-icon\n\t\t\t\t\t\tv-if=\"!sending\"\n\t\t\t\t\t\tname=\"arrow-right\"\n\t\t\t\t\t\tsize=\"24\"\n\t\t\t\t\t\t:color=\"inputMessage.trim() ? '#fff' : '#ccc'\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t\t<u-loading v-else color=\"#fff\" size=\"24\" mode=\"circle\"></u-loading>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 更多操作菜单 -->\n\t\t<u-action-sheet :list=\"moreActionList\" v-model=\"showMoreActionSheet\" @click=\"handleMoreAction\"></u-action-sheet>\n\n\t\t<!-- 消息操作菜单 -->\n\t\t<u-action-sheet :list=\"messageActionList\" v-model=\"showMessageActionSheet\" @click=\"handleMessageAction\"></u-action-sheet>\n\n\t\t<!-- 模型切换弹窗 -->\n\t\t<u-popup v-model=\"showModelSelector\" mode=\"bottom\" border-radius=\"20\">\n\t\t\t<view class=\"model-selector\">\n\t\t\t\t<view class=\"selector-header\">\n\t\t\t\t\t<text class=\"selector-title\">切换AI模型</text>\n\t\t\t\t\t<u-icon name=\"close\" @click=\"showModelSelector = false\"></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"model-options\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"model in availableModels\" \n\t\t\t\t\t\t:key=\"model.modelCode\"\n\t\t\t\t\t\tclass=\"model-option\"\n\t\t\t\t\t\t:class=\"{ active: currentModelCode === model.modelCode }\"\n\t\t\t\t\t\t@click=\"changeModel(model)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"model-name\">{{ model.modelName }}</text>\n\t\t\t\t\t\t<u-icon \n\t\t\t\t\t\t\t:name=\"currentModelCode === model.modelCode ? 'checkmark-circle-fill' : 'circle'\" \n\t\t\t\t\t\t\t:color=\"currentModelCode === model.modelCode ? '#0175FE' : '#ccc'\"\n\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\t</view>\n</template>\n\n<script>\n\timport marked from '@/common/marked.js'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tconversationId: '',\n\t\t\t\tconversationTitle: 'AI对话',\n\t\t\t\tcurrentModelCode: '',\n\t\t\t\tcurrentModelName: '',\n\t\t\t\tmessageList: [],\n\t\t\t\tinputMessage: '',\n\t\t\t\tsending: false,\n\t\t\t\taiTyping: false,\n\t\t\t\tscrollTop: 0,\n\t\t\t\tscrollIntoView: '', // 微信小程序滚动到指定元素\n\t\t\t\tuserAvatar: '',\n\t\t\t\t\n\t\t\t\t// 历史消息加载\n\t\t\t\thistoryPage: 1,\n\t\t\t\thistoryLimit: 20,\n\t\t\t\thasMoreHistory: true,\n\t\t\t\tloadingHistory: false,\n\t\t\t\t\n\t\t\t\t// 模型相关\n\t\t\t\tavailableModels: [],\n\t\t\t\tshowModelSelector: false,\n\t\t\t\t\n\t\t\t\t// 更多操作\n\t\t\t\tshowMoreActionSheet: false,\n\t\t\t\tmoreActionList: [\n\t\t\t\t\t{ text: '编辑标题', value: 'editTitle' },\n\t\t\t\t\t{ text: '清空对话', value: 'clearChat' },\n\t\t\t\t\t{ text: '删除对话', value: 'deleteChat', color: '#f56c6c' }\n\t\t\t\t],\n\n\t\t\t\t// 消息操作\n\t\t\t\tshowMessageActionSheet: false,\n\t\t\t\tcurrentMessage: null,\n\t\t\t\tmessageActionList: [\n\t\t\t\t\t{ text: '复制消息', value: 'copy' },\n\t\t\t\t\t{ text: '重新发送', value: 'resend' }\n\t\t\t\t],\n\n\t\t\t\t// 流式输出相关\n\t\t\t\tstreamTaskId: '',\n\t\t\t\tstreamPollingTimer: null,\n\t\t\t\tstreamScrollTimer: null, // 流式滚动定时器\n\t\t\t\tstreamCurrentIndex: 0,\n\t\t\t\tstreamMessage: null // 当前正在流式输出的消息对象\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.conversationId = options.conversationId\n\t\t\tthis.currentModelCode = options.modelCode || 'deepseek-chat'\n\n\t\t\t// 获取用户头像\n\t\t\tthis.userAvatar = this.$queue.getData('avatar') || '/static/default-avatar.png'\n\n\t\t\t// 如果没有conversationId，添加欢迎消息\n\t\t\tif (!this.conversationId) {\n\t\t\t\tthis.messageList = [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'welcome_' + Date.now(),\n\t\t\t\t\t\trole: 'assistant',\n\t\t\t\t\t\tcontent: '你好！我是AI助手，有什么可以帮助你的吗？\\n\\n我现在支持 **Markdown格式** 的回复，包括：\\n\\n- **粗体文本**\\n- *斜体文本*\\n- `行内代码`\\n- 代码块\\n- 列表等\\n\\n```javascript\\nconsole.log(\"Hello, World!\");\\n```\\n\\n请随时向我提问！',\n\t\t\t\t\t\tcreateTime: new Date().toISOString()\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t\tthis.conversationId = 'temp_' + Date.now()\n\t\t\t}\n\n\t\t\tthis.initChat()\n\t\t},\n\n\t\tonReady() {\n\t\t\t// 页面渲染完成后再次尝试滚动\n\t\t\tsetTimeout(() => {\n\t\t\t\tconsole.log('页面渲染完成，尝试滚动到底部')\n\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t}, 1000)\n\t\t},\n\n\t\tonShow() {\n\t\t\t// 页面显示时也尝试滚动到底部\n\t\t\tsetTimeout(() => {\n\t\t\t\tconsole.log('页面显示，尝试滚动到底部')\n\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t}, 500)\n\t\t},\n\n\t\tonUnload() {\n\t\t\t// 清理流式轮询定时器\n\t\t\tthis.stopStreamPolling()\n\n\t\t\t// 如果有正在进行的流式任务，尝试取消\n\t\t\tif (this.streamTaskId) {\n\t\t\t\tthis.$Request.post(`/app/ai/chat/stream/${this.streamTaskId}/cancel`).catch(error => {\n\t\t\t\t\tconsole.log('取消流式任务失败:', error)\n\t\t\t\t})\n\t\t\t}\n\n\t\t\t// 页面卸载时保存对话状态\n\t\t\tthis.saveConversationState()\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化聊天\n\t\t\tinitChat() {\n\t\t\t\tthis.loadAvailableModels()\n\t\t\t\tthis.loadConversationDetail()\n\t\t\t\tthis.loadChatHistory()\n\n\t\t\t\t// 延迟滚动，确保数据加载完成后再滚动到底部\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tconsole.log('初始化完成，开始滚动到底部')\n\t\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t\t}, 800)\n\t\t\t},\n\n\t\t\t// 加载可用模型\n\t\t\tloadAvailableModels() {\n\t\t\t\tthis.$Request.get('/app/ai/chat/models').then(res => {\n\t\t\t\t\tconsole.log('模型列表:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tthis.availableModels = res.data || []\n\t\t\t\t\t\tthis.updateCurrentModelName()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载模型失败:', res.msg)\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('加载模型失败:', error)\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 加载对话详情\n\t\t\tloadConversationDetail() {\n\t\t\t\tif (!this.conversationId || String(this.conversationId).startsWith('temp_')) {\n\t\t\t\t\t// 临时对话，不需要加载详情\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.$Request.get(`/app/ai/chat/conversation/${this.conversationId}`).then(res => {\n\t\t\t\t\tconsole.log('对话详情:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tconst detail = res.data\n\t\t\t\t\t\tthis.conversationTitle = detail.title || 'AI对话'\n\t\t\t\t\t\tthis.currentModelCode = detail.modelCode || this.currentModelCode\n\t\t\t\t\t\tthis.updateCurrentModelName()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载对话详情失败:', res.msg)\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('加载对话详情失败:', error)\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 加载聊天历史\n\t\t\tloadChatHistory(loadMore = false) {\n\t\t\t\tif (this.loadingHistory) return\n\t\t\t\tif (!this.conversationId || String(this.conversationId).startsWith('temp_')) {\n\t\t\t\t\t// 临时对话，不需要加载历史\n\t\t\t\t\tthis.loadingHistory = false\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.loadingHistory = true\n\n\t\t\t\t// 首次加载时先从缓存获取\n\t\t\t\tif (!loadMore && this.$aiStore) {\n\t\t\t\t\tconst cachedMessages = this.$aiStore.getChatHistory(this.conversationId)\n\t\t\t\t\tif (cachedMessages.length > 0) {\n\t\t\t\t\t\tthis.messageList = [...this.messageList, ...cachedMessages]\n\t\t\t\t\t\tconsole.log('缓存消息加载完成，消息数量:', this.messageList.length)\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t\t\t\t\t}, 500)\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tlet data = {\n\t\t\t\t\tpage: loadMore ? this.historyPage : 1,\n\t\t\t\t\tlimit: this.historyLimit\n\t\t\t\t}\n\n\t\t\t\tthis.$Request.get(`/app/ai/chat/conversation/${this.conversationId}/history`, data).then(res => {\n\t\t\t\t\tconsole.log('聊天历史:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tconst newMessages = res.data.list || []\n\n\t\t\t\t\t\tif (loadMore) {\n\t\t\t\t\t\t\t// 加载更多历史消息，插入到列表前面\n\t\t\t\t\t\t\tthis.messageList.unshift(...newMessages.reverse())\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 首次加载，合并到现有消息\n\t\t\t\t\t\t\tif (newMessages.length > 0) {\n\t\t\t\t\t\t\t\tthis.messageList = [...this.messageList, ...newMessages]\n\n\t\t\t\t\t\t\t\t// 首次加载完成后滚动到底部\n\t\t\t\t\t\t\t\tconsole.log('历史消息加载完成，消息数量:', this.messageList.length)\n\t\t\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t\t\t\t\t\t\t}, 500)\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 缓存消息历史\n\t\t\t\t\t\t\tif (this.$aiStore) {\n\t\t\t\t\t\t\t\tthis.$aiStore.setChatHistory(this.conversationId, newMessages)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 检查是否还有更多历史消息\n\t\t\t\t\t\tthis.hasMoreHistory = newMessages.length >= this.historyLimit\n\t\t\t\t\t\tif (loadMore && newMessages.length > 0) {\n\t\t\t\t\t\t\tthis.historyPage++\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载聊天历史失败:', res.msg)\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('加载聊天历史失败:', error)\n\t\t\t\t\t// 网络错误时使用缓存数据\n\t\t\t\t\tif (!loadMore && this.messageList.length <= 1 && this.$aiStore) {\n\t\t\t\t\t\tconst cachedMessages = this.$aiStore.getChatHistory(this.conversationId)\n\t\t\t\t\t\tif (cachedMessages.length > 0) {\n\t\t\t\t\t\t\tthis.messageList = [...this.messageList, ...cachedMessages]\n\t\t\t\t\t\t\tthis.$queue.showToast('网络异常，显示缓存消息')\n\n\t\t\t\t\t\t\t// 缓存数据加载后滚动到底部\n\t\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t\t\t\t\t\t}, 100)\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthis.loadingHistory = false\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 加载更多历史消息\n\t\t\tloadMoreHistory() {\n\t\t\t\tif (this.hasMoreHistory && !this.loadingHistory) {\n\t\t\t\t\tthis.loadChatHistory(true)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 发送消息\n\t\t\tsendMessage() {\n\t\t\t\tconst message = this.inputMessage.trim()\n\t\t\t\tif (!message || this.sending) {\n\t\t\t\t\tconsole.log('消息为空或正在发送中，忽略发送请求')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconsole.log('发送消息，使用流式模式')\n\n\t\t\t\t// 添加用户消息到列表\n\t\t\t\tconst userMessage = {\n\t\t\t\t\tid: Date.now(),\n\t\t\t\t\trole: 'user',\n\t\t\t\t\tcontent: message,\n\t\t\t\t\tcreateTime: new Date().toISOString()\n\t\t\t\t}\n\t\t\t\tthis.messageList.push(userMessage)\n\n\t\t\t\t// 清空输入框\n\t\t\t\tthis.inputMessage = ''\n\t\t\t\tthis.scrollToBottom()\n\n\t\t\t\t// 显示AI正在输入\n\t\t\t\tthis.aiTyping = true\n\t\t\t\tthis.sending = true\n\n\t\t\t\t// 如果是临时对话，先创建对话\n\t\t\t\tif (!this.conversationId || String(this.conversationId).startsWith('temp_')) {\n\t\t\t\t\tthis.createConversationAndSend(message, userMessage)\n\t\t\t\t} else {\n\t\t\t\t\t// 使用流式发送模式\n\t\t\t\t\tconsole.log('使用流式发送模式')\n\t\t\t\t\tthis.sendMessageStream(message, userMessage)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 创建对话并发送消息\n\t\t\tcreateConversationAndSend(message, userMessage) {\n\t\t\t\tlet data = {\n\t\t\t\t\tmodelCode: this.currentModelCode,\n\t\t\t\t\ttitle: message.length > 20 ? message.substring(0, 20) + '...' : message\n\t\t\t\t}\n\n\t\t\t\tthis.$Request.post('/app/ai/chat/conversation/create', data).then(res => {\n\t\t\t\t\tconsole.log('创建对话:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tthis.conversationId = res.data.id\n\t\t\t\t\t\tthis.conversationTitle = data.title\n\t\t\t\t\t\t// 创建成功后使用流式发送消息\n\t\t\t\t\t\tthis.sendMessageStream(message, userMessage)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.handleSendError(res.msg || '创建对话失败')\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('创建对话失败:', error)\n\t\t\t\t\tthis.handleSendError('创建对话失败，请重试')\n\t\t\t\t})\n\t\t\t},\n\n\n\n\t\t\t// 处理发送错误\n\t\t\thandleSendError(errorMsg) {\n\t\t\t\tconst errorMessage = {\n\t\t\t\t\tid: Date.now() + 1,\n\t\t\t\t\trole: 'assistant',\n\t\t\t\t\tcontent: errorMsg,\n\t\t\t\t\tcreateTime: new Date().toISOString(),\n\t\t\t\t\tstatus: 'error'\n\t\t\t\t}\n\t\t\t\tthis.messageList.push(errorMessage)\n\t\t\t\tthis.aiTyping = false\n\t\t\t\tthis.sending = false\n\t\t\t\tthis.scrollToBottom()\n\t\t\t},\n\n\t\t\t// 流式发送消息\n\t\t\tsendMessageStream(message, userMessage) {\n\t\t\t\tconsole.log('开始流式发送消息:', message)\n\t\t\t\tlet data = {\n\t\t\t\t\tmessage: message,\n\t\t\t\t\tmodelCode: this.currentModelCode\n\t\t\t\t}\n\n\t\t\t\tthis.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/send-stream`, data).then(res => {\n\t\t\t\t\tconsole.log('启动流式发送响应:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tthis.streamTaskId = res.data.taskId\n\t\t\t\t\t\tthis.streamCurrentIndex = 0\n\t\t\t\t\t\tconsole.log('获得流式任务ID:', this.streamTaskId)\n\n\t\t\t\t\t\t// 创建流式消息对象\n\t\t\t\t\t\tthis.streamMessage = {\n\t\t\t\t\t\t\tid: Date.now() + 1,\n\t\t\t\t\t\t\trole: 'assistant',\n\t\t\t\t\t\t\tcontent: '',\n\t\t\t\t\t\t\tcreateTime: new Date().toISOString(),\n\t\t\t\t\t\t\tisStreaming: true\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.messageList.push(this.streamMessage)\n\t\t\t\t\t\tconsole.log('创建流式消息对象，开始轮询')\n\n\t\t\t\t\t\t// 立即滚动到底部显示新消息\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\t// 开始轮询获取流式内容\n\t\t\t\t\t\tthis.startStreamPolling()\n\n\t\t\t\t\t\t// 缓存用户消息\n\t\t\t\t\t\tif (this.$aiStore) {\n\t\t\t\t\t\t\tthis.$aiStore.addMessageToHistory(this.conversationId, userMessage)\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('启动流式发送失败:', res.msg)\n\t\t\t\t\t\tthis.handleSendError(res.msg || '启动流式发送失败')\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('启动流式发送请求失败:', error)\n\t\t\t\t\tthis.handleSendError('启动流式发送失败，请重试')\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 开始流式轮询\n\t\t\tstartStreamPolling() {\n\t\t\t\tif (this.streamPollingTimer) {\n\t\t\t\t\tclearInterval(this.streamPollingTimer)\n\t\t\t\t}\n\t\t\t\tif (this.streamScrollTimer) {\n\t\t\t\t\tclearInterval(this.streamScrollTimer)\n\t\t\t\t}\n\n\t\t\t\tconsole.log('开始流式轮询，任务ID:', this.streamTaskId)\n\t\t\t\tthis.streamPollingTimer = setInterval(() => {\n\t\t\t\t\tthis.pollStreamChunks()\n\t\t\t\t}, 300) // 每300ms轮询一次\n\n\t\t\t\t// 额外的滚动定时器，确保实时滚动到底部\n\t\t\t\tthis.streamScrollTimer = setInterval(() => {\n\t\t\t\t\tif (this.streamMessage && this.streamMessage.isStreaming) {\n\t\t\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t\t\t}\n\t\t\t\t}, 500) // 每500ms滚动一次（微信小程序滚动频率不宜过高）\n\t\t\t},\n\n\t\t\t// 轮询获取流式分块\n\t\t\tpollStreamChunks() {\n\t\t\t\tif (!this.streamTaskId) {\n\t\t\t\t\tconsole.log('没有流式任务ID，停止轮询')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tlet data = { fromIndex: this.streamCurrentIndex }\n\t\t\t\tconsole.log(`轮询流式分块，任务ID: ${this.streamTaskId}, 当前索引: ${this.streamCurrentIndex}`)\n\n\t\t\t\tthis.$Request.get(`/app/ai/chat/stream/${this.streamTaskId}/chunks`, data).then(res => {\n\t\t\t\t\tconsole.log('获取流式分块响应:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tconst chunks = res.data.chunks || []\n\t\t\t\t\t\tconsole.log(`收到 ${chunks.length} 个新分块`)\n\n\t\t\t\t\t\t// 处理新的分块\n\t\t\t\t\t\tchunks.forEach(chunk => {\n\t\t\t\t\t\t\tconsole.log(`处理分块，内容长度: ${chunk.chunkIndex}, 是否完成: ${chunk.isLast}`)\n\n\t\t\t\t\t\t\t// 更新流式消息内容（总是更新，不比较索引）\n\t\t\t\t\t\t\tif (this.streamMessage) {\n\t\t\t\t\t\t\t\tthis.streamMessage.content = chunk.accumulatedContent\n\t\t\t\t\t\t\t\tconsole.log('更新消息内容:', chunk.accumulatedContent.length, '字符')\n\n\t\t\t\t\t\t\t\t// 实时滚动到最新消息\n\t\t\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\t\t\tthis.scrollToLatestMessage()\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.streamCurrentIndex = chunk.chunkIndex\n\n\t\t\t\t\t\t\t// 检查是否完成\n\t\t\t\t\t\t\tif (chunk.isLast) {\n\t\t\t\t\t\t\t\tconsole.log('流式输出完成')\n\t\t\t\t\t\t\t\tthis.finishStreamOutput(chunk)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('获取流式分块失败:', res.msg)\n\t\t\t\t\t\tthis.stopStreamPolling()\n\t\t\t\t\t\tthis.handleStreamError('获取流式内容失败')\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('轮询流式分块请求失败:', error)\n\t\t\t\t\tthis.stopStreamPolling()\n\t\t\t\t\tthis.handleStreamError('网络错误，流式输出中断')\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 完成流式输出\n\t\t\tfinishStreamOutput(lastChunk) {\n\t\t\t\tthis.stopStreamPolling()\n\n\t\t\t\tif (this.streamMessage) {\n\t\t\t\t\t// 标记流式输出完成\n\t\t\t\t\tthis.streamMessage.isStreaming = false\n\t\t\t\t\tthis.streamMessage.content = lastChunk.accumulatedContent\n\n\t\t\t\t\t// 缓存AI消息\n\t\t\t\t\tif (this.$aiStore) {\n\t\t\t\t\t\tthis.$aiStore.addMessageToHistory(this.conversationId, this.streamMessage)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.aiTyping = false\n\t\t\t\tthis.sending = false\n\t\t\t\tthis.streamTaskId = ''\n\t\t\t\tthis.streamMessage = null\n\t\t\t\tthis.scrollToBottom()\n\t\t\t},\n\n\t\t\t// 处理流式错误\n\t\t\thandleStreamError(errorMsg) {\n\t\t\t\tthis.stopStreamPolling()\n\n\t\t\t\tif (this.streamMessage) {\n\t\t\t\t\tthis.streamMessage.content = errorMsg\n\t\t\t\t\tthis.streamMessage.status = 'error'\n\t\t\t\t\tthis.streamMessage.isStreaming = false\n\t\t\t\t}\n\n\t\t\t\tthis.aiTyping = false\n\t\t\t\tthis.sending = false\n\t\t\t\tthis.streamTaskId = ''\n\t\t\t\tthis.streamMessage = null\n\t\t\t},\n\n\t\t\t// 停止流式轮询\n\t\t\tstopStreamPolling() {\n\t\t\t\tif (this.streamPollingTimer) {\n\t\t\t\t\tclearInterval(this.streamPollingTimer)\n\t\t\t\t\tthis.streamPollingTimer = null\n\t\t\t\t}\n\t\t\t\tif (this.streamScrollTimer) {\n\t\t\t\t\tclearInterval(this.streamScrollTimer)\n\t\t\t\t\tthis.streamScrollTimer = null\n\t\t\t\t}\n\t\t\t},\n\n\n\n\t\t\t// 更新对话标题\n\t\t\tupdateConversationTitle(firstMessage) {\n\t\t\t\tif (!this.conversationId || String(this.conversationId).startsWith('temp_')) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 使用第一条消息的前20个字符作为标题\n\t\t\t\tconst title = firstMessage.length > 20 ? firstMessage.substring(0, 20) + '...' : firstMessage\n\t\t\t\tlet data = { title: title }\n\n\t\t\t\tthis.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/title`, data).then(res => {\n\t\t\t\t\tconsole.log('更新标题:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tthis.conversationTitle = title\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('更新对话标题失败:', res.msg)\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('更新对话标题失败:', error)\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 切换模型\n\t\t\tswitchModel() {\n\t\t\t\tthis.showModelSelector = true\n\t\t\t},\n\n\t\t\t// 更改模型\n\t\t\tchangeModel(model) {\n\t\t\t\tif (model.modelCode === this.currentModelCode) {\n\t\t\t\t\tthis.showModelSelector = false\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!this.conversationId || String(this.conversationId).startsWith('temp_')) {\n\t\t\t\t\t// 临时对话，直接切换\n\t\t\t\t\tthis.currentModelCode = model.modelCode\n\t\t\t\t\tthis.updateCurrentModelName()\n\t\t\t\t\tthis.$queue.showToast(`已切换到 ${model.modelName}`)\n\t\t\t\t\tthis.showModelSelector = false\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tlet data = { modelCode: model.modelCode }\n\n\t\t\t\tthis.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/switch-model`, data).then(res => {\n\t\t\t\t\tconsole.log('切换模型:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tthis.currentModelCode = model.modelCode\n\t\t\t\t\t\tthis.updateCurrentModelName()\n\t\t\t\t\t\tthis.$queue.showToast(`已切换到 ${model.modelName}`)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$queue.showToast(res.msg || '切换模型失败')\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('切换模型失败:', error)\n\t\t\t\t\tthis.$queue.showToast('切换模型失败，请重试')\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthis.showModelSelector = false\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 更新当前模型名称\n\t\t\tupdateCurrentModelName() {\n\t\t\t\tconst model = this.availableModels.find(m => m.modelCode === this.currentModelCode)\n\t\t\t\tthis.currentModelName = model ? model.modelName : this.currentModelCode\n\t\t\t},\n\n\t\t\t// 显示输入菜单\n\t\t\tshowInputMenu() {\n\t\t\t\t// 更新菜单列表，添加切换模型选项\n\t\t\t\tthis.moreActionList = [\n\t\t\t\t\t{ text: '切换模型', value: 'switchModel' },\n\t\t\t\t\t{ text: '编辑标题', value: 'editTitle' },\n\t\t\t\t\t{ text: '清空对话', value: 'clearChat' },\n\t\t\t\t\t{ text: '删除对话', value: 'deleteChat', color: '#f56c6c' }\n\t\t\t\t]\n\t\t\t\tthis.showMoreActionSheet = true\n\t\t\t},\n\n\t\t\t// 显示更多操作（保持兼容性）\n\t\t\tshowMoreActions() {\n\t\t\t\tthis.showInputMenu()\n\t\t\t},\n\n\t\t\t// 处理更多操作\n\t\t\thandleMoreAction(index) {\n\t\t\t\tconst action = this.moreActionList[index]\n\t\t\t\tswitch (action.value) {\n\t\t\t\t\tcase 'switchModel':\n\t\t\t\t\t\tthis.switchModel()\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'editTitle':\n\t\t\t\t\t\tthis.editTitle()\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'clearChat':\n\t\t\t\t\t\tthis.clearChat()\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'deleteChat':\n\t\t\t\t\t\tthis.deleteChat()\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 编辑标题\n\t\t\teditTitle() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '编辑标题',\n\t\t\t\t\teditable: true,\n\t\t\t\t\tplaceholderText: '请输入对话标题',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm && res.content.trim()) {\n\t\t\t\t\t\t\tif (!this.conversationId || String(this.conversationId).startsWith('temp_')) {\n\t\t\t\t\t\t\t\t// 临时对话，直接更新标题\n\t\t\t\t\t\t\t\tthis.conversationTitle = res.content.trim()\n\t\t\t\t\t\t\t\tthis.$queue.showToast('标题更新成功')\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tlet data = { title: res.content.trim() }\n\t\t\t\t\t\t\tthis.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/title`, data).then(result => {\n\t\t\t\t\t\t\t\tconsole.log('更新标题:', result)\n\t\t\t\t\t\t\t\tif (result.code === 0) {\n\t\t\t\t\t\t\t\t\tthis.conversationTitle = res.content.trim()\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast('标题更新成功')\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast(result.msg || '更新失败')\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).catch(error => {\n\t\t\t\t\t\t\t\tconsole.error('更新标题失败:', error)\n\t\t\t\t\t\t\t\tthis.$queue.showToast('更新失败，请重试')\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 清空对话\n\t\t\tclearChat() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认清空',\n\t\t\t\t\tcontent: '清空后消息记录将无法恢复，确定要清空对话吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.messageList = []\n\t\t\t\t\t\t\tthis.$queue.showToast('对话已清空')\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 删除对话\n\t\t\tdeleteChat() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: '删除后无法恢复，确定要删除这个对话吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tif (!this.conversationId || String(this.conversationId).startsWith('temp_')) {\n\t\t\t\t\t\t\t\t// 临时对话，直接返回\n\t\t\t\t\t\t\t\tthis.$queue.showToast('删除成功')\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthis.$Request.post(`/app/ai/chat/conversation/${this.conversationId}/delete`).then(result => {\n\t\t\t\t\t\t\t\tconsole.log('删除对话:', result)\n\t\t\t\t\t\t\t\tif (result.code === 0) {\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast('删除成功')\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast(result.msg || '删除失败')\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).catch(error => {\n\t\t\t\t\t\t\t\tconsole.error('删除对话失败:', error)\n\t\t\t\t\t\t\t\tthis.$queue.showToast('删除失败，请重试')\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 滚动到底部\n\t\t\tscrollToBottom() {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t// 微信小程序使用scroll-into-view滚动到底部锚点\n\t\t\t\t\tthis.scrollIntoView = 'bottom-anchor'\n\t\t\t\t\tconsole.log('滚动到底部锚点')\n\n\t\t\t\t\t// 清空scrollIntoView，避免影响后续滚动\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.scrollIntoView = ''\n\t\t\t\t\t}, 300)\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 滚动到最新消息\n\t\t\tscrollToLatestMessage() {\n\t\t\t\tif (this.messageList.length > 0) {\n\t\t\t\t\tconst latestMessage = this.messageList[this.messageList.length - 1]\n\t\t\t\t\tconsole.log('滚动到最新消息:', latestMessage.id, '消息总数:', this.messageList.length)\n\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t// 先尝试使用scroll-into-view\n\t\t\t\t\t\tthis.scrollIntoView = 'msg-' + latestMessage.id\n\n\t\t\t\t\t\t// 同时使用scroll-top作为备选方案\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.scrollTop = 999999\n\t\t\t\t\t\t\tthis.scrollIntoView = ''\n\t\t\t\t\t\t}, 200)\n\n\t\t\t\t\t\t// 再次尝试滚动到底部锚点\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.scrollIntoView = 'bottom-anchor'\n\t\t\t\t\t\t}, 400)\n\n\t\t\t\t\t\t// 最终清空\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.scrollIntoView = ''\n\t\t\t\t\t\t}, 600)\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('没有消息，滚动到底部')\n\t\t\t\t\tthis.scrollToBottom()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 格式化时间\n\t\t\tformatTime(timeStr) {\n\t\t\t\tif (!timeStr) return ''\n\n\t\t\t\t// 处理iOS兼容性：将 \"yyyy-MM-dd HH:mm:ss\" 格式转换为 \"yyyy/MM/dd HH:mm:ss\"\n\t\t\t\tlet processedTimeStr = timeStr\n\t\t\t\tif (typeof timeStr === 'string') {\n\t\t\t\t\t// 如果是 \"2025-08-04 17:10:46\" 格式，转换为 \"2025/08/04 17:10:46\"\n\t\t\t\t\tprocessedTimeStr = timeStr.replace(/-/g, '/')\n\t\t\t\t}\n\n\t\t\t\tconst time = new Date(processedTimeStr)\n\n\t\t\t\t// 检查日期是否有效\n\t\t\t\tif (isNaN(time.getTime())) {\n\t\t\t\t\tconsole.warn('无效的时间格式:', timeStr)\n\t\t\t\t\treturn ''\n\t\t\t\t}\n\n\t\t\t\treturn time.toLocaleTimeString('zh-CN', {\n\t\t\t\t\thour: '2-digit',\n\t\t\t\t\tminute: '2-digit'\n\t\t\t\t})\n\t\t\t},\n\n\n\n\t\t\t// 显示消息操作菜单\n\t\t\tshowMessageActions(message) {\n\t\t\t\tthis.currentMessage = message\n\t\t\t\t// 根据消息类型动态设置操作选项\n\t\t\t\tif (message.role === 'user') {\n\t\t\t\t\tthis.messageActionList = [\n\t\t\t\t\t\t{ text: '复制消息', value: 'copy' },\n\t\t\t\t\t\t{ text: '重新发送', value: 'resend' }\n\t\t\t\t\t]\n\t\t\t\t} else {\n\t\t\t\t\tthis.messageActionList = [\n\t\t\t\t\t\t{ text: '复制消息', value: 'copy' }\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t\tthis.showMessageActionSheet = true\n\t\t\t},\n\n\t\t\t// 处理消息操作\n\t\t\thandleMessageAction(index) {\n\t\t\t\tconst action = this.messageActionList[index]\n\t\t\t\tswitch (action.value) {\n\t\t\t\t\tcase 'copy':\n\t\t\t\t\t\tthis.copyMessage()\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase 'resend':\n\t\t\t\t\t\tthis.resendMessage(this.currentMessage)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 复制消息\n\t\t\tcopyMessage() {\n\t\t\t\tif (this.currentMessage) {\n\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\tdata: this.currentMessage.content,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tthis.$queue.showToast('消息已复制')\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\tthis.$queue.showToast('复制失败')\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 重新发送消息\n\t\t\tresendMessage(message) {\n\t\t\t\tif (message.role !== 'user') return\n\n\t\t\t\t// 重新发送用户消息\n\t\t\t\tthis.inputMessage = message.content\n\t\t\t\tthis.sendMessage()\n\t\t\t},\n\n\t\t\t// 保存对话状态\n\t\t\tsaveConversationState() {\n\t\t\t\t// 可以在这里保存一些本地状态\n\t\t\t\tuni.setStorageSync(`chat_${this.conversationId}`, {\n\t\t\t\t\tscrollPosition: this.scrollTop,\n\t\t\t\t\tlastViewTime: new Date().toISOString()\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 解析markdown为rich-text节点\n\t\t\tparseMarkdown(content) {\n\t\t\t\tif (!content) return []\n\n\t\t\t\ttry {\n\t\t\t\t\t// 使用marked解析markdown\n\t\t\t\t\tconst html = marked(content)\n\n\t\t\t\t\t// 将HTML转换为rich-text节点格式\n\t\t\t\t\treturn this.htmlToNodes(html)\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Markdown解析失败:', error)\n\t\t\t\t\t// 解析失败时返回纯文本\n\t\t\t\t\treturn [{\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\ttext: content\n\t\t\t\t\t}]\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 将HTML转换为rich-text节点\n\t\t\thtmlToNodes(html) {\n\t\t\t\t// 简单的HTML到节点转换\n\t\t\t\t// 这里可以根据需要扩展更多标签支持\n\t\t\t\tconst nodes = []\n\n\t\t\t\t// 处理段落\n\t\t\t\thtml = html.replace(/<p>(.*?)<\\/p>/g, (_, content) => {\n\t\t\t\t\tnodes.push({\n\t\t\t\t\t\tname: 'div',\n\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\tstyle: 'margin-bottom: 16rpx;'\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchildren: this.parseInlineElements(content)\n\t\t\t\t\t})\n\t\t\t\t\treturn ''\n\t\t\t\t})\n\n\t\t\t\t// 处理标题\n\t\t\t\thtml = html.replace(/<h([1-6])>(.*?)<\\/h[1-6]>/g, (_, level, content) => {\n\t\t\t\t\tconst fontSize = level === '1' ? '36rpx' : level === '2' ? '32rpx' : '30rpx'\n\t\t\t\t\tnodes.push({\n\t\t\t\t\t\tname: 'div',\n\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\tstyle: `font-size: ${fontSize}; font-weight: bold; margin: 20rpx 0 16rpx 0;`\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchildren: [{\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\ttext: content\n\t\t\t\t\t\t}]\n\t\t\t\t\t})\n\t\t\t\t\treturn ''\n\t\t\t\t})\n\n\t\t\t\t// 处理代码块\n\t\t\t\thtml = html.replace(/<pre><code>(.*?)<\\/code><\\/pre>/gs, (_, content) => {\n\t\t\t\t\tnodes.push({\n\t\t\t\t\t\tname: 'div',\n\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\tstyle: 'background: #f5f5f5; padding: 20rpx; border-radius: 8rpx; margin: 16rpx 0; font-family: monospace; font-size: 28rpx;'\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchildren: [{\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\ttext: content.trim()\n\t\t\t\t\t\t}]\n\t\t\t\t\t})\n\t\t\t\t\treturn ''\n\t\t\t\t})\n\n\t\t\t\t// 处理列表\n\t\t\t\thtml = html.replace(/<ul>(.*?)<\\/ul>/gs, (_, content) => {\n\t\t\t\t\tconst listItems = content.match(/<li>(.*?)<\\/li>/g) || []\n\t\t\t\t\tconst children = listItems.map(item => {\n\t\t\t\t\t\tconst text = item.replace(/<\\/?li>/g, '')\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tname: 'div',\n\t\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\t\tstyle: 'margin: 8rpx 0;'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tchildren: [{\n\t\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\t\ttext: '• ' + text\n\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tnodes.push({\n\t\t\t\t\t\tname: 'div',\n\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\tstyle: 'margin: 16rpx 0;'\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchildren: children\n\t\t\t\t\t})\n\t\t\t\t\treturn ''\n\t\t\t\t})\n\n\t\t\t\t// 处理剩余的纯文本\n\t\t\t\tif (html.trim()) {\n\t\t\t\t\tnodes.push({\n\t\t\t\t\t\tname: 'div',\n\t\t\t\t\t\tchildren: this.parseInlineElements(html)\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\treturn nodes.length > 0 ? nodes : [{\n\t\t\t\t\ttype: 'text',\n\t\t\t\t\ttext: html || content\n\t\t\t\t}]\n\t\t\t},\n\n\t\t\t// 解析行内元素\n\t\t\tparseInlineElements(content) {\n\t\t\t\tconst elements = []\n\t\t\t\tlet remaining = content\n\n\t\t\t\t// 处理粗体\n\t\t\t\tremaining = remaining.replace(/<strong>(.*?)<\\/strong>/g, (_, text) => {\n\t\t\t\t\telements.push({\n\t\t\t\t\t\tname: 'span',\n\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\tstyle: 'font-weight: bold;'\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchildren: [{\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\ttext: text\n\t\t\t\t\t\t}]\n\t\t\t\t\t})\n\t\t\t\t\treturn '{{PLACEHOLDER}}'\n\t\t\t\t})\n\n\t\t\t\t// 处理斜体\n\t\t\t\tremaining = remaining.replace(/<em>(.*?)<\\/em>/g, (_, text) => {\n\t\t\t\t\telements.push({\n\t\t\t\t\t\tname: 'span',\n\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\tstyle: 'font-style: italic;'\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchildren: [{\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\ttext: text\n\t\t\t\t\t\t}]\n\t\t\t\t\t})\n\t\t\t\t\treturn '{{PLACEHOLDER}}'\n\t\t\t\t})\n\n\t\t\t\t// 处理行内代码\n\t\t\t\tremaining = remaining.replace(/<code>(.*?)<\\/code>/g, (_, text) => {\n\t\t\t\t\telements.push({\n\t\t\t\t\t\tname: 'span',\n\t\t\t\t\t\tattrs: {\n\t\t\t\t\t\t\tstyle: 'background: #f0f0f0; padding: 4rpx 8rpx; border-radius: 4rpx; font-family: monospace;'\n\t\t\t\t\t\t},\n\t\t\t\t\t\tchildren: [{\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\ttext: text\n\t\t\t\t\t\t}]\n\t\t\t\t\t})\n\t\t\t\t\treturn '{{PLACEHOLDER}}'\n\t\t\t\t})\n\n\t\t\t\t// 如果没有特殊格式，直接返回文本\n\t\t\t\tif (elements.length === 0) {\n\t\t\t\t\treturn [{\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\ttext: content\n\t\t\t\t\t}]\n\t\t\t\t}\n\n\t\t\t\t// 处理占位符和普通文本的混合\n\t\t\t\tconst parts = remaining.split('{{PLACEHOLDER}}')\n\t\t\t\tconst result = []\n\n\t\t\t\tfor (let i = 0; i < parts.length; i++) {\n\t\t\t\t\tif (parts[i]) {\n\t\t\t\t\t\tresult.push({\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\ttext: parts[i]\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\tif (i < elements.length) {\n\t\t\t\t\t\tresult.push(elements[i])\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn result\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.chat-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100vh;\n\t\tbackground: #f5f5f5;\n\t\tposition: relative;\n\t}\n\n\t.chat-header {\n\t\tbackground: #0175FE;\n\t\tcolor: white;\n\t\tpadding: 30rpx;\n\t\ttext-align: center;\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: 100;\n\t}\n\n\t.conversation-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: white;\n\t}\n\n\t.message-list {\n\t\tflex: 1;\n\t\tpadding: 20rpx;\n\t\tpadding-bottom: 120rpx; /* 为底部输入框留出空间 */\n\t\toverflow-y: auto;\n\t\theight: calc(100vh - 200rpx); /* 减去头部和输入框的高度 */\n\t}\n\n\t.load-more-history {\n\t\ttext-align: center;\n\t\tpadding: 20rpx;\n\t\tcolor: #666;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.message-item {\n\t\tmargin-bottom: 30rpx;\n\t\twidth: 100%;\n\t}\n\n\t.message-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tgap: 20rpx;\n\t\twidth: 100%;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.user-message {\n\t\tflex-direction: row-reverse;\n\t}\n\n\t.ai-message {\n\t\tflex-direction: row;\n\t}\n\n\t.message-avatar {\n\t\tflex-shrink: 0;\n\t}\n\n\t.message-content {\n\t\tmax-width: 70%;\n\t\tpadding: 20rpx 24rpx;\n\t\tborder-radius: 16rpx;\n\t\tposition: relative;\n\t}\n\n\t.user-content {\n\t\tbackground: #0175FE;\n\t\tcolor: white;\n\t\tborder-bottom-right-radius: 6rpx;\n\t\tmargin-left: auto;\n\t}\n\n\t.ai-content {\n\t\tbackground: white;\n\t\tcolor: #333;\n\t\tborder-bottom-left-radius: 6rpx;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\tmargin-right: auto;\n\t}\n\n\t.ai-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 12rpx;\n\t\tpadding-bottom: 8rpx;\n\t\tborder-bottom: 1rpx solid #eee;\n\t}\n\n\t.ai-name {\n\t\tfont-size: 24rpx;\n\t\tcolor: #0175FE;\n\t\tfont-weight: 500;\n\t}\n\n\t.message-time {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999;\n\t}\n\n\t.message-text {\n\t\tfont-size: 30rpx;\n\t\tline-height: 1.6;\n\t\tword-wrap: break-word;\n\t}\n\n\t.message-status {\n\t\tmargin-top: 12rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8rpx;\n\t}\n\n\t.error-text {\n\t\tcolor: #f56c6c;\n\t\tfont-size: 24rpx;\n\t}\n\n\t.typing-indicator {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 12rpx;\n\t\tcolor: #666;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.input-area {\n\t\tbackground: white;\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-top: 1rpx solid #eee;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 1000;\n\t\t/* #ifdef H5 */\n\t\tpadding-bottom: env(safe-area-inset-bottom);\n\t\t/* #endif */\n\t}\n\n\t.input-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tgap: 16rpx;\n\t\tmin-height: 80rpx;\n\t}\n\n\t.menu-btn {\n\t\twidth: 72rpx;\n\t\theight: 72rpx;\n\t\tborder-radius: 50%;\n\t\tbackground: #f8f9fa;\n\t\tborder: 1rpx solid #e9ecef;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t\tflex-shrink: 0;\n\t}\n\n\t.menu-btn:active {\n\t\tbackground: #e9ecef;\n\t\ttransform: scale(0.95);\n\t}\n\n\t.message-input {\n\t\tflex: 1;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 36rpx;\n\t\tpadding: 20rpx 25rpx;\n\t\tborder: 1rpx solid #e9ecef;\n\t\tfont-size: 30rpx;\n\t\tline-height: 1.4;\n\t\tmin-height: 72rpx;\n\t\tmax-height: 200rpx;\n\t\tresize: none;\n\t\toutline: none;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.message-input:focus {\n\t\tborder-color: #0175FE;\n\t\tbackground: #fff;\n\t\tbox-shadow: 0 0 0 6rpx rgba(1, 117, 254, 0.1);\n\t}\n\n\t.send-btn {\n\t\twidth: 72rpx;\n\t\theight: 72rpx;\n\t\tborder-radius: 50%;\n\t\tbackground: #e9ecef;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t\tflex-shrink: 0;\n\t\tcursor: pointer;\n\t}\n\n\t.send-btn-active {\n\t\tbackground: linear-gradient(135deg, #0175FE 0%, #0056d3 100%);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(1, 117, 254, 0.3);\n\t\ttransform: scale(1.05);\n\t}\n\n\t.send-btn:active {\n\t\ttransform: scale(0.95);\n\t}\n\n\t.send-btn-active:active {\n\t\ttransform: scale(1.0);\n\t}\n\n\t.model-selector {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\tpadding: 40rpx 30rpx;\n\t\tmax-height: 60vh;\n\t}\n\n\t.selector-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 40rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 1rpx solid #eee;\n\t}\n\n\t.selector-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\n\t.model-options {\n\t\tmax-height: 400rpx;\n\t\toverflow-y: auto;\n\t}\n\n\t.model-option {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 16rpx;\n\t\tbackground: #f8f9fa;\n\t\ttransition: all 0.3s ease;\n\n\t\t&.active {\n\t\t\tbackground: #e6f3ff;\n\t\t\tborder: 1rpx solid #0175FE;\n\t\t}\n\t}\n\n\t.model-name {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t}\n\n\t.loading-indicator {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: 12rpx;\n\t\tcolor: #666;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.sending-indicator {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8rpx;\n\t\tcolor: #999;\n\t\tfont-size: 24rpx;\n\t}\n\n\t.typing-indicator {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 12rpx;\n\t\tcolor: #666;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.message-text-container {\n\t\tdisplay: inline;\n\t\tposition: relative;\n\t}\n\n\t.stream-cursor {\n\t\tcolor: #0175FE;\n\t\tfont-weight: bold;\n\t\tanimation: blink 1s infinite;\n\t\tmargin-left: 2rpx;\n\t}\n\n\t@keyframes blink {\n\t\t0%, 50% { opacity: 1; }\n\t\t51%, 100% { opacity: 0; }\n\t}\n\n\t/* Markdown内容样式 */\n\t.markdown-content {\n\t\tline-height: 1.6;\n\t\tword-wrap: break-word;\n\t}\n\n\t/* rich-text组件内的样式会自动应用 */\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=style&index=0&id=1edb40f6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=style&index=0&id=1edb40f6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627869\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}