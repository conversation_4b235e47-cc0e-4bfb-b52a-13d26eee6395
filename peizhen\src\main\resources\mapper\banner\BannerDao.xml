<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.banner.dao.BannerDao">

    <select id="selectList" resultType="com.sqx.modules.banner.entity.Banner">
        select * from banner
        where classify=#{classify} and `state`=1
        order by `sort` desc
    </select>

    <select id="selectLists" resultType="com.sqx.modules.banner.entity.Banner">
        select * from banner
        where classify=#{classify}
        order by `sort` desc
    </select>
    <select id="selectPage" resultType="com.sqx.modules.banner.entity.Banner">
        select * from banner
        where classify=#{classify}
        <if test="state!=null and state!=-1">
            and state=#{state}
        </if>
    </select>


</mapper>