<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.department.dao.DepartmentDao">

    <select id="departmentExcelOut" resultType="com.sqx.modules.department.vo.DepartmentOut">
        select
        d.*,
        IFNULL((SELECT department_name FROM department dp WHERE d.parent_id = dp.department_id),"无上级") AS parentName,
        h.hospital_name
        from department d,
        hospital h
        WHERE d.hospital_id = h.hospital_id
        <if test="department.hospitalId!=null">
            and d.hospital_id = #{department.hospitalId}
        </if>
        <if test="department.isEnable!=null">
            and d.is_enable = #{department.isEnable}
        </if>
        <if test="department.departmentName!=null">
            and d.department_name like concat("%",#{department.departmentName},"%")
        </if>
        <if test="startTime!=null and startTime!='' and endTime!=null and endTime!=''">
            and date_format(d.create_time,'%Y-%m-%d') BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="startTime!=null and startTime!='' and  (endTime==null or endTime=='')">
            and date_format(d.create_time,'%Y-%m-%d') &gt;= #{startTime}
        </if>
        <if test="endTime!=null and endTime!='' and  (startTime==null or startTime=='')">
            and date_format(d.create_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        order by sort asc
    </select>
</mapper>