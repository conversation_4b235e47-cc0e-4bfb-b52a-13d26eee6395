package com.sqx.modules.pay.controller.app;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.pay.dao.PayDetailsDao;
import com.sqx.modules.pay.entity.PayDetails;
import com.sqx.modules.pay.service.WxService;
import com.sqx.modules.task.entity.HelpOrder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/2/26
 */
@RestController
@Api(value = "微信支付", tags = {"微信支付"})
@RequestMapping("/app/wxPay")
@Slf4j
public class ApiWeiXinPayController {

    @Autowired
    private WxService wxService;
    @Autowired
    private PayDetailsDao payDetailsDao;

    @Login
    @ApiOperation("微信jsapi支付充值订单")
    @PostMapping("/wxPayJsApiOrder")
    public Result wxPayJsApiOrder(Long orderId,Long payClassifyId, HttpServletRequest request) throws Exception {
        return wxService.payOrder(orderId, 3,payClassifyId, request);
    }

    @Login
    @ApiOperation("微信公众号支付充值订单")
    @PostMapping("/wxPayMpOrder")
    public Result wxPayMpOrder(Long orderId,Long payClassifyId, HttpServletRequest request) throws Exception {
        return wxService.payOrder(orderId, 2,payClassifyId, request);
    }

    @Login
    @ApiOperation("微信app支付订单")
    @PostMapping("/payAppOrder")
    public Result payAppOrder(Long orderId,Long payClassifyId, HttpServletRequest request) throws Exception {
        return wxService.payOrder(orderId,1,payClassifyId, request);
    }
    @Login
    @ApiOperation("微信h5支付充值订单")
    @PostMapping("/wxPayH5Order")
    public Result wxPayH5Order(Long orderId,Long payClassifyId, HttpServletRequest request) throws Exception {
        return wxService.payOrder(orderId, 4,payClassifyId, request);
    }


    @Login
    @ApiOperation("支付家政订单")
    @PostMapping("/wxPayOrder")
    public Result wxPayOrder(Long orderId, Integer classify, HttpServletRequest request) throws Exception {
        return wxService.wxPayOrder(orderId, classify, request);
    }

    @Login
    @ApiOperation("支付万能订单")
    @PostMapping("/wxPayHelpOrder")
    public Result wxPayHelpOrder(@RequestBody HelpOrder helpOrder, @RequestAttribute Long userId, HttpServletRequest request) throws Exception {
        helpOrder.setUserId(userId);
        return wxService.wxPayHelpOrder(helpOrder, helpOrder.getClassify(), request);
    }

    @Login
    @ApiOperation("微信支付缴纳保证金")
    @PostMapping("/paySafetyMoney")
    public Result wxPaySafetyMoney(@RequestAttribute Long userId, Integer classify,Integer type, HttpServletRequest request) throws Exception {
        return wxService.wxPaySafetyMoney(userId, classify,type, request);
    }


    @PostMapping("/notify")
    @ApiOperation("微信回调")
    public String wxPayNotify(HttpServletRequest request) {
        String resXml = "";
        try {
            InputStream inputStream = request.getInputStream();
            //将InputStream转换成xmlString
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder sb = new StringBuilder();
            String line = null;
            try {
                while ((line = reader.readLine()) != null) {
                    sb.append(line + "\n");
                }
            } catch (IOException e) {
                log.info(e.getMessage());
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            resXml = sb.toString();
            String result = wxService.payBack(resXml, 1);
            log.info("成功");
            log.info(result);

            return result;
        } catch (Exception e) {
            log.info("微信手机支付失败:" + e.getMessage());
            String result = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
            log.info("失败");
            log.info(result);
            return result;
        }
    }

    @PostMapping("/notifyJsApi")
    @ApiOperation("微信回调")
    public String notifyJsApi(HttpServletRequest request) {
        String resXml = "";
        try {
            InputStream inputStream = request.getInputStream();
            //将InputStream转换成xmlString
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder sb = new StringBuilder();
            String line = null;
            try {
                while ((line = reader.readLine()) != null) {
                    sb.append(line + "\n");
                }
            } catch (IOException e) {
                log.info(e.getMessage());
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            resXml = sb.toString();
            String result = wxService.payBack(resXml, 3);
            log.info("成功");
            log.info(result);
            return result;
        } catch (Exception e) {
            log.info("微信手机支付失败:" + e.getMessage());
            String result = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
            log.info("失败");
            log.info(result);
            return result;
        }
    }

    @PostMapping("/notifyMp")
    @ApiOperation("微信回调")
    public String notifyMp(HttpServletRequest request) {
        String resXml = "";
        try {
            InputStream inputStream = request.getInputStream();
            //将InputStream转换成xmlString
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder sb = new StringBuilder();
            String line = null;
            try {
                while ((line = reader.readLine()) != null) {
                    sb.append(line + "\n");
                }
            } catch (IOException e) {
                log.info(e.getMessage());
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            resXml = sb.toString();
            String result = wxService.payBack(resXml, 2);
            log.info("成功");
            log.info(result);
            return result;
        } catch (Exception e) {
            log.info("微信手机支付失败:" + e.getMessage());
            String result = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>" + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
            log.info("失败");
            log.info(result);
            return result;
        }
    }

}
