package com.sqx.modules.appointInformation.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.common.utils.Result;
import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.sqx.modules.appointInformation.service.AppointInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
/**
 * 预约信息-管理端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Slf4j
@Api(value = "预约信息-管理端", tags = {"预约信息-管理端"})
@RestController
@RequestMapping("/admin/Information/")
public class AdmInformationController {
    @Autowired
    private AppointInformationService informationService;

    /**
     * 修改预约信息
     * @param hopeTime 预约时间
     * @param remarks 备注
     * @param ordersId 订单id
     * @return
     */
    @ApiOperation(("修改预约信息"))
    @PostMapping("updateInformation")
    public Result updateInformation(String hopeTime,String remarks,Long ordersId) {
        AppointInformation information = informationService.getOne(new QueryWrapper<AppointInformation>().eq("orders_id", ordersId));
        if (information==null){
            return Result.error("订单不存在");
        }
        information.setHopeTime(hopeTime);
        information.setRemarks(remarks);
        return informationService.updateById(information) ? Result.success() : Result.error();
    }
}

