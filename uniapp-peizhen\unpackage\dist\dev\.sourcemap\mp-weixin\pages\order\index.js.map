{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/order/index.vue?5fdf", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/order/index.vue?4402", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/order/index.vue?1756", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/order/index.vue?21f9", "uni-app:///pages/order/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/order/index.vue?0d88", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/order/index.vue?4762"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "meTabs", "empty", "data", "goods", "game", "tabs", "title", "status", "tabIndex", "page", "limit", "userId", "nick<PERSON><PERSON>", "avatar", "reverTrue", "code", "count", "onLoad", "onHide", "getApp", "onShow", "console", "uni", "methods", "getOrder", "id", "url", "orderTakingUserId", "bindclose", "look", "getlist", "state", "res", "d", "icon", "tabChange", "cancelOrder", "content", "success", "that", "cancel", "delOrder", "goNav", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyExvB;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;MAAA;;MAEAC;MACAC;MACAC;MACAJ;MACAK;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;IACA;MACAC;MACAC;QACAhB;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IAEA;EAEA;EACAiB;IACAC;MACA;QACAC;MACA;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAH;YACAI,oEACAC;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QACAC;QACAtB;QACAC;MACA;MACA;QACA;UACAY;UACA;UACA;YACA;UACA;UACAU;YACA;YACA;YACA;YACA;cACAC;YACA;cACAA;YACA;cACAA;YACA;cACAA;YACA;cACAA;YACA;cACAA;YACA;YACA;UACA;QACA;MACA;QACA;QACAX;UACAhB;UACA4B;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAb;QACAhB;MACA;MACA;IACA;IACA;IACA8B;MACA;MACAd;QACAhB;QACA+B;QACAC;UACA;YACA;cACAb;cACAlB;YACA;YACAgC;cACA;gBACAjB;kBACAhB;gBACA;gBACAiC;gBACAA;cACA;YACA;UACA;YACAlB;UACA;QACA;MACA;IACA;IACA;IACAmB;MACA;MACAlB;QACAhB;QACA+B;QACAC;UACA;YACA;cACAb;cACAlB;YACA;YACAgC;cACA;gBACAjB;kBACAhB;gBACA;gBACAiC;gBACAA;cACA;YACA;UACA;YACAlB;UACA;QACA;MACA;IACA;IACA;IACAoB;MACA;MACAnB;QACAhB;QACA+B;QACAC;UACA;YACA;cACAb;YACA;YACAc;cACA;gBACAjB;kBACAhB;gBACA;gBACAiC;gBACAA;cACA;YACA;UACA;YACAlB;UACA;QACA;MACA;IACA;IACAqB;MACApB;QACAI;MACA;IACA;EACA;EACAiB;IACA;MACArB;QACAhB;QACA4B;MACA;IACA;MACA;MACA;IACA;EACA;EACAU;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpUA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0ca91b30&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0ca91b30&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-avatar/u-avatar\" */ \"@/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goods.length\n  var g1 = _vm.goods.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<!-- 菜单悬浮的原理: 通过给菜单添加position:sticky实现, 用法超简单, 仅APP端的低端机不兼容 https://caniuse.com/#feat=css-sticky -->\n<template>\n\t<view>\n\t\t<view class=\"sticky-tabs bg head\">\n\t\t\t<me-tabs v-model=\"tabIndex\" nameKey='title' :tabs=\"tabs\" @change=\"tabChange\"></me-tabs>\n\t\t</view>\n\t\t<view v-if=\"goods.length > 0\" class=\"margin-lr-sm margin-top-16 padding-sm bg radius\"\n\t\t\tv-for=\"(item,index) in goods\" :key='index' @click=\"goNav('/my/order/pay?id='+item.ordersId+'&isTrue=1')\">\n\t\t\t<view class=\"flex justify-between text-26\">\n\t\t\t\t<view class=\"text-blue\">{{item.statusName}}</view>\n\t\t\t\t<view style=\"color: #999999;\">{{item.updateTime}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex u-p-t-30\">\n\t\t\t\t<view class=\"u-m-r-10\" v-if=\"item.appointInformation\">\n\t\t\t\t\t<u-avatar :src=\"item.appointInformation.hospitalImg?item.appointInformation.hospitalImg: '../../static/logo.png'\" mode=\"square\"\n\t\t\t\t\t\tsize=\"100\">\n\t\t\t\t\t</u-avatar>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-flex-1 text-white margin-left-xs\">\n\t\t\t\t\t<view class=\"text-30  text-bold u-line-1\" style=\"width: 560rpx;\" v-if=\"item.appointInformation\">\n\t\t\t\t\t\t{{item.appointInformation.serviceName}}{{item.appointInformation.hospitalName}}订单</view>\n\t\t\t\t\t<view style=\"color: #999999;font-size: 26upx;\" class=\"margin-top-xs\" v-if=\"item.appointInformation\">\n\t\t\t\t\t\t就诊人:{{item.appointInformation.patientInfo?item.appointInformation.patientInfo.realName:'暂无'}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex u-p-t-20 justify-between align-center\">\n\t\t\t\t<view class=\"text-white flex-sub \">\n\t\t\t\t\t实付：<text class=\"text-df\">￥</text><text class=\"text-xl text-bold\">{{item.payMoney}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex text-right\">\n\t\t\t\t\t<view class=\"btn\" v-if=\"item.state == 1\" @click.stop=\"look(item)\">查看确认码</view>\n\t\t\t\t\t<view v-if=\"item.state == 0||item.state == 4\" @click.stop=\"cancelOrder(item)\" class=\"btn\">取消订单\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.state == 0\" @click.stop=\"goNav('/my/order/pay?id='+item.ordersId+'&isTrue=0')\"\n\t\t\t\t\t\tclass=\"btn\">去支付</view>\n\t\t\t\t\t<!-- <view v-if=\"item.state == 1\" @click=\"goNav('/my/order/pay?id='+item.ordersId+'&isTrue=1')\" class=\"btn\">查看详情</view> -->\n\n\t\t\t\t\t<view v-if=\"item.state == 1\" @click.stop=\"cancel(item)\" class=\"btn\">订单完成</view>\n\t\t\t\t\t<view v-if=\"item.state == 2\" @click.stop=\"getOrder(item.ordersId)\" class=\"btn\">去投诉</view>\n\t\t\t\t\t<view v-if=\"item.state == 2 && item.commentCount == 0\"\n\t\t\t\t\t\************=\"goNav('/my/order/feedback?id='+item.orderTakingId+ '&ordersNo='+item.ordersNo)\"\n\t\t\t\t\t\tclass=\"btn\">去评价</view>\n\t\t\t\t\t<view v-if=\"item.state == 3\" @click.stop=\"delOrder(item)\" class=\"btn\">删除订单</view>\n\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<empty v-if=\"goods.length == 0\" content=\"暂无数据\"></empty>\n\n\t\t<view class=\"boxtk\" v-if=\"reverTrue\">\n\t\t\t<view class=\"whitebox padding\">\n\t\t\t\t<view class=\"flex justify-between align-center\">\n\t\t\t\t\t<view style=\"font-size:38upx;color:#333333\" class=\"text-bold\">确认码</view>\n\t\t\t\t\t<view @click=\"bindclose()\">\n\t\t\t\t\t\t<image src=\"/static/images/msg/close.png\" style=\"width:35upx;height:35upx;\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"margin-top-xl text-center\" style=\"font-size: 88upx;\">\n\t\t\t\t\t{{code}}\n\t\t\t\t</view>\n\n\t\t\t</view>\n\t\t</view>\n\n\t</view>\n</template>\n\n<script>\n\timport meTabs from \"@/components/mescroll-uni/me-tabs/me-tabs.vue\";\n\timport empty from '@/components/empty.vue'\n\n\texport default {\n\t\tcomponents: {\n\t\t\tmeTabs,\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tgoods: [], // 数据列表\n\t\t\t\tgame: [],\n\t\t\t\ttabs: [{\n\t\t\t\t\ttitle: '全部',\n\t\t\t\t\tstatus: ''\n\t\t\t\t}, {\n\t\t\t\t\ttitle: '待付款',\n\t\t\t\t\tstatus: '0'\n\t\t\t\t}, {\n\t\t\t\t\ttitle: '待接单',\n\t\t\t\t\tstatus: '4'\n\t\t\t\t}, {\n\t\t\t\t\ttitle: '待服务',\n\t\t\t\t\tstatus: '5'\n\t\t\t\t}, {\n\t\t\t\t\ttitle: '进行中',\n\t\t\t\t\tstatus: '1'\n\t\t\t\t}, {\n\t\t\t\t\ttitle: '已完成',\n\t\t\t\t\tstatus: '2'\n\t\t\t\t}, {\n\t\t\t\t\ttitle: '已取消',\n\t\t\t\t\tstatus: '3'\n\t\t\t\t}],\n\t\t\t\ttabIndex: 0, // tab下标\n\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10,\n\t\t\t\tuserId: 0,\n\t\t\t\tstatus: 1,\n\t\t\t\tnickName: '',\n\t\t\t\tavatar: '',\n\t\t\t\treverTrue: false,\n\t\t\t\tcode: '',\n\t\t\t\tcount: ''\n\t\t\t}\n\t\t},\n\t\tonLoad(e) {\n\t\t\tthis.nickName = uni.getStorageSync('nickName')\n\t\t},\n\t\tonHide(){\n\t\t\tgetApp().globalData.tabIndex = ''\n\t\t},\n\t\tonShow() {\n\t\t\tthis.userId = uni.getStorageSync('userId')\n\t\t\tif (this.userId) {\n\t\t\t\tconsole.log(getApp().globalData.tabIndex,'zzzzzzzzzzzzzz')\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle:'加载中',\n\t\t\t\t})\n\t\t\t\tif(getApp().globalData.tabIndex){\n\t\t\t\t\tthis.tabIndex = getApp().globalData.tabIndex\n\t\t\t\t\tthis.getlist()\n\t\t\t\t}else{\n\t\t\t\t\tthis.tabIndex = 0\n\t\t\t\t\tthis.getlist()\n\t\t\t\t}\n\t\t\t\t\n\t\t\t}\n\n\t\t},\n\t\tmethods: {\n\t\t\tgetOrder(id) {\n\t\t\t\tlet data = {\n\t\t\t\t\tid: id\n\t\t\t\t}\n\t\t\t\tthis.$Request.get('/app/orders/queryOrders', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tlet item = res.data\n\t\t\t\t\t\t// uni.navigateTo({\n\t\t\t\t\t\t// \turl: '/my/order/complain?id=' + item.ordersId + '&byUserId=' + item\n\t\t\t\t\t\t// \t\t.orderTakingUserId + '&userName=' + item.user.userName + '&title=' + item\n\t\t\t\t\t\t// \t\t.orderTaking.myLevel + '&ordersNo=' + item.ordersNo\n\t\t\t\t\t\t// })\n\t\t\t\t\t\tlet title = item.appointInformation.serviceName+item.appointInformation.hospitalName+'订单'\n\t\t\t\t\t\t// console.log(title)\n\t\t\t\t\t\t// return\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/my/order/complain?id=' + item.ordersId + '&byUserId=' + item\n\t\t\t\t\t\t\t\t.orderTakingUserId  + '&title=' + title+ '&ordersNo=' + item.ordersNo\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindclose() {\n\t\t\t\tthis.reverTrue = false\n\t\t\t},\n\t\t\tlook(e) {\n\t\t\t\tthis.reverTrue = true\n\t\t\t\tthis.code = e.code\n\t\t\t},\n\t\t\tgetlist() {\n\t\t\t\tlet curTab = this.tabs[this.tabIndex].status\n\n\t\t\t\tlet data = {\n\t\t\t\t\tstate: curTab,\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit\n\t\t\t\t}\n\t\t\t\tthis.$Request.get('/app/orders/selectMyOrder', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\tthis.count = res.data.totalCount\n\t\t\t\t\t\tif (this.page == 1) {\n\t\t\t\t\t\t\tthis.goods = []\n\t\t\t\t\t\t}\n\t\t\t\t\t\tres.data.records.forEach(d => {\n\t\t\t\t\t\t\t// if (d.gameName) {\n\t\t\t\t\t\t\t// \td.gameName = d.gameName.split(',')\n\t\t\t\t\t\t\t// }\n\t\t\t\t\t\t\tif (d.state == 0) {\n\t\t\t\t\t\t\t\td.statusName = '待付款'\n\t\t\t\t\t\t\t} else if (d.state == 1) {\n\t\t\t\t\t\t\t\td.statusName = '进行中'\n\t\t\t\t\t\t\t} else if (d.state == 2) {\n\t\t\t\t\t\t\t\td.statusName = '已完成'\n\t\t\t\t\t\t\t} else if (d.state == 3) {\n\t\t\t\t\t\t\t\td.statusName = '已取消'\n\t\t\t\t\t\t\t} else if (d.state == 4) {\n\t\t\t\t\t\t\t\td.statusName = '待接单'\n\t\t\t\t\t\t\t} else if (d.state == 5) {\n\t\t\t\t\t\t\t\td.statusName = '待服务'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.goods.push(d);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}).catch(() => {\n\t\t\t\t\t//联网失败, 结束加载\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 切换菜单\n\t\t\ttabChange() {\n\t\t\t\tthis.goods = []; // 置空列表,显示加载进度条\n\t\t\t\tthis.page = 1\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle:'加载中',\n\t\t\t\t})\n\t\t\t\tthis.getlist()\n\t\t\t},\n\t\t\t// 取消订单\n\t\t\tcancelOrder(e) {\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确认取消订单吗?',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\t\t\t\tstatus: '3'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.get('/app/orders/cancelOrder', data).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle:'订单已取消'\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\tthat.page = 1\n\t\t\t\t\t\t\t\t\tthat.getlist()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 完成订单\n\t\t\tcancel(e) {\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '订单完成后款项将支付给服务方，确认完成订单吗?',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\t\t\t\tstatus: '2'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.get('/app/orders/cancelOrder', data).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle:'订单已完成'\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\tthat.page = 1\n\t\t\t\t\t\t\t\t\tthat.getlist()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//删除\n\t\t\tdelOrder(e) {\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确定删除订单吗?',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.get('/app/orders/deleteOrder', data).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: \"删除成功\"\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\tthat.page = 1\n\t\t\t\t\t\t\t\t\tthat.getlist()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoNav(url) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tif (this.count == this.goods.length) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已经到底了',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\tthis.page = this.page + 1;\n\t\t\t\tthis.getlist()\n\t\t\t}\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\tthis.getlist()\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/*\n\tsticky生效条件：\n\t1、父元素不能overflow:hidden或者overflow:auto属性。(mescroll-body设置:sticky=\"true\"即可, mescroll-uni本身没有设置overflow)\n\t2、必须指定top、bottom、left、right4个值之一，否则只会处于相对定位\n\t3、父元素的高度不能低于sticky元素的高度\n\t4、sticky元素仅在其父元素内生效,所以父元素必须是 mescroll\n\t*/\n\t.sticky-tabs {\n\t\tz-index: 990;\n\t\tposition: sticky;\n\t\ttop: var(--window-top);\n\t\t// background-color: #fff;\n\t}\n\n\t// 使用mescroll-uni,则top为0\n\t.mescroll-uni,\n\t::v-deep .mescroll-uni {\n\t\t.sticky-tabs {\n\t\t\ttop: 0;\n\t\t}\n\t}\n\n\t.head {\n\t\t/* #ifdef APP */\n\t\tpadding-top: 100upx;\n\t\t/* #endif */\n\n\t}\n\n\t.demo-tip {\n\t\tpadding: 18upx;\n\t\tfont-size: 24upx;\n\t\ttext-align: center;\n\t}\n\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.btn {\n\t\tcolor: #0175FE;\n\t\tbackground: #D9EBFF;\n\t\tpadding: 15upx 30upx;\n\t\tborder-radius: 50upx;\n\t\tmargin-left: 20upx;\n\t\tfont-size: 24upx;\n\t}\n\n\t.boxtk {\n\t\twidth: 100%;\n\t\theight: 162vh;\n\t\tbackground: rgba(0, 0, 0, 0.7);\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 99;\n\n\t}\n\n\t.whiteboxs {\n\t\t// width: 461upx;\n\t\t// height: 563upx;\n\t\ttext-align: -webkit-center;\n\t\tborder-radius: 32upx;\n\t\tmargin: 0 auto;\n\t\tposition: fixed;\n\t\ttop: 185px;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 99;\n\t}\n\n\t.whitebox {\n\t\twidth: 550upx;\n\t\theight: 350upx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 32upx;\n\t\tmargin: 0 auto;\n\t\tposition: fixed;\n\t\ttop: 450upx;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 99;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627752\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}