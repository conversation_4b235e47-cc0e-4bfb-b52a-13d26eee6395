package com.sqx.modules.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sqx.modules.app.entity.Address;
import com.sqx.modules.app.entity.App;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地址
 *
 */
@Mapper
public interface AddressDao extends BaseMapper<Address> {

    int updateAddressIsDefault(@Param("userId") Long userId);

}
