package com.sqx.modules.orders.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.orders.dao.PayOrderDao;
import com.sqx.modules.orders.entity.PayOrder;
import com.sqx.modules.orders.service.PayOrderService;
import com.sqx.modules.utils.AliPayOrderUtil;
import lombok.AllArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
@AllArgsConstructor
public class PayOrderServiceImpl extends ServiceImpl<PayOrderDao, PayOrder> implements PayOrderService {
    private CommonInfoService commonInfoService;

    @Override
    public Result insertOrder(Long userId, BigDecimal money) {
        CommonInfo one = commonInfoService.findOne(332);
        if (!"是".equals(one.getValue())){
            return Result.error("充值功能暂未开放");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int i = money.compareTo(BigDecimal.ZERO);
        if (i == 1) {
            //创建订单模板
            PayOrder order = new PayOrder();
            //订单编号
            order.setOrdersNo(AliPayOrderUtil.createOrderId());
            //设置充值金额
            order.setPayMoney(money);
            //查询金币比例
            String scale = commonInfoService.findOne(154).getValue();
            Integer scel = Integer.parseInt(scale);
            //计算
            BigDecimal nmoeny = money.multiply(BigDecimal.valueOf(scel));
            //设置金币金额
            order.setMoney(nmoeny);
            //状态
            order.setState(0);
            //用户id
            order.setUserId(userId);
            //设置创建时间
            order.setCreateTime(simpleDateFormat.format(new Date()));
            //插入到表中
            baseMapper.insert(order);

            return Result.success().put("data", order);
        } else {
            return Result.error("充值的金额不合法！");
        }
    }
}
