<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.hospital.dao.HospitalDao">

    <select id="getCityList" resultType="java.lang.String">
        SELECT city
        FROM hospital
        WHERE city_initial = #{letter}
        GROUP BY city
    </select>

    <select id="getLetter" resultType="com.sqx.modules.hospital.entity.LetterCity">
        SELECT city_initial as letter
        FROM hospital
        GROUP BY city_initial
        ORDER BY city_initial asc
    </select>
    <select id="getHospitalOrderCount" resultType="com.sqx.modules.hospital.entity.Hospital">
        select * from (
        SELECT
        h.*,
        (SELECT COUNT(1) FROM
        appoint_information a,
        orders o
        LEFT JOIN tb_user u
        ON o.order_taking_user_id = u.user_id
        WHERE o.state=4 AND o.hospital_id = h.hospital_id and a.orders_id = o.orders_id

        <if test="ordersNo!=null and ordersNo!=''">
            and o.orders_no like concat("%",#{ordersNo},"%")
        </if>
        <if test="riderPhone!=null and riderPhone!=''">
            and u.phone like concat("%",#{riderPhone},"%")
        </if>
        ) AS ordersCount
        FROM
        hospital h
        where 1=1
        <if test="hospitalName!=null and hospitalName!=''">
            and h.hospital_name like concat("%",#{hospitalName},"%")
        </if>
        <if test="city!=null and city!=''">
            and (h.city like concat("%",#{city},"%") or h.province like concat("%",#{city},"%"))
        </if>
        ) a1
        where ordersCount>0
    </select>
</mapper>