package com.sqx.modules.app.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.modules.app.dao.AddressDao;
import com.sqx.modules.app.dao.AppDao;
import com.sqx.modules.app.entity.Address;
import com.sqx.modules.app.entity.App;
import com.sqx.modules.app.service.AddressService;
import com.sqx.modules.app.service.AppService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("AddressService")
public class AddressServiceImpl extends ServiceImpl<AddressDao, Address> implements AddressService {

    @Override
    public int updateAddressIsDefault(Long userId){
        return baseMapper.updateAddressIsDefault(userId);
    }


}
