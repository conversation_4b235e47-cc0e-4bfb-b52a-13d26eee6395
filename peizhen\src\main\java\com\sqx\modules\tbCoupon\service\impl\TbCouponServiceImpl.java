package com.sqx.modules.tbCoupon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.pay.entity.PayClassify;
import com.sqx.modules.pay.entity.PayDetails;
import com.sqx.modules.pay.service.PayClassifyService;
import com.sqx.modules.tbCoupon.dao.TbCouponDao;
import com.sqx.modules.tbCoupon.entity.TbCoupon;
import com.sqx.modules.tbCoupon.entity.TbCouponUser;
import com.sqx.modules.tbCoupon.service.TbCouponService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
public class TbCouponServiceImpl extends ServiceImpl<TbCouponDao, TbCoupon> implements TbCouponService {

    @Autowired
    private TbCouponDao tbCouponDao;
    @Autowired
    private PayClassifyService classifyService;
    @Override
    public Result addCoupon(TbCoupon tbCoupon) {
        tbCoupon.setDeleteFlag(0);
        tbCouponDao.insert(tbCoupon);
        return Result.success();
    }

    @Override
    public IPage<TbCoupon> getCouponPageList(Integer page, Integer limit, TbCoupon tbCoupon) {
        Page<TbCoupon> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return tbCouponDao.selectPage(pages, new QueryWrapper<>(tbCoupon));
    }

    @Override
    public Result deleteCoupon(Long couponId) {
        int count = classifyService.count(new QueryWrapper<PayClassify>().eq("coupon_id", couponId));
        if (count>0){
            return Result.error("当前优惠券已被绑定充值赠送,请先修改");
        }
        return Result.upStatus(tbCouponDao.deleteById(couponId));
    }

    @Override
    public Result updateCoupon(TbCoupon tbCoupon) {
        return Result.upStatus(tbCouponDao.updateById(tbCoupon));
    }




}
