{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedback/index.vue?194a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedback/index.vue?1063", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedback/index.vue?968a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedback/index.vue?221b", "uni-app:///my/feedback/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedback/index.vue?6ec0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/feedback/index.vue?a777"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "msgContents", "stars", "imageList", "sendDate", "score", "content", "contact", "onLoad", "appid", "imei", "p", "md", "app_version", "plus_version", "os", "net", "methods", "close", "chooseMsg", "uni", "itemList", "success", "chooseImg", "sourceType", "sizeType", "count", "chooseStar", "previewImage", "urls", "send", "console", "icon", "title", "state", "setTimeout", "showCancel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCexvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACAC;QACAC;QACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAH;QACAI;QACAC;QACAC;QACAJ;UACA;QACA;MACA;IACA;IACAK;MACA;MACA;IACA;IACAC;MACA;MACAR;QACAS;MACA;IACA;IACAC;MACA;MACAC;MAEA;QACAX;UACAY;UACAC;QACA;QACA;MACA;MACA;QACAb;UACAY;UACAC;QACA;QACA;MACA;MACA;MACA;QACAA;QACA3B;QACA4B;MACA;QACA;UACAd;YACAa;UACA;UACAE;YACAf;UACA;QACA;UACAA;UACAA;YACAgB;YACAH;YACA3B;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAA2iC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACA/jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/feedback/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/feedback/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=d3473126&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/feedback/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=d3473126&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\" style=\"background-color: #ffffff;\">\n\t\t<view class=\"feedback-title\">\n\t\t\t<text>问题和意见</text>\n\t\t\t<text @tap=\"chooseMsg\">快速键入</text>\n\t\t</view>\n\t\t<view class=\"feedback-body\"><textarea placeholder=\"请详细描述你的问题和意见...\" v-model=\"sendDate.content\" class=\"feedback-textare\" /></view>\n\t\t<view class=\"feedback-title\"><text>QQ/邮箱</text></view>\n\t\t<view class=\"feedback-body\"><input class=\"feedback-input\" v-model=\"sendDate.contact\" placeholder=\"方便我们联系你 \" /></view>\n\n\t\t<button style=\"\" class=\"feedback-submit\" @tap=\"send\">提交</button>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tmsgContents: ['界面显示错乱', '启动缓慢，卡出翔了', 'UI无法直视，丑哭了', '偶发性崩溃'],\n\t\t\tstars: [1, 2, 3, 4, 5],\n\t\t\timageList: [],\n\t\t\tsendDate: {\n\t\t\t\tscore: 5,\n\t\t\t\tcontent: '',\n\t\t\t\tcontact: ''\n\t\t\t}\n\t\t};\n\t},\n\tonLoad() {\n\t\tlet deviceInfo = {\n\t\t\tappid: plus.runtime.appid,\n\t\t\timei: plus.device.imei, //设备标识\n\t\t\tp: plus.os.name === 'Android' ? 'a' : 'i', //平台类型，i表示iOS平台，a表示Android平台。\n\t\t\tmd: plus.device.model, //设备型号\n\t\t\tapp_version: plus.runtime.version,\n\t\t\tplus_version: plus.runtime.innerVersion, //基座版本号\n\t\t\tos: plus.os.version,\n\t\t\tnet: '' + plus.networkinfo.getCurrentType()\n\t\t};\n\t\tthis.sendDate = Object.assign(deviceInfo, this.sendDate);\n\t},\n\tmethods: {\n\t\tclose(e) {\n\t\t\tthis.imageList.splice(e, 1);\n\t\t},\n\t\tchooseMsg() {\n\t\t\t//快速输入\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: this.msgContents,\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tthis.sendDate.content = this.msgContents[res.tapIndex];\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tchooseImg() {\n\t\t\t//选择图片\n\t\t\tuni.chooseImage({\n\t\t\t\tsourceType: ['camera', 'album'],\n\t\t\t\tsizeType: 'compressed',\n\t\t\t\tcount: 8 - this.imageList.length,\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tthis.imageList = this.imageList.concat(res.tempFilePaths);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tchooseStar(e) {\n\t\t\t//点击评星\n\t\t\tthis.sendDate.score = e;\n\t\t},\n\t\tpreviewImage() {\n\t\t\t//预览图片\n\t\t\tuni.previewImage({\n\t\t\t\turls: this.imageList\n\t\t\t});\n\t\t},\n\t\tsend() {\n\t\t\t//发送反馈\n\t\t\tconsole.log(JSON.stringify(this.sendDate));\n\n\t\t\tif (!this.sendDate.content) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请输入反馈内容'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (!this.sendDate.contact) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请填写QQ或邮箱'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.$queue.showLoading('加载中...');\n\t\t\tthis.$Request.postJson('/app/message/insertMessage', {\n\t\t\t\ttitle: this.sendDate.contact,\n\t\t\t\tcontent: JSON.stringify(this.sendDate),\n\t\t\t\tstate: 2\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '投诉成功'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\ttitle: '投诉失败',\n\t\t\t\t\t\tcontent: res.msg\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style>\n@font-face {\n\tfont-family: uniicons;\n\tfont-weight: normal;\n\tfont-style: normal;\n\tsrc: url('https://img-cdn-qiniu.dcloud.net.cn/fonts/uni.ttf') format('truetype');\n}\npage {\n\tbackground-color: #F5F5F5 !important;\n}\nview {\n\tfont-size: 28upx;\n}\n\n\n/*问题反馈*/\n.feedback-title {\n\tdisplay: flex;\n\tflex-direction: row;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20upx;\n\tcolor: #8f8f94;\n\tfont-size: 28upx;\n}\n.feedback-star-view.feedback-title {\n\tjustify-content: flex-start;\n\tmargin: 0;\n}\n\n.feedback-body {\n\tfont-size: 32upx;\n\tpadding: 16upx;\n\tmargin: 16upx;\n\tborder-radius: 16upx;\n\tbackground: #FFFFFF;\n\t/* color: #FFF; */\n}\n.feedback-textare {\n\theight: 200upx;\n\tfont-size: 34upx;\n\tline-height: 50upx;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tpadding: 20upx 30upx 0;\n\n}\n.feedback-input {\n\tfont-size: 32upx;\n\theight: 60upx;\n\tpadding: 15upx 20upx;\n\tline-height: 60upx;\n}\n\n\n.feedback-submit {\n\tbackground: #557EFD;\n\tcolor: #ffffff;\n\tmargin: 20upx;\n\tmargin-top: 32upx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447624487\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}