package com.sqx.modules.app.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.entity.UserCertification;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserCertificationService;
import com.sqx.modules.app.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@AllArgsConstructor
@RequestMapping("/app/userCertification")
@Api("app实 名 认 证")
public class AppUserCertificationController {
    @Autowired
    private UserCertificationService userCertificationService;
    @Autowired
    private UserService userService;
    /**
     * 实名认证
     **/
    @RequestMapping("/insert")
    @Login
    @ApiOperation("实名认证")
    public Result insert(@RequestAttribute Long userId, @RequestBody UserCertification userCertification) {
        userCertification.setUserId(userId);
        return userCertificationService.insert(userCertification);
    }

    /**
     * 是否进行实名
     */
    @RequestMapping("/isInsert")
    @Login
    @ApiOperation("是否进行实名")
    public Result isInsert(@RequestAttribute Long userId) {
        return userCertificationService.isInsert(userId);
    }

    /**
     * 查询自己的实名信息
     */
    @RequestMapping("/queryInsert")
    @Login
    @ApiOperation("查询自己的实名信息")
    public Result queryInsert(@RequestAttribute Long userId) {

        return userCertificationService.queryInsert(userId);
    }

    /**
     * @param avatar     头像
     * @param serviceIds 服务ids
     * @param details    简介
     * @return
     */
    @Login
    @PostMapping("/setRealRealInfo")
    @ApiOperation("修改实名信息(无需审核)")
    public Result setRealRealInfo(@RequestAttribute("userId") Long userId, String avatar, String serviceIds, String details) {
        UserCertification certification = userCertificationService.getOne(new QueryWrapper<UserCertification>().eq("user_id", userId));
        if (certification != null) {
            certification.setAvatar(avatar);
            certification.setServiceIds(serviceIds);
            certification.setDetails(details);
            return userCertificationService.updateById(certification) ? Result.success() : Result.error();
        }
        return Result.error("请先实名认证");
    }
    @GetMapping("/getNursingInfo")
    @ApiOperation("获取护工信息")
    public Result getNursingInfo(Long userId){
        UserCertification userCertification = userCertificationService.getUserCertification(userId);

        UserEntity userEntity = userService.getNursingInfo(userId);
        userCertification.setUserEntity(userEntity);
        return Result.success().put("data", userCertification);
    }
}
