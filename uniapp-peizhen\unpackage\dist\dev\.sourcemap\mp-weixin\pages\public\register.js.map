{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/register.vue?a6c8", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/register.vue?f721", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/register.vue?fa74", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/register.vue?bb24", "uni-app:///pages/public/register.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/register.vue?384b", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/register.vue?403e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userName", "showAgree", "code", "phone", "password", "required", "sending", "sendTime", "count", "relation", "state", "invitation", "platform", "onLoad", "methods", "showMa", "invitationMa", "newMa", "isShowAgree", "sendMsg", "uni", "showCancel", "title", "content", "countDown", "setTimeout", "inputChange", "navBack", "navTo", "url", "<PERSON><PERSON><PERSON><PERSON>", "openId", "msg", "getUserInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0D3vB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EAOA;EACAC;IACAC;MAAA;MACA;MACA;QACA;UACA;QAEA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QAEA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA,IACAhB,QACA,KADAA;MAEA;QACA;MACA;QACA;MACA;QACA;QACA;UACA;YACA;YACA;YACA;YACAiB;UACA;YACAA;YACAA;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MACA,IACAhB,QACA,KADAA;MAEA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACAiB;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAP;IACA;IAEAQ;MACAR;QACAS;MACA;IACA;IACAC;MAAA;MACA,IACA9B,WAMA,KANAA;QACAG,QAKA,KALAA;QACAC,WAIA,KAJAA;QACAF,OAGA,KAHAA;QACAD,YAEA,KAFAA;QACAU,aACA,KADAA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;QACA;QACA;UACAX;UACAI;UACAD;UACA4B;UACApB;UACAC;UACAoB;QACA;UACA;YACA;YAEA;YACA;YACA;YACA;YACA,oEACA;YACA;YACAP;cACAL;gBACAS;cACA;YACA;UACA;YACAT;YACAA;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;QACA;QACA;QACA;UACAvB;UACAI;UACAD;UACA4B;UACApB;UACAC;QACA;UACA;YACA;YACA;YACA;YACA;YACA,oEACA;YACA;YACA;YACA;YACAa;cACAL;gBACAS;cACA;YACA;UACA;YACAT;cACAC;cACAC;cACAC;YACA;UACA;UACAH;QACA;MACA;IACA;IACA;IACAa;MAAA;MACA;QACA;UACAb;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;UACA;UACAA;UACAK;YACAL;cACAS;YACA;UACA;QACA;UACAT;YACAC;YACAC;YACAC;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1SA;AAAA;AAAA;AAAA;AAA03C,CAAgB,+tCAAG,EAAC,C;;;;;;;;;;;ACA94C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/public/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/public/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=33acbfa0&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/public/register.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=33acbfa0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"wrapper\">\r\n\t\t\t<view class=\"input-content\">\r\n\t\t\t\t<view class=\"cu-form-group\"\r\n\t\t\t\t\tstyle=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\r\n\t\t\t\t\t<view class=\"title text-black\">用户名</view>\r\n\t\t\t\t\t<input :value=\"userName\" placeholder=\"请输入用户名\" data-key=\"userName\" @input=\"inputChange\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cu-form-group\"\r\n\t\t\t\t\tstyle=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\r\n\t\t\t\t\t<view class=\"title text-black\">手机号</view>\r\n\t\t\t\t\t<input type=\"number\" :value=\"phone\" placeholder=\"请输入手机号\" maxlength=\"11\" data-key=\"phone\"\r\n\t\t\t\t\t\t@input=\"inputChange\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cu-form-group padding-right-xs\"\r\n\t\t\t\t\tstyle=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\r\n\t\t\t\t\t<text class=\"title text-black\">验证码</text>\r\n\t\t\t\t\t<input type=\"number\" :value=\"code\" placeholder=\"请输入验证码\" maxlength=\"6\" data-key=\"code\"\r\n\t\t\t\t\t\t@input=\"inputChange\" @confirm=\"toLogin\" />\r\n\t\t\t\t\t<button class=\"send-msg\" @click=\"sendMsg\" :disabled=\"sending\">{{ sendTime }}</button>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"cu-form-group\"\r\n\t\t\t\t\tstyle=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\">\r\n\t\t\t\t\t<text class=\"title text-black\">设置密码</text>\r\n\t\t\t\t\t<input type=\"password\" :value=\"password\" placeholder=\"请设置密码\" placeholder-class=\"input-empty\"\r\n\t\t\t\t\t\tmaxlength=\"20\" minlength=\"6\" data-key=\"password\" @input=\"inputChange\" @confirm=\"toLogin\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cu-form-group\"\r\n\t\t\t\t\tstyle=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\"\r\n\t\t\t\t\tv-if=\"required == '是'\">\r\n\t\t\t\t\t<text class=\"title text-black\">邀请码</text>\r\n\t\t\t\t\t<input type=\"\" maxlength=\"6\" :value=\"invitation\" placeholder=\"请填写邀请码(必填)\" data-key=\"invitation\"\r\n\t\t\t\t\t\t@input=\"inputChange\" @confirm=\"toLogin\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cu-form-group\"\r\n\t\t\t\t\tstyle=\"border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px\"\r\n\t\t\t\t\tv-if=\"required == '否'\">\r\n\t\t\t\t\t<text class=\"title text-black\">邀请码</text>\r\n\t\t\t\t\t<input type=\"\" maxlength=\"6\" :value=\"invitation\" placeholder=\"请填写邀请码(选填)\" data-key=\"invitation\"\r\n\t\t\t\t\t\t@input=\"inputChange\" @confirm=\"toLogin\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"confirm-btn\" @click=\"toLogin\">立即注册</button>\r\n\t\t\t<view class=\"footer\">\r\n\t\t\t\t<text @tap=\"isShowAgree\" class=\"cuIcon\"\r\n\t\t\t\t\t:class=\"showAgree ? 'cuIcon-radiobox' : 'cuIcon-round'\">同意</text>\r\n\t\t\t\t<!-- 协议地址 -->\r\n\t\t\t\t<navigator url=\"/my/setting/mimi\" open-type=\"navigate\">《隐私政策》</navigator>\r\n\t\t\t\t和\r\n\t\t\t\t<navigator url=\"/my/setting/xieyi\" open-type=\"navigate\">《用户服务协议》</navigator>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserName: '',\r\n\t\t\t\tshowAgree: false,\r\n\t\t\t\tcode: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tpassword: '',\r\n\t\t\t\trequired: '否',\r\n\t\t\t\tsending: false,\r\n\t\t\t\tsendTime: '获取验证码',\r\n\t\t\t\tcount: 60,\r\n\t\t\t\trelation: \"\",\r\n\t\t\t\tstate: '',\r\n\t\t\t\tinvitation: '',\r\n\t\t\t\tplatform: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.invitation = this.$queue.getData('inviterCode') ? this.$queue.getData('inviterCode') : '';\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.platform = 'app'\r\n\t\t\t//#endif\r\n\t\t\t//#ifdef H5\r\n\t\t\tthis.platform = 'H5'\r\n\t\t\t// #endif \r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tshowMa() {\r\n\t\t\t\t//查询官方邀请码\r\n\t\t\t\tthis.$Request.getT('/app/common/type/4').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.invitationCode = res.data.value;\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 注册邀请码必填\r\n\t\t\tinvitationMa() {\r\n\t\t\t\tthis.$Request.getT('/app/common/type/3').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.required = res.data.value;\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 获取新用户优惠券数量\r\n\t\t\tnewMa() {\r\n\t\t\t\tthis.$Request.getT('/app/common/type/119').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.amount = res.data.value;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tisShowAgree() {\r\n\t\t\t\t//是否选择协议\r\n\t\t\t\tthis.showAgree = !this.showAgree;\r\n\t\t\t},\r\n\t\t\tsendMsg() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tphone\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (!phone) {\r\n\t\t\t\t\tthis.$queue.showToast('请输入手机号');\r\n\t\t\t\t} else if (phone.length !== 11) {\r\n\t\t\t\t\tthis.$queue.showToast('请输入正确的手机号');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$queue.showLoading('正在发送验证码...');\r\n\t\t\t\t\tthis.$Request.getT('/app/Login/sendMsg/' + phone + '/1').then(res => {\r\n\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\tthis.sending = true;\r\n\t\t\t\t\t\t\tthis.$queue.showToast('验证码发送成功请注意查收');\r\n\t\t\t\t\t\t\tthis.countDown();\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\ttitle: '短信发送失败',\r\n\t\t\t\t\t\t\t\tcontent: res.msg ? res.msg : '请一分钟后再获取验证码'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcountDown() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcount\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (count === 1) {\r\n\t\t\t\t\tthis.count = 60;\r\n\t\t\t\t\tthis.sending = false;\r\n\t\t\t\t\tthis.sendTime = '获取验证码';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.count = count - 1;\r\n\t\t\t\t\tthis.sending = true;\r\n\t\t\t\t\tthis.sendTime = count - 1 + '秒后重新获取';\r\n\t\t\t\t\tsetTimeout(this.countDown.bind(this), 1000);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinputChange(e) {\r\n\t\t\t\tconst key = e.currentTarget.dataset.key;\r\n\t\t\t\tthis[key] = e.detail.value;\r\n\t\t\t},\r\n\t\t\tnavBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\r\n\t\t\tnavTo(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoLogin() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tuserName,\r\n\t\t\t\t\tphone,\r\n\t\t\t\t\tpassword,\r\n\t\t\t\t\tcode,\r\n\t\t\t\t\tshowAgree,\r\n\t\t\t\t\tinvitation,\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (!userName) {\r\n\t\t\t\t\tthis.$queue.showToast('请输入用户名');\r\n\t\t\t\t} else if (!phone) {\r\n\t\t\t\t\tthis.$queue.showToast('请输入手机号');\r\n\t\t\t\t} else if (!code) {\r\n\t\t\t\t\tthis.$queue.showToast('请输入验证码');\r\n\t\t\t\t} else if (!password) {\r\n\t\t\t\t\tthis.$queue.showToast('请设置密码');\r\n\t\t\t\t} else if (password.length < 6) {\r\n\t\t\t\t\tthis.$queue.showToast('密码位数必须大于六位');\r\n\t\t\t\t} else if (!showAgree) {\r\n\t\t\t\t\tthis.$queue.showToast('请先同意《协议》');\r\n\t\t\t\t} else if (!invitation && this.required == '是') {\r\n\t\t\t\t\tthis.$queue.showToast('请填写邀请码');\r\n\t\t\t\t} else if (!invitation && this.required == '否') {\r\n\t\t\t\t\tthis.logining = true;\r\n\t\t\t\t\tthis.$queue.showLoading('注册中...');\r\n\t\t\t\t\tthis.$Request.post(`/app/Login/registApp`, {\r\n\t\t\t\t\t\tuserName: userName,\r\n\t\t\t\t\t\tpassword: password,\r\n\t\t\t\t\t\tphone: phone,\r\n\t\t\t\t\t\topenId: this.$queue.getData('openid') ? this.$queue.getData('openid') : '',\r\n\t\t\t\t\t\tinvitation: this.invitation,\r\n\t\t\t\t\t\tplatform: this.platform,\r\n\t\t\t\t\t\tmsg: code\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\tthis.$queue.showToast('注册成功');\r\n\r\n\t\t\t\t\t\t\tthis.$queue.setData(\"token\", res.token);\r\n\t\t\t\t\t\t\tthis.$queue.setData('userId', res.user.userId);\r\n\t\t\t\t\t\t\tthis.$queue.setData('userName', res.user.userName);\r\n\t\t\t\t\t\t\tthis.$queue.setData('phone', res.user.phone);\r\n\t\t\t\t\t\t\tthis.$queue.setData('avatar', res.user.avatar ? res.user.avatar :\r\n\t\t\t\t\t\t\t\t'../../static/logo.png');\r\n\t\t\t\t\t\t\tthis.getUserInfo()\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\ttitle: '注册失败',\r\n\t\t\t\t\t\t\t\tcontent: res.msg\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.logining = true;\r\n\t\t\t\t\tthis.$queue.showLoading('注册中...');\r\n\t\t\t\t\tthis.$Request.post(`/app/Login/registApp?msg=${code}`, {\r\n\t\t\t\t\t\tuserName: userName,\r\n\t\t\t\t\t\tpassword: password,\r\n\t\t\t\t\t\tphone: phone,\r\n\t\t\t\t\t\topenId: this.$queue.getData('openid') ? this.$queue.getData('openid') : '',\r\n\t\t\t\t\t\tinvitation: this.invitation,\r\n\t\t\t\t\t\tplatform: this.platform\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\tthis.$queue.setData(\"token\", res.token);\r\n\t\t\t\t\t\t\tthis.$queue.setData('userId', res.user.userId);\r\n\t\t\t\t\t\t\tthis.$queue.setData('userName', res.user.userName);\r\n\t\t\t\t\t\t\tthis.$queue.setData('phone', res.user.phone);\r\n\t\t\t\t\t\t\tthis.$queue.setData('avatar', res.user.avatar ? res.user.avatar :\r\n\t\t\t\t\t\t\t\t'../../static/logo.png');\r\n\t\t\t\t\t\t\tthis.$queue.setData(\"invitationCode\", res.user.invitationCode);\r\n\t\t\t\t\t\t\tthis.$queue.setData(\"inviterCode\", res.user.inviterCode);\r\n\t\t\t\t\t\t\tthis.getUserInfo()\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\ttitle: '注册失败',\r\n\t\t\t\t\t\t\t\tcontent: res.msg\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//获取用户信息\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\tthis.$Request.get(\"/app/user/selectUserById\").then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tthis.$queue.setData('avatar', res.data.avatar ? res.data.avatar : '../../static/logo.png');\r\n\t\t\t\t\t\tthis.$queue.setData('userId', res.data.userId);\r\n\t\t\t\t\t\tthis.$queue.setData('userName', res.data.userName);\r\n\t\t\t\t\t\tthis.$queue.setData('phone', res.data.phone);\r\n\t\t\t\t\t\tthis.$queue.setData(\"invitationCode\", res.user.invitationCode);\r\n\t\t\t\t\t\tthis.$queue.setData(\"inviterCode\", res.user.inviterCode);\r\n\r\n\t\t\t\t\t\tthis.userName = res.data.userName\r\n\t\t\t\t\t\tthis.invitationCode = res.data.invitationCode\r\n\t\t\t\t\t\tuni.setStorageSync('invitationCode', res.data.invitationCode)\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\ttitle: '登录失败',\r\n\t\t\t\t\t\t\tcontent: res.msg\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.$queue.logout();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\theight: 100%;\r\n\t\tbackground: #FFFFFF !important;\r\n\t}\r\n\r\n\t.footer {\r\n\t\tpadding-left: 140upx;\r\n\t\tmargin-top: 32upx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\t// color: #FFFFFF;\r\n\t}\r\n\r\n\t.send-msg {\r\n\t\tborder-radius: 30px;\r\n\t\tcolor: white;\r\n\t\theight: 30px;\r\n\t\tfont-size: 10px;\r\n\t\tline-height: 30px;\r\n\t\tbackground: #557EFD;\r\n\t}\r\n\r\n\r\n\t.container {\r\n\t\tpadding-top: 32upx;\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #ffffff !important;\r\n\t}\r\n\r\n\t.wrapper {\r\n\t\tposition: relative;\r\n\t\tz-index: 90;\r\n\t\tbackground: #ffffff;\r\n\t\tpadding-bottom: 20px;\r\n\t}\r\n\r\n\r\n\t.input-content {\r\n\t\tpadding: 32upx 80upx;\r\n\t}\r\n\r\n\r\n\t.confirm-btn {\r\n\t\twidth: 600upx;\r\n\t\theight: 80upx;\r\n\t\tline-height: 80upx;\r\n\t\tborder-radius: 60upx;\r\n\t\tmargin-top: 32upx;\r\n\t\tbackground: #557EFD;\r\n\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32upx;\r\n\r\n\t\t&:after {\r\n\t\t\tborder-radius: 60px;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627761\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}