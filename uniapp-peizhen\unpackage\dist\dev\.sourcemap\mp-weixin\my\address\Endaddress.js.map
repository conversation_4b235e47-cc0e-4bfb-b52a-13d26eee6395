{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/address/Endaddress.vue?565f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/address/Endaddress.vue?cdc8", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/address/Endaddress.vue?cc7c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/address/Endaddress.vue?39cd", "uni-app:///my/address/Endaddress.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/address/Endaddress.vue?df21", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/address/Endaddress.vue?c78a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "picker<PERSON><PERSON><PERSON>", "data", "id", "isDefault", "mobile", "name", "cityaddress", "detailaddress", "latitude", "longitude", "province", "city", "district", "onLoad", "uni", "title", "methods", "change", "bindmap", "success", "console", "that", "shengcheng", "icon", "getAddressList", "bindhelp", "phone", "detailsAddress", "addressId", "setTimeout", "checkMobile", "switch1Change"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC4E7vB;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACAC;QACAC;MACA;MACA;IACA;MACAD;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAJ;QACAK;UACA;UACAC;UACAA;UACAA;UACAA;UACAC;UACAA;UACAA;UACAA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;YACA;YACA;YACA;YACA;UACA;QAEA;UACAR;YACAC;YACAQ;UACA;QACA;MACA;IAEA;IACAC;MAAA;MACAV;QACAC;MACA;MACA;QACAK;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAN;MACA;IACA;IACAW;MAAA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;QACApB;QACAqB;QACAhB;QACAC;QACAC;QACAe;QACAxB;QACAyB;QACA;QACA;QACApB;QACAC;QACA;;QAEA;MACA;;MACA;QACAK;UACAS;UACAR;QACA;QACA;MACA;MACA;QACAD;UACAS;UACAR;QACA;QACA;MACA;MACA;QACAD;UACAS;UACAR;QACA;QACA;MACA;MACA;QACAD;UACAS;UACAR;QACA;QACA;MACA;MAEA;QACA;UACA;YACAD;YACA;YACAe;cACAf;YACA;UACA;YACAA;YACA;UACA;QACA;MACA;QACA;UACA;YACAA;YACA;YACAe;cACAf;YACA;UACA;YACAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAgB;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7QA;AAAA;AAAA;AAAA;AAAgjC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACApkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/address/Endaddress.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/address/Endaddress.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Endaddress.vue?vue&type=template&id=140dce0e&\"\nvar renderjs\nimport script from \"./Endaddress.vue?vue&type=script&lang=js&\"\nexport * from \"./Endaddress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Endaddress.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/address/Endaddress.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Endaddress.vue?vue&type=template&id=140dce0e&\"", "var components\ntry {\n  components = {\n    uField: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-field/u-field\" */ \"@/uview-ui/components/u-field/u-field.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Endaddress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Endaddress.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t<view class=\"part1\">\n\t\t\t<view class=\"mobile\">\n\t\t\t\t<u-field v-model=\"mobile\" placeholder=\"联系电话\" icon=\"phone-fill\" maxlength=\"11\" type=\"number\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\t\t\t<view class=\"name\">\n\t\t\t\t<u-field v-model=\"name\" placeholder=\"姓名\" icon=\"account-fill\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\n\t\t\t<view class=\"address\" @click=\"bindmap\">\n\t\t\t\t<u-field v-model=\"cityaddress\" placeholder=\"地址\" :disabled=\"true\" icon=\"map-fill\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\t\t\t<view class=\"detailaddress\">\n\t\t\t\t<u-field v-model=\"detailaddress\" placeholder=\"详细地址\" icon=\"map-fill\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\t\t\t<view\n\t\t\t\tstyle=\"height: 100upx;background:#FFFFFF;display: flex;margin-top: 30upx;justify-content: space-between;padding: 0rpx 22rpx;\">\n\t\t\t\t<view style=\"font-size: 30upx;color: #333333;text-align: left;padding: 30upx;width: 70%;\">设为默认地址</view>\n\t\t\t\t<switch type=\"switch\" :checked='isDefault === 1 ? true : false' @change=\"switch1Change\"\n\t\t\t\t\tstyle=\"padding: 20upx;transform:scale(0.9)\" color=\"#557EFD\" />\n\t\t\t</view>\n\t\t\t<view class=\"btn\" @click=\"bindhelp\">确定</view>\n\t\t</view>\n\t\t<!-- <view style=\"margin-top: 20upx;background-color: #FFFFFF;height: max-content;padding: 20upx 20upx 20upx 35upx;\">\n\t\t\t<input type=\"text\" placeholder=\"收货人\" style=\"height: 80upx;color: #000000;font-size: 30upx;\" v-model=\"consignee\" />\n\t\t\t<input type=\"number\" placeholder=\"手机号码\" maxlength=\"11\" style=\"height: 80upx;margin-top: 20upx;color: #000000;font-size: 30upx;\"\n\t\t\t v-model=\"mobile\" />\n\t\t\t<view style=\"height: 1upx;margin-top: 50upx;\"></view>\n\t\t\t<pickerAddress style=\"padding-bottom: 20upx;color: #000000;font-size: 30upx;height: 30upx;\" @change=\"change\">{{provinces}}</pickerAddress>\n\t\t\t<input type=\"text\" v-model=\"detail\" placeholder=\"详情地址:如道路，门牌号，小区，楼栋号，单元室等\" style=\"height: 80upx;margin-top: 30upx;color: #000000;font-size: 30upx;\" />\n\t\t</view> -->\n\n\t\t<!-- #endif -->\n\t\t<!-- #ifndef MP-WEIXIN -->\n\t\t<view class=\"part1\">\n\t\t\t<view class=\"mobile\">\n\t\t\t\t<u-field v-model=\"mobile\" placeholder=\"联系电话\" icon=\"phone-fill\" maxlength=\"11\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\t\t\t<view class=\"name\">\n\t\t\t\t<u-field v-model=\"name\" placeholder=\"姓名\" icon=\"account-fill\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\n\t\t\t<view class=\"address\" @click=\"bindmap\">\n\t\t\t\t<u-field v-model=\"cityaddress\" placeholder=\"地址\" :disabled=\"true\" icon=\"map-fill\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\t\t\t<view class=\"detailaddress\">\n\t\t\t\t<u-field v-model=\"detailaddress\" placeholder=\"详细地址\" icon=\"map-fill\" label-align=\"center\">\n\t\t\t\t</u-field>\n\t\t\t</view>\n\t\t\t<view\n\t\t\t\tstyle=\"height: 100upx;background:#FFFFFF;display: flex;margin-top: 30upx;justify-content: space-between;padding: 0rpx 22rpx;\">\n\t\t\t\t<view style=\"font-size: 34upx;color: #333333;text-align: left;padding: 30upx;width: 70%;\">设为默认地址</view>\n\t\t\t\t<switch type=\"switch\" :checked='isDefault === 1 ? true : false' @change=\"switch1Change\"\n\t\t\t\t\tstyle=\"padding: 20upx;transform:scale(0.9)\" color=\"#557EFD\" />\n\t\t\t</view>\n\t\t\t<view class=\"btn\" @click=\"bindhelp\">确定</view>\n\t\t</view>\n\t\t<!-- #endif -->\n\t\t<!-- <button\n\t\t\tstyle=\"width: 85%;height: 90upx;background: #FF332F;font-size: 32upx;color: #FFFFFF;margin-top: 40upx;border-radius: 50upx;line-height: 100upx;\"\n\t\t\t@tap='setEditAddress'>保存</button> -->\n\t</view>\n</template>\n\n<script>\n\t// import pickerAddress from '@/components/wangding-pickerAddress/wangding-pickerAddress.vue'\n\timport pickerAddress from '@/my/components/wangding-pickerAddress/wangding-pickerAddress.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\tpickerAddress\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// provinces: '选择地址',\n\t\t\t\t// consignee: '',\n\t\t\t\t// mobile: '',\n\t\t\t\t// detail: '',\n\t\t\t\t// createAt: '',\n\t\t\t\tid: '',\n\t\t\t\tisDefault: 0,\n\t\t\t\t// id: 0,\n\t\t\t\tmobile: '',\n\t\t\t\tname: '',\n\t\t\t\tcityaddress: '',\n\t\t\t\tdetailaddress: '',\n\t\t\t\tlatitude: '',\n\t\t\t\tlongitude: '',\n\t\t\t\tprovince:'',\n\t\t\t\tcity:'',\n\t\t\t\tdistrict:''\n\t\t\t}\n\t\t},\n\t\tonLoad(d) {\n\t\t\tlet id = d.id;\n\t\t\tif (id) {\n\t\t\t\tthis.id = d.id;\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '编辑地址'\n\t\t\t\t});\n\t\t\t\tthis.getAddressList(id);\n\t\t\t} else {\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '添加地址'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tchange(data) {\n\t\t\t\tthis.provinces = data.data.join('')\n\t\t\t},\n\t\t\tbindmap() {\n\t\t\t\tvar that = this\n\t\t\t\t// if (that.ciaddress == '') {\n\t\t\t\tuni.chooseLocation({\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t// console.log(res)\n\t\t\t\t\t\tconsole.log('位置名称：' + res.name);\n\t\t\t\t\t\tconsole.log('详细地址：' + res.address);\n\t\t\t\t\t\tconsole.log('纬度：' + res.latitude);\n\t\t\t\t\t\tconsole.log('经度：' + res.longitude);\n\t\t\t\t\t\tthat.detailaddress = res.name\n\t\t\t\t\t\tthat.latitude = res.latitude\n\t\t\t\t\t\tthat.longitude = res.longitude\n\t\t\t\t\t\tthat.shengcheng(res.longitude, res.latitude)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// }\n\t\t\t},\n\t\t\tshengcheng(longitude, latitude) {\n\t\t\t\tthis.$Request.getT('/app/Login/selectCity?lat=' + latitude + '&lng=' + longitude).then(res => {\n\t\t\t\t\t// console.log(res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tlet address = res.data.result?res.data.result.ad_info:''\n\t\t\t\t\t\tif(address){\n\t\t\t\t\t\t\tthis.cityaddress = address.province + address.city + address.district\n\t\t\t\t\t\t\tthis.province = address.province\n\t\t\t\t\t\t\tthis.city = address.city\n\t\t\t\t\t\t\tthis.district = address.district\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle:res.msg,\n\t\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t},\n\t\t\tgetAddressList(id) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t});\n\t\t\t\tthis.$Request.getT('/app/address/selectAddressByAddressId?addressId=' + id).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.name = res.data.name;\n\t\t\t\t\t\tthis.mobile = res.data.phone;\n\t\t\t\t\t\tthis.cityaddress =res.data.province+ res.data.city+res.data.district;\n\t\t\t\t\t\tthis.detailaddress = res.data.detailsAddress;\n\t\t\t\t\t\tthis.isDefault = res.data.isDefault;\n\t\t\t\t\t\tthis.userId = res.data.userId;\n\t\t\t\t\t\tthis.latitude = res.data.latitude;\n\t\t\t\t\t\tthis.longitude = res.data.longitude;\n\t\t\t\t\t\tthis.province = res.data.province\n\t\t\t\t\t\tthis.city = res.data.city\n\t\t\t\t\t\tthis.district = res.data.district\n\t\t\t\t\t}\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t});\n\t\t\t},\n\t\t\tbindhelp() {\n\t\t\t\tif (this.id) {\n\t\t\t\t\tthis.$queue.showLoading('修改中...')\n\t\t\t\t} else {\n\t\t\t\t\tthis.$queue.showLoading('添加中...')\n\t\t\t\t}\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tlet data = {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tphone: this.mobile,\n\t\t\t\t\tprovince:this.province,\n\t\t\t\t\tcity:this.city,\n\t\t\t\t\tdistrict:this.district,\n\t\t\t\t\tdetailsAddress:this.detailaddress,\n\t\t\t\t\tisDefault:this.isDefault,\n\t\t\t\t\taddressId: this.id,\n\t\t\t\t\t// address: this.cityaddress,\n\t\t\t\t\t// addressDetail: this.detailaddress,\n\t\t\t\t\tlatitude: this.latitude,\n\t\t\t\t\tlongitude: this.longitude,\n\t\t\t\t\t// userId: userId,\n\t\t\t\t\t\n\t\t\t\t\t// addressDefault: this.isDefault\n\t\t\t\t}\n\t\t\t\tif (this.mobile.length < 11) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入正确的电话信息'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (this.name != undefined && this.name != '') {} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '收货人不能为空'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (this.cityaddress == '选择地址') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请选择地址信息'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (this.detailaddress != undefined && this.detailaddress != '') {} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '地址信息不能为空'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (this.id) {\n\t\t\t\t\tthis.$Request.postJson('/app/address/updateAddress', data).then(res => {\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t\tthis.$queue.showToast(\"修改成功!\");\n\t\t\t\t\t\t\tsetTimeout(d => {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t\tthis.$queue.showToast(res.msg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.$Request.postJson('/app/address/insertAddress', data).then(res => {\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t\tthis.$queue.showToast(\"添加成功!\");\n\t\t\t\t\t\t\tsetTimeout(d => {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\t\tthis.$queue.showToast(res.msg);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t//校验手机格式\n\t\t\tcheckMobile(mobile) {\n\t\t\t\treturn RegExp(/^1[34578]\\d{9}$/).test(mobile);\n\t\t\t},\n\t\t\tswitch1Change(e) {\n\t\t\t\tthis.isDefault = e.detail.value == true ? 1 : 0;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\tbody {\n\t\tbackground: #FFFFFF;\n\t}\n\n\t.part1 {\n\t\twidth: 100%;\n\t\tbackground: #ffffff;\n\t\tmargin-top: 24rpx;\n\t\tpadding-bottom: 40upx;\n\t}\n\n\t.btn {\n\t\twidth: 90%;\n\t\theight: 80upx;\n\t\tbackground: #557EFD;\n\t\tborder-radius: 14upx;\n\t\tmargin: 0 auto;\n\t\tcolor: white;\n\t\ttext-align: center;\n\t\tline-height: 80upx;\n\t\tletter-spacing: 2upx;\n\t\tmargin-top: 40upx;\n\t}\n\n\t.u-icon__icon {\n\t\tfont-size: 45rpx !important;\n\t}\n\t\n\t.u-field {\n\t\tpadding: 35rpx 28rpx !important;\n\t}\n\t\n\t.u-label {\n\t\tflex: 0 0 42px !important;\n\t}\n\t.u-field__input-wrap {\n\t    font-size: 30rpx !important;\n\t}\n\t.u-textarea-class {\n\t    font-size: 30rpx !important;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Endaddress.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Endaddress.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621961\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}