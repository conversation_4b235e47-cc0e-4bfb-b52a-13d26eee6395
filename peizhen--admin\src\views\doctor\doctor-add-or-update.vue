<template>
  <el-dialog
    :title="!dataForm.doctorId ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="医生姓名" prop="doctorName">
            <el-input v-model="dataForm.doctorName" placeholder="医生姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="医生工号" prop="doctorCode">
            <el-input v-model="dataForm.doctorCode" placeholder="医生工号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属医院" prop="hospitalId">
            <el-select v-model="dataForm.hospitalId" placeholder="请选择医院" style="width: 100%" @change="onHospitalChange">
              <el-option
                v-for="item in hospitalList"
                :key="item.hospitalId"
                :label="item.hospitalName"
                :value="item.hospitalId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属科室" prop="departmentId">
            <el-select v-model="dataForm.departmentId" placeholder="请选择科室" style="width: 100%">
              <el-option
                v-for="item in departmentList"
                :key="item.departmentId"
                :label="item.departmentName"
                :value="item.departmentId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="dataForm.phone" placeholder="联系电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="dataForm.gender">
              <el-radio v-for="item in genderList" :key="item.code" :label="parseInt(item.code)">{{item.value}}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生日期" prop="birthDate">
            <el-date-picker
              v-model="dataForm.birthDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身份证号" prop="idCard">
            <el-input v-model="dataForm.idCard" placeholder="身份证号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职称" prop="title">
            <el-select v-model="dataForm.title" placeholder="请选择职称" style="width: 100%">
              <el-option v-for="item in titleList" :key="item.code" :label="item.value" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学历" prop="education">
            <el-select v-model="dataForm.education" placeholder="请选择学历" style="width: 100%">
              <el-option v-for="item in educationList" :key="item.code" :label="item.value" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专业技术等级" prop="professionalLevel">
            <el-input v-model="dataForm.professionalLevel" placeholder="专业技术等级"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入职日期" prop="entryDate">
            <el-date-picker
              v-model="dataForm.entryDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作状态" prop="workStatus">
            <el-select v-model="dataForm.workStatus" placeholder="请选择工作状态" style="width: 100%">
              <el-option v-for="item in workStatusList" :key="item.code" :label="item.value" :value="parseInt(item.code)"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="执业证书编号" prop="licenseNumber">
            <el-input v-model="dataForm.licenseNumber" placeholder="执业证书编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="dataForm.sort" :min="0" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="专业特长" prop="specialty">
        <el-input v-model="dataForm.specialty" type="textarea" placeholder="专业特长"></el-input>
      </el-form-item>
      
      <el-form-item label="执业范围" prop="practiceScope">
        <el-input v-model="dataForm.practiceScope" type="textarea" placeholder="执业范围"></el-input>
      </el-form-item>
      
      <el-form-item label="标签" prop="tag">
        <el-input v-model="dataForm.tag" placeholder="标签，多个用逗号分隔"></el-input>
      </el-form-item>
      
      <el-form-item label="医生简介" prop="introduction">
        <el-input v-model="dataForm.introduction" type="textarea" :rows="3" placeholder="医生简介"></el-input>
      </el-form-item>
      
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="dataForm.remarks" type="textarea" :rows="2" placeholder="备注"></el-input>
      </el-form-item>
      
      <el-form-item label="头像" prop="avatar">
        <el-upload
          class="avatar-uploader"
          :action="uploadUrl"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload">
          <img v-if="dataForm.avatar" :src="dataForm.avatar" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      
      <el-form-item label="执业资格证书" prop="qualificationCert">
        <el-upload
          class="upload-demo"
          :action="uploadUrl"
          :on-success="handleCertSuccess"
          :file-list="certFileList">
          <el-button size="small" type="primary">点击上传</el-button>
        </el-upload>
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否启用" prop="isEnable">
            <el-radio-group v-model="dataForm.isEnable">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        doctorId: 0,
        hospitalId: '',
        departmentId: '',
        doctorName: '',
        doctorCode: '',
        phone: '',
        email: '',
        gender: 1,
        birthDate: '',
        idCard: '',
        avatar: '',
        title: '',
        professionalLevel: '',
        education: '',
        specialty: '',
        introduction: '',
        tag: '',
        remarks: '',
        qualificationCert: '',
        licenseNumber: '',
        practiceScope: '',
        entryDate: '',
        workStatus: 1,
        sort: 0,
        isEnable: 1
      },
      dataRule: {
        doctorName: [
          { required: true, message: '医生姓名不能为空', trigger: 'blur' }
        ],
        hospitalId: [
          { required: true, message: '请选择医院', trigger: 'change' }
        ],
        departmentId: [
          { required: true, message: '请选择科室', trigger: 'change' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
        ],
        idCard: [
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '身份证号格式不正确', trigger: 'blur' }
        ]
      },
      hospitalList: [],
      departmentList: [],
      uploadUrl: '',
      certFileList: [],
      // 字典数据
      genderList: [],        // 性别字典
      titleList: [],         // 职称字典
      educationList: [],     // 学历字典
      workStatusList: []     // 工作状态字典
    }
  },
  created () {
    this.uploadUrl = this.$http.adornUrl('/sys/oss/upload')
  },
  methods: {
    init (id) {
      this.dataForm.doctorId = id || 0
      this.visible = true
      this.getHospitalList()
      this.loadDictData() // 加载字典数据
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.doctorId) {
          this.$http({
            url: this.$http.adornUrl(`/doctor/info/${this.dataForm.doctorId}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.doctor
              if (this.dataForm.hospitalId) {
                this.getDepartmentList(this.dataForm.hospitalId)
              }
              if (this.dataForm.qualificationCert) {
                this.certFileList = [{
                  name: '执业资格证书',
                  url: this.dataForm.qualificationCert
                }]
              }
            }
          })
        }
      })
    },
    // 获取医院列表
    getHospitalList () {
      this.$http({
        url: this.$http.adornUrl('/admin/hospital/selectHospitalList'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.hospitalList = data.data || []
        }
      })
    },
    // 医院改变时获取科室列表
    onHospitalChange (hospitalId) {
      this.dataForm.departmentId = ''
      this.departmentList = []
      if (hospitalId) {
        this.getDepartmentList(hospitalId)
      }
    },
    // 获取科室列表
    getDepartmentList (hospitalId) {
      this.$http({
        url: this.$http.adornUrl('/admin/department/selectDepartmentList'),
        method: 'get',
        params: this.$http.adornParams({
          'hospitalId': hospitalId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.departmentList = data.data || []
        }
      })
    },
    // 头像上传成功
    handleAvatarSuccess (res, file) {
      if (res && res.code === 0) {
        this.dataForm.avatar = res.url
      } else {
        this.$message.error('上传失败')
      }
    },
    // 头像上传前验证
    beforeAvatarUpload (file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    // 证书上传成功
    handleCertSuccess (res, file) {
      if (res && res.code === 0) {
        this.dataForm.qualificationCert = res.url
      } else {
        this.$message.error('上传失败')
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/doctor/${!this.dataForm.doctorId ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },

    // ========== 字典数据加载方法 ==========
    // 加载字典数据
    loadDictData() {
      // 加载性别字典
      this.loadDictByType('性别', (data) => {
        this.genderList = data
      })

      // 加载职称字典
      this.loadDictByType('医生职称', (data) => {
        this.titleList = data
      })

      // 加载学历字典
      this.loadDictByType('学历', (data) => {
        this.educationList = data
      })

      // 加载工作状态字典
      this.loadDictByType('工作状态', (data) => {
        this.workStatusList = data
      })
    },

    // 根据类型加载字典数据
    loadDictByType(type, callback) {
      this.$http({
        url: this.$http.adornUrl('sys/dict/selectDictList'),
        method: 'get',
        params: this.$http.adornParams({ 'type': type })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          if (callback && typeof callback === 'function') {
            callback(data.data || [])
          }
        }
      })
    }
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
