{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/pay.vue?c13c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/pay.vue?6a8a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/pay.vue?bdb6", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/pay.vue?f05a", "uni-app:///my/order/pay.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/pay.vue?ebec", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/pay.vue?98d4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isPay", "customStyle", "backgroundColor", "color", "width", "margin", "customStyle2", "id", "order", "user", "game", "isTrue", "czSel", "phone", "isVip", "showPay", "openWay", "openLists", "onLoad", "uni", "title", "image", "text", "onShow", "methods", "peiveImgs", "current", "urls", "goInfo", "url", "bindclost", "saveImg", "copyClick", "success", "icon", "bindGps", "latitude", "longitude", "name", "address", "console", "fail", "bindphone", "content", "phoneNumber", "binddetail", "getOrder", "delOrder", "that", "getIsVip", "pay", "orderId", "classify", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "setTimeout", "ordersId", "cancel", "status", "cancelOrder", "callPay", "document", "onBridgeReady", "WeixinJSBridge", "selectWay", "setPayment", "orderInfo", "goMsg", "userId", "focusedUserId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,0CAA0C,EAAE,IAAI,EAAE,IAAI,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAAkuB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmQtvB;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAJ;QACAC;QAEAC;QACAC;MACA;MACAE;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;IACA;IACA;MACAC;QACAC;MACA;IACA;IACA;IAmBA;MACAC;MACAC;MACAf;IACA;MACAc;MACAC;MACAf;IACA;IACA;;IAiCA;EACA;EACAgB;IACA;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAN;QACAO;QACAC;MACA;IACA;IACAC;MACAT;QACAU;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAZ;QACAQ;QACAD;MACA;IACA;IACAM;MACAb;QACApB;QACAkC;UACAd;YACAc;cACAd;gBACAC;gBACAc;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACAhB;QACAiB;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAN;UACAO;QACA;QACAC;UACAD;QACA;MACA;IACA;IACA;IACAE;MACA;MAEAvB;QACAC;QACAuB;QACAV;UACA;YACAO;YACArB;cACAyB;YACA;UACA;YACAJ;UACA;QACA;MACA;IACA;IACAK;MACAL;MACArB;QAEAU;MACA;IACA;IACAiB;MAAA;MACA;QACAvC;MACA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAwC;MACA;MACA5B;QACAC;QACAuB;QACAV;UACA;YACA;cACA1B;YACA;YACAyC;cACA;gBACA7B;kBACAC;gBACA;gBACA;gBACAD;cACA;YACA;UACA;YACAqB;UACA;QACA;MACA;IACA;IACAS;MAAA;MACA;QACA;UACA;UACA9B;QACA;MACA;IACA;IACA;IACA+B;MACA;QACA;MACA;MACA;MACAF;MACA7B;QACAC;QACAuB;QACAV;UACA;YACAe;YACA;YACA;YACA;cAAA;;cAEAA;gBACAG;gBACAC;cACA;gBACA;kBACAjC;oBACAkC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAzB;sBACAd;sBACAA;wBACAC;sBACA;sBACA4B;sBACA7B;sBACAwC;wBACAxC;sBACA;oBACA;oBACAsB;sBACAO;sBACA7B;sBACA6B,sBACA;oBACA;kBACA;gBACA;kBACAA;kBACA7B;oBACAC;oBACAc;kBACA;gBACA;cACA;YA2CA;YAAA,CAqCA;cAAA;cACAc;gBACAY;cACA;gBACA;kBACAzC;oBACAC;kBACA;kBACA4B;kBACAW;oBACAxC;kBACA;gBACA;kBACA6B;kBACA7B;oBACAC;oBACAc;kBACA;gBACA;cACA;YACA;UAEA;YACAc;YACAR;UACA;QACA;MAEA;IAEA;IACA;IACAqB;MACA;MACA1C;QACAC;QACAuB;QACAV;UACA;YACA;cACA1B;cACAuD;YACA;YACAd;cACA;gBACAA;cACA;YACA;UACA;YACAR;UACA;QACA;MACA;IACA;IACA;IACAuB;MACA;QACAxD;QACAuD;MACA;MACA;QACA;UACA3C;YACAC;YACAc;UACA;UACAyB;YACAxC;UACA;QACA;MACA;IACA;IACA6C;MACA;QACA;UACAC;QACA;UACAA;UACAA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACAC,sBACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QACA;QAAA;QACA;MACA,GACA;QACA;UACA;UACA;UACAhD;UACAA;YACAC;UACA;UACA4B;UACA7B;UACAwC;YACAxC;UACA;QACA;UACA6B;UACA7B;QACA;QACAgD;MACA,EACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAlD;QACAkC;QACAiB;QAAA;QACArC;UACAd;UACAA;YACAC;UACA;UACA4B;UACA7B;UACAwC;YACAxC;UACA;QACA;QACAsB;UACAO;UACA7B;UACAqB;QACA;MACA;IACA;IACA+B;MAAA;MACA;QACAC;QACAC;MACA;MACA;QACA;UACA,6FACAhE;UACAU;YACAU,yEACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvyBA;AAAA;AAAA;AAAA;AAAyiC,CAAgB,o8BAAG,EAAC,C;;;;;;;;;;;ACA7jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/order/pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/order/pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pay.vue?vue&type=template&id=94000b5c&\"\nvar renderjs\nimport script from \"./pay.vue?vue&type=script&lang=js&\"\nexport * from \"./pay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pay.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/order/pay.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=template&id=94000b5c&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-button/u-button\" */ \"@/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.order.appointInformation\n    ? _vm.__map(_vm.order.appointInformation.tags, function (ite, ind) {\n        var $orig = _vm.__get_orig(ite)\n        var g0 = _vm.order.appointInformation.tags.length\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  var g1 =\n    _vm.order.state == 1 || _vm.order.state == 2 || _vm.order.state == 5\n      ? _vm.order.ridePhone.replace(/^(\\d{3})\\d{4}(\\d{4})$/, \"$1****$2\")\n      : null\n  var g2 =\n    _vm.order.appointInformation &&\n    _vm.order.appointInformation &&\n    _vm.order.appointInformation.imgRemarks &&\n    _vm.order.appointInformation.imgRemarks\n      ? _vm.order.appointInformation.imgRemarks.split(\",\")\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      _vm.peiveImgs(index, _vm.order.appointInformation.imgRemarks.split(\",\"))\n    }\n    _vm.e1 = function ($event) {\n      _vm.showPay = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"padding-lr padding-bottom\">\n\t\t\t<view class=\"status\" v-if=\"order.state==0\">订单待付款...</view>\n\t\t\t<view class=\"status\" v-if=\"order.state==1\">订单进行中...</view>\n\t\t\t<view class=\"status\" v-if=\"order.state==2\">订单已完成...</view>\n\t\t\t<view class=\"status\" v-if=\"order.state==3\">订单已取消...</view>\n\t\t\t<view class=\"status\" v-if=\"order.state==4\">订单待接单...</view>\n\t\t\t<view class=\"status\" v-if=\"order.state==5\">订单待服务...</view>\n\n\t\t\t<view class=\"types flex align-center justify-between\">\n\t\t\t\t<view class=\"types-box-l flex align-center\" style=\"width: 60%;\" v-if=\"order.appointInformation\">\n\t\t\t\t\t<image :src=\"order.appointInformation.img?order.appointInformation.img: '../../static/logo.png'\"\n\t\t\t\t\t\tstyle=\"width: 74rpx;height: 66rpx;margin-left: 44rpx;\" mode=\"\">\n\t\t\t\t\t</image>\n\t\t\t\t\t<view class=\"types-r\">\n\t\t\t\t\t\t<view class=\"types-r-t\">{{order.appointInformation.serviceName}}</view>\n\t\t\t\t\t\t<view class=\"types-r-b flex flex-wrap align-center\">\n\t\t\t\t\t\t\t<view v-for=\"(ite,ind) in order.appointInformation.tags\" :key=\"ind\">\n\t\t\t\t\t\t\t\t{{ite}} <text v-if=\"ind!=order.appointInformation.tags.length-1\"\n\t\t\t\t\t\t\t\t\tclass=\"padding-lr-xs\">|</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"types-box-r flex align-center\">\n\t\t\t\t\t{{order.state!=0 && order.state!=3?'实付：':''}}  \n\t\t\t\t\t<text>¥</text><span>{{order.payMoney}}</span>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view @click=\"goInfo(order.orderTakingUserId)\" class=\"bg padding radius margin-top\" v-if=\"order.state == 1 || order.state == 2 || order.state == 5\">\n\t\t\t\t<view>联系接单人</view>\n\t\t\t\t<view class=\"flex align-center margin-top\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<image :src=\"order.rideAvatar?order.rideAvatar:'../../static/logo.png'\"\n\t\t\t\t\t\t\tstyle=\"width: 80rpx;height: 80rpx;border-radius: 60upx;\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"text-white margin-left-sm\" @click.stop=\"bindphone(order.ridePhone)\">\n\t\t\t\t\t\t<view>{{order.rideUserName}}</view>\n\t\t\t\t\t\t<view class=\"margin-top-xs\">{{order.ridePhone.replace(/^(\\d{3})\\d{4}(\\d{4})$/, \"$1****$2\")}}\t<u-icon name=\"phone\"></u-icon></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"bg padding radius margin-top\" v-if=\"order.appointInformation\">\n\t\t\t\t<view class=\"text-lg text-bold\">\n\t\t\t\t\t预约信息\n\t\t\t\t</view>\n\t\t\t\t<view class=\" margin-right-xs\">\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 180upx;color: #999999;\">期望就诊时间</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.hopeTime}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">就诊医院</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.hospitalName}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.departmentName\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">就诊科室</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.departmentName}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.badNo\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">就诊床号</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.badNo}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.nursingNeeds\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">护理需求</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.nursingNeeds}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.symptom\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">病症</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.symptom}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.selfAbility\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">自理能力</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.selfAbility}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.serviceNum\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">服务天数</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.serviceNum}}天</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">就诊人</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.patientInfo?order.appointInformation.patientInfo.realName:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">就诊关系</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.patientInfo?order.appointInformation.patientInfo.relationship:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.phone\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">联系电话</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.phone}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">就诊人电话</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.patientInfo?order.appointInformation.patientInfo.phone:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.patientInfo && order.appointInformation.patientInfo.emergencyPhone\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">紧急联系人</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.patientInfo?order.appointInformation.patientInfo.emergencyPhone:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.drugsType\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">药物类型</view>\n\t\t\t\t\t\t<view>{{order.appointInformation?order.appointInformation.drugsType:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.drugsName\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">药物名称</view>\n\t\t\t\t\t\t<view>{{order.appointInformation?order.appointInformation.drugsName:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.reportType\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">报告信息</view>\n\t\t\t\t\t\t<view>{{order.appointInformation?order.appointInformation.reportType:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.exclusiveType\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">专享类型</view>\n\t\t\t\t\t\t<view>{{order.appointInformation?order.appointInformation.exclusiveType:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation.userDetailsAddress\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">{{order.appointInformation.serviceType==1?'接送地址':'收件地址'}}</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.userProvince}}{{order.appointInformation.userCity}}{{order.appointInformation.userDistrict}}{{order.appointInformation.userDetailsAddress}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- <view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">服务天数</view>\n\t\t\t\t\t\t<view>{{order.appointInformation.serviceNum?order.appointInformation.serviceNum+'天':'暂无'}}</view>\n\t\t\t\t\t</view> -->\n\t\t\t\t\t<view class=\"margin-top-lg\" v-if=\"order.appointInformation && order.appointInformation.imgRemarks\" style=\"width: 165rpx;color: #999999;\">图片资料</view>\n\t\t\t\t\t<view class=\"margin-top-lg flex flex-wrap\" v-if=\"order.appointInformation && order.appointInformation.imgRemarks\">\n\t\t\t\t\t\t<image @click=\"peiveImgs(index,order.appointInformation.imgRemarks.split(','))\" :src=\"item\" style=\"width: 195rpx;height: 195rpx;border-radius: 10rpx;margin-right: 10rpx;margin-bottom: 10rpx;\" v-for=\"(item,index) in order.appointInformation.imgRemarks?order.appointInformation.imgRemarks.split(','):[]\" mode=\"\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\" margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 165rpx;color: #999999;\">特殊需求</view>\n\t\t\t\t\t\t<view class=\"text-white margin-top\">{{order.appointInformation.remarks?order.appointInformation.remarks:'暂无'}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"bg padding radius margin-top\" style=\"margin-bottom: 140rpx;\">\n\t\t\t\t<view class=\"text-lg text-bold\">\n\t\t\t\t\t订单信息\n\t\t\t\t</view>\n\t\t\t\t<view class=\"margin-right-xs\">\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.appointInformation\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">服务类型</view>\n\t\t\t\t\t\t<view class=\"text-white flex align-center\">{{order.appointInformation.serviceName}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">订单编号</view>\n\t\t\t\t\t\t<view class=\"text-white flex align-center\">{{order.ordersNo}}\n\t\t\t\t\t\t\t<image src=\"../static/copy.png\" style=\"width: 45rpx;height: 45rpx;margin-left: 5upx;\"\n\t\t\t\t\t\t\t\************=\"copyClick(order.ordersNo)\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">下单时间</view>\n\t\t\t\t\t\t<view class=\"text-white\">{{order.createTime}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex justify-between margin-top-lg\" v-if=\"order.orderMoney>order.payMoney\">\n\t\t\t\t\t\t<view class=\"\" style=\"width: 150upx;color: #999999;\">优惠金额</view>\n\t\t\t\t\t\t<view class=\"text-white\">￥{{order.orderMoney - order.payMoney}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"box\" v-if=\"showPay\">\n\t\t\t\t<view class=\"bottbox\">\n\t\t\t\t\t<view class=\"flex align-start justify-between\">\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tstyle=\"width: 100%;text-align: center;font-size:38rpx;font-weight: bold;margin-top:15rpx;margin-bottom: 80rpx;\">\n\t\t\t\t\t\t\t选择支付方式\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"margin-right margin-top-sm\" @click=\"bindclost\">\n\t\t\t\t\t\t\t<image src=\"../static/colse.png\" style=\"width: 60upx;height: 60upx;\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"display: flex;\" v-for=\"(item,index) in openLists\" :key='index'>\n\t\t\t\t\t\t<view style=\"width: 100%;display: flex;height: 100upx;align-items: center;padding: 20upx 30rpx;\"\n\t\t\t\t\t\t\tv-if=\"item.text === '零钱' && czSel != '否'\">\n\t\t\t\t\t\t\t<view style=\"display: flex;width:80%;align-items: center;\">\n\t\t\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\n\t\t\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view style=\"width: 20%;display: flex;justify-content: flex-end;\">\n\t\t\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item.id)'>\n\t\t\t\t\t\t\t\t\t<label class=\"tui-radio\">\n\t\t\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"width: 100%;display: flex;height: 100upx;align-items: center;padding: 20upx 30rpx;\"\n\t\t\t\t\t\t\tv-if=\"item.text != '零钱'\">\n\t\t\t\t\t\t\t<view style=\"display: flex;width:80%;align-items: center;\">\n\t\t\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\n\t\t\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view style=\"width: 20%;display: flex;justify-content: flex-end;\">\n\t\t\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item.id)'>\n\t\t\t\t\t\t\t\t\t<label class=\"tui-radio\">\n\t\t\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tstyle=\"width: 690rpx;height: 80rpx;background:#1789FD;color:#FFFFFF;text-align: center;line-height: 80rpx;border-radius: 50rpx;margin: 30rpx;\"\n\t\t\t\t\t\t@click=\"pay()\">确认支付</view>\n\t\t\t\t</view>\n\n\t\t\t</view>\n\t\t\t<!-- <u-popup v-model=\"showPay\" mode=\"bottom\" border-radius=\"14\" :closeable=\"true\">\n\t\t\t\t<view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tstyle=\"width: 100%;text-align: center;font-size:38rpx;font-weight: bold;margin-top:15rpx;margin-bottom: 80rpx;\">\n\t\t\t\t\t\t选择支付方式\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"display: flex;height: 100upx;align-items: center;padding: 20upx 30rpx;\"\n\t\t\t\t\t\tv-for=\"(item,index) in openLists\" :key='index'>\n\t\t\t\t\t\t<view style=\"display: flex;width:80%;align-items: center;\">\n\t\t\t\t\t\t\t<image :src=\"item.image\" style=\"width: 55upx;height: 55upx;border-radius: 50upx;\"></image>\n\t\t\t\t\t\t\t<view style=\"font-size: 30upx;margin-left: 20upx;width: 70%;\">{{item.text}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"width: 20%;display: flex;justify-content: flex-end;\">\n\t\t\t\t\t\t\t<radio-group name=\"openWay\" style=\"margin-left: 20upx;\" @tap='selectWay(item.id)'>\n\t\t\t\t\t\t\t\t<label class=\"tui-radio\">\n\t\t\t\t\t\t\t\t\t<radio color=\"#1789FD\" :checked=\"openWay === item.id ? true : false\" />\n\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t</radio-group>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tstyle=\"width: 690rpx;height: 80rpx;background:#1789FD;color:#FFFFFF;text-align: center;line-height: 80rpx;border-radius: 50rpx;margin: 30rpx;\"\n\t\t\t\t\t\t@click=\"pay()\">确认支付</view>\n\t\t\t\t</view>\n\t\t\t</u-popup>\n -->\n\t\t\t<view class=\"flex tabber padding-top-sm padding-bottom-sm align-center\"\n\t\t\t\tv-if=\"order.state == 0||order.state == 3\">\n\t\t\t\t<u-button @click=\"cancelOrder(order)\" shape=\"circle\" class=\"margin-right\" :custom-style=\"customStyle\"\n\t\t\t\t\t:hair-line=\"false\" v-if=\"order.state == 0\">取消订单\n\t\t\t\t</u-button>\n\t\t\t\t<!-- <view class=\"btn\" @click=\"openBox()\" v-if=\"order.state == 0\">立即支付</view> -->\n\t\t\t\t<u-button class=\"margin-right\" shape=\"circle\" :custom-style=\"customStyle2\" :hair-line=\"false\"\n\t\t\t\t\t@click=\"showPay=true\" v-if=\"order.state == 0\">立即支付\n\t\t\t\t</u-button>\n\t\t\t\t<u-button v-if=\"order.state == 3\" class=\"margin-right\" :custom-style=\"customStyle\" shape=\"circle\"\n\t\t\t\t\t:plain=\"true\" @click=\"delOrder(order)\">删除订单</u-button>\n\t\t\t\t<u-button v-if=\"order.state == 1\" class=\"margin-right\" :custom-style=\"customStyle\" shape=\"circle\"\n\t\t\t\t\t:plain=\"true\" @click=\"cancel(item)\" 订单完成</u-button>\n\t\t\t</view>\n\t\t</view>\n\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisPay:true,\n\t\t\t\tcustomStyle: {\n\t\t\t\t\tbackgroundColor: '#557EFD',\n\t\t\t\t\tcolor: '#FFFFFF',\n\t\t\t\t\twidth: \"200upx\",\n\t\t\t\t\tmargin: \"0 30upx 0upx 0\"\n\t\t\t\t},\n\t\t\t\tcustomStyle2: {\n\t\t\t\t\tbackgroundColor: '#557EFD',\n\t\t\t\t\tcolor: '#FFFFFF',\n\n\t\t\t\t\twidth: \"200upx\",\n\t\t\t\t\tmargin: \"0 30upx 0upx 0\"\n\t\t\t\t},\n\t\t\t\tid: '',\n\t\t\t\torder: {\n\t\t\t\t\tuser: {},\n\t\t\t\t\tgame: {}\n\t\t\t\t},\n\t\t\t\tisTrue: 0,\n\t\t\t\tczSel: '否',\n\t\t\t\tphone: '',\n\t\t\t\tisVip: false,\n\t\t\t\tshowPay: false,\n\t\t\t\topenWay: 0,\n\t\t\t\topenLists: [],\n\n\t\t\t}\n\t\t},\n\t\tonLoad(e) {\n\t\t\tthis.czSel = this.$queue.getData('czSel');\n\t\t\tthis.isTrue = e.isTrue\n\t\t\tif (this.isTrue) {\n\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\ttitle: '订单详情'\n\t\t\t\t})\n\t\t\t}\n\t\t\tthis.id = e.id\n\t\t\t// #ifdef APP-PLUS\n\t\t\tthis.openLists = [{\n\t\t\t\timage: '../../static/images/zhifubao.png',\n\t\t\t\ttext: '支付宝',\n\t\t\t\tid: 1\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/icon_weixin.png',\n\t\t\t\ttext: '微信',\n\t\t\t\tid: 2\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/lingqian.png',\n\t\t\t\ttext: '零钱',\n\t\t\t\tid: 3\n\t\t\t}];\n\t\t\tthis.openWay = 1;\n\t\t\t// #endif\n\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.openLists = [{\n\t\t\t\timage: '../../static/images/icon_weixin.png',\n\t\t\t\ttext: '微信',\n\t\t\t\tid: 2\n\t\t\t}, {\n\t\t\t\timage: '../../static/images/lingqian.png',\n\t\t\t\ttext: '零钱',\n\t\t\t\tid: 3\n\t\t\t}];\n\t\t\tthis.openWay = 2;\n\t\t\t// #endif\n\n\t\t\t// #ifdef H5\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\n\t\t\t\tthis.openLists = [{\n\t\t\t\t\timage: '/static/images/zhifubao.png',\n\t\t\t\t\ttext: '支付宝',\n\t\t\t\t\tid: 1\n\t\t\t\t}, {\n\t\t\t\t\timage: '/static/images/lingqian.png',\n\t\t\t\t\ttext: '零钱',\n\t\t\t\t\tid: 3\n\t\t\t\t}, {\n\t\t\t\t\timage: '/static/images/icon_weixin.png',\n\t\t\t\t\ttext: '微信',\n\t\t\t\t\tid: 2\n\t\t\t\t}];\n\t\t\t\tthis.openWay = 1;\n\t\t\t} else {\n\t\t\t\tthis.openLists = [{\n\t\t\t\t\timage: '/static/images/zhifubao.png',\n\t\t\t\t\ttext: '支付宝',\n\t\t\t\t\tid: 1\n\t\t\t\t}, {\n\t\t\t\t\timage: '/static/images/lingqian.png',\n\t\t\t\t\ttext: '零钱',\n\t\t\t\t\tid: 3\n\t\t\t\t}];\n\t\t\t\tthis.openWay = 1;\n\t\t\t}\n\t\t\t// #endif\n\t\t\t// this.getOrder()\n\t\t},\n\t\tonShow() {\n\t\t\tthis.showPay = false\n\t\t\tthis.getOrder()\n\t\t\tthis.token = uni.getStorageSync('token')\n\t\t\tif (uni.getStorageSync('token')) {\n\t\t\t\tthis.getIsVip()\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t//预览图片\n\t\t\tpeiveImgs(index,imgs){\n\t\t\t\tuni.previewImage({\n\t\t\t\t\tcurrent:index,\n\t\t\t\t\turls:imgs\n\t\t\t\t})\n\t\t\t},\n\t\t\tgoInfo(userId){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl:'/my/introduction/introduction?userId='+userId\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindclost() {\n\t\t\t\tthis.showPay = false\n\t\t\t},\n\t\t\t// 查看图片\n\t\t\tsaveImg(imgs, index) {\n\t\t\t\tif (this.LBSelect) {\n\t\t\t\t\tif (index == this.LBIndex - 1) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// console.log(imgs)\n\t\t\t\tlet that = this;\n\t\t\t\tlet imgArr = imgs\n\t\t\t\t// imgArr.push(imgs);\n\t\t\t\t// //预览图片\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: imgArr,\n\t\t\t\t\tcurrent: imgArr[index]\n\t\t\t\t});\n\t\t\t},\n\t\t\tcopyClick(copy) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: copy,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tuni.getClipboardData({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: \"复制成功\",\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 一键导航\n\t\t\tbindGps(latitude, longitude, name) {\n\t\t\t\tuni.openLocation({\n\t\t\t\t\tlatitude: Number(latitude), //要去的纬度-地址       \n\t\t\t\t\tlongitude: Number(longitude), //要去的经度-地址\n\t\t\t\t\tname: name, //地址名称\n\t\t\t\t\taddress: name, //详细地址名称\n\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\tconsole.log('导航成功');\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(error) {\n\t\t\t\t\t\tconsole.log(error)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 拨打电话\n\t\t\tbindphone(phone) {\n\t\t\t\tlet that = this\n\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '是否拨打电话',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tconsole.log('用户点击确定', that.phone);\n\t\t\t\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\t\t\t\tphoneNumber: phone //仅为示例\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tbinddetail(e) {\n\t\t\t\tconsole.log(e)\n\t\t\t\tuni.navigateTo({\n\n\t\t\t\t\turl: '/pages/index/game/order?id=' + e.orderTakingId\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetOrder() {\n\t\t\t\tlet data = {\n\t\t\t\t\tid: this.id\n\t\t\t\t}\n\t\t\t\tthis.$Request.get('/app/orders/queryOrders', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.order = res.data\n\t\t\t\t\t\tif (this.order.appointInformation.tags) {\n\t\t\t\t\t\t\tthis.order.appointInformation.tags = this.order.appointInformation.tags.split(\"|\");\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// if (this.order.startImg) {\n\t\t\t\t\t\t// \tthis.order.startImg = this.order.startImg.split(\",\");\n\t\t\t\t\t\t// }\n\t\t\t\t\t\t// if (this.order.endImg) {\n\t\t\t\t\t\t// \tthis.order.endImg = this.order.endImg.split(\",\");\n\t\t\t\t\t\t// }\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tdelOrder(e) {\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确定删除订单吗?',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.get('/app/orders/deleteOrder', data).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: \"删除成功\"\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t// that.mescroll.resetUpScroll()\n\t\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetIsVip() {\n\t\t\t\tthis.$Request.get(\"/app/UserVip/isUserVip\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.isVip = res.data\n\t\t\t\t\t\tuni.setStorageSync('isVIP', res.data)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 支付订单\n\t\t\tpay() {\n\t\t\t\tif(this.isPay==false){\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet that = this\n\t\t\t\tthat.showPay = false;\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '付款提示',\n\t\t\t\t\tcontent: '确认支付' + that.order.payMoney + '元吗?',\n\t\t\t\t\tsuccess: function(re) {\n\t\t\t\t\t\tif (re.confirm) {\n\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t//classify  1app  2公众号 3小程序\n\t\t\t\t\t\t\tlet classify = 1;\n\t\t\t\t\t\t\tif (that.openWay == 2) { //微信\n\t\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\n\t\t\t\t\t\t\t\t\torderId: that.order.ordersId,\n\t\t\t\t\t\t\t\t\tclassify: 3\n\t\t\t\t\t\t\t\t}).then(red => {\n\t\t\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\t\t\tuni.requestPayment({\n\t\t\t\t\t\t\t\t\t\t\tprovider: 'wxpay',\n\t\t\t\t\t\t\t\t\t\t\ttimeStamp: red.data.timestamp,\n\t\t\t\t\t\t\t\t\t\t\tnonceStr: red.data.noncestr,\n\t\t\t\t\t\t\t\t\t\t\tpackage: red.data.package,\n\t\t\t\t\t\t\t\t\t\t\tsignType: red.data.signType,\n\t\t\t\t\t\t\t\t\t\t\tpaySign: red.data.sign,\n\t\t\t\t\t\t\t\t\t\t\tsuccess: function(redd) {\n\t\t\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('EditAddress')\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\t\t\t\tthat.$queue.showToast(\n\t\t\t\t\t\t\t\t\t\t\t\t\t'支付失败');\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\n\t\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\t\tlet ua = navigator.userAgent.toLowerCase();\n\t\t\t\t\t\t\t\tconsole.log(ua)\n\t\t\t\t\t\t\t\tif (ua.indexOf('micromessenger') !== -1) {\n\t\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\n\t\t\t\t\t\t\t\t\t\torderId: that.order.ordersId,\n\t\t\t\t\t\t\t\t\t\tclassify: 4\n\t\t\t\t\t\t\t\t\t}).then(red => {\n\t\t\t\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\t\t\t\tthat.callPay(red.data);\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// #endif\n\n\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/wxPay/wxPayOrder\", {\n\t\t\t\t\t\t\t\t\torderId: that.order.ordersId,\n\t\t\t\t\t\t\t\t\tclassify: 1\n\t\t\t\t\t\t\t\t}).then(red => {\n\t\t\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\t\t\tconsole.log(red,'+++++++++++++++++++++')\n\t\t\t\t\t\t\t\t\t\tthat.setPayment('wxpay', JSON.stringify(red.data));\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\n\t\t\t\t\t\t\t} else if (that.openWay == 1) { //支付宝\n\t\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payOrder\", {\n\t\t\t\t\t\t\t\t\torderId: that.order.ordersId,\n\t\t\t\t\t\t\t\t\tclassify: 2\n\t\t\t\t\t\t\t\t}).then(red => {\n\t\t\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\t\t\tconst div = document.createElement('div')\n\t\t\t\t\t\t\t\t\t\tdiv.innerHTML = red.data //此处form就是后台返回接收到的数据\n\t\t\t\t\t\t\t\t\t\tdocument.body.appendChild(div)\n\t\t\t\t\t\t\t\t\t\tdocument.forms[0].submit()\n\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/aliPay/payOrder\", {\n\t\t\t\t\t\t\t\t\torderId: that.order.ordersId,\n\t\t\t\t\t\t\t\t\tclassify: 1\n\t\t\t\t\t\t\t\t}).then(red => {\n\t\t\t\t\t\t\t\t\tif (red.code == 0) {\n\t\t\t\t\t\t\t\t\t\tthat.setPayment('alipay', red.data);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: red.msg,\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t} else if (that.openWay == 3) { //零钱\n\t\t\t\t\t\t\t\tthat.$Request.post(\"/app/orders/payMoney\", {\n\t\t\t\t\t\t\t\t\tordersId: that.order.ordersId,\n\t\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t} else if (re.cancel) {\n\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t})\n\n\t\t\t},\n\t\t\t// 完成订单\n\t\t\tcancel(e) {\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '订单完成后款项将支付给服务方，确认完成订单吗?',\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\t\t\t\tstatus: '2'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthat.$Request.get('/app/orders/cancelOrder', data).then(res => {\n\t\t\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\t\t\tthat.mescroll.resetUpScroll()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 取消订单\n\t\t\tcancelOrder(e) {\n\t\t\t\tlet data = {\n\t\t\t\t\tid: e.ordersId,\n\t\t\t\t\tstatus: '3'\n\t\t\t\t}\n\t\t\t\tthis.$Request.get('/app/orders/cancelOrder', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '取消成功',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tcallPay: function(response) {\n\t\t\t\tif (typeof WeixinJSBridge === \"undefined\") {\n\t\t\t\t\tif (document.addEventListener) {\n\t\t\t\t\t\tdocument.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);\n\t\t\t\t\t} else if (document.attachEvent) {\n\t\t\t\t\t\tdocument.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));\n\t\t\t\t\t\tdocument.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.onBridgeReady(response);\n\t\t\t\t}\n\t\t\t},\n\t\t\tonBridgeReady: function(response) {\n\t\t\t\tlet that = this;\n\t\t\t\tif (!response.package) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tWeixinJSBridge.invoke(\n\t\t\t\t\t'getBrandWCPayRequest', {\n\t\t\t\t\t\t\"appId\": response.appid, //公众号名称，由商户传入\n\t\t\t\t\t\t\"timeStamp\": response.timestamp, //时间戳，自1970年以来的秒数\n\t\t\t\t\t\t\"nonceStr\": response.noncestr, //随机串\n\t\t\t\t\t\t\"package\": response.package,\n\t\t\t\t\t\t\"signType\": response.signType, //微信签名方式：\n\t\t\t\t\t\t\"paySign\": response.sign //微信签名\n\t\t\t\t\t},\n\t\t\t\t\tfunction(res) {\n\t\t\t\t\t\tif (res.err_msg === \"get_brand_wcpay_request:ok\") {\n\t\t\t\t\t\t\t// 使用以上方式判断前端返回,微信团队郑重提示：\n\t\t\t\t\t\t\t//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。\n\t\t\t\t\t\t\tuni.removeStorageSync('EditAddress')\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '支付成功'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tWeixinJSBridge.log(response.err_msg);\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t},\n\t\t\tselectWay: function(id) {\n\t\t\t\tthis.openWay = id;\n\t\t\t},\n\t\t\tsetPayment(name, order) {\n\t\t\t\tlet that = this;\n\t\t\t\tuni.requestPayment({\n\t\t\t\t\tprovider: name,\n\t\t\t\t\torderInfo: order, //微信、支付宝订单数据\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tuni.removeStorageSync('EditAddress')\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '支付成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 1000)\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\tthat.isPay = true\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tconsole.log(err,12)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoMsg() {\n\t\t\t\tlet data = {\n\t\t\t\t\tuserId: uni.getStorageSync('userId'),\n\t\t\t\t\tfocusedUserId: this.order.user.userId\n\t\t\t\t}\n\t\t\t\tthis.$Request.postJson('/app/chat/insertChatConversation ', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tlet id = this.order.user.userId == res.data.userId ? res.data.focusedUserId : this.order\n\t\t\t\t\t\t\t.user.userId\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/msg/im?chatConversationId=' + res.data.chatConversationId +\n\t\t\t\t\t\t\t\t'&byUserId=' + id\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground: #f7f7f7;\n\t}\n\n\t.status {\n\t\tmargin-top: 40rpx;\n\t\tcolor: #333333;\n\t\tfont-size: 38rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.types {\n\t\twidth: 100%;\n\t\theight: 192rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 16rpx;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.types-box-r {\n\t\t/* width: 200upx; */\n\t\ttext-align: right;\n\t\tmargin-right: 30rpx;\n\t\tcolor: #FF2D01;\n\t\tfont-size: 26rpx;\n\t}\n\n\t.types-box-r>text {\n\t\tfont-size: 38rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.types-box-r>span {\n\t\tfont-size: 52rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.types-r {\n\t\tmargin-left: 30rpx;\n\t}\n\n\t.types-r-t {\n\t\tcolor: #333333;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.types-r-b {\n\t\tcolor: #999999;\n\t\tfont-size: 26rpx;\n\t\tfont-weight: 500;\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.bg {\n\t\tbackground: #FFFFFF;\n\t}\n\n\t.tabber {\n\t\twidth: 100%;\n\t\tbackground: #ffffff;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 9;\n\t\tjustify-content: flex-end;\n\t\theight: 127rpx;\n\t\t/* padding-right: 30rpx; */\n\t}\n\n\t.tit {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tword-wrap: break-word;\n\t\twhite-space: normal;\n\t\t-webkit-line-clamp: 2;\n\t\tfont-size: 30upx;\n\t\t/* font-weight: bold; */\n\t\tcolor: #a9a9a9;\n\t}\n\n\t.btn {\n\t\tbackground: #557EFD;\n\t\tcolor: #FFFFFF;\n\t\tborder-radius: 50upx;\n\t\tpadding: 24upx 0upx;\n\t\ttext-align: center;\n\t\twidth: 200upx;\n\t\tmargin: 0 30upx 0upx 0;\n\t}\n\n\t.box {\n\t\twidth: 100%;\n\t\theight: 100vh;\n\t\tbackground: rgba(0, 0, 0, 0.7);\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 9999;\n\t}\n\n\t.bottbox {\n\t\tposition: fixed;\n\t\tbottom: 0upx;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 9999;\n\t\tbackground: #ffffff;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447624537\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}