{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-loadmore/u-loadmore.vue?413e", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-loadmore/u-loadmore.vue?ddb3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-loadmore/u-loadmore.vue?e711", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-loadmore/u-loadmore.vue?c277", "uni-app:///uview-ui/components/u-loadmore/u-loadmore.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-loadmore/u-loadmore.vue?bec0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-loadmore/u-loadmore.vue?b761"], "names": ["name", "props", "bgColor", "type", "default", "icon", "fontSize", "color", "status", "iconType", "loadText", "loadmore", "loading", "nomore", "isDot", "iconColor", "marginTop", "marginBottom", "height", "data", "dotText", "computed", "loadTextStyle", "position", "zIndex", "backgroundColor", "cricleStyle", "borderColor", "flowerStyle", "showText", "text", "methods", "loadMore"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wMAEN;AACP,KAAK;AACL;AACA,aAAa,yNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuB5wB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,gBAiBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;UACAO;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;EACA;EACAe;IACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAf;QACAD;QACAiB;QACAC;QACAC;QACA;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;IACA;IACA;IACA;IACAC;MACA,QACA;IACA;IACA;IACAC;MACA;MACA,kEACA,gEACA,oEACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAA+6C,CAAgB,yvCAAG,EAAC,C;;;;;;;;;;;ACAn8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-loadmore/u-loadmore.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-loadmore.vue?vue&type=template&id=874545c0&scoped=true&\"\nvar renderjs\nimport script from \"./u-loadmore.vue?vue&type=script&lang=js&\"\nexport * from \"./u-loadmore.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-loadmore.vue?vue&type=style&index=0&id=874545c0&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"874545c0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-loadmore/u-loadmore.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loadmore.vue?vue&type=template&id=874545c0&scoped=true&\"", "var components\ntry {\n  components = {\n    uLine: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-line/u-line\" */ \"@/uview-ui/components/u-line/u-line.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-loading/u-loading\" */ \"@/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$u.addUnit(_vm.height)\n  var s0 = _vm.__get_style([_vm.loadTextStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loadmore.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loadmore.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-load-more-wrap\" :style=\"{\n\t\tbackgroundColor: bgColor,\n\t\tmarginBottom: marginBottom + 'rpx',\n\t\tmarginTop: marginTop + 'rpx',\n\t\theight: $u.addUnit(height)\n\t}\">\n\t\t<u-line color=\"#d4d4d4\" length=\"50\"></u-line>\n\t\t<!-- 加载中和没有更多的状态才显示两边的横线 -->\n\t\t<view :class=\"status == 'loadmore' || status == 'nomore' ? 'u-more' : ''\" class=\"u-load-more-inner\">\n\t\t\t<view class=\"u-loadmore-icon-wrap\">\n\t\t\t\t<u-loading class=\"u-loadmore-icon\" :color=\"iconColor\" :mode=\"iconType == 'circle' ? 'circle' : 'flower'\" :show=\"status == 'loading' && icon\"></u-loading>\n\t\t\t</view>\n\t\t\t<!-- 如果没有更多的状态下，显示内容为dot（粗点），加载特定样式 -->\n\t\t\t<view class=\"u-line-1\" :style=\"[loadTextStyle]\" :class=\"[(status == 'nomore' && isDot == true) ? 'u-dot-text' : 'u-more-text']\" @tap=\"loadMore\">\n\t\t\t\t{{ showText }}\n\t\t\t</view>\n\t\t</view>\n\t\t<u-line color=\"#d4d4d4\" length=\"50\"></u-line>\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * loadmore 加载更多\n\t * @description 此组件一般用于标识页面底部加载数据时的状态。\n\t * @tutorial https://www.uviewui.com/components/loadMore.html\n\t * @property {String} status 组件状态（默认loadmore）\n\t * @property {String} bg-color 组件背景颜色，在页面是非白色时会用到（默认#ffffff）\n\t * @property {Boolean} icon 加载中时是否显示图标（默认true）\n\t * @property {String} icon-type 加载中时的图标类型（默认circle）\n\t * @property {String} icon-color icon-type为circle时有效，加载中的动画图标的颜色（默认#b7b7b7）\n\t * @property {Boolean} is-dot status为nomore时，内容显示为一个\"●\"（默认false）\n\t * @property {String} color 字体颜色（默认#606266）\n\t * @property {String Number} margin-top 到上一个相邻元素的距离\n\t * @property {String Number} margin-bottom 到下一个相邻元素的距离\n\t * @property {Object} load-text 自定义显示的文字，见上方说明示例\n\t * @event {Function} loadmore status为loadmore时，点击组件会发出此事件\n\t * @example <u-loadmore :status=\"status\" icon-type=\"iconType\" load-text=\"loadText\" />\n\t */\n\texport default {\n\t\tname: \"u-loadmore\",\n\t\tprops: {\n\t\t\t// 组件背景色\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'transparent'\n\t\t\t},\n\t\t\t// 是否显示加载中的图标\n\t\t\ticon: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 字体大小\n\t\t\tfontSize: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '28'\n\t\t\t},\n\t\t\t// 字体颜色\n\t\t\tcolor: {\n\t\t\t\ttype: String, \n\t\t\t\tdefault: '#606266'\n\t\t\t},\n\t\t\t// 组件状态，loadmore-加载前的状态，loading-加载中的状态，nomore-没有更多的状态\n\t\t\tstatus: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'loadmore'\n\t\t\t},\n\t\t\t// 加载中状态的图标，flower-花朵状图标，circle-圆圈状图标\n\t\t\ticonType: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'circle'\n\t\t\t},\n\t\t\t// 显示的文字\n\t\t\tloadText: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tloadmore: '加载更多',\n\t\t\t\t\t\tloading: '正在加载...',\n\t\t\t\t\t\tnomore: '没有更多了'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 在“没有更多”状态下，是否显示粗点\n\t\t\tisDot: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 加载中显示圆圈动画时，动画的颜色\n\t\t\ticonColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#b7b7b7'\n\t\t\t},\n\t\t\t// 上边距\n\t\t\tmarginTop: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\t// 下边距\n\t\t\tmarginBottom: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\t// 高度，单位rpx\n\t\t\theight: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 'auto'\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 粗点\n\t\t\t\tdotText: \"●\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 加载的文字显示的样式\n\t\t\tloadTextStyle() {\n\t\t\t\treturn {\n\t\t\t\t\tcolor: this.color,\n\t\t\t\t\tfontSize: this.fontSize + 'rpx',\n\t\t\t\t\tposition: 'relative',\n\t\t\t\t\tzIndex: 1,\n\t\t\t\t\tbackgroundColor: this.bgColor,\n\t\t\t\t\t// 如果是加载中状态，动画和文字需要距离近一点\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 加载中圆圈动画的样式\n\t\t\tcricleStyle() {\n\t\t\t\treturn {\n\t\t\t\t\tborderColor: `#e5e5e5 #e5e5e5 #e5e5e5 ${this.circleColor}`\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 加载中花朵动画形式\n\t\t\t// 动画由base64图片生成，暂不支持修改\n\t\t\tflowerStyle() {\n\t\t\t\treturn {\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 显示的提示文字\n\t\t\tshowText() {\n\t\t\t\tlet text = '';\n\t\t\t\tif(this.status == 'loadmore') text = this.loadText.loadmore;\n\t\t\t\telse if(this.status == 'loading') text = this.loadText.loading;\n\t\t\t\telse if(this.status == 'nomore' && this.isDot) text = this.dotText;\n\t\t\t\telse text = this.loadText.nomore;\n\t\t\t\treturn text;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tloadMore() {\n\t\t\t\t// 只有在“加载更多”的状态下才发送点击事件，内容不满一屏时无法触发底部上拉事件，所以需要点击来触发\n\t\t\t\tif(this.status == 'loadmore') this.$emit('loadmore');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t@import \"../../libs/css/style.components.scss\";\n\t\n\t/* #ifdef MP */\n\t// 在mp.scss中，赋予了u-line为flex: 1，这里需要一个明确的长度，所以重置掉它\n\t// 在组件内部，把组件名(u-line)当做选择器，在微信开发工具会提示不合法，但不影响使用\n\tu-line {\n\t\tflex: none;\n\t}\n\t/* #endif */\n\t\n\t.u-load-more-wrap {\n\t\t@include vue-flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t\n\t.u-load-more-inner {\n\t\t@include vue-flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 0 12rpx;\n\t}\n\t\n\t.u-more {\n\t\tposition: relative;\n\t\t@include vue-flex;\n\t\tjustify-content: center;\n\t}\n\t\n\t.u-dot-text {\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.u-loadmore-icon-wrap {\n\t\tmargin-right: 8rpx;\n\t}\n\t\n\t.u-loadmore-icon {\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loadmore.vue?vue&type=style&index=0&id=874545c0&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-loadmore.vue?vue&type=style&index=0&id=874545c0&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627674\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}