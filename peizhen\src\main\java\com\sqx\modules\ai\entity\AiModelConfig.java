package com.sqx.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AI模型配置实体类
 */
@Data
@ApiModel("AI模型配置")
@TableName("ai_model_config")
public class AiModelConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("模型配置ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("模型名称")
    private String modelName;

    @ApiModelProperty("模型代码")
    private String modelCode;

    @ApiModelProperty("API地址")
    private String apiUrl;

    @ApiModelProperty("API密钥")
    private String apiKey;

    @ApiModelProperty("模型类型")
    private String modelType;

    @ApiModelProperty("最大token数")
    private Integer maxTokens;

    @ApiModelProperty("温度参数")
    private BigDecimal temperature;

    @ApiModelProperty("top_p参数")
    private BigDecimal topP;

    @ApiModelProperty("频率惩罚")
    private BigDecimal frequencyPenalty;

    @ApiModelProperty("存在惩罚")
    private BigDecimal presencePenalty;

    @ApiModelProperty("是否启用")
    private Integer isEnabled;

    @ApiModelProperty("排序")
    private Integer sortOrder;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("更新时间")
    private String updateTime;

    @ApiModelProperty("备注")
    private String remark;
}
