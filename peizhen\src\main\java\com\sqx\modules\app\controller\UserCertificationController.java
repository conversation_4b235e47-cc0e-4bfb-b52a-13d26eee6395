package com.sqx.modules.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserCertification;
import com.sqx.modules.app.service.UserCertificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/userCertification")
@Api("实 名 认 证")
public class UserCertificationController {
    private UserCertificationService userCertificationService;


    /**
     * 实名列表
     */
    @RequestMapping("/queryCertification")
    @ApiOperation("实名列表")
    public Result queryCertification(Long page, Long limit, @ApiParam("状态") String status, @ApiParam("姓名") String name, Integer authentication, String phone) {

        return userCertificationService.queryCertification(page, limit, status, name, authentication);
    }

    /**
     * 查询已经进行实名的用户
     */
    @RequestMapping("/queryUserCertification")
    @ApiOperation("查询已经进行实名的用户")
    public Result queryUserCertification(Long page, Long limit, String name, String phone, Integer authentication) {
        if (page == null || limit == null) {
            return Result.error("分页条件为空");
        } else {
            IPage<UserCertification> iPage = new Page<>(page, limit);

            return userCertificationService.queryUserCertification(iPage, name, phone, authentication);
        }
    }

    /**
     * 审核实名认证
     */
    @RequestMapping("/auditorUserCertification")
    @ApiOperation("审核实名认证")
    public Result auditorUserCertification(Integer status, Long id, String remek) {
        return userCertificationService.auditorUserCertification(status, id, remek);
    }

    @PostMapping("/updateUserCertification")
    @ApiOperation("修改实名认证信息")
    public Result updateUserCertification(@RequestBody UserCertification userCertification) {
        userCertificationService.updateById(userCertification);
        return Result.success();
    }

    @PostMapping("/deleteCertification")
    @ApiOperation("删除实名认证信息")
    public Result deleteCertification(Long id) {
        userCertificationService.removeById(id);
        return Result.success();
    }
}
