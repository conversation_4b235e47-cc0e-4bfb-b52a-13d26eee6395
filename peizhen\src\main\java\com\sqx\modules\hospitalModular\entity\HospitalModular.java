package com.sqx.modules.hospitalModular.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *模块管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
public class HospitalModular implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模块id
     */
    @TableId(value = "modular_id", type = IdType.AUTO)
    @ApiModelProperty("模块id")
    private Long modularId;

    /**
     * 模块名称
     */
    @ApiModelProperty("模块名称")
    @TableField(condition = SqlCondition.LIKE)
    private String modularName;

    /**
     * 标签
     */
    @TableField(condition = SqlCondition.LIKE)
    @ApiModelProperty("标签")
    private String tags;

    /**
     * 图片
     */
    @ApiModelProperty("image")
    private String image;

    /**
     * 是否启用(0否 1是)
     */
    @ApiModelProperty("是否启用(0否 1是)")
    private Integer isEnable;
    /**
     * 1banner位置 2列表
     */
    @ApiModelProperty("1banner位置 2列表")
    private Integer type;
}
