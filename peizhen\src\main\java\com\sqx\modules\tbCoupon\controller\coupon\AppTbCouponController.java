package com.sqx.modules.tbCoupon.controller.coupon;

import com.sqx.common.utils.Result;
import com.sqx.modules.sys.controller.AbstractController;
import com.sqx.modules.tbCoupon.entity.TbCoupon;
import com.sqx.modules.tbCoupon.service.TbCouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "用户端-优惠券", tags = {"用户端-优惠券"})
@RequestMapping(value = "/app/coupon/")
public class AppTbCouponController extends AbstractController {

    @Autowired
    private TbCouponService tbCouponService;

    @ApiOperation("获取优惠券列表")
    @GetMapping(value = "getCouponPageList")
    public Result getCouponPageList(Integer page, Integer limit, TbCoupon tbCoupon) {
        tbCoupon.setIsEnable(1);
        tbCoupon.setDeleteFlag(0);
        return Result.success().put("data", tbCouponService.getCouponPageList(page, limit, tbCoupon));
    }



}