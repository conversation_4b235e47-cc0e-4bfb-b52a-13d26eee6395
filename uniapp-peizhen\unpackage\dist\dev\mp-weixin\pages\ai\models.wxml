<view class="models-container data-v-1516d4f0"><view class="header data-v-1516d4f0"><text class="title data-v-1516d4f0">选择AI模型</text><text class="subtitle data-v-1516d4f0">不同模型具有不同的特点和能力</text></view><view class="models-list data-v-1516d4f0"><block wx:for="{{modelList}}" wx:for-item="model" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectModel',['$0'],[[['modelList','id',model.id]]]]]]]}}" class="{{['model-item','data-v-1516d4f0',(selectedModel===model.modelCode)?'active':'']}}" bindtap="__e"><view class="model-info data-v-1516d4f0"><view class="model-header data-v-1516d4f0"><text class="model-name data-v-1516d4f0">{{model.modelName}}</text><u-tag vue-id="{{'8dc2c704-1-'+__i0__}}" text="{{model.status===1?'可用':'不可用'}}" type="{{model.status===1?'success':'error'}}" size="mini" class="data-v-1516d4f0" bind:__l="__l"></u-tag></view><text class="model-description data-v-1516d4f0">{{model.description||'暂无描述'}}</text><view class="model-params data-v-1516d4f0"><text class="param-item data-v-1516d4f0">{{"温度: "+(model.temperature||0.7)}}</text><text class="param-item data-v-1516d4f0">{{"最大Token: "+(model.maxTokens||4000)}}</text></view></view><view class="model-select data-v-1516d4f0"><u-icon vue-id="{{'8dc2c704-2-'+__i0__}}" name="{{selectedModel===model.modelCode?'checkmark-circle-fill':'circle'}}" color="{{selectedModel===model.modelCode?'#0175FE':'#ccc'}}" size="24" class="data-v-1516d4f0" bind:__l="__l"></u-icon></view></view></block></view><view class="bottom-actions data-v-1516d4f0"><u-button vue-id="8dc2c704-3" type="primary" disabled="{{!selectedModel}}" data-event-opts="{{[['^click',[['confirmSelection']]]]}}" bind:click="__e" class="data-v-1516d4f0" bind:__l="__l" vue-slots="{{['default']}}">确认选择</u-button></view><block wx:if="{{loading}}"><view class="loading-overlay data-v-1516d4f0"><view class="loading-content data-v-1516d4f0"><text class="data-v-1516d4f0">加载中...</text></view></view></block></view>