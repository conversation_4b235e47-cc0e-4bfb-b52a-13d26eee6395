# 医生字典配置功能说明

## 功能概述

将医生表中适合用字典配置的字段改为从字典表动态获取，提高系统的可配置性和维护性。不再使用硬编码的选项，而是通过字典表统一管理。

## 实现的字典配置

### 1. 字典类型

以下医生相关字段已改为字典配置：

#### 1.1 性别字典
- **字典类型**: `性别`
- **选项**: 
  - 男 (code: 1)
  - 女 (code: 2)

#### 1.2 医生职称字典
- **字典类型**: `医生职称`
- **选项**:
  - 主任医师 (code: 1)
  - 副主任医师 (code: 2)
  - 主治医师 (code: 3)
  - 住院医师 (code: 4)

#### 1.3 学历字典
- **字典类型**: `学历`
- **选项**:
  - 博士 (code: 1)
  - 硕士 (code: 2)
  - 本科 (code: 3)
  - 专科 (code: 4)

#### 1.4 工作状态字典
- **字典类型**: `工作状态`
- **选项**:
  - 在职 (code: 1)
  - 离职 (code: 2)
  - 休假 (code: 3)

### 2. 数据库配置

在 `peizhen.sql` 中添加了字典数据：

```sql
-- 性别字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('性别', '性别', NULL, NULL, 1, '性别分类', 0);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('男', NULL, '1', '男', 1, '男性', (SELECT id FROM sys_dict WHERE type = '性别' AND parent_id = 0));
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('女', NULL, '2', '女', 2, '女性', (SELECT id FROM sys_dict WHERE type = '性别' AND parent_id = 0));

-- 医生职称字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('医生职称', '医生职称', NULL, NULL, 1, '医生职称分类', 0);
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('主任医师', NULL, '1', '主任医师', 1, '主任医师职称', (SELECT id FROM sys_dict WHERE type = '医生职称' AND parent_id = 0));
-- ... 其他职称选项

-- 学历字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('学历', '学历', NULL, NULL, 1, '学历分类', 0);
-- ... 学历选项

-- 工作状态字典
INSERT INTO `sys_dict` (`name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES ('工作状态', '工作状态', NULL, NULL, 1, '医生工作状态分类', 0);
-- ... 工作状态选项
```

## 前端实现

### 1. universalMission页面

#### 1.1 数据结构
```javascript
data() {
  return {
    // 字典数据
    genderList: [],        // 性别字典
    titleList: [],         // 职称字典
    educationList: [],     // 学历字典
    workStatusList: []     // 工作状态字典
  }
}
```

#### 1.2 模板修改
将硬编码的选项改为动态渲染：

**性别选择**：
```html
<el-radio-group v-model="doctorForm.gender">
  <el-radio v-for="item in genderList" :key="item.code" :label="parseInt(item.code)">{{item.value}}</el-radio>
</el-radio-group>
```

**职称选择**：
```html
<el-select v-model="doctorForm.title" placeholder="请选择职称" style="width: 100%">
  <el-option v-for="item in titleList" :key="item.code" :label="item.value" :value="item.value"></el-option>
</el-select>
```

**学历选择**：
```html
<el-select v-model="doctorForm.education" placeholder="请选择学历" style="width: 100%">
  <el-option v-for="item in educationList" :key="item.code" :label="item.value" :value="item.value"></el-option>
</el-select>
```

**工作状态选择**：
```html
<el-select v-model="doctorForm.workStatus" placeholder="请选择工作状态" style="width: 100%">
  <el-option v-for="item in workStatusList" :key="item.code" :label="item.value" :value="parseInt(item.code)"></el-option>
</el-select>
```

#### 1.3 方法实现
```javascript
// 加载字典数据
loadDictData() {
  this.loadDictByType('性别', (data) => { this.genderList = data })
  this.loadDictByType('医生职称', (data) => { this.titleList = data })
  this.loadDictByType('学历', (data) => { this.educationList = data })
  this.loadDictByType('工作状态', (data) => { this.workStatusList = data })
},

// 根据类型加载字典数据
loadDictByType(type, callback) {
  this.$http({
    url: this.$http.adornUrl('sys/dict/selectDictList'),
    method: 'get',
    params: this.$http.adornParams({ 'type': type })
  }).then(({ data }) => {
    if (data && data.code === 0) {
      if (callback && typeof callback === 'function') {
        callback(data.data || [])
      }
    }
  })
}
```

### 2. doctor-add-or-update页面

同样的实现方式，在独立的医生管理页面中也使用字典配置。

## API接口

使用现有的字典查询接口：

```javascript
// 获取字典数据
this.$http({
  url: this.$http.adornUrl('sys/dict/selectDictList'),
  method: 'get',
  params: this.$http.adornParams({ 'type': '字典类型' })
}).then(({ data }) => {
  if (data && data.code === 0) {
    // 处理字典数据
    this.dictList = data.data
  }
})
```

## 优势

### 1. 可维护性
- 字典数据统一管理
- 修改选项无需改代码
- 便于系统管理员配置

### 2. 扩展性
- 可以轻松添加新的选项
- 支持选项的排序和启用/禁用
- 便于国际化扩展

### 3. 一致性
- 全系统使用统一的字典数据
- 避免硬编码导致的不一致
- 便于数据标准化

### 4. 灵活性
- 支持动态配置
- 可以根据业务需求调整选项
- 便于个性化定制

## 使用说明

### 1. 添加新的字典类型
1. 在数据库中添加字典数据
2. 在前端页面中添加对应的数据字段
3. 在loadDictData方法中添加加载逻辑
4. 在模板中使用v-for渲染选项

### 2. 修改字典选项
1. 直接在数据库sys_dict表中修改
2. 前端会自动获取最新的字典数据
3. 无需重启服务或修改代码

### 3. 注意事项
- 字典的code值要与数据库字段的数据类型匹配
- 删除字典项前要确保没有关联数据使用
- 字典数据的order_num字段控制显示顺序

这个字典配置功能大大提升了系统的可配置性和维护性，使医生管理更加灵活和标准化。
