package com.sqx.modules.pay.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sqx.modules.tbCoupon.entity.TbCoupon;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description pay_classify
 * <AUTHOR>
 * @date 2022-04-06
 */
@Data
public class PayClassify implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 充值分类id
     */
    private Long payClassifyId;

    /**
     * 售价
     */
    private BigDecimal price;

    /**
     * 优惠券
     */
    private Long couponId;

    @TableField(exist = false)
    private TbCoupon coupon;

    private Integer giveNum;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 时间
     */
    private String createTime;

    public PayClassify() {}
}
