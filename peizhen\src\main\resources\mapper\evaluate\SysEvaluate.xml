<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.evaluate.dao.SysEvaluateDao">


    <select id="getTotalScore" resultType="java.lang.String">
        select sum(satisfaction_flag)
        from sys_evaluate
        where rider_user_id = #{userId} and is_delete =0
    </select>
    <select id="getUserEvaluateList" resultType="com.sqx.modules.evaluate.entity.SysEvaluate">
        SELECT
        *
        FROM
        (SELECT
        e.*,
        (SELECT user_name FROM tb_user u WHERE u.user_id = e.user_id)AS userName,
        (SELECT avatar FROM tb_user u WHERE u.user_id = e.user_id)AS userAvatar,
        (SELECT user_name FROM tb_user u WHERE u.user_id = e.rider_user_id)AS rideName,
        (SELECT avatar FROM tb_user u WHERE u.user_id = e.rider_user_id)AS rideAvatar
        FROM
        sys_evaluate e) AS t1
        WHERE 1 = 1 and is_delete =0
        <if test="evaluate.userId!=null">
            and t1.user_id = #{evaluate.userId}
        </if>
        <if test="evaluate.riderUserId!=null">
            and t1.rider_user_id = #{evaluate.riderUserId}
        </if>
        <if test="evaluate.satisfactionFlag!=null">
            and t1.satisfaction_flag = #{evaluate.satisfactionFlag}
        </if>
        <if test="evaluate.indentNumber!=null and evaluate.indentNumber!=''">
            and t1.indent_number like concat("%",#{evaluate.indentNumber},"%")
        </if>
        <if test="evaluate.evaluateMessage!=null and evaluate.evaluateMessage!=''">
            and t1.evaluate_message like concat("%",#{evaluate.evaluateMessage},"%")
        </if>
        <if test="evaluate.userName!=null and evaluate.userName!=''">
            and t1.userName like concat("%",#{evaluate.userName},"%")
        </if>
        <if test="evaluate.rideName!=null and evaluate.rideName!=''">
            and t1.rideName like concat("%",#{evaluate.rideName},"%")
        </if>
        order by create_time desc
    </select>
</mapper>