<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.task.dao.HelpTakeDao">

	<insert id="insertHelpTake" parameterType="com.sqx.modules.task.entity.HelpTake" useGeneratedKeys="true" keyProperty="id">
		insert into help_take
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="orderId != null ">order_id,</if>
			<if test="userId != null ">user_id,</if>
			<if test="money != null  ">money,</if>
			<if test="status != null ">status,</if>
			<if test="createTime != null ">create_time,</if>
			<if test="endTime != null ">end_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="orderId != null ">#{orderId},</if>
			<if test="userId != null ">#{userId},</if>
			<if test="money != null  ">#{money},</if>
			<if test="status != null ">#{status},</if>
			<if test="createTime != null ">#{createTime},</if>
			<if test="endTime != null ">#{endTime},</if>
		</trim>
	</insert>


	<select id="selectRunHelpOrder" resultType="Map">
		select t.id as helpTakeId,o.id as helpOrderId,o.content,t.money,o.delivery_time as deliveryTime,o.user_id as userId,g.game_name as serviceName,t.status,t.create_time as createTime,
		o.province,o.city,o.district,o.details_address as detailsAddress,o.`name`,o.phone,u.user_name as userName,ut.user_name as helpTakeUserName,t.user_id as helpTakeUserId
		from help_take t
		left join help_order o on t.id=o.help_take_id
		left join tb_user u on u.user_id=o.user_id
		left join tb_user ut on ut.user_id=t.user_id
		left join game g on g.id=o.game_id
		where 1=1
		<if test="userId!=null and userId!=0">
			and  t.user_id=#{userId}
		</if>
		<if test="status!=null and status!=0">
			and t.status=#{status}
		</if>
		order by t.create_time desc
	</select>

	<select id="selectRunHelpOrderList" resultType="Map">
		select t.id as helpTakeId,o.id as helpOrderId,o.content,t.money,o.delivery_time as deliveryTime,o.user_id as userId,g.game_name as serviceName,t.status,t.create_time as createTime,
		o.province,o.city,o.district,o.details_address as detailsAddress,o.`name`,o.phone,u.user_name as userName,ut.user_name as helpTakeUserName,t.user_id as helpTakeUserId
		from help_take t
		left join help_order o on t.id=o.help_take_id
		left join tb_user u on u.user_id=o.user_id
		left join tb_user ut on ut.user_id=t.user_id
		left join game g on g.id=o.game_id
		where 1=1
		<if test="status!=null and status!=0">
			and t.status=#{status}
		</if>
		<if test="phone!=null and phone!=''">
			and u.phone like CONCAT('%',#{phone},'%')
		</if><if test="startTime!=null and startTime!=''">
		and date_format(t.create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
	</if>
		<if test="endTime!=null and endTime!=''">
			and date_format(t.create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
		</if>
		order by t.create_time desc
	</select>

	<select id="helpTakeListExcel" resultType="Map">
		select t.id as helpTakeId,o.id as helpOrderId,o.content,t.money,o.delivery_time as deliveryTime,o.user_id as userId,g.game_name as serviceName,t.status,t.create_time as createTime,
		o.province,o.city,o.district,o.details_address as detailsAddress,o.`name`,o.phone,u.user_name as userName,ut.user_name as helpTakeUserName,t.user_id as helpTakeUserId
		from help_take t
		left join help_order o on t.id=o.help_take_id
		left join tb_user u on u.user_id=o.user_id
		left join tb_user ut on ut.user_id=t.user_id
		left join game g on g.id=o.game_id
		where 1=1
		<if test="status!=null and status!=0">
			and t.status=#{status}
		</if>
		<if test="phone!=null and phone!=''">
			and u.phone like CONCAT('%',#{phone},'%')
		</if>
		<if test="startTime!=null and startTime!=''">
			and date_format(t.create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
		</if>
		<if test="endTime!=null and endTime!=''">
			and date_format(t.create_time,'%Y-%m-%d')&lt;=date_format(#{endTime},'%Y-%m-%d')
		</if>
		order by t.create_time desc
	</select>



	<select id="countHelpTakeByCreateTime" resultType="Integer">
		select count(*) from help_take where status in (1,2)
		<if test="flag!=null and flag==1">
			and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(create_time,'%Y')=date_format(#{time},'%Y')
		</if>
	</select>


	<select id="sumMoneyBySend" resultType="Double">
		select sum(money) from help_take where status=2
		<if test="flag!=null and flag==1">
			and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(create_time,'%Y')=date_format(#{time},'%Y')
		</if>
	</select>

	<select id="selectHelpTakeCount" resultType="Integer">
		select count(*) from help_take where user_id=#{userId}
		<if test="startTime!=null and startTime!=''">
			and date_format(create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
		</if>
		<if test="endTime!=null and endTime!=''">
			and date_format(create_time,'%Y-%m-%d')>=date_format(#{endTime},'%Y-%m-%d')
		</if>
	</select>

	<select id="selectHelpTakeRefundMoneyByUserId" resultType="BigDecimal">
		select ifnull(sum(money),0.00) from help_take where user_id=#{userId} and status=3
		<if test="startTime!=null and startTime!=''">
			and date_format(create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
		</if>
		<if test="endTime!=null and endTime!=''">
			and date_format(create_time,'%Y-%m-%d')>=date_format(#{endTime},'%Y-%m-%d')
		</if>
	</select>

	<select id="selectHelpTakeRefundCountByUserId" resultType="Integer">
		select count(*) from help_take where user_id=#{userId} and status=#{status}
		<if test="startTime!=null and startTime!=''">
			and date_format(create_time,'%Y-%m-%d')>=date_format(#{startTime},'%Y-%m-%d')
		</if>
		<if test="endTime!=null and endTime!=''">
			and date_format(create_time,'%Y-%m-%d')>=date_format(#{endTime},'%Y-%m-%d')
		</if>
	</select>


</mapper>