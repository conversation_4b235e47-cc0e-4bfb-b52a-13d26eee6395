{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/index.vue?77b3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/index.vue?0396", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/index.vue?5d5f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/index.vue?0a6a", "uni-app:///pages/ai/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/index.vue?d19e", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/index.vue?8aa0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "conversationList", "loading", "creating", "page", "limit", "loadmoreStatus", "showActionSheet", "currentItem", "actionList", "text", "value", "color", "showEditTitle", "editTitleValue", "selected<PERSON><PERSON>l", "modelList", "onLoad", "onShow", "onPullDownRefresh", "onReachBottom", "methods", "loadModels", "console", "uni", "loadConversations", "refreshData", "loadMore", "createNewChat", "url", "enterChat", "selectModel", "showActions", "handleAction", "editTitle", "updateTitle", "title", "cancelEdit", "deleteConversation", "content", "success", "getModelName", "getSelectedModelName", "formatTime", "processedTimeStr", "month", "day"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiFxvB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACAC;QACA;UACA;UACA;UACA;UACA;YACA;UACA;YACA;YACAC;UACA;QACA;UACAD;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;MACA;MAEA;MACA;QACA;QACA;MACA;MAEA;QACArB;QACAC;MACA;MAEA;QACAkB;QACA;UACA;UACA;YACA;UACA;YAAA;YACA;UACA;;UAEA;UACA;YACA;UACA;YACA;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;QACAA;QACA;MACA;QACA;QACAC;MACA;IACA;IAEA;IACAE;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBACA;kBACA;kBACAJ;oBACAK;kBACA;gBACA;kBACAN;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAO;MACAN;QACAK;MACA;IACA;IAEA;IACAE;MACAP;QACAK;MACA;IACA;IAEA;IACAG;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;MACA;MAEA;QAAAC;MAAA;MAEA;QACAb;QACA;UACA;UACA;YAAA;UAAA;UACA;YACA;UACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAc;MACA;IACA;IAEA;IACAC;MAAA;MACAd;QACAY;QACAG;QACAC;UACA;YACA;cACAjB;cACA;gBACA;gBACA;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBACA;cACA;gBACA;cACA;YACA;cACAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAkB;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;MACA;QACAC;MACA;MAEA;;MAEA;MACA;QACArB;QACA;MACA;MAEA;MACA;MAEA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QAAA;QACA;MACA;QACA;QACA;UACAsB;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrXA;AAAA;AAAA;AAAA;AAA+4C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAn6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ai/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/ai/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2e7a9c24&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2e7a9c24&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2e7a9c24\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ai/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2e7a9c24&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-loadmore/u-loadmore\" */ \"@/uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n    uActionSheet: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-action-sheet/u-action-sheet\" */ \"@/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-modal/u-modal\" */ \"@/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getSelectedModelName()\n  var g0 = _vm.conversationList.length === 0 && !_vm.loading\n  var l0 = _vm.__map(_vm.conversationList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var m1 = _vm.getModelName(item.modelCode)\n    var m2 = _vm.formatTime(item.updateTime)\n    return {\n      $orig: $orig,\n      m1: m1,\n      m2: m2,\n    }\n  })\n  var g1 = _vm.conversationList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"ai-container\">\n\t\t<!-- 顶部操作栏 -->\n\t\t<view class=\"top-bar\">\n\t\t\t<view class=\"page-header\">\n\t\t\t\t<text class=\"page-title\">AI助手</text>\n\t\t\t\t<text class=\"page-subtitle\">{{ getSelectedModelName() }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"top-actions\">\n\t\t\t\t<view class=\"action-btn model-btn\" @click=\"selectModel\">\n\t\t\t\t\t<u-icon name=\"setting\" size=\"18\" color=\"#666\"></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-btn new-chat-btn\" @click=\"createNewChat\">\n\t\t\t\t\t<u-icon name=\"plus\" size=\"20\" color=\"#fff\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 对话列表 -->\n\t\t<view class=\"conversation-list\">\n\t\t\t<view v-if=\"conversationList.length === 0 && !loading\" class=\"empty-state\">\n\t\t\t\t<view class=\"empty-icon\">\n\t\t\t\t\t<u-icon name=\"chat\" size=\"80\" color=\"#ddd\"></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"empty-title\">还没有对话记录</text>\n\t\t\t\t<text class=\"empty-subtitle\">开始你的第一次AI对话吧</text>\n\t\t\t\t<view class=\"empty-action\">\n\t\t\t\t\t<view class=\"start-chat-btn\" @click=\"createNewChat\">\n\t\t\t\t\t\t<u-icon name=\"plus\" size=\"20\" color=\"#fff\" style=\"margin-right: 8rpx;\"></u-icon>\n\t\t\t\t\t\t<text class=\"btn-text\">开始对话</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-for=\"item in conversationList\" :key=\"item.id\" class=\"conversation-item\" @click=\"enterChat(item)\">\n\t\t\t\t<view class=\"conversation-avatar\">\n\t\t\t\t\t<view class=\"avatar-icon\">\n\t\t\t\t\t\t<u-icon name=\"chat\" size=\"24\" color=\"#0175FE\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"conversation-content\">\n\t\t\t\t\t<view class=\"conversation-header\">\n\t\t\t\t\t\t<text class=\"conversation-title\">{{ item.title || '新对话' }}</text>\n\t\t\t\t\t\t<view class=\"conversation-meta\">\n\t\t\t\t\t\t\t<text class=\"conversation-model\">{{ getModelName(item.modelCode) }}</text>\n\t\t\t\t\t\t\t<text class=\"update-time\">{{ formatTime(item.updateTime) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"conversation-preview\">\n\t\t\t\t\t\t<text class=\"last-message\">{{ item.lastMessage || '开始新的对话...' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"conversation-actions\" @click.stop>\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" size=\"20\" color=\"#ccc\" @click=\"showActions(item)\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 加载更多 -->\n\t\t<u-loadmore :status=\"loadmoreStatus\" @loadmore=\"loadMore\" v-if=\"conversationList.length > 0\"></u-loadmore>\n\n\t\t<!-- 操作菜单 -->\n\t\t<u-action-sheet :list=\"actionList\" v-model=\"showActionSheet\" @click=\"handleAction\"></u-action-sheet>\n\n\t\t<!-- 编辑标题弹窗 -->\n\t\t<u-modal v-model=\"showEditTitle\" title=\"编辑标题\" :show-cancel-button=\"true\" @confirm=\"updateTitle\" @cancel=\"cancelEdit\">\n\t\t\t<view class=\"edit-title-content\">\n\t\t\t\t<input\n\t\t\t\t\tv-model=\"editTitleValue\"\n\t\t\t\t\tplaceholder=\"请输入对话标题\"\n\t\t\t\t\tmaxlength=\"50\"\n\t\t\t\t\tclass=\"title-input\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t</u-modal>\n\t</view>\n</template>\n\n<script>\n\timport empty from '../../components/empty.vue'\n\n\texport default {\n\t\tcomponents: {\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tconversationList: [],\n\t\t\t\tloading: false,\n\t\t\t\tcreating: false,\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 20,\n\t\t\t\tloadmoreStatus: 'loadmore',\n\t\t\t\tshowActionSheet: false,\n\t\t\t\tcurrentItem: null,\n\t\t\t\tactionList: [\n\t\t\t\t\t{ text: '编辑标题', value: 'edit' },\n\t\t\t\t\t{ text: '删除对话', value: 'delete', color: '#f56c6c' }\n\t\t\t\t],\n\t\t\t\tshowEditTitle: false,\n\t\t\t\teditTitleValue: '',\n\t\t\t\tselectedModel: 'deepseek-chat', // 默认模型\n\t\t\t\tmodelList: []\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadModels()\n\t\t\tthis.loadConversations()\n\t\t},\n\t\tonShow() {\n\t\t\t// 从模型选择页面返回时刷新\n\t\t\tconst selectedModel = uni.getStorageSync('selectedAiModel')\n\t\t\tif (selectedModel && selectedModel !== this.selectedModel) {\n\t\t\t\tthis.selectedModel = selectedModel\n\t\t\t}\n\t\t\t// 刷新对话列表\n\t\t\tthis.refreshData()\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.refreshData()\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.loadMore()\n\t\t},\n\t\tmethods: {\n\t\t\t// 加载可用模型\n\t\t\tloadModels() {\n\t\t\t\tthis.$Request.get('/app/ai/chat/models').then(res => {\n\t\t\t\t\tconsole.log('模型列表:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tthis.modelList = res.data || []\n\t\t\t\t\t\t// 设置默认选中的模型\n\t\t\t\t\t\tconst savedModel = uni.getStorageSync('selectedAiModel')\n\t\t\t\t\t\tif (savedModel) {\n\t\t\t\t\t\t\tthis.selectedModel = savedModel\n\t\t\t\t\t\t} else if (this.modelList.length > 0) {\n\t\t\t\t\t\t\tthis.selectedModel = this.modelList[0].modelCode\n\t\t\t\t\t\t\tuni.setStorageSync('selectedAiModel', this.selectedModel)\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载模型失败:', res.msg)\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('加载模型失败:', error)\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 加载对话列表\n\t\t\tloadConversations(refresh = false) {\n\t\t\t\tif (this.loading) return\n\n\t\t\t\tthis.loading = true\n\t\t\t\tif (refresh) {\n\t\t\t\t\tthis.page = 1\n\t\t\t\t\tthis.conversationList = []\n\t\t\t\t}\n\n\t\t\t\tlet data = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit\n\t\t\t\t}\n\n\t\t\t\tthis.$Request.get('/app/ai/chat/conversation/list', data).then(res => {\n\t\t\t\t\tconsole.log('对话列表:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tconst newList = res.data.list || []\n\t\t\t\t\t\tif (refresh) {\n\t\t\t\t\t\t\tthis.conversationList = newList\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.conversationList.push(...newList)\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 更新加载状态\n\t\t\t\t\t\tif (newList.length < this.limit) {\n\t\t\t\t\t\t\tthis.loadmoreStatus = 'nomore'\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.loadmoreStatus = 'loadmore'\n\t\t\t\t\t\t\tthis.page++\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载对话列表失败:', res.msg)\n\t\t\t\t\t\tthis.$queue.showToast(res.msg || '加载失败，请重试')\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('加载对话列表失败:', error)\n\t\t\t\t\tthis.$queue.showToast('加载失败，请重试')\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthis.loading = false\n\t\t\t\t\tuni.stopPullDownRefresh()\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 刷新数据\n\t\t\trefreshData() {\n\t\t\t\tthis.loadConversations(true)\n\t\t\t},\n\n\t\t\t// 加载更多\n\t\t\tloadMore() {\n\t\t\t\tif (this.loadmoreStatus === 'loadmore') {\n\t\t\t\t\tthis.loadConversations()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 创建新对话\n\t\t\tasync createNewChat() {\n\t\t\t\tif (this.creating) return\n\n\t\t\t\tif (!this.selectedModel) {\n\t\t\t\t\tthis.$queue.showToast('请先选择AI模型')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.creating = true\n\t\t\t\ttry {\n\t\t\t\t\t// 直接跳转到聊天页面，不需要先创建对话\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/ai/chat?modelCode=${this.selectedModel}&isNew=true`\n\t\t\t\t\t})\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('跳转失败:', error)\n\t\t\t\t\tthis.$queue.showToast('跳转失败，请重试')\n\t\t\t\t} finally {\n\t\t\t\t\tthis.creating = false\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 进入聊天页面\n\t\t\tenterChat(item) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/ai/chat?conversationId=${item.id}&modelCode=${item.modelCode}`\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 选择模型\n\t\t\tselectModel() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/ai/models'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 显示操作菜单\n\t\t\tshowActions(item) {\n\t\t\t\tthis.currentItem = item\n\t\t\t\tthis.showActionSheet = true\n\t\t\t},\n\n\t\t\t// 处理操作\n\t\t\thandleAction(index) {\n\t\t\t\tconst action = this.actionList[index]\n\t\t\t\tif (action.value === 'edit') {\n\t\t\t\t\tthis.editTitle()\n\t\t\t\t} else if (action.value === 'delete') {\n\t\t\t\t\tthis.deleteConversation()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 编辑标题\n\t\t\teditTitle() {\n\t\t\t\tthis.editTitleValue = this.currentItem.title || ''\n\t\t\t\tthis.showEditTitle = true\n\t\t\t},\n\n\t\t\t// 更新标题\n\t\t\tupdateTitle() {\n\t\t\t\tif (!this.editTitleValue.trim()) {\n\t\t\t\t\tthis.$queue.showToast('标题不能为空')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tlet data = { title: this.editTitleValue.trim() }\n\n\t\t\t\tthis.$Request.post(`/app/ai/chat/conversation/${this.currentItem.id}/title`, data).then(res => {\n\t\t\t\t\tconsole.log('更新标题:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\t// 更新本地数据\n\t\t\t\t\t\tconst index = this.conversationList.findIndex(item => item.id === this.currentItem.id)\n\t\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\t\tthis.conversationList[index].title = this.editTitleValue.trim()\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.$queue.showToast('标题更新成功')\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$queue.showToast(res.msg || '更新失败')\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('更新标题失败:', error)\n\t\t\t\t\tthis.$queue.showToast('更新失败，请重试')\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 取消编辑\n\t\t\tcancelEdit() {\n\t\t\t\tthis.editTitleValue = ''\n\t\t\t},\n\n\t\t\t// 删除对话\n\t\t\tdeleteConversation() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: '删除后无法恢复，确定要删除这个对话吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.$Request.post(`/app/ai/chat/conversation/${this.currentItem.id}/delete`).then(result => {\n\t\t\t\t\t\t\t\tconsole.log('删除对话:', result)\n\t\t\t\t\t\t\t\tif (result.code === 0) {\n\t\t\t\t\t\t\t\t\t// 从列表中移除\n\t\t\t\t\t\t\t\t\tconst index = this.conversationList.findIndex(item => item.id === this.currentItem.id)\n\t\t\t\t\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\t\t\t\t\tthis.conversationList.splice(index, 1)\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast('删除成功')\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast(result.msg || '删除失败')\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).catch(error => {\n\t\t\t\t\t\t\t\tconsole.error('删除对话失败:', error)\n\t\t\t\t\t\t\t\tthis.$queue.showToast('删除失败，请重试')\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 获取模型名称\n\t\t\tgetModelName(modelCode) {\n\t\t\t\tconst model = this.modelList.find(m => m.modelCode === modelCode)\n\t\t\t\treturn model ? model.modelName : modelCode\n\t\t\t},\n\n\t\t\t// 获取当前选中的模型名称\n\t\t\tgetSelectedModelName() {\n\t\t\t\tconst model = this.modelList.find(m => m.modelCode === this.selectedModel)\n\t\t\t\treturn model ? model.modelName : '选择模型'\n\t\t\t},\n\n\t\t\t// 格式化时间\n\t\t\tformatTime(timeStr) {\n\t\t\t\tif (!timeStr) return ''\n\n\t\t\t\t// 处理iOS兼容性：将 \"yyyy-MM-dd HH:mm:ss\" 格式转换为 \"yyyy/MM/dd HH:mm:ss\"\n\t\t\t\tlet processedTimeStr = timeStr\n\t\t\t\tif (typeof timeStr === 'string') {\n\t\t\t\t\tprocessedTimeStr = timeStr.replace(/-/g, '/')\n\t\t\t\t}\n\n\t\t\t\tconst time = new Date(processedTimeStr)\n\n\t\t\t\t// 检查日期是否有效\n\t\t\t\tif (isNaN(time.getTime())) {\n\t\t\t\t\tconsole.warn('无效的时间格式:', timeStr)\n\t\t\t\t\treturn ''\n\t\t\t\t}\n\n\t\t\t\tconst now = new Date()\n\t\t\t\tconst diff = now - time\n\n\t\t\t\tif (diff < 60000) { // 1分钟内\n\t\t\t\t\treturn '刚刚'\n\t\t\t\t} else if (diff < 3600000) { // 1小时内\n\t\t\t\t\treturn Math.floor(diff / 60000) + '分钟前'\n\t\t\t\t} else if (diff < 86400000) { // 24小时内\n\t\t\t\t\treturn Math.floor(diff / 3600000) + '小时前'\n\t\t\t\t} else if (diff < 604800000) { // 7天内\n\t\t\t\t\treturn Math.floor(diff / 86400000) + '天前'\n\t\t\t\t} else {\n\t\t\t\t\t// 超过7天显示具体日期\n\t\t\t\t\treturn time.toLocaleDateString('zh-CN', {\n\t\t\t\t\t\tmonth: 'short',\n\t\t\t\t\t\tday: 'numeric'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.ai-container {\n\t\t// background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tmin-height: 100vh;\n\t}\n\n\t.top-bar {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(10px);\n\t\tpadding: 30rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: 100;\n\t}\n\n\t.page-header {\n\t\tflex: 1;\n\t}\n\n\t.page-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 4rpx;\n\t}\n\n\t.page-subtitle {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tdisplay: block;\n\t}\n\n\t.top-actions {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 16rpx;\n\t}\n\n\t.action-btn {\n\t\twidth: 72rpx;\n\t\theight: 72rpx;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.model-btn {\n\t\tbackground: #f5f5f5;\n\t\tborder: 1rpx solid #e0e0e0;\n\t}\n\n\t.new-chat-btn {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.conversation-list {\n\t\tpadding: 20rpx;\n\t}\n\n\t.empty-state {\n\t\ttext-align: center;\n\t\tpadding: 120rpx 40rpx;\n\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\tmargin: 20rpx;\n\t\tborder-radius: 24rpx;\n\t\tbackdrop-filter: blur(10px);\n\t}\n\n\t.empty-icon {\n\t\tmargin-bottom: 32rpx;\n\t}\n\n\t.empty-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 12rpx;\n\t}\n\n\t.empty-subtitle {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tdisplay: block;\n\t\tmargin-bottom: 48rpx;\n\t}\n\n\t.empty-action {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\n\t.start-chat-btn {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: #fff;\n\t\tpadding: 24rpx 48rpx;\n\t\tborder-radius: 50rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.btn-text {\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t}\n\n\t.conversation-item {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(10px);\n\t\tmargin-bottom: 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 24rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.conversation-item:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);\n\t}\n\n\t.conversation-avatar {\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.avatar-icon {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 50%;\n\t\tbackground: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.conversation-content {\n\t\tflex: 1;\n\t\tmin-width: 0;\n\t}\n\n\t.conversation-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: 12rpx;\n\t}\n\n\t.conversation-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tline-height: 1.4;\n\t\tflex: 1;\n\t\tmargin-right: 16rpx;\n\t}\n\n\t.conversation-meta {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-end;\n\t\tgap: 8rpx;\n\t}\n\n\t.conversation-model {\n\t\tfont-size: 22rpx;\n\t\tcolor: #667eea;\n\t\tbackground: rgba(102, 126, 234, 0.1);\n\t\tpadding: 6rpx 12rpx;\n\t\tborder-radius: 12rpx;\n\t\tborder: 1rpx solid rgba(102, 126, 234, 0.2);\n\t\twhite-space: nowrap;\n\t}\n\n\t.update-time {\n\t\tfont-size: 22rpx;\n\t\tcolor: #999;\n\t\twhite-space: nowrap;\n\t}\n\n\t.conversation-preview {\n\t\tmargin-top: 8rpx;\n\t}\n\n\t.last-message {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.5;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-line-clamp: 2;\n\t\t-webkit-box-orient: vertical;\n\t}\n\n\t.conversation-actions {\n\t\tpadding: 16rpx;\n\t\tmargin-left: 8rpx;\n\t}\n\n\t.edit-title-content {\n\t\tpadding: 40rpx 0;\n\t}\n\n\t.title-input {\n\t\twidth: 100%;\n\t\tpadding: 24rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 30rpx;\n\t\toutline: none;\n\t\tbackground: #fafafa;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.title-input:focus {\n\t\tborder-color: #667eea;\n\t\tbackground: #fff;\n\t\tbox-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2e7a9c24&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2e7a9c24&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627820\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}