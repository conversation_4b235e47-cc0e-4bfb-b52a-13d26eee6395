package com.sqx.modules.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Query;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.dao.AiModelConfigDao;
import com.sqx.modules.ai.entity.AiModelConfig;
import com.sqx.modules.ai.service.AiModelConfigService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * AI模型配置服务实现
 */
@Service
public class AiModelConfigServiceImpl extends ServiceImpl<AiModelConfigDao, AiModelConfig> implements AiModelConfigService {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String modelName = (String) params.get("modelName");
        String modelType = (String) params.get("modelType");
        Integer isEnabled = params.get("isEnabled") != null ? Integer.valueOf(params.get("isEnabled").toString()) : null;

        IPage<AiModelConfig> page = this.page(
            new Query<AiModelConfig>().getPage(params),
            new QueryWrapper<AiModelConfig>()
                .like(StringUtils.isNotBlank(modelName), "model_name", modelName)
                .eq(StringUtils.isNotBlank(modelType), "model_type", modelType)
                .eq(isEnabled != null, "is_enabled", isEnabled)
                .orderByAsc("sort_order")
                .orderByAsc("model_id")
        );

        return new PageUtils(page);
    }

    @Override
    public Result getEnabledModels() {
        return Result.success().put("data", baseMapper.selectEnabledModels());
    }

    @Override
    public AiModelConfig getByModelCode(String modelCode) {
        return baseMapper.selectByModelCode(modelCode);
    }

    @Override
    public Result saveModelConfig(AiModelConfig modelConfig) {
        try {
            // 检查模型代码是否已存在
            AiModelConfig existing = getByModelCode(modelConfig.getModelCode());
            if (existing != null) {
                return Result.error("模型代码已存在");
            }

            modelConfig.setCreateTime(dateFormat.format(new Date()));
            modelConfig.setUpdateTime(dateFormat.format(new Date()));
            
            if (modelConfig.getIsEnabled() == null) {
                modelConfig.setIsEnabled(1);
            }
            if (modelConfig.getSortOrder() == null) {
                modelConfig.setSortOrder(0);
            }

            this.save(modelConfig);
            return Result.success("保存成功");
        } catch (Exception e) {
            return Result.error("保存失败: " + e.getMessage());
        }
    }

    @Override
    public Result updateModelConfig(AiModelConfig modelConfig) {
        try {
            // 检查模型是否存在
            AiModelConfig existing = this.getById(modelConfig.getId());
            if (existing == null) {
                return Result.error("模型配置不存在");
            }

            // 如果修改了模型代码，检查是否与其他模型冲突
            if (!existing.getModelCode().equals(modelConfig.getModelCode())) {
                AiModelConfig codeExists = getByModelCode(modelConfig.getModelCode());
                if (codeExists != null && !codeExists.getId().equals(modelConfig.getId())) {
                    return Result.error("模型代码已存在");
                }
            }

            modelConfig.setUpdateTime(dateFormat.format(new Date()));
            this.updateById(modelConfig);
            return Result.success("更新成功");
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @Override
    public Result deleteModelConfig(Long modelId) {
        try {
            AiModelConfig existing = this.getById(modelId);
            if (existing == null) {
                return Result.error("模型配置不存在");
            }

            this.removeById(modelId);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @Override
    public Result toggleModelStatus(Long modelId, Integer status) {
        try {
            AiModelConfig existing = this.getById(modelId);
            if (existing == null) {
                return Result.error("模型配置不存在");
            }

            existing.setIsEnabled(status);
            existing.setUpdateTime(dateFormat.format(new Date()));
            this.updateById(existing);
            
            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success(statusText + "成功");
        } catch (Exception e) {
            return Result.error("操作失败: " + e.getMessage());
        }
    }
}
