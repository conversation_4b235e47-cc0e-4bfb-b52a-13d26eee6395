{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/im.vue?14b8", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/im.vue?4b7b", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/im.vue?4cde", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/im.vue?b6d5", "uni-app:///pages/msg/im.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/im.vue?25b8", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/im.vue?a338"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "connected", "connecting", "msg", "type4", "listRight", "chat", "userHead", "content", "sendType", "type", "chatId", "ListItem", "ShopState", "ShopordersId", "Shopimage", "Shopmoney", "ShopTitle", "orderState", "ordersId", "userId", "orderimage", "orderNum", "teamId", "hand", "index", "page", "size", "countDown", "chatConversationId", "byUserId", "computed", "showMsg", "onUnload", "uni", "onLoad", "onShow", "onHide", "methods", "copy", "title", "showCancel", "cancelText", "confirmText", "success", "getDateDiff", "result", "connect", "that", "console", "url", "header", "method", "fail", "setTimeout", "close", "getTimeOrListItem1", "res", "d", "scrollTop", "duration", "getChatSave", "phone", "userName", "storeId", "storeHead", "storeName", "setChatSave", "messageType", "avatar", "chooseImage", "count", "sourceType", "filePath", "name", "config", "info", "viewImg", "imgsArray", "current", "urls"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+G;AAC/G;AACsD;AACL;AACa;;;AAG9D;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAiuB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8CrvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;UACAC;QACA;QACAC;QACAC;QACAC;MACA;MACAF;MACAG;MACAD;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA,wCAEA;MACA;IACA;EACA;EACAC;IACAH;EACA;EACAI;IACAC;MAAA;MACAL;QACAM;QACAhC;QACAiC;QACAC;QACAC;QACAC;UACA;YACAV;cACAlC;cACA4C;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAGA;QACAC;QACAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QACAA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACAb;UACA1B;UACAiC;QACA;QACA;MACA;MACAO;MACAd;QACAM;MACA;MACAS;MACAf;QACA;QACA;QACA;QACAgB;QACAlD;UACA;YACAG;UACA;QACA;QACAgD;UACA;QACA;QACAC;QACAR;UACAV;UACAc;UACA;QACA;QACAK;UACA;UACAJ;QACA;MACA;MACAf;QACAc;QACAA;QACAd;QACA;QACA;QACA;QACA;QACAe;MACA;MACAf;QACAc;QACAA;QACAd;QACAA;UACA1B;UACAiC;QACA;QACAQ;MACA;MACAf;QACAe;QAEAK;UACAN;QACA;MAEA;MACAd;QACAc;QACAA;QACAA;QACAC;MACA;IACA;IACAM;MACArB;IACA;IACAsB;MAAA;MACA,sHACA;QACA;QACA;UACA;UACAC;YACA;cACA;cACA;cACAC;YACA;YACA;UACA;UACA;UAAA;UACAJ;YACApB;cACAyB;cACAC;YACA;UACA;QACA;QACA1B;MACA;IACA;IACA2B;MAAA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;QACA1C;QACAb;QACAwD;QACAC;QACAC;QACAC;MACA;MACA;QACA;UACA;UACAhC;YACAM;UACA;UACA;QACA;MACA;IACA;IACA2B;MACA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAL;MACA;MACAb;MACA;QACAzC;QACA4D;QACAhD;QACAS;MAEA;MACA7B;MACA;MACAkC;QACAlC;QACA4C;UAEA;UACA;YACAyB;UACA;UACA;YACA/D;cACAC;YACA;YACAC;YACAE;YACAU;UACA;UACA6B;UACA;UACAK;YACAN;UACA;UACAC;QACA;QACAI;UACAJ;QACA;MACA;MACA;IACA;IACA;IACAqB;MAAA;MACApC;QACAqC;QACAC;QACA5B;UACA;YACA;YACAV;cAAA;cACAgB;cAAA;cACAuB;cACAC;cACA9B;gBACAK;gBACA;gBACA;gBACAf;cACA;YACA;UACA;QACA;MACA;IACA;IACAyC;MACA;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;UACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;MACA5C;QACA6C;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxaA;AAAA;AAAA;AAAA;AAAwiC,CAAgB,m8BAAG,EAAC,C;;;;;;;;;;;ACA5jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/msg/im.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/msg/im.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./im.vue?vue&type=template&id=6f86f9aa&\"\nvar renderjs\nimport script from \"./im.vue?vue&type=script&lang=js&\"\nexport * from \"./im.vue?vue&type=script&lang=js&\"\nimport style0 from \"./im.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/msg/im.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./im.vue?vue&type=template&id=6f86f9aa&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./im.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./im.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view style=\"width: 100%;padding-bottom: 140rpx;\">\n\t\t\t<view style=\"display: flex;flex-direction: column;\" v-for=\"(item,index) in ListItem\" :key='index' >\n\t\t\t\t<view style=\"margin-top: 15rpx;width: 100%;text-align: center;font-size: 26rpx;color: #999999;\">\n\t\t\t\t\t{{item.createTime}}</view>\n\n\t\t\t\t<view v-if=\"item.userId === userId\" style=\"width: 83%;margin-left: 15%;\">\n\t\t\t\t\t<view class=\"chat-listitem\" style=\"float: right;\">\n\t\t\t\t\t\t<view v-if=\"item.content && item.messageType === 1\" @longpress=\"copy(item.content)\"\n\t\t\t\t\t\t\tclass=\"chat-listitem-text\" style=\"margin-right: 20rpx;\">{{item.content}}</view>\n\t\t\t\t\t\t<image @tap=\"viewImg(item.content)\" v-if=\"item.content && item.messageType === 2\" :src=\"item.content\"\n\t\t\t\t\t\t\tstyle=\"height: 200rpx;width: 200rpx;margin-right: 20rpx;\"></image>\n\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t<image v-if=\"item.avatar\" :src=\"item.avatar\" class=\"chat-listitem-image\"></image>\n\t\t\t\t\t\t\t<image v-else src=\"../../static/logo.png\" class=\"chat-listitem-image\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"item.userId != userId\" style=\"width: 83%;margin-right: 15%;\">\n\t\t\t\t\t<view class=\"chat-listitem\" style=\"float: left;margin-left: 10rpx;\">\n\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t<image :src=\"item.avatar\" class=\"chat-listitem-image\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"item.content && item.messageType === 1\" class=\"chat-listitem-text1\"\n\t\t\t\t\t\t\tstyle=\"margin-left: 20rpx;\">{{item.content}}</view>\n\t\t\t\t\t\t<image @tap=\"viewImg(item.content)\" v-if=\"item.content && item.messageType === 2\" :src=\"item.content\"\n\t\t\t\t\t\t\tstyle=\"height: 200rpx;width: 200rpx;margin-left: 20rpx;\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 底部聊天输入框 -->\n\t\t<view class=\"input-box \">\n\t\t\t<view class=\"justify-between padding-lr align-center\" style=\"display: flex;width: 100%;background: #FFFFFF;\">\n\t\t\t\t<image src=\"../../static/images/msg/add.png\" @click=\"chooseImage(['album'])\"\n\t\t\t\t\tstyle=\"width: 70rpx;height: 70rpx;margin-right: 12rpx;\"></image>\n\t\t\t\t<input confirm-type=\"send\" @confirm='setChatSave(1)' type=\"text\" v-model=\"content\"\n\t\t\t\t\tstyle=\"width: 72%;height: 70rpx;background: #F5F5F5;margin: 0 10rpx;border-radius: 5rpx;padding-left: 10rpx;\" />\n\t\t\t\t<view class=\"save\" @tap='setChatSave(1)'>发送</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport configdata from '../../common/config.js';\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tconnected: false,\n\t\t\t\tconnecting: false,\n\t\t\t\tmsg: false,\n\t\t\t\ttype4: [],\n\t\t\t\tlistRight: {\n\t\t\t\t\tchat: {\n\t\t\t\t\t\tuserHead: \"\"\n\t\t\t\t\t},\n\t\t\t\t\tcontent: \"\",\n\t\t\t\t\tsendType: 1,\n\t\t\t\t\ttype: 1\n\t\t\t\t},\n\t\t\t\tcontent: '',\n\t\t\t\tchatId: '',\n\t\t\t\ttype: 1,\n\t\t\t\tListItem: [],\n\t\t\t\tShopState: false,\n\t\t\t\tShopordersId: '',\n\t\t\t\tShopimage: '',\n\t\t\t\tShopmoney: '',\n\t\t\t\tShopTitle: '',\n\t\t\t\torderState: false,\n\t\t\t\tordersId: '',\n\t\t\t\tuserId: '',\n\t\t\t\torderimage: '',\n\t\t\t\torderNum: '',\n\t\t\t\tteamId: '',\n\t\t\t\thand: 1,\n\t\t\t\tindex: 0,\n\t\t\t\tpage: 0,\n\t\t\t\tsize: 1000,\n\t\t\t\tcountDown: '',\n\t\t\t\tchatConversationId: '',\n\t\t\t\tbyUserId: ''\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\tshowMsg() {\n\t\t\t\tif (this.connected) {\n\t\t\t\t\tif (this.msg) {\n\t\t\t\t\t\treturn '收到消息：' + this.msg\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn '等待接收消息'\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\treturn '尚未连接'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonUnload() {\n\t\t\t// uni.closeSocket()\n\t\t\tuni.hideLoading()\n\t\t},\n\t\tonLoad(d) {\n\t\t\tthis.userId = this.$queue.getData('userId');\n\t\t\tthis.byUserId = d.byUserId\n\t\t\tthis.chatConversationId = d.chatConversationId;\n\t\t\tthis.connect();\n\n\t\t\t// if (d.teamName) {\n\t\t\t// \tuni.setNavigationBarTitle({\n\t\t\t// \t\ttitle: d.teamName\n\t\t\t// \t});\n\t\t\t// }\n\t\t},\n\t\tonShow() {\n\t\t\tif (this.connected || this.connecting) {\n\n\t\t\t} else {\n\t\t\t\tthis.connect();\n\t\t\t}\n\t\t},\n\t\tonHide() {\n\t\t\tuni.closeSocket()\n\t\t},\n\t\tmethods: {\n\t\t\tcopy(content) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '温馨提示',\n\t\t\t\t\tcontent: '确认要复制此文字吗？',\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\tdata: content,\n\t\t\t\t\t\t\t\tsuccess: r => {\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast('复制成功');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetDateDiff(data) {\n\t\t\t\t// 传进来的data必须是日期格式，不能是时间戳\n\t\t\t\t//var str = data;\n\t\t\t\t//将字符串转换成时间格式\n\t\t\t\tvar timePublish = new Date(data);\n\t\t\t\tvar timeNow = new Date();\n\t\t\t\tvar minute = 1000 * 60;\n\t\t\t\tvar hour = minute * 60;\n\t\t\t\tvar day = hour * 24;\n\t\t\t\tvar month = day * 30;\n\t\t\t\tvar result = \"2\";\n\n\t\t\t\tvar diffValue = timeNow - timePublish;\n\t\t\t\tvar diffMonth = diffValue / month;\n\t\t\t\tvar diffWeek = diffValue / (7 * day);\n\t\t\t\tvar diffDay = diffValue / day;\n\t\t\t\tvar diffHour = diffValue / hour;\n\t\t\t\tvar diffMinute = diffValue / minute;\n\n\n\t\t\t\tif (diffMonth > 3) {\n\t\t\t\t\tresult = timePublish.getFullYear() + \"-\";\n\t\t\t\t\tresult += timePublish.getMonth() + \"-\";\n\t\t\t\t\tresult += timePublish.getDate();\n\t\t\t\t} else if (diffMonth > 1) { //月\n\t\t\t\t\tresult = data.substring(0, 10);\n\t\t\t\t} else if (diffWeek > 1) { //周\n\t\t\t\t\tresult = data.substring(0, 10);\n\t\t\t\t} else if (diffDay > 1) { //天\n\t\t\t\t\tresult = data.substring(0, 10);\n\t\t\t\t} else if (diffHour > 1) { //小时\n\t\t\t\t\tresult = parseInt(diffHour) + \"小时前\";\n\t\t\t\t} else if (diffMinute > 1) { //分钟\n\t\t\t\t\tresult = parseInt(diffMinute) + \"分钟前\";\n\t\t\t\t} else {\n\t\t\t\t\tresult = \"刚刚\";\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\t\n\t\t\tconnect() {\n\t\t\t\tlet that = this;\n\t\t\t\tlet userId = that.$queue.getData('userId');\n\t\t\t\tif (that.connected || that.connecting) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tcontent: '正在连接或者已经连接，请勿重复连接',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tthat.connecting = true\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '连接中...'\n\t\t\t\t})\n\t\t\t\tconsole.log(userId,'*******************')\n\t\t\t\tuni.connectSocket({\n\t\t\t\t\t// url: 'ws://************:8881/gameTeamChat/' + userId + '_' + this.teamId,\n\t\t\t\t\t// url: 'wss://game.shengqianxiong.com.cn/wss/gameTeamChat/' + userId + '_' + this.teamId,\n\t\t\t\t\t// url: 'ws://************:8180/sqx_fast/chatSocket/' + userId,\n\t\t\t\t\turl: this.config(\"WSHOST1\") + userId,\n\t\t\t\t\tdata() {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tmsg: 'Hello'\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\theader: {\n\t\t\t\t\t\t'content-type': 'application/json'\n\t\t\t\t\t},\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tthat.getTimeOrListItem1();\n\t\t\t\t\t\t// 这里是接口调用成功的回调，不是连接成功的回调，请注意\n\t\t\t\t\t},\n\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t// 这里是接口调用失败的回调，不是连接失败的回调，请注意\n\t\t\t\t\t\tconsole.log(\"--------------\"+JSON.stringify(err))\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tuni.onSocketOpen((res) => {\n\t\t\t\t\tthat.connecting = false\n\t\t\t\t\tthat.connected = true\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t// \ttitle: '连接成功'\n\t\t\t\t\t// })\n\t\t\t\t\tconsole.log('onOpen', res);\n\t\t\t\t});\n\t\t\t\tuni.onSocketError((err) => {\n\t\t\t\t\tthat.connecting = false\n\t\t\t\t\tthat.connected = false\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tcontent: '网络较差，请稍后再试',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t})\n\t\t\t\t\tconsole.log('onError', err);\n\t\t\t\t});\n\t\t\t\tuni.onSocketMessage(function (res) {\n\t\t\t\t\tconsole.log('收到服务器内容：' + JSON.stringify(res));\n\t\t\t\t\t\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthat.getTimeOrListItem1();\n\t\t\t\t\t}, 50);\n\t\t\t\t\t\n\t\t\t\t});\n\t\t\t\tuni.onSocketClose((res) => {\n\t\t\t\t\tthat.connected = false\n\t\t\t\t\tthat.startRecive = false\n\t\t\t\t\tthat.msg = false\n\t\t\t\t\tconsole.log('onClose', res)\n\t\t\t\t});\n\t\t\t},\n\t\t\tclose() {\n\t\t\t\tuni.closeSocket()\n\t\t\t},\n\t\t\tgetTimeOrListItem1() {\n\t\t\t\tthis.$Request.get('/app/chat/selectChatContent?page=1&limit=1000&chatConversationId=' + this.chatConversationId).then(\n\t\t\t\t\tres => {\n\t\t\t\t\t\tthis.ListItem = [];\n\t\t\t\t\t\tif (res.data) {\n\t\t\t\t\t\t\tvar time = '';\n\t\t\t\t\t\t\tres.data.list.forEach(d => {\n\t\t\t\t\t\t\t\tif (!d.avatar) {\n\t\t\t\t\t\t\t\t\t// d.chat.userHead = '../../static/logo.png';\n\t\t\t\t\t\t\t\t\tlet avatar = this.$queue.getData('avatar');\n\t\t\t\t\t\t\t\t\td.avatar = avatar\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis.ListItem.push(d);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.ListItem = this.ListItem.reverse();;\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\t\tscrollTop: 99999,\n\t\t\t\t\t\t\t\t\tduration: 0\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\tgetChatSave() {\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tlet phone = this.$queue.getData('phone');\n\t\t\t\tlet userName = this.$queue.getData('userName');\n\t\t\t\tif (!phone) {\n\t\t\t\t\tphone = this.$queue.getData('userName');\n\t\t\t\t}\n\t\t\t\tlet avatar = this.$queue.getData('avatar');\n\t\t\t\tlet data = {\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\tuserHead: avatar,\n\t\t\t\t\tuserName: userName,\n\t\t\t\t\tstoreId: '0',\n\t\t\t\t\tstoreHead: '省钱兄电竞',\n\t\t\t\t\tstoreName: ''\n\t\t\t\t}\n\t\t\t\tthis.$Request.postJson('/chat/save', data).then(res => {\n\t\t\t\t\tif (res.status === 0) {\n\t\t\t\t\t\tthis.chatId = res.data.chatId;\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.getTimeOrListItem1();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tsetChatSave(type) {\n\t\t\t\t//type:1文字 2图片\n\t\t\t\tif (type === 1 && this.content == '') {\n\t\t\t\t\tthis.$queue.showToast('请输入聊天内容');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t// if (this.chatId == '' || this.chatId == undefined) {\n\t\t\t\t// \tthis.$queue.showToast('网络较差，请稍后再试');\n\t\t\t\t// \treturn;\n\t\t\t\t// }\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tlet avatar = this.$queue.getData('avatar');\n\t\t\t\tlet phone = this.$queue.getData('phone');\n\t\t\t\tlet userName = this.$queue.getData('userName');\n\t\t\t\tif (!phone) {\n\t\t\t\t\tphone = this.$queue.getData('userName');\n\t\t\t\t}\n\t\t\t\tconsole.log(this.byUserId)\n\t\t\t\tlet data = {\n\t\t\t\t\tcontent: this.content,\n\t\t\t\t\tmessageType: type,\n\t\t\t\t\tuserId: this.byUserId,\n\t\t\t\t\tchatConversationId: this.chatConversationId,\n\t\t\t\t\t\n\t\t\t\t}\n\t\t\t\tdata = JSON.stringify(data);\n\t\t\t\tlet that = this;\n\t\t\t\tuni.sendSocketMessage({\n\t\t\t\t\tdata: data,\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\n\t\t\t\t\t\tlet avatar = that.$queue.getData('avatar');\n\t\t\t\t\t\tif (!avatar) {\n\t\t\t\t\t\t\tavatar = '../../static/logo.png';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlet data = {\n\t\t\t\t\t\t\tchat: {\n\t\t\t\t\t\t\t\tuserHead: avatar\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tcontent: that.content,\n\t\t\t\t\t\t\ttype: type,\n\t\t\t\t\t\t\tuserId: userId\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log(data,'data99999999999999999')\n\t\t\t\t\t\t// that.ListItem.push(data);\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthat.getTimeOrListItem1();\n\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\tconsole.log(that.content);\n\t\t\t\t\t},\n\t\t\t\t\tfail(err) {\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tthis.content = '';\n\t\t\t},\n\t\t\t//发送图片\n\t\t\tchooseImage(sourceType) {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tfor (let i = 0; i < res.tempFilePaths.length; i++) {\n\t\t\t\t\t\t\tthis.$queue.showLoading(\"上传中...\");\n\t\t\t\t\t\t\tuni.uploadFile({ // 上传接口\n\t\t\t\t\t\t\t\turl: this.config(\"APIHOST1\") + '/alioss/upload', //真实的接口地址\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePaths[i],\n\t\t\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\t\t\tsuccess: (uploadFileRes) => {\n\t\t\t\t\t\t\t\t\tconsole.log(uploadFileRes)\n\t\t\t\t\t\t\t\t\tthis.content = JSON.parse(uploadFileRes.data).data;\n\t\t\t\t\t\t\t\t\tthis.setChatSave(2);\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tconfig: function(name) {\n\t\t\t\tvar info = null;\n\t\t\t\tif (name) {\n\t\t\t\t\tvar name2 = name.split(\".\"); //字符分割\n\t\t\t\t\tif (name2.length > 1) {\n\t\t\t\t\t\tinfo = configdata[name2[0]][name2[1]] || null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tinfo = configdata[name] || null;\n\t\t\t\t\t}\n\t\t\t\t\tif (info == null) {\n\t\t\t\t\t\tlet web_config = cache.get(\"web_config\");\n\t\t\t\t\t\tif (web_config) {\n\t\t\t\t\t\t\tif (name2.length > 1) {\n\t\t\t\t\t\t\t\tinfo = web_config[name2[0]][name2[1]] || null;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tinfo = web_config[name] || null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn info;\n\t\t\t},\n\t\t\t//查看大图\n\t\t\tviewImg(item) {\n\t\t\t\tlet imgsArray = [];\n\t\t\t\timgsArray[0] = item;\n\t\t\t\tuni.previewImage({\n\t\t\t\t\tcurrent: 0,\n\t\t\t\t\turls: imgsArray\n\t\t\t\t});\n\t\t\t},\n\t\t},\n\t};\n</script>\n\n<style>\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\t\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n\t.input-box {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\theight: 120rpx;\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tbox-sizing: content-box;\n\t\tz-index: 999;\n\t\t/* background-color: #ececec; */\n\t\t/* padding: 0 5rpx; */\n\t}\n\n\t.chat-listitem {\n\t\tdisplay: flex;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 10rpx;\n\t}\n\n\t.chat-listitem-text {\n\t\tcolor: #FFFFFF;\n\t\tbackground: #557EFD;\n\t\tmargin-top: 10rpx;\n\t\twidth: fit-content;\n\t\tpadding: 15rpx;\n\t\tfont-size: 30rpx;\n\t\theight: max-content;\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\t\tborder-radius: 10rpx;\n\t}\n.chat-listitem-text1 {\n\t\t/* color: #FFFFFF; */\n\t\tbackground: #FFFFFF;\n\t\tmargin-top: 10rpx;\n\t\twidth: fit-content;\n\t\tpadding: 15rpx;\n\t\tfont-size: 30rpx;\n\t\theight: max-content;\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\t\tborder-radius: 10rpx;\n\t}\n\t.chat-listitem-image-type4 {\n\t\t/* color: #FFFFFF; */\n\t\tbackground: #FFFFFF;\n\t\twidth: fit-content;\n\t\tfont-size: 30rpx;\n\t\theight: max-content;\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\t\tborder-top-left-radius: 20rpx;\n\t\tborder-top-right-radius: 20rpx;\n\t}\n\n\t.chat-listitem-image {\n\t\tmargin-top: 5rpx;\n\t\twidth: 75rpx;\n\t\theight: 75rpx;\n\t\tborder-radius: 5rpx;\n\t}\n\n\t.save {\n\t\twidth: 130rpx;\n\t\ttext-align: center;\n\t\tborder-radius: 70rpx;\n\t\theight: 70rpx;\n\t\tcolor: #FFF;\n\t\tbackground: #557EFD;\n\t\tmargin: 5rpx 10rpx 0;\n\t\tline-height: 70rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./im.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./im.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447626798\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}