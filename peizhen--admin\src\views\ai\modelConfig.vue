<template>
  <div>
    <!-- 搜索条件 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="模型名称">
          <el-input v-model="searchForm.modelName" placeholder="请输入模型名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="模型类型">
          <el-select v-model="searchForm.modelType" placeholder="请选择模型类型" clearable>
            <el-option label="DeepSeek" value="deepseek"></el-option>
            <el-option label="Kimi" value="kimi"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.isEnabled" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增模型</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="dataListLoading" :data="dataList" border style="width: 100%;">
      <el-table-column prop="id" header-align="center" align="center" label="ID" width="80"></el-table-column>
      <el-table-column prop="modelName" header-align="center" align="center" label="模型名称"></el-table-column>
      <el-table-column prop="modelCode" header-align="center" align="center" label="模型代码"></el-table-column>
      <el-table-column prop="modelType" header-align="center" align="center" label="模型类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.modelType === 'deepseek'" type="primary">DeepSeek</el-tag>
          <el-tag v-else-if="scope.row.modelType === 'kimi'" type="success">Kimi</el-tag>
          <el-tag v-else type="info">{{ scope.row.modelType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="maxTokens" header-align="center" align="center" label="最大Token" width="100"></el-table-column>
      <el-table-column prop="temperature" header-align="center" align="center" label="温度" width="80"></el-table-column>
      <el-table-column prop="isEnabled" header-align="center" align="center" label="状态" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isEnabled"
            :active-value="1"
            :inactive-value="0"
            @change="toggleStatus(scope.row)"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="sortOrder" header-align="center" align="center" label="排序" width="80"></el-table-column>
      <el-table-column prop="createTime" header-align="center" align="center" label="创建时间" width="160"></el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './modelConfig-add-or-update'

export default {
  data() {
    return {
      searchForm: {
        modelName: '',
        modelType: '',
        isEnabled: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/ai/model/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          modelName: this.searchForm.modelName,
          modelType: this.searchForm.modelType,
          isEnabled: this.searchForm.isEnabled
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.data.list
          this.totalPage = data.data.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        modelName: '',
        modelType: '',
        isEnabled: ''
      }
      this.getDataList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定要删除该模型配置吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/ai/model/delete/${id}`),
          method: 'post'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 切换状态
    toggleStatus(row) {
      this.$http({
        url: this.$http.adornUrl(`/ai/model/toggle/${row.id}`),
        method: 'post',
        params: this.$http.adornParams({
          status: row.isEnabled
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500
          })
        } else {
          this.$message.error(data.msg)
          // 恢复原状态
          row.isEnabled = row.isEnabled === 1 ? 0 : 1
        }
      }).catch(() => {
        // 恢复原状态
        row.isEnabled = row.isEnabled === 1 ? 0 : 1
      })
    }
  }
}
</script>

<style scoped>
.search-form {
  background: #f5f5f5;
  padding: 20px;
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 20px;
}
</style>
