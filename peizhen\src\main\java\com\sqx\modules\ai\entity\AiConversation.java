package com.sqx.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * AI对话会话实体类
 */
@Data
@ApiModel("AI对话会话")
@TableName("ai_conversation")
public class AiConversation implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("对话会话ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("对话标题")
    private String title;

    @ApiModelProperty("使用的模型ID")
    private Long modelId;

    @ApiModelProperty("模型代码")
    private String modelCode;

    @ApiModelProperty("消息数量")
    private Integer messageCount;

    @ApiModelProperty("总token消耗")
    private Integer totalTokens;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("更新时间")
    private String updateTime;

    // 关联字段，不存储到数据库
    @TableField(exist = false)
    @ApiModelProperty("模型配置信息")
    private AiModelConfig modelConfig;

    @TableField(exist = false)
    @ApiModelProperty("最新消息")
    private String lastMessage;

    @TableField(exist = false)
    @ApiModelProperty("消息列表")
    private List<AiMessage> messages;

    @TableField(exist = false)
    @ApiModelProperty("用户名称")
    private String userName;

    @TableField(exist = false)
    @ApiModelProperty("用户手机号")
    private String phone;
}
