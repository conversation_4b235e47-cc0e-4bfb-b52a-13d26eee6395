package com.sqx.modules.hospital.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.entity.LetterCity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Mapper
public interface HospitalDao extends BaseMapper<Hospital> {

    List<String> getCityList(@Param("letter") String letter);

    List<LetterCity> getLetter();

    List<Hospital> getHospitalOrderCount(String riderPhone, String hospitalName, String city, String ordersNo);
}
