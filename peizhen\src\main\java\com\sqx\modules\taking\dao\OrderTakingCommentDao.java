package com.sqx.modules.taking.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.taking.entity.CommentFabulous;
import com.sqx.modules.taking.entity.TakingCommnt;
import com.sqx.modules.taking.response.TakingDetailsResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface OrderTakingCommentDao extends BaseMapper<TakingCommnt> {
    IPage<Map<String,Object>> selectOrderTakingComment(Page<TakingDetailsResponse> page, @Param("takingId") Long id);

    IPage<Map<String,Object>> selectOrderTakingCommentByUserId(Page<TakingDetailsResponse> page, @Param("userId") Long userId);


    int selectCommentNumber(@Param("id") Long id);

    CommentFabulous selectGoodsNum(@Param("commentId") Long commentId, @Param("userId") Long userId);

    int deleteGoodsNum(@Param("id") Long id);

    int insertGoodsNum(@Param("commentId") Long commentId, @Param("userId") Long userId);

    Double selectAvgScore(@Param("orderTakingId") Long orderTakingId);

    Integer selectCommntCountByUserId(Long userId,String startTime,String endTime);

}
