<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.app.dao.CityAgencyDao">

    <select id="selectCityAgencyList" resultType="com.sqx.modules.app.entity.CityAgency">
        select c.* from city_agency c
        where 1=1
        <if test="userName!=null and userName!=''">
            and c.user_name=#{userName}
        </if>
        <if test="phone!=null and phone!=''">
            and c.phone=#{phone}
        </if>
        <if test="classify!=null">
            and c.classify=#{classify}
        </if>
        order by c.create_time desc
    </select>


</mapper>