<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.hospitalEmploy.dao.HospitalEmployDao">

    <select id="getEmployList" resultType="com.sqx.modules.hospitalEmploy.entity.HospitalEmploy">
        SELECT *
        FROM `hospital_employ`
        WHERE is_enable = 1
        <if test="authentication==1">
            and modular_id in (1,3)
        </if>
        <if test="authentication==2">
            and modular_id =2
        </if>
        order by sort asc
    </select>
</mapper>