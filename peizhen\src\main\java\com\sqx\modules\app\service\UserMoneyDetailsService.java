package com.sqx.modules.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserMoneyDetails;

import java.util.HashMap;

public interface UserMoneyDetailsService extends IService<UserMoneyDetails> {
    Result queryUserMoneyDetails(Integer page, Integer limit, Long userId,Integer classify,Integer type);
    Double monthIncome(String date,Long userId);

    IPage<HashMap<String,Object>> getRankingList(Integer page, Integer limit,Integer classify);

    IPage<HashMap<String,Object>> getAgentProfitList(Long userId, Integer page, Integer limit);

    IPage<UserMoneyDetails> getBondDetails(Long userId,Integer page, Integer limit,UserMoneyDetails userMoneyDetails);
}
