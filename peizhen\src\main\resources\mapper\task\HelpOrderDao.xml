<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.task.dao.HelpOrderDao">

	<select id="selectNewHelpOrderList" resultType="com.sqx.modules.task.entity.HelpOrder">
		select * from (
		select o.*,u.user_name as userName,u.avatar,g.game_name as serviceName,
		(st_distance (point (o.longitude, o.latitude),point(#{longitude},#{latitude}) ) *111195) as distance
		       from help_order o
		left join tb_user u on u.user_id=o.user_id
		left join game g on g.id=o.game_id
		where o.status=2
		<if test="gameId!=null">
			and o.game_id=#{gameId}
		</if>
		) a
		<if test="sort==null">
			order by create_time desc
		</if>
		<if test="sort==1">
			order by commission desc
		</if>
		<if test="sort==2">
			order by commission asc
		</if>
		<if test="sort==3">
			order by distance desc
		</if>
		<if test="sort==4">
			order by distance asc
		</if>
	</select>

	<select id="selectHelpOrderByClassifyList" resultType="com.sqx.modules.task.entity.HelpOrder">
		select o.*,u.user_name as userName,u.avatar,g.game_name as serviceName from help_order o
		left join tb_user u on u.user_id=o.user_id
		left join game g on g.id=o.game_id
		where o.status=2 and classify_id=#{classifyId}
		<if test="gameId!=null">
			and o.game_id=#{gameId}
		</if>
		order by create_time desc
	</select>

	<select id="selectHelpOrderByContentList" resultType="com.sqx.modules.task.entity.HelpOrder">
		select o.*,u.user_name as userName,u.avatar,g.game_name as serviceName from help_order o
		left join tb_user u on u.user_id=o.user_id
		left join game g on g.id=o.game_id
		where o.status=2 and content like CONCAT('%',#{content},'%')
		<if test="gameId!=null">
			and o.game_id=#{gameId}
		</if>
		order by create_time desc
	</select>

	<select id="selectStatusHelpOrder" resultType="com.sqx.modules.task.entity.HelpOrder">
		select o.*,u.user_name as userName,u.avatar,g.game_name as serviceName from help_order o
		left join tb_user u on u.user_id=o.user_id
		left join game g on g.id=o.game_id
		where 1=1
		<if test="content!=null and content!=''">
			and o.content like CONCAT('%',#{content},'%')
		</if>
		<if test="phone!=null and phone!=''">
			and o.phone like CONCAT('%',#{phone},'%')
		</if>
		<if test="status!=null and status!=0">
			and o.status = #{status}
		</if>
		<if test="gameId!=null">
			and o.game_id=#{gameId}
		</if>
		order by create_time desc
	</select>


	<select id="countHelpOrderByCreateTime" resultType="Integer">
		select count(*) from help_order where 1=1
		<if test="flag!=null and flag==1">
			and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(create_time,'%Y')=date_format(#{time},'%Y')
		</if>
	</select>


	<select id="sumPrice" resultType="Double">
		select sum(commission) from help_order where status in (1,2)
		<if test="flag!=null and flag==1">
			and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
		</if>
		<if test="flag!=null and flag==2">
			and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
		</if>
		<if test="flag!=null and flag==3">
			and date_format(create_time,'%Y')=date_format(#{time},'%Y')
		</if>
	</select>


</mapper>