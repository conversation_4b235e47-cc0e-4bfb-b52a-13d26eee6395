{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/gird/guanzhu.vue?826f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/gird/guanzhu.vue?a851", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/gird/guanzhu.vue?4a0c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/gird/guanzhu.vue?0ad2", "uni-app:///my/gird/guanzhu.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/gird/guanzhu.vue?be35", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/gird/guanzhu.vue?006c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "dataList", "type", "page", "limit", "onLoad", "console", "uni", "title", "methods", "getFansList", "getFollowList", "insert", "followUserId", "that", "icon", "setTimeout", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAsuB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC6B1vB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACAC;MACAC;IACA;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACAP;QACAC;MACA;MACA;QACAG;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACAD;QACA;QACAC;MACA;IACA;IACA;IACAI;MAAA;MACA;QACAR;QACAC;MACA;MACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACAE;QACA;QACAC;QACAA;MACA;IACA;IACAK;MACA;MACA;QACAC;MACA;MACAC;QACAR;QACA;UACAC;YACAC;YACAO;UACA;UACAC;YACA;cACAF;YACA;cACAA;YACA;UACA;QACA;MACA;IACA;EACA;EACAG;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;IACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA6iC,CAAgB,w8BAAG,EAAC,C;;;;;;;;;;;ACAjkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/gird/guanzhu.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/gird/guanzhu.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./guanzhu.vue?vue&type=template&id=376d0376&\"\nvar renderjs\nimport script from \"./guanzhu.vue?vue&type=script&lang=js&\"\nexport * from \"./guanzhu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./guanzhu.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/gird/guanzhu.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./guanzhu.vue?vue&type=template&id=376d0376&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-avatar/u-avatar\" */ \"@/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dataList.length\n  var g1 = _vm.dataList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./guanzhu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./guanzhu.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<view v-if=\"dataList.length != 0\" class=\"bg u-flex u-p-l-30 u-p-t-30 u-p-b-10 u-p-r-30\" v-for=\"(item,index) in dataList\" :key='index'>\n\t\t\t<view class=\"u-m-r-10\">\n\t\t\t\t<u-avatar :src=\"item.avatar?item.avatar: '../../static/logo.png'\" size=\"100\"></u-avatar>\n\t\t\t</view>\n\t\t\t<view class=\"u-flex-1 text-white margin-left-xs\">\n\t\t\t\t<view class=\"u-font-16  text-bold\">{{item.userName}}</view>\n\t\t\t\t<view class=\"u-font-14 margin-top-sm u-tips-color\" @click=\"goNav('/pages/me/vip/index')\">{{item.updateTime?item.updateTime:''}}</view>\n\t\t\t</view>\n\t\t\t<view>\n\t\t\t\t<view v-if=\"item.status == 1\" @click=\"insert(item)\" class=\"round\"\n\t\t\t\t\tstyle=\"color: white;background: #557EFD;padding: 10upx 24upx;width: 150upx;text-align: center;font-size: 22upx;\">\n\t\t\t\t\t互相关注</view>\n\t\t\t\t<view v-if=\"item.status == 2 && type == 1\" @click=\"insert(item)\" class=\"round\"\n\t\t\t\t\tstyle=\"color: white;background: #557EFD;padding: 10upx 24upx;width: 150upx;text-align: center;font-size: 22upx;\">\n\t\t\t\t\t回关</view>\n\t\t\t\t<view v-if=\"item.status == 2 && type == 2\" @click=\"insert(item)\" class=\"round\"\n\t\t\t\t\tstyle=\"color: white;background: #557EFD;padding: 10upx 24upx;width: 150upx;text-align: center;font-size: 22upx;\">\n\t\t\t\t\t已关注</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<empty v-if=\"dataList.length == 0\" ></empty>\n\t</view>\n</template>\n\n<script>\n\timport empty from '../../components/empty.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdataList: [],\n\t\t\t\ttype: 1,\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10\n\t\t\t}\n\t\t},\n\t\tonLoad(e) {\n\t\t\tconsole.log(e)\n\t\t\tthis.$queue.showLoading(\"加载中...\");\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: e.name\n\t\t\t})\n\t\t\tthis.type = e.type\n\t\t\tif (this.type == 1) {\n\t\t\t\tthis.getFansList()\n\t\t\t} else {\n\t\t\t\tthis.getFollowList()\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取粉丝数量\n\t\t\tgetFansList() {\n\t\t\t\tlet data = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit\n\t\t\t\t}\n\t\t\t\tthis.$Request.get(\"/app/userFollow/selectFans\", data).then(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif(this.page == 1) {\n\t\t\t\t\t\t\tthis.dataList = res.data.list\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.dataList = [...this.dataList, ...res.data.list]\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log(res.msg)\n\t\t\t\t\t}\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 获取关注数量\n\t\t\tgetFollowList() {\n\t\t\t\tlet data = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit\n\t\t\t\t}\n\t\t\t\tthis.$Request.get(\"/app/userFollow/selectMyFollow\", data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif(this.page == 1) {\n\t\t\t\t\t\t\tthis.dataList = res.data.list\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.dataList = [...this.dataList, ...res.data.list]\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log(res.msg)\n\t\t\t\t\t}\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t});\n\t\t\t},\n\t\t\tinsert(e) {\n\t\t\t\tlet that = this\n\t\t\t\tlet data = {\n\t\t\t\t\tfollowUserId: e.userId\n\t\t\t\t}\n\t\t\t\tthat.$Request.get(\"/app/userFollow/insert\", data).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t\t\tif (that.type == 1) {\n\t\t\t\t\t\t\t\tthat.getFansList()\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthat.getFollowList()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, 500)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tthis.page = this.page + 1;\n\t\t\tif (e.type == 1) {\n\t\t\t\tthis.getFansList()\n\t\t\t} else {\n\t\t\t\tthis.getFollowList()\n\t\t\t}\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\t// this.dataList = []\n\t\t\tif (this.type == 1) {\n\t\t\t\tthis.getFansList()\n\t\t\t} else {\n\t\t\t\tthis.getFollowList()\n\t\t\t}\n\t\t},\n\t}\n</script>\n\n<style>\npage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.bg {\n\t\tbackground-color: #FFFFFF;\n\t}\n\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./guanzhu.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./guanzhu.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447623241\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}