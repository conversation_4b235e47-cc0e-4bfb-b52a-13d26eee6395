package com.sqx.modules.taking.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.taking.service.OrderTakingCommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/takingComment")
@Api(value = "评论", tags = {"评论"})
public class OrderTakingCommentController {

    @Autowired
    private OrderTakingCommentService orderTakingCommentService;

    /**
     * 查看我的评论
     */
     @GetMapping("/queryMyComment")
     public Result queryMyComment(Long userId){
         return orderTakingCommentService.queryMyComment(userId);
     }

    @GetMapping("/selectOrderTakingComment")
    @ApiOperation("查看评论")
    public Result selectOrderTakingComment(Integer page, Integer limit, Long id) {
        return orderTakingCommentService.selectOrderTakingComment(page, limit, id);
    }

    @PostMapping("/deleteOrderTakingComment/{id}")
    @ApiOperation("删除评论")
    public Result deleteOrderTakingComment(@PathVariable Long id){
         orderTakingCommentService.removeById(id);
         return Result.success();
    }

}
