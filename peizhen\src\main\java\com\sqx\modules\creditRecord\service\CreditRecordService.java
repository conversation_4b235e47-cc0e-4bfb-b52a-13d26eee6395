package com.sqx.modules.creditRecord.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.creditRecord.entity.CreditRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
public interface CreditRecordService extends IService<CreditRecord> {

    IPage<CreditRecord> getCreditRecordList(Integer page, Integer limit, CreditRecord creditRecord);

    void updateUserCreditRecord(UserEntity userEntity, Integer type, Integer score, String remark);
}
