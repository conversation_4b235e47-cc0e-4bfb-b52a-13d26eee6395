package com.sqx.modules.taking.controller;

import cn.hutool.db.sql.Order;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.orders.dao.OrdersDao;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.taking.dao.OrderTakingDao;
import com.sqx.modules.taking.entity.OrderTaking;
import com.sqx.modules.taking.response.OrderTakingResponse;
import com.sqx.modules.taking.service.OrderTakingService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/orderTaking")
public class OrderTakingController {

    @Autowired
    private OrderTakingService orderTakingService;
    @Autowired
    private OrdersDao ordersDao;
    @Autowired
    private OrderTakingDao orderTakingDao;
    @Autowired
    private OrdersService ordersService;

    /**
     * 发布接单
     */
    @ApiOperation("发布接单")
    @GetMapping("/insertOrderTaking")
    public Result insertOrderTaking(@Param("游戏id类型") String gameId, @Param("我的段位") String myLevel, @Param("接单段位") String orderLevel, @Param("接单时间") String orderTakingTime, @Param("接单大区") String orderTakingArea, @Param("价格") BigDecimal money, BigDecimal memberMoney, @Param("语音介绍") String voiceIntroduce, @Param("主页图片") String homepageImg, @Param("发布人") Long userId, @Param("城市") String city, @Param("精度") String longitude, @Param("维度") String latitude, Integer sec, Integer classify, String unit, String detailsImg, Integer authentication, Integer minNum, String region, String detailadd, String serviceName, String carType, String safeguard) {
        return orderTakingService.insertOrderTaking(gameId, myLevel, orderLevel, orderTakingTime, orderTakingArea, money, memberMoney, voiceIntroduce, homepageImg, userId, city, longitude, latitude, sec, classify, unit, detailsImg, authentication, minNum, region, detailadd, serviceName, carType, safeguard);
    }

    /**
     * 删除订单
     */
    @ApiOperation("删除接单")
    @GetMapping("/deleteOrderTaking")
    public Result deleteOrderTaking(Long id) {
        return orderTakingService.deleteOrderTaking(id);
    }

    /**
     * 查询所有接单
     */
    @ApiOperation("查询所有接单")
    @GetMapping("/queryAllOrderTaking")
    public Result queryAllOrderTaking(@ApiParam("页") Integer page, @ApiParam("条") Integer limit, String city, String gameId, Long status, String userName, Long userId, Integer classify) {
        return orderTakingService.queryAllOrderTaking(page, limit, city, gameId, status, userName, userId, classify);
    }

    /**
     * 审核接单
     */
    @ApiOperation("审核接单")
    @GetMapping("/auditorOrderTaking")
    public Result auditorOrderTaking(Long id, Integer status, String content) {
        return orderTakingService.auditorOrderTaking(id, status, content);
    }

    /**
     * 修改
     */
    @ApiOperation("修改接单")
    @GetMapping("/updateTakingOrder")
    public Result updateTakingOrder(Long id, String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money, String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude, Integer sec, Integer classify, String unit, Integer authentication, String detailsImg, Integer minNum, String region, String detailadd, String serviceName, String carType, String safeguard) {
        return orderTakingService.updateTakingOrder(id, gameId, myLevel, orderLevel, orderTakingTime, orderTakingArea, money, voiceIntroduce, homepageImg, userId, city, longitude, latitude, sec, classify, unit, authentication, detailsImg, minNum, region, detailadd, serviceName, carType, safeguard);
    }

    @ApiOperation("修改接单")
    @GetMapping("/updateTakingOrders")
    public Result updateTakingOrders(Long id, String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money, BigDecimal memberMoney, BigDecimal oldMoney, String voiceIntroduce, String homepageImg, Long userId, String city, String longitude, String latitude, Integer sec, Integer classify, String unit, Integer authentication, String detailsImg, Integer minNum, String region, String detailadd, String serviceName, String carType, String safeguard) {
        return orderTakingService.updateTakingOrders(id, gameId, myLevel, orderLevel, orderTakingTime, orderTakingArea, money, memberMoney, oldMoney, voiceIntroduce, homepageImg, userId, city, longitude, latitude, sec, classify, unit, authentication, detailsImg, minNum, region, detailadd, serviceName, carType, safeguard);
    }

    @PostMapping("/updateOrderTakingStatus/{id}")
    @ApiOperation("修改状态")
    public Result updateOrderTakingStatus(@PathVariable Long id) {
        OrderTaking byId = orderTakingService.getById(id);
        if (byId.getStatus() == 0) {
            byId.setStatus(2);
        } else {
            byId.setStatus(0);
        }
        orderTakingService.updateById(byId);
        return Result.success();
    }

    @PostMapping("/updateOrderTakingRecommend/{id}")
    @ApiOperation("修改状态")
    public Result updateOrderTakingRecommend(@PathVariable Long id) {
        OrderTaking byId = orderTakingService.getById(id);
        if ("0".equals(byId.getIsRecommend())) {
            byId.setIsRecommend("1");
        } else {
            byId.setIsRecommend("0");
        }
        orderTakingService.updateById(byId);
        return Result.success();
    }

    @ApiOperation("任务分析")
    @GetMapping("/taskAnalysis")
    public Result taskAnalysis(String time, Integer flag) {
        Map<String, Object> map = new HashMap<>();
        //订单总金额
        BigDecimal allMoney = ordersDao.getAllMoney(time, flag, null);
        //订单总数量
//        Integer orderCount = ordersDao.orderCount(time, flag);
        //订单收入金额
        BigDecimal income = ordersDao.getAllMoney(time, flag, 2);
        //订单退款金额
        BigDecimal refund = ordersDao.getAllMoney(time, flag, 3);
        //订单待结算金额
        BigDecimal settlement = ordersDao.settlement(time, flag);
        //待支付金额
        BigDecimal waitPayment = ordersDao.getAllMoney(time, flag, 0);


        map.put("allMoney", allMoney);
//        map.put("orderCount", orderCount);
        map.put("income", income);
        map.put("waitPayment", waitPayment);
        map.put("refund", refund);
        map.put("settlement", settlement);

        return Result.success().put("data", map);
    }

    @ApiOperation("接单收入分析")
    @GetMapping("/incomeAnalysis")
    public Result incomeAnalysis(String time, Integer flag) {
        return Result.success().put("data", ordersService.incomeAnalysisOrders(time, flag));
    }


}
