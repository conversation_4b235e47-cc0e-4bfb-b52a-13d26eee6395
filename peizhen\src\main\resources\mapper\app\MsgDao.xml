<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.app.dao.MsgDao">


    <select id="findByPhone" resultType="com.sqx.modules.app.entity.Msg">
        select * from msg where phone=#{phone}
    </select>

    <select id="findByPhoneAndCode" resultType="com.sqx.modules.app.entity.Msg">
      select * from msg where phone=#{phone} and code=#{msg}
    </select>

</mapper>