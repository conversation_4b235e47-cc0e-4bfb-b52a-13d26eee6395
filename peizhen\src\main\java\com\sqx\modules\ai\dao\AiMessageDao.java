package com.sqx.modules.ai.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sqx.modules.ai.entity.AiMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI对话消息DAO
 */
@Mapper
public interface AiMessageDao extends BaseMapper<AiMessage> {

    /**
     * 查询对话的消息列表
     */
    List<AiMessage> selectConversationMessages(@Param("conversationId") Long conversationId, 
                                              @Param("limit") Integer limit);

    /**
     * 分页查询对话的消息列表
     */
    IPage<AiMessage> selectConversationMessagesPage(IPage<AiMessage> page, 
                                                   @Param("conversationId") Long conversationId);

    /**
     * 统计对话的消息数量和token消耗
     */
    AiMessage selectConversationStats(@Param("conversationId") Long conversationId);
}
