package com.sqx.modules.riderLocation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserCertification;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserCertificationService;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.common.entity.CommonInfo;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.service.HospitalService;
import com.sqx.modules.riderLocation.dao.RiderLocationDao;
import com.sqx.modules.riderLocation.entity.RiderLocation;
import com.sqx.modules.riderLocation.service.RiderLocationService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;

@Service
public class RiderLocationServiceImpl extends ServiceImpl<RiderLocationDao, RiderLocation> implements RiderLocationService {

    @Autowired
    private RiderLocationDao riderLocationDao;
    @Autowired
    private CommonInfoService commonInfoService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserCertificationService certificationService;
    @Autowired
    private HospitalService hospitalService;

    @Transactional
    @Override
    public Result riderLocation(Long userId, Double lng, Double lat, String province, String city, String district, String addressDetails) {
        UserEntity userEntity = userService.getById(userId);
        UserCertification userCertification = certificationService.getOne(new QueryWrapper<UserCertification>().eq("user_id", userId));
        if (userEntity != null && userCertification != null && userCertification.getStatus() == 1) {
            RiderLocation riderLocation = baseMapper.selectOne(new QueryWrapper<RiderLocation>().eq("user_id", userId));
            if (riderLocation == null) {
                riderLocation = new RiderLocation();
                riderLocation.setUserId(userId);
                riderLocation.setLng(lng);
                riderLocation.setLat(lat);
                riderLocation.setProvince(province);
                riderLocation.setCity(city);
                riderLocation.setDistrict(district);
                riderLocation.setAddressDetails(addressDetails);
                riderLocation.setUpdateTime(LocalDateTime.now());
                baseMapper.insert(riderLocation);
            } else {
                riderLocation.setLng(lng);
                riderLocation.setLat(lat);
                riderLocation.setProvince(province);
                riderLocation.setCity(city);
                riderLocation.setDistrict(district);
                riderLocation.setAddressDetails(addressDetails);
                riderLocation.setUpdateTime(LocalDateTime.now());
                baseMapper.updateById(riderLocation);
            }

        }
        return Result.success();
    }

    @Override
    public Result selectRiderLocation(Long riderUserId, Double lng, Double lat) {
        HashMap hashMap = new HashMap();
        //查看骑手即时位置
        RiderLocation riderLocation = baseMapper.selectOne(new QueryWrapper<RiderLocation>().eq("user_id", riderUserId));
        //计算骑手到用户的距离
        Double aDouble = riderLocationDao.calculateDistance(lng, lat, riderLocation.getLng(), riderLocation.getLat());
        //计算预计送达时间
        CommonInfo one = commonInfoService.findOne(301);
        Double a = aDouble / Double.valueOf(one.getValue());
        Integer integer = Integer.valueOf(a.intValue());

        Calendar c = Calendar.getInstance();
        c.add(Calendar.MINUTE, +integer);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String mDateTime = formatter.format(c.getTime());

        hashMap.put("riderLocation", riderLocation);
        hashMap.put("aDouble", aDouble);
        hashMap.put("mDateTime", mDateTime);
        return Result.success().put("data", hashMap);
    }


    @Override
    public Long getDistanceNearRide(String longitude, String latitude, Double cashDeposit, Double distance) {
        return baseMapper.getDistanceNearRide(longitude, latitude, cashDeposit, distance);
    }

    @Override
    public IPage<RiderLocation> selectAllRiderLocation(Integer page, Integer limit, RiderLocation riderLocation) {
        Page<RiderLocation> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return baseMapper.selectAllRiderLocation(pages, riderLocation);


    }

    @Override
    public HashMap<String, Object> getMapOrderData(String riderPhone, String hospitalName, String city, String ordersNo) {
        HashMap<String, Object> hashMap = new HashMap<>();
        List<Hospital> hospitalList = hospitalService.getHospitalOrderCount(riderPhone, hospitalName, city, ordersNo);
        List<RiderLocation> riderLocations = baseMapper.selectRiderList(riderPhone, hospitalName, city, ordersNo);
        for (RiderLocation riderLocation : riderLocations) {
            riderLocation.setUserEntity(userService.getById(riderLocation.getUserId()));
        }

        hashMap.put("hospitalList", hospitalList);
        hashMap.put("riderLocations", riderLocations);
        return hashMap;


    }


}
