package com.sqx.modules.task.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.task.entity.HelpTake;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/7
 */
@Mapper
public interface HelpTakeDao extends BaseMapper<HelpTake> {

    int insertHelpTake(HelpTake helpTake);

    IPage<Map<String,Object>> selectRunHelpOrder(Page<Map<String,Object>> page,@Param("status") Integer status,@Param("userId") Long userId);

    IPage<Map<String,Object>> selectRunHelpOrderList(Page<Map<String,Object>> page, @Param("status") Integer status,@Param("phone")  String phone,String startTime,String endTime);

    List<Map<String,Object>> helpTakeListExcel( @Param("status") Integer status, @Param("phone")  String phone,String startTime,String endTime);

    Integer countHelpTakeByCreateTime(@Param("time")String time,@Param("flag")Integer flag);

    Double sumMoneyBySend(@Param("time")String time,@Param("flag")Integer flag);

    Integer selectHelpTakeCount(Long userId,String startTime,String endTime);

    BigDecimal selectHelpTakeRefundMoneyByUserId(Long userId,String startTime,String endTime);

    Integer selectHelpTakeRefundCountByUserId(Long userId,String startTime,String endTime,Integer status);

}
