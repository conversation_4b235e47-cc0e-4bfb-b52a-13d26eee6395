/**
 * AI对话相关API封装
 */
import HttpRequest from './httpRequest'

const aiRequest = {
	/**
	 * 获取可用的AI模型列表
	 */
	getModels() {
		return HttpRequest.get('/app/ai/chat/models')
	},

	/**
	 * 创建新的对话会话
	 * @param {Object} data - {modelCode: string, title?: string}
	 */
	createConversation(data) {
		return HttpRequest.post('/app/ai/chat/conversation/create', data)
	},

	/**
	 * 获取用户的对话列表
	 * @param {Object} params - {page: number, limit: number}
	 */
	getConversationList(params) {
		return HttpRequest.get('/app/ai/chat/conversation/list', params)
	},

	/**
	 * 获取对话详情
	 * @param {string} conversationId 
	 */
	getConversationDetail(conversationId) {
		return HttpRequest.get(`/app/ai/chat/conversation/${conversationId}`)
	},

	/**
	 * 更新对话标题
	 * @param {string} conversationId 
	 * @param {Object} data - {title: string}
	 */
	updateConversationTitle(conversationId, data) {
		return HttpRequest.post(`/app/ai/chat/conversation/${conversationId}/title`, data)
	},

	/**
	 * 删除对话
	 * @param {string} conversationId 
	 */
	deleteConversation(conversationId) {
		return HttpRequest.post(`/app/ai/chat/conversation/${conversationId}/delete`)
	},

	/**
	 * 切换对话模型
	 * @param {string} conversationId 
	 * @param {Object} data - {modelCode: string}
	 */
	switchModel(conversationId, data) {
		return HttpRequest.post(`/app/ai/chat/conversation/${conversationId}/switch-model`, data)
	},

	/**
	 * 发送消息
	 * @param {string} conversationId 
	 * @param {Object} data - {message: string, modelCode?: string}
	 */
	sendMessage(conversationId, data) {
		return HttpRequest.post(`/app/ai/chat/conversation/${conversationId}/send`, data)
	},

	/**
	 * 获取对话历史
	 * @param {string} conversationId 
	 * @param {Object} params - {page: number, limit: number}
	 */
	getConversationHistory(conversationId, params) {
		return HttpRequest.get(`/app/ai/chat/conversation/${conversationId}/history`, params)
	}
}

export default aiRequest
