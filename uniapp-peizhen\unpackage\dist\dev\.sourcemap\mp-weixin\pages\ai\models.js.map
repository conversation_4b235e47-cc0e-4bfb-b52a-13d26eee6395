{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/models.vue?fc50", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/models.vue?f0ec", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/models.vue?7ec9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/models.vue?f4b2", "uni-app:///pages/ai/models.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/models.vue?8a47", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/ai/models.vue?1e71"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "modelList", "selected<PERSON><PERSON>l", "loading", "onLoad", "methods", "loadModels", "console", "selectModel", "confirmSelection", "uni", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iMAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAquB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwDzvB;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACA;UACA;UACA;UACA;YACA;cAAA;YAAA;YACA;cACA;YACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;MACA;;MAEA;MACAC;;MAEA;MACA;QAAA;MAAA;MACA;;MAEA;MACAC;QACAD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAg5C,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACAp6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ai/models.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/ai/models.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./models.vue?vue&type=template&id=1516d4f0&scoped=true&\"\nvar renderjs\nimport script from \"./models.vue?vue&type=script&lang=js&\"\nexport * from \"./models.vue?vue&type=script&lang=js&\"\nimport style0 from \"./models.vue?vue&type=style&index=0&id=1516d4f0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1516d4f0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ai/models.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./models.vue?vue&type=template&id=1516d4f0&scoped=true&\"", "var components\ntry {\n  components = {\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-tag/u-tag\" */ \"@/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-button/u-button\" */ \"@/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./models.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./models.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"models-container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">选择AI模型</text>\n\t\t\t<text class=\"subtitle\">不同模型具有不同的特点和能力</text>\n\t\t</view>\n\n\t\t<view class=\"models-list\">\n\t\t\t<view \n\t\t\t\tv-for=\"model in modelList\" \n\t\t\t\t:key=\"model.id\" \n\t\t\t\tclass=\"model-item\" \n\t\t\t\t:class=\"{ active: selectedModel === model.modelCode }\"\n\t\t\t\t@click=\"selectModel(model)\"\n\t\t\t>\n\t\t\t\t<view class=\"model-info\">\n\t\t\t\t\t<view class=\"model-header\">\n\t\t\t\t\t\t<text class=\"model-name\">{{ model.modelName }}</text>\n\t\t\t\t\t\t<u-tag \n\t\t\t\t\t\t\t:text=\"model.status === 1 ? '可用' : '不可用'\" \n\t\t\t\t\t\t\t:type=\"model.status === 1 ? 'success' : 'error'\"\n\t\t\t\t\t\t\tsize=\"mini\"\n\t\t\t\t\t\t></u-tag>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"model-description\">{{ model.description || '暂无描述' }}</text>\n\t\t\t\t\t<view class=\"model-params\">\n\t\t\t\t\t\t<text class=\"param-item\">温度: {{ model.temperature || 0.7 }}</text>\n\t\t\t\t\t\t<text class=\"param-item\">最大Token: {{ model.maxTokens || 4000 }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"model-select\">\n\t\t\t\t\t<u-icon \n\t\t\t\t\t\t:name=\"selectedModel === model.modelCode ? 'checkmark-circle-fill' : 'circle'\" \n\t\t\t\t\t\t:color=\"selectedModel === model.modelCode ? '#0175FE' : '#ccc'\"\n\t\t\t\t\t\tsize=\"24\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"bottom-actions\">\n\t\t\t<u-button type=\"primary\" @click=\"confirmSelection\" :disabled=\"!selectedModel\">\n\t\t\t\t确认选择\n\t\t\t</u-button>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view v-if=\"loading\" class=\"loading-overlay\">\n\t\t\t<view class=\"loading-content\">\n\t\t\t\t<text>加载中...</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmodelList: [],\n\t\t\t\tselectedModel: '',\n\t\t\t\tloading: false\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadModels()\n\t\t\t// 获取当前选中的模型\n\t\t\tthis.selectedModel = uni.getStorageSync('selectedAiModel') || ''\n\t\t},\n\t\tmethods: {\n\t\t\t// 加载模型列表\n\t\t\tloadModels() {\n\t\t\t\tthis.loading = true\n\t\t\t\tthis.$Request.get('/app/ai/chat/models').then(res => {\n\t\t\t\t\tconsole.log('模型列表:', res)\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tthis.modelList = res.data || []\n\t\t\t\t\t\t// 如果没有选中的模型，默认选择第一个可用的\n\t\t\t\t\t\tif (!this.selectedModel && this.modelList.length > 0) {\n\t\t\t\t\t\t\tconst firstAvailable = this.modelList.find(m => m.status === 1)\n\t\t\t\t\t\t\tif (firstAvailable) {\n\t\t\t\t\t\t\t\tthis.selectedModel = firstAvailable.modelCode\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$queue.showToast(res.msg || '加载模型失败')\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('加载模型失败:', error)\n\t\t\t\t\tthis.$queue.showToast('加载模型失败，请重试')\n\t\t\t\t}).finally(() => {\n\t\t\t\t\tthis.loading = false\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 选择模型\n\t\t\tselectModel(model) {\n\t\t\t\tif (model.status !== 1) {\n\t\t\t\t\tthis.$queue.showToast('该模型暂不可用')\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.selectedModel = model.modelCode\n\t\t\t},\n\n\t\t\t// 确认选择\n\t\t\tconfirmSelection() {\n\t\t\t\tif (!this.selectedModel) {\n\t\t\t\t\tthis.$queue.showToast('请选择一个模型')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 保存选择的模型\n\t\t\t\tuni.setStorageSync('selectedAiModel', this.selectedModel)\n\t\t\t\t\n\t\t\t\t// 显示选择成功提示\n\t\t\t\tconst selectedModelInfo = this.modelList.find(m => m.modelCode === this.selectedModel)\n\t\t\t\tthis.$queue.showToast(`已选择 ${selectedModelInfo?.modelName || this.selectedModel}`)\n\t\t\t\t\n\t\t\t\t// 返回上一页\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack()\n\t\t\t\t}, 1000)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.models-container {\n\t\tbackground: #f5f5f5;\n\t\tmin-height: 100vh;\n\t\tpadding-bottom: 120rpx;\n\t}\n\n\t.header {\n\t\tbackground: #fff;\n\t\tpadding: 40rpx 30rpx;\n\t\ttext-align: center;\n\t\tborder-bottom: 1rpx solid #eee;\n\t}\n\n\t.title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.subtitle {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\n\t.models-list {\n\t\tpadding: 20rpx;\n\t}\n\n\t.model-item {\n\t\tbackground: #fff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.3s ease;\n\n\t\t&.active {\n\t\t\tborder: 2rpx solid #0175FE;\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba(1, 117, 254, 0.2);\n\t\t}\n\t}\n\n\t.model-info {\n\t\tflex: 1;\n\t}\n\n\t.model-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.model-name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t}\n\n\t.model-description {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tline-height: 1.5;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: block;\n\t}\n\n\t.model-params {\n\t\tdisplay: flex;\n\t\tgap: 30rpx;\n\t}\n\n\t.param-item {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tbackground: #f8f9fa;\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\n\t.model-select {\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.bottom-actions {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: #fff;\n\t\tpadding: 30rpx;\n\t\tborder-top: 1rpx solid #eee;\n\t\tz-index: 100;\n\t}\n\n\t.loading-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(255, 255, 255, 0.8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t}\n\n\t.loading-content {\n\t\tbackground: #fff;\n\t\tpadding: 40rpx;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./models.vue?vue&type=style&index=0&id=1516d4f0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./models.vue?vue&type=style&index=0&id=1516d4f0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627879\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}