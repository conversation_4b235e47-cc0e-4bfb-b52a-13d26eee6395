{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/message.vue?3436", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/message.vue?01b9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/message.vue?8bdf", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/message.vue?146e", "uni-app:///pages/msg/message.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/message.vue?48df", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/message.vue?b6c0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniLoadMore", "empty", "data", "tabFromIndex", "tabCurrentIndex", "fromInfo", "list", "page", "limit", "scrollTop", "tabList", "state", "text", "totalElements", "onPageScroll", "onReachBottom", "onPullDownRefresh", "onLoad", "methods", "goDet", "console", "uni", "url", "tabClicks", "loadData", "res"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAsuB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0C1vB;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACAC;MACA;QACAC;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;UACAjB;UACAC;UACAG;QACA;QACA;UACA;YACA;cACA;YACA;cACAc;gBACA;cACA;YACA;UACA;UACAJ;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpIA;AAAA;AAAA;AAAA;AAAy3C,CAAgB,8tCAAG,EAAC,C;;;;;;;;;;;ACA74C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/msg/message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/msg/message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./message.vue?vue&type=template&id=28a66f10&\"\nvar renderjs\nimport script from \"./message.vue?vue&type=script&lang=js&\"\nexport * from \"./message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./message.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/msg/message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=28a66f10&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<view class=\"navbar\">\n\t\t\t<view v-for=\"(item, index) in tabList\" :key=\"index\" class=\"flex-sub\"\n\t\t\t\t @click=\"tabClicks(item.state)\">\n\t\t\t\t<view class=\"nav-item\" :class=\"{ current: tabFromIndex === item.state }\">{{ item.text }}</view>\n\t\t\t\t<view class=\"text-center\" style=\"margin-top: -30upx;\">\n\t\t\t\t\t<image v-if=\"tabFromIndex === item.state \" src=\"../../static/images/index/wx.png\" style=\"width: 55upx;height: 16upx;margin-top: -5upx;\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- #ifdef H5 -->\n\t\t<view style=\"margin-top: 80upx;\">\n\t\t<!-- #endif -->\n\t\t<!-- #ifndef H5 -->\n\t\t<view style=\"margin-top: 90upx;\">\n\t\t<!-- #endif -->\n\t\t\t<view v-for=\"(item, index) in list\" :key=\"index\" class=\"item\" @click=\"goDet(item.content)\">\n\t\t\t\t<view class=\"flex justify-between\"\n\t\t\t\t\tstyle=\"font-size: 30upx;width: 100%;overflow: hidden;text-overflow: ellipsis;white-space:nowrap\">\n\t\t\t\t\t<view>{{ item.title }}</view>\n\t\t\t\t\t<view v-if=\"item.isSee == 0\"\n\t\t\t\t\t\tstyle=\"height: 32rpx;width: 32rpx;border-radius: 100rpx;background-color: red;color: #FFF;text-align: center;\">\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"color: #999999;font-size: 28upx;margin-top: 10upx;\">{{ item.content }}</view>\n\t\t\t\n\t\t\t\t<view style=\"margin-top: 10upx;color: #999999;font-size: 28upx;text-align: right;\">{{ item.createAt }}\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"list.length-1!=index\" style=\"width: 100%;height: 1rpx;background: #f5f5f5;margin-top: 30upx;\"></view>\n\t\t\t\t\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- <view v-if=\"list.length === 0\" style=\"background: #1c1b20;text-align: center;padding-top: 140upx;color: #FFFFFF;\">暂无消息</view> -->\n\t\t<empty v-if=\"list.length === 0\" content=\"暂无消息\" show=\"false\"></empty>\n\t</view>\n</template>\n\n<script>\n\timport uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';\n\timport empty from '@/components/empty';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tuniLoadMore,\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttabFromIndex: 5,\n\t\t\t\ttabCurrentIndex: 0,\n\t\t\t\tfromInfo: 5,\n\t\t\t\tlist: [],\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10,\n\t\t\t\tscrollTop: false,\n\t\t\t\ttabList: [{\n\t\t\t\t\t\tstate: 5,\n\t\t\t\t\t\ttext: '用户消息',\n\t\t\t\t\t\ttotalElements: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tstate: 4,\n\t\t\t\t\t\ttext: '订单消息',\n\t\t\t\t\t\ttotalElements: 0\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t};\n\t\t},\n\t\tonPageScroll: function(e) {\n\t\t\tthis.scrollTop = e.scrollTop > 200;\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tthis.page = this.page + 1;\n\t\t\tthis.loadData();\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\tthis.loadData();\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.$queue.showLoading(\"加载中...\")\n\t\t\tthis.loadData();\n\t\t},\n\t\tmethods: {\n\t\t\tgoDet(e) {\n\t\t\t\tconsole.log(e.indexOf('下单'))\n\t\t\t\tif (e.indexOf('下单') != -1) {\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl:'/pages/order/index'\n\t\t\t\t\t})\n\t\t\t\t}else if (e.indexOf('任务') != -1) {\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl:'/pages/order/index'\n\t\t\t\t\t})\n\t\t\t\t} \n\t\t\t},\n\t\t\t//顶部渠道点击\n\t\t\ttabClicks(index) {\n\t\t\t\tthis.list = [];\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.tabFromIndex = index;\n\t\t\t\tthis.$queue.showLoading(\"加载中...\")\n\t\t\t\tthis.loadData();\n\t\t\t},\n\t\t\t//获取消息列表\n\t\t\tloadData() {\n\t\t\t\tlet that = this;\n\t\t\t\tlet number = 10;\n\t\t\t\tlet token = this.$queue.getData('token');\n\t\t\t\tif (token) {\n\t\t\t\t\tlet data = {\n\t\t\t\t\t\tpage: this.page,\n\t\t\t\t\t\tlimit: this.limit,\n\t\t\t\t\t\tstate: this.tabFromIndex\n\t\t\t\t\t}\n\t\t\t\t\tthis.$Request.getT('/app/message/selectMessageByUserId', data).then(res => {\n\t\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\t\tif (this.page == 1) {\n\t\t\t\t\t\t\t\tthis.list = res.data.list\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tres.data.list.forEach(d => {\n\t\t\t\t\t\t\t\t\tthis.list.push(d);\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\">\n\tpage,\n\tpage {\n\t\tbackground: #ffffff;\n\t}\n\n\t.content {\n\t\tbackground: #ffffff;\n\t\theight: 100%;\n\t}\n\n\t.swiper-box {\n\t\theight: calc(100% - 40px);\n\t}\n\n\t.list-scroll-content {\n\t\theight: 100%;\n\t}\n\n\t.navbar {\n\t\tdisplay: flex;\n\t\theight: 40px;\n\t\tpadding: 0 5px;\n\t\t// background: #1E1F31;\n\t\t// box-shadow: 0 1px 5px rgba(0, 0, 0, 0.06);\n\t\tcolor: #000000;\n\t\tposition: relative;\n\t\tbackground: #FFFFFF;\n\t\tposition: fixed;\n\t\t/* #ifdef H5 */\n\t\ttop:80upx;\n\t\t/* #endif */\n\t\t/* #ifndef H5 */\n\t\ttop: 0;\n\t\t/* #endif */\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 10;\n\n\t\t.nav-item {\n\t\t\t\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\theight: 100%;\n\t\t\tfont-size: 15px;\n\t\t\t// color: #FFFFFF;\n\t\t\tposition: relative;\n\n\t\t\t&.current {\n\t\t\t\tcolor: #557EFD;\n\n\t\t\t\t// &:after {\n\t\t\t\t// \tcontent: '';\n\t\t\t\t// \tposition: absolute;\n\t\t\t\t// \tleft: 50%;\n\t\t\t\t// \tbottom: 0;\n\t\t\t\t// \ttransform: translateX(-50%);\n\t\t\t\t// \twidth: 44px;\n\t\t\t\t// \theight: 0;\n\t\t\t\t// \tborder-bottom: 2px solid #557EFD;\n\t\t\t\t// }\n\t\t\t}\n\t\t}\n\t}\n\n\t.uni-swiper-item {\n\t\theight: auto;\n\t}\n\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.item {\n\t\tbackground: #FFFFFF;\n\t\tpadding: 16rpx;\n\t\tmargin: 16rpx;\n\t\tfont-size: 28rpx;\n\t\t// box-shadow: 7px 9px 34px rgba(0, 0, 0, 0.1);\n\t\tborder-radius: 16upx;\n\t}\n\n\n\t/* load-more */\n\t.uni-load-more {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\theight: 40px;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.uni-load-more__text {\n\t\tfont-size: 14px;\n\t\t// color: #999;\n\t}\n\n\t.uni-load-more__img {\n\t\theight: 24px;\n\t\twidth: 24px;\n\t\tmargin-right: 10px;\n\t}\n\n\t.uni-load-more__img>view {\n\t\tposition: absolute;\n\t}\n\n\t.uni-load-more__img>view view {\n\t\twidth: 6px;\n\t\theight: 2px;\n\t\tborder-top-left-radius: 1px;\n\t\tborder-bottom-left-radius: 1px;\n\t\tbackground: #999;\n\t\tposition: absolute;\n\t\topacity: 0.2;\n\t\ttransform-origin: 50%;\n\t\tanimation: load 1.56s ease infinite;\n\t}\n\n\t.uni-load-more__img>view view:nth-child(1) {\n\t\ttransform: rotate(90deg);\n\t\ttop: 2px;\n\t\tleft: 9px;\n\t}\n\n\t.uni-load-more__img>view view:nth-child(2) {\n\t\ttransform: rotate(180deg);\n\t\ttop: 11px;\n\t\tright: 0;\n\t}\n\n\t.uni-load-more__img>view view:nth-child(3) {\n\t\ttransform: rotate(270deg);\n\t\tbottom: 2px;\n\t\tleft: 9px;\n\t}\n\n\t.uni-load-more__img>view view:nth-child(4) {\n\t\ttop: 11px;\n\t\tleft: 0;\n\t}\n\n\t.load1,\n\t.load2,\n\t.load3 {\n\t\theight: 24px;\n\t\twidth: 24px;\n\t}\n\n\t.load2 {\n\t\ttransform: rotate(30deg);\n\t}\n\n\t.load3 {\n\t\ttransform: rotate(60deg);\n\t}\n\n\t.load1 view:nth-child(1) {\n\t\tanimation-delay: 0s;\n\t}\n\n\t.load2 view:nth-child(1) {\n\t\tanimation-delay: 0.13s;\n\t}\n\n\t.load3 view:nth-child(1) {\n\t\tanimation-delay: 0.26s;\n\t}\n\n\t.load1 view:nth-child(2) {\n\t\tanimation-delay: 0.39s;\n\t}\n\n\t.load2 view:nth-child(2) {\n\t\tanimation-delay: 0.52s;\n\t}\n\n\t.load3 view:nth-child(2) {\n\t\tanimation-delay: 0.65s;\n\t}\n\n\t.load1 view:nth-child(3) {\n\t\tanimation-delay: 0.78s;\n\t}\n\n\t.load2 view:nth-child(3) {\n\t\tanimation-delay: 0.91s;\n\t}\n\n\t.load3 view:nth-child(3) {\n\t\tanimation-delay: 1.04s;\n\t}\n\n\t.load1 view:nth-child(4) {\n\t\tanimation-delay: 1.17s;\n\t}\n\n\t.load2 view:nth-child(4) {\n\t\tanimation-delay: 1.3s;\n\t}\n\n\t.load3 view:nth-child(4) {\n\t\tanimation-delay: 1.43s;\n\t}\n\n\t@-webkit-keyframes load {\n\t\t0% {\n\t\t\topacity: 1;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 0.2;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627831\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}