{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/index.vue?29b0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/index.vue?475f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/index.vue?ff4d", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/index.vue?2e1f", "uni-app:///pages/my/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/index.vue?3f7c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/my/index.vue?3de9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "avatar", "is<PERSON>ogin", "userName", "browse", "fans", "follow", "visitor", "userId", "isVip", "invitationCode", "czSel", "geRen", "Qe", "XCXIsSelect", "renzheng", "isPromotion", "isAgent", "yhq", "yue", "ts", "showModal", "arr", "appID", "dailishang", "youhuiImage", "onLoad", "onShow", "classify", "methods", "goAiChat", "uni", "url", "goYouhuiImage", "goRider", "appId", "path", "extraData", "success", "console", "title", "content", "openMsg", "withSubscriptions", "confirmText", "cancelText", "tmplIds", "fail", "that", "<PERSON><PERSON><PERSON><PERSON>", "goNavsTab", "getApp", "applyhome", "tuiguang", "selectKeFu", "phoneNumber", "extInfo", "corpId", "goNav", "goNavs", "goLogin", "getAmount", "getUserInfo", "getRenZheng", "getRenZhengs", "getIsVip"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7CA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgOxvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;MAAA;MACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACAC;IACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAA;QACA;UACA;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EAEA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MACAF;QACAC;MACA;IACA;IACA;IACAE;MACA;MACA;QAEA;QACAH;UACAI;UACAC;UACAC;YACA;UACA;UACAC;YACA;YACAC;UACA;QACA;MA+DA;QACAR;UACAS;UACAC;UACAH;YACA;cACAC;cACAR;gBACAC;cACA;YACA;cACAO;YACA;UACA;QACA;MACA;IACA;IACA;IACAG;MACAH;MACA;MACAR;QACAY;QAAA;QACAL;UACAC;UACA;UACA;YACAR;YACAA;cAAA;cACAO;gBACAC;cACA;YACA;UACA;YAAA;YACAA;YACAR;YACAA;cACAS;cACAC;cACAG;cACAC;cACAP;gBACA;kBACA3C;oBACAmD;oBACAR;sBACAC,gCACA;sBACA;sBACA;wBACAA;wBACA;sBACA;oBACA;;oBACAQ;sBACAR;oBACA;kBACA;kBACA;kBACAA;kBACAS;gBACA;kBACAT;kBACA;kBACAS;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACAX;MACA;QACAY;QACApB;UACAC;QACA;MAEA;QACAD;UACAS;UACAC;UACAH;YACA;cACAC;cACAR;gBACAC;cACA;YACA;cACAO;YACA;UACA;QACA;MACA;IACA;IACAa;MACA;QACA;UACArB;YACAC;UACA;QACA;UACAD;YACAC;UACA;QACA;MACA;QACAD;UACAS;UACAC;UACAH;YACA;cACAC;cACAR;gBACAC;cACA;YACA;cACAO;YACA;UACA;QACA;MACA;IAEA;IACAc;MACA;QACA;UACAtB;YACAC;UACA;QACA;UACAD;YACAC;UACA;QACA;MACA;QACAD;UACAS;UACAC;UACAH;YACA;cACAC;cACAR;gBACAC;cACA;YACA;cACAO;YACA;UACA;QACA;MACA;IAEA;IACA;IACAe;MACA;MACA;QAAA;QACAvB;UACAwB;QACA;MACA;QAAA;QACA;QAEA5D;UACA6D;YACAxB;UACA;UACAyB;UACAnB;QACA;MAUA;QAAA;QACAP;UACAC;QACA;MACA;IACA;IACA0B;MACAnB;MACA;QACAR;UACAC;QACA;QACA;MACA;MAEA;QACAD;UACAC;QACA;MAEA;QACAD;UACAS;UACAC;UACAH;YACA;cACAC;cACAR;gBACAC;cACA;YACA;cACAO;YACA;UACA;QACA;MACA;IACA;IACAoB;MACApB;MACA;QACAR;UACAC;QACA;MAEA;QACAD;UACAS;UACAC;UACAH;YACA;cACAC;cACAR;gBACAC;cACA;YACA;cACAO;YACA;UACA;QACA;MACA;IACA;IACAqB;MACA7B;QACAC;MACA;IACA;IACA6B;MAAA;MACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;UACA;UACA;UAEA;YAAA;YACA;UACA;YACA;UACA;UACA;YAAA;YACA;UACA;YACA;UACA;UAEA/B;UACAA;UACAA;UACAA;UACAA;UACAA;QAEA;MACA;IACA;IACA;IACAgC;MAAA;MACA;MACA;QACAxB;QACA;UACA;YACA;YACAR;UACA;YACA;YACAA;UACA;YACA;YACAA;UACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACA;IACAiC;MAAA;MACA;MACA;QACAzB;QACA;UACA;YAAA;YACA;YACAR;UACA;YAAA;YACA;YACAA;UACA;YAAA;YACA;YACAA;UACA;YAAA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAkC;MAAA;MACA;QACA;UACA;UACAlC;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC1vBA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4dcceeb0&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4dcceeb0&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.XCXIsSelect != \"否\" && _vm.czSel != \"否\" && _vm.yue > 10000\n      ? (_vm.yue / 10000).toFixed(2)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"header flex justify-center\">\n\t\t\t<view class=\"header-box\">\n\t\t\t\t<view class=\"header-box-info flex align-center justify-between\">\n\t\t\t\t\t<view class=\"flex align-center\">\n\t\t\t\t\t\t<image :src=\"avatar\" mode=\"\" @click=\"goNav('/pages/my/userinfo')\"></image>\n\t\t\t\t\t\t<view class=\"header-box-info-b\" v-if=\"!isLogin\">\n\t\t\t\t\t\t\t<view class=\"header-box-info-name\">\n\t\t\t\t\t\t\t\t{{userName}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"header-box-info-id\">\n\t\t\t\t\t\t\t\tID：{{invitationCode}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"header-box-info-b\" v-else @click=\"goLogin('/pages/public/login')\">\n\t\t\t\t\t\t\t<view class=\"header-box-info-name\">\n\t\t\t\t\t\t\t\t登录\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"!isLogin&&XCXIsSelect != '否'\" class=\"\" @click=\"goNav('/pages/msg/message')\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/msg.png\" style=\"width: 46upx;height: 52rpx;\" mode=\"\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 1111111 -->\n\t\t\t\t<view class=\"money flex justify-around align-center\" v-if=\"XCXIsSelect != '否'\">\n\t\t\t\t\t<view class=\"flex justify-between align-center\" style=\"width: 606rpx;height: 100%;\">\n\t\t\t\t\t\t<view class=\"money-item\" @click=\"goNav('/my/wallet/index')\" v-if=\"czSel != '否'\">\n\t\t\t\t\t\t\t<view class=\"money-item-price\">\n\t\t\t\t\t\t\t\t{{yue>10000?(yue/10000).toFixed(2)+'w':yue}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"money-item-title\">\n\t\t\t\t\t\t\t\t余额\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"money-item\" @click=\"goNav('/my/youhuijuan/index')\">\n\t\t\t\t\t\t\t<view class=\"money-item-price\">\n\t\t\t\t\t\t\t\t{{yhq}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"money-item-title\">\n\t\t\t\t\t\t\t\t优惠券\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"money-item\" @click=\"goNav('/my/order/tousuList')\">\n\t\t\t\t\t\t\t<view class=\"money-item-price\">\n\t\t\t\t\t\t\t\t{{ts}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"money-item-title\">\n\t\t\t\t\t\t\t\t投诉记录\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 订单 -->\n\t\t<view class=\"order flex justify-center\" v-if=\"XCXIsSelect != '否'\" @click=\"goNavs('/pages/order/index')\">\n\t\t\t<view class=\"order-box\">\n\t\t\t\t<view class=\"order-box-title flex justify-between align-center\">\n\t\t\t\t\t<text style=\"margin-left: 30rpx;color: #1A1A1A;font-size: 32rpx;font-weight: 800;\">我的订单</text>\n\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#1A1A1A\" size=\"32\" style=\"margin-right: 30rpx;\"></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-box-box flex justify-around align-center\">\n\t\t\t\t\t<view class=\"order-box-box-item flex justify-center flex-wrap\"\n\t\t\t\t\t\************=\"goNavsTab('/pages/order/index',1)\">\n\t\t\t\t\t\t<image src=\"../../static/images/dfk.png\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\n\t\t\t\t\t\t<view class=\"\" style=\"margin-top: 10rpx;color: #1A1A1A;font-size: 24rpx;\">\n\t\t\t\t\t\t\t待付款\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"order-box-box-item flex justify-center flex-wrap\"\n\t\t\t\t\t\************=\"goNavsTab('/pages/order/index',4)\">\n\t\t\t\t\t\t<image src=\"../../static/images/jxz.png\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\n\t\t\t\t\t\t<view class=\"\" style=\"margin-top: 10rpx;color: #1A1A1A;font-size: 24rpx;\">\n\t\t\t\t\t\t\t进行中\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"order-box-box-item flex justify-center flex-wrap\"\n\t\t\t\t\t\************=\"goNavsTab('/pages/order/index',5)\">\n\t\t\t\t\t\t<image src=\"../../static/images/ywc.png\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\n\t\t\t\t\t\t<view class=\"\" style=\"margin-top: 10rpx;color: #1A1A1A;font-size: 24rpx;\">\n\t\t\t\t\t\t\t已完成\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"order-box-box-item flex justify-center flex-wrap\"\n\t\t\t\t\t\************=\"goNavsTab('/pages/order/index',6)\">\n\t\t\t\t\t\t<image src=\"../../static/images/yqx.png\" style=\"width: 50rpx;height: 50rpx;\" mode=\"\"></image>\n\t\t\t\t\t\t<view class=\"\" style=\"margin-top: 10rpx;color: #1A1A1A;font-size: 24rpx;\">\n\t\t\t\t\t\t\t已取消\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"header-box-tg flex justify-center align-center\" v-if=\"dailishang != '否' && XCXIsSelect != '否'\">\n\t\t\t<view class=\"header-box-tg1 flex justify-center align-center\">\n\t\t\t\t<view class=\"header-box-tg-c flex justify-between align-center\">\n\t\t\t\t\t<view class=\"header-box-tg-c-l flex align-center justify-between\" @click=\"applyhome()\">\n\t\t\t\t\t\t<view class=\"\">\n\t\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 28rpx;font-weight: bold;\">\n\t\t\t\t\t\t\t\t{{isAgent==0?'代理商申请':'代理商中心'}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"\" style=\"color: #999999;font-size: 24rpx;font-weight: 400;margin-top: 10rpx;\">\n\t\t\t\t\t\t\t\t{{isAgent==0?'申请成为代理商':'进入代理商中心'}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image src=\"../../static/images/dl.png\"\n\t\t\t\t\t\t\tstyle=\"width: 68rpx;height: 68rpx;border-radius: 16rpx;margin-right: 30rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"header-box-tg-c-r flex align-center justify-between\" @click=\"tuiguang()\">\n\t\t\t\t\t\t<view class=\"\" style=\"margin-left: 30rpx;\">\n\t\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 28rpx;font-weight: bold;\">\n\t\t\t\t\t\t\t\t{{isPromotion ==0?'推广赚钱':'推广中心'}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"\" style=\"color: #999999;font-size: 24rpx;font-weight: 400;margin-top: 10rpx;\">\n\t\t\t\t\t\t\t\t{{isPromotion ==0?'申请推广赚钱':'进入推广中心'}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image src=\"../../static/images/tg.png\" style=\"width: 68rpx;height: 68rpx;border-radius: 16rpx;\"\n\t\t\t\t\t\t\tmode=\"\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</view>\n\t\t\n\t\t<!-- 推荐工具 -->\n\t\t<view class=\"utils flex justify-center\">\n\t\t\t<view class=\"utils-box\">\n\t\t\t\t<view class=\"flex align-center flex-wrap\"\n\t\t\t\t\tstyle=\"margin-left: 25rpx;margin-top: 20rpx;margin-bottom: 20rpx;\">\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goNav('/my/other/car?type=4')\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/che.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\"></image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t就诊人管理\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goNav('/pages/my/invitationUser')\" v-if=\"XCXIsSelect != '否'\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/yaoqing.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t分享好友\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goAiChat()\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/ai-chat.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\tAI助手\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\" @click=\"goRider\"\n\t\t\t\t\t\tv-if=\"XCXIsSelect != '否'\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/pzruzhu.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t陪诊师入驻\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goNav('/my/other/work')\" v-if=\"XCXIsSelect != '否'\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/zhaopin.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t职务招聘\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goNav('/my/address/address')\" v-if=\"XCXIsSelect != '否'\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/address.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t地址管理\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goNav('/my/evaluation/evaluation')\" v-if=\"XCXIsSelect != '否'\">\n\t\t\t\t\t\t<image src=\"../../static/images/pingjia.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t我的评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\" @click=\"selectKeFu()\"\n\t\t\t\t\t\tv-if=\"XCXIsSelect != '否'\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/kefu.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t客服中心\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goNav('/my/feedbackIndex/feedbackIndex','noLogin')\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/help.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\">\n\t\t\t\t\t\t</image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t帮助中心\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"utils-box-item flex justify-center align-center flex-wrap\"\n\t\t\t\t\t\t@click=\"goNav('/my/setting/index')\">\n\t\t\t\t\t\t<image src=\"../../static/images/my/set.png\" style=\"width: 56rpx;height: 56rpx;\" mode=\"\"></image>\n\t\t\t\t\t\t<view class=\"\" style=\"color: #333333;font-size: 26rpx;margin-top: 15rpx;\">\n\t\t\t\t\t\t\t设置中心\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"margin\" @click=\"goYouhuiImage(youhuiImage.url)\" v-if=\"youhuiImage\">\n\t\t\t<image :src=\"youhuiImage.imageUrl\" style=\"width: 100%;height: 220rpx;\"></image>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tavatar: '../../static/logo.png',\n\t\t\t\tisLogin: true,\n\t\t\t\tuserName: '匿名',\n\t\t\t\tbrowse: 0, //浏览数\n\t\t\t\tfans: 0, //粉丝数\n\t\t\t\tfollow: 0, //关注数\n\t\t\t\tvisitor: 0, //访客数\n\t\t\t\tuserId: '',\n\t\t\t\tisVip: false,\n\t\t\t\tinvitationCode: '', //邀请码\n\t\t\t\tczSel: '否',\n\t\t\t\tgeRen: 0,\n\t\t\t\tQe: 0,\n\t\t\t\tXCXIsSelect: '否',\n\t\t\t\trenzheng: false,\n\t\t\t\tisPromotion: '', //推广员  1是  0或者null 不是\n\t\t\t\tisAgent: '', //代理商  1是  0或者null 不是\n\t\t\t\tyhq: 0, //优惠券数量\n\t\t\t\tyue: 0, //余额\n\t\t\t\tts: 0, //投诉数量\n\t\t\t\tshowModal: true,\n\t\t\t\tarr: [],\n\t\t\t\tappID: '',\n\t\t\t\tdailishang: '否',\n\t\t\t\tyouhuiImage: ''\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.invitationCode = this.$queue.getData('invitationCode');\n\t\t\tthis.dailishang = this.$queue.getData('dailishang')\n\t\t\tthis.XCXIsSelect = this.$queue.getData(\"XCXIsSelect\");\n\t\t\tthis.$Request.getT('/app/common/type/239').then(res => { //陪诊师傅端微信小程序APPID 248\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tthis.appID = res.data.value;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tonShow() {\n\t\t\tthis.$Request.get(\"/app/banner/selectBannerList\", {\n\t\t\t\tclassify: 10\n\t\t\t}).then(res => {\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tthis.youhuiImage = res.data[0]\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.czSel = this.$queue.getData('czSel');\n\t\t\tthis.Qe = uni.getStorageSync(\"Qe\")\n\t\t\tthis.geRen = uni.getStorageSync(\"geRen\")\n\t\t\tif (this.Qe == 2 || this.geRen == 2) {\n\t\t\t\tthis.renzheng = false\n\t\t\t} else {\n\t\t\t\tthis.renzheng = true\n\t\t\t}\n\t\t\tthis.userId = uni.getStorageSync('userId')\n\t\t\tif (this.userId) {\n\t\t\t\tthis.isLogin = false\n\t\t\t\tthis.getUserInfo()\n\t\t\t\tthis.getRenZheng()\n\t\t\t\tthis.getRenZhengs()\n\t\t\t\tthis.getAmount()\n\t\t\t\tthis.getIsVip()\n\t\t\t\tthis.getuhNum()\n\t\t\t\tthis.$Request.getT('/app/common/type/308').then(res => { //下单成功\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif (res.data && res.data.value) {\n\t\t\t\t\t\t\tthis.arr.push(res.data.value)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t//订阅\n\t\t\t\tif (this.showModal) {\n\t\t\t\t\tthis.openMsg()\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t} else {\n\t\t\t\tthis.isLogin = true\n\t\t\t\tthis.userName = '匿名'\n\t\t\t\tthis.browse = 0\n\t\t\t\tthis.fans = 0\n\t\t\t\tthis.follow = 0\n\t\t\t\tthis.visitor = 0\n\t\t\t\tthis.yhq = 0\n\t\t\t\tthis.yue = 0\n\t\t\t\tthis.ts = 0\n\t\t\t\tthis.avatar = '../../static/logo.png'\n\t\t\t}\n\n\t\t},\n\t\tmethods: {\n\t\t\t// 跳转AI对话\n\t\t\tgoAiChat() {\n\t\t\t\tif (!this.isLogin) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/ai/index'\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tthis.goLogin('/pages/public/login')\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoYouhuiImage(url){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl:url\n\t\t\t\t})\n\t\t\t},\n\t\t\t//跳转小程序\n\t\t\tgoRider() {\n\t\t\t\tlet token = this.$queue.getData(\"token\");\n\t\t\t\tif (token) {\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tlet that = this\n\t\t\t\t\tuni.navigateToMiniProgram({\n\t\t\t\t\t\tappId: that.appID,\n\t\t\t\t\t\tpath: '/pages/index/index',\n\t\t\t\t\t\textraData: {\n\t\t\t\t\t\t\t'data1': 'test'\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\t// 打开成功\n\t\t\t\t\t\t\tconsole.log(\"打开成功\")\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\tthis.$Request.get('/app/common/type/321').then(res => {\n\t\t\t\t\t\tif (res.code == 0 && res.data.value) {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\tcontent: '请跳转至' + res.data.value + '师傅端进行入驻',\n\t\t\t\t\t\t\t\tconfirmText: '一键复制',\n\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\tcomplete(ret) {\n\t\t\t\t\t\t\t\t\tif (ret.confirm) {\n\t\t\t\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\t\t\t\tdata: res.data.value,\n\t\t\t\t\t\t\t\t\t\t\tsuccess(re) {\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '复制成功',\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请联系客服',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef APP\n\t\t\t\t\tthis.$Request.get('/app/common/type/320').then(res => {\n\t\t\t\t\t\tif (res.code == 0 && res.data.value) {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\tcontent: '请跳转至' + res.data.value + '下载师傅端进行入驻',\n\t\t\t\t\t\t\t\tconfirmText: '一键复制',\n\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\tcomplete(ret) {\n\t\t\t\t\t\t\t\t\tif (ret.confirm) {\n\t\t\t\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\t\t\t\tdata: res.data.value,\n\t\t\t\t\t\t\t\t\t\t\tsuccess(re) {\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '复制成功',\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请联系客服',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t// #endif\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 开启订阅消息\n\t\t\topenMsg() {\n\t\t\t\tconsole.log('订阅消息')\n\t\t\t\tvar that = this\n\t\t\t\tuni.getSetting({\n\t\t\t\t\twithSubscriptions: true, //是否获取用户订阅消息的订阅状态，默认false不返回\n\t\t\t\t\tsuccess(ret) {\n\t\t\t\t\t\tconsole.log(ret.subscriptionsSetting, '------------------')\n\t\t\t\t\t\t// if (ret.subscriptionsSetting.itemSettings && Object.keys(ret.subscriptionsSetting.itemSettings).length == 2) {\n\t\t\t\t\t\tif (ret.subscriptionsSetting.itemSettings) {\n\t\t\t\t\t\t\tuni.setStorageSync('sendMsg', true)\n\t\t\t\t\t\t\tuni.openSetting({ // 打开设置页 \n\t\t\t\t\t\t\t\tsuccess(rea) {\n\t\t\t\t\t\t\t\t\tconsole.log(rea.authSetting)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息\n\t\t\t\t\t\t\tconsole.log(99999)\n\t\t\t\t\t\t\tuni.setStorageSync('sendMsg', false)\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\tcontent: '为了更好的体验,请绑定消息推送',\n\t\t\t\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\twx.requestSubscribeMessage({\n\t\t\t\t\t\t\t\t\t\t\ttmplIds: that.arr,\n\t\t\t\t\t\t\t\t\t\t\tsuccess(re) {\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(JSON.stringify(re),\n\t\t\t\t\t\t\t\t\t\t\t\t\t'++++++++++++++')\n\t\t\t\t\t\t\t\t\t\t\t\tvar datas = JSON.stringify(re);\n\t\t\t\t\t\t\t\t\t\t\t\tif (datas.indexOf(\"accept\") != -1) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(re)\n\t\t\t\t\t\t\t\t\t\t\t\t\t// uni.setStorageSync('sendMsg', true)\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\tfail: (res) => {\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t\t// uni.setStorageSync('sendMsg', true)\n\t\t\t\t\t\t\t\t\t\tconsole.log('确认')\n\t\t\t\t\t\t\t\t\t\tthat.showModal = false\n\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\t\t\tconsole.log('取消')\n\t\t\t\t\t\t\t\t\t\t// uni.setStorageSync('sendMsg', false)\n\t\t\t\t\t\t\t\t\t\tthat.showModal = true\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 优惠券\n\t\t\tgetuhNum() {\n\t\t\t\tthis.$Request.getT(\"/app/user/getUserData\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.yhq = res.data.couponCount\n\t\t\t\t\t\tthis.yue = res.data.userMoney\n\t\t\t\t\t\tthis.ts = res.data.messageCount\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//订单跳转\n\t\t\tgoNavsTab(e, tabIndex) {\n\t\t\t\tconsole.log(e)\n\t\t\t\tif (this.userId) {\n\t\t\t\t\tgetApp().globalData.tabIndex = tabIndex\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl: e\n\t\t\t\t\t})\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tapplyhome() {\n\t\t\t\tif (this.userId) {\n\t\t\t\t\tif (this.isAgent == 0) {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/my/other/cooperation'\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/my/other/agent',\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t},\n\t\t\ttuiguang() {\n\t\t\t\tif (this.userId) {\n\t\t\t\t\tif (this.isPromotion == 0) {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/my/other/team'\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/my/team/team',\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t},\n\t\t\t//跳转客服\n\t\t\tselectKeFu() {\n\t\t\t\tlet SelKeFu = this.$queue.getData('SelKeFu');\n\t\t\t\tif (SelKeFu + '' == '1') { //手机号\n\t\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\t\tphoneNumber: uni.getStorageSync('kefuPhone')\n\t\t\t\t\t});\n\t\t\t\t} else if (SelKeFu + '' == '2') { //企业微信\n\t\t\t\t\tlet that = this\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\twx.openCustomerServiceChat({\n\t\t\t\t\t\textInfo: {\n\t\t\t\t\t\t\turl: that.$queue.getData('kefu')\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcorpId: that.$queue.getData('kefuAppId'),\n\t\t\t\t\t\tsuccess(res) {}\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\twindow.location.href = that.$queue.getData('kefu');\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifdef APP\n\t\t\t\t\tlet kefu = that.$queue.getData('kefu')\n\t\t\t\t\tconsole.log(kefu)\n\t\t\t\t\tplus.runtime.openURL(kefu, function(res) {});\n\t\t\t\t\t// #endif\n\t\t\t\t} else { //客服二维码页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/kefu/kefu'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoNav(e, type) {\n\t\t\t\tconsole.log(e)\n\t\t\t\tif (type == 'noLogin') {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: e\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (this.userId) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: e\n\t\t\t\t\t})\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoNavs(e, name) {\n\t\t\t\tconsole.log(e)\n\t\t\t\tif (this.userId) {\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl: e\n\t\t\t\t\t})\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您还未登录,请先登录',\n\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/public/login'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoLogin(e) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: e\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetAmount() {\n\t\t\t\tthis.$Request.get(\"/app/userBrowse/selectAmount\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.browse = res.data.browse\n\t\t\t\t\t\tthis.fans = res.data.fans\n\t\t\t\t\t\tthis.follow = res.data.follow\n\t\t\t\t\t\tthis.visitor = res.data.visitor\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetUserInfo() {\n\t\t\t\tthis.$Request.get(\"/app/user/selectUserById\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.userName = res.data.userName\n\t\t\t\t\t\tthis.invitationCode = res.data.invitationCode\n\t\t\t\t\t\tthis.avatar = res.data.avatar ? res.data.avatar : '../../static/logo.png'\n\t\t\t\t\t\tthis.isAuthentication = res.data.isAuthentication\n\n\t\t\t\t\t\tif (res.data.isPromotion == null || res.data.isPromotion == 0) { //推广员\n\t\t\t\t\t\t\tthis.isPromotion = '0'\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.isPromotion = res.data.isPromotion\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (res.data.isAgent == null || res.data.isAgent == 0) { //代理商\n\t\t\t\t\t\t\tthis.isAgent = '0'\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.isAgent = res.data.isAgent\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tuni.setStorageSync('feiRate', res.data.feiRate) //代理商佣金比例\n\t\t\t\t\t\tuni.setStorageSync('isAuthentication', res.data.isAuthentication)\n\t\t\t\t\t\tuni.setStorageSync('avatar', res.data.avatar)\n\t\t\t\t\t\tuni.setStorageSync('invitationCode', res.data.invitationCode)\n\t\t\t\t\t\tuni.setStorageSync('zhiFuBao', res.data.zhiFuBao)\n\t\t\t\t\t\tuni.setStorageSync('zhiFuBaoName', res.data.zhiFuBaoName)\n\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 个人认证数据\n\t\t\tgetRenZheng() {\n\t\t\t\tlet classify\n\t\t\t\tthis.$Request.get(\"/app/userCertification/queryInsert?classify=\" + 1).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif (res.data == null) {\n\t\t\t\t\t\t\tthis.geRen = 0\n\t\t\t\t\t\t\tuni.setStorageSync(\"geRen\", this.geRen)\n\t\t\t\t\t\t} else if (res.data.status == 0) {\n\t\t\t\t\t\t\tthis.geRen = 1\n\t\t\t\t\t\t\tuni.setStorageSync(\"geRen\", this.geRen)\n\t\t\t\t\t\t} else if (res.data.status == 1) {\n\t\t\t\t\t\t\tthis.geRen = 2\n\t\t\t\t\t\t\tuni.setStorageSync(\"geRen\", this.geRen)\n\t\t\t\t\t\t} else if (res.data.status == 2) {\n\t\t\t\t\t\t\tthis.geRen = 3\n\t\t\t\t\t\t\tuni.setStorageSync(\"geRen\", this.geRen)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 企业认证数据\n\t\t\tgetRenZhengs() {\n\t\t\t\tlet classify\n\t\t\t\tthis.$Request.get(\"/app/userCertification/queryInsert?classify=\" + 2).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tif (res.data == null) { //未实名\n\t\t\t\t\t\t\tthis.Qe = 0\n\t\t\t\t\t\t\tuni.setStorageSync(\"Qe\", this.Qe)\n\t\t\t\t\t\t} else if (res.data.status == 0) { //审核中\n\t\t\t\t\t\t\tthis.Qe = 1\n\t\t\t\t\t\t\tuni.setStorageSync(\"Qe\", this.Qe)\n\t\t\t\t\t\t} else if (res.data.status == 1) { //已实名\n\t\t\t\t\t\t\tthis.Qe = 2\n\t\t\t\t\t\t\tuni.setStorageSync(\"Qe\", this.Qe)\n\t\t\t\t\t\t} else if (res.data.status == 2) { //已拒绝\n\t\t\t\t\t\t\tthis.Qe = 3\n\t\t\t\t\t\t\tuni.setStorageSync(\"Qe\", this.Qe)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetIsVip() {\n\t\t\t\tthis.$Request.get(\"/app/UserVip/isUserVip\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.isVip = res.data\n\t\t\t\t\t\tuni.setStorageSync('isVIP', res.data)\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\tpage {\n\t\tbackground: #F7F6F5;\n\t}\n\n\t.header {\n\t\twidth: 100%;\n\t\t// height: 350rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tbackground-image: linear-gradient(#e1edff, #F7F6F5);\n\n\t\t// background-color: #e1edff;\n\t\t.header-box {\n\t\t\twidth: 686rpx;\n\t\t\theight: 100%;\n\n\t\t\t.money {\n\t\t\t\twidth: 686rpx;\n\t\t\t\theight: 170rpx;\n\t\t\t\tbackground-color: #ffffff;\n\t\t\t\tborder-radius: 24rpx;\n\t\t\t\tmargin-top: 40rpx;\n\n\t\t\t\t.money-item {\n\t\t\t\t\twidth: calc(100% / 3);\n\t\t\t\t\ttext-align: center;\n\n\t\t\t\t\t.money-item-price {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tfont-size: 44rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\n\t\t\t\t\t.money-item-title {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.header-box-info {\n\t\t\t\twidth: 100%rpx;\n\t\t\t\theight: 100rpx;\n\t\t\t\tmargin-top: 80rpx;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 100rpx;\n\t\t\t\t\theight: 100rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t}\n\n\t\t\t\t.header-box-info-b {\n\t\t\t\t\tmargin-left: 16rpx;\n\t\t\t\t}\n\n\t\t\t\t.header-box-info-name {\n\t\t\t\t\tcolor: #0C0C0C;\n\t\t\t\t\tfont-size: 38rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\n\t\t\t\t.header-box-info-id {\n\t\t\t\t\tcolor: #0C0C0C;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t}\n\n\t.header-box-tg {\n\t\tmargin-top: 20rpx;\n\t\twidth: 100%;\n\t\theight: 150rpx;\n\n\t\t.header-box-tg1 {\n\t\t\twidth: 686rpx;\n\t\t\theight: 100%;\n\t\t\tbackground: #ffffff;\n\t\t\tborder-radius: 24rpx;\n\t\t}\n\n\t\t.header-box-tg-c {\n\t\t\twidth: 626rpx;\n\t\t\theight: 90rpx;\n\t\t}\n\n\t\t.header-box-tg-c-l {\n\t\t\twidth: 50%;\n\t\t\theight: 100%;\n\t\t\tborder-right: 1px solid #E6E6E6;\n\t\t\tbox-sizing: border-box;\n\t\t}\n\n\t\t.header-box-tg-c-r {\n\t\t\twidth: 50%;\n\t\t\theight: 100%;\n\t\t}\n\t}\n\n\t.order {\n\t\twidth: 100%;\n\t\theight: 236rpx;\n\n\t\t// margin-top: 20rpx;\n\t\t.order-box {\n\t\t\twidth: 686rpx;\n\t\t\theight: 100%;\n\t\t\tborder-radius: 24rpx;\n\t\t\tbackground-color: #ffffff;\n\n\t\t\t.order-box-title {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 88rpx;\n\t\t\t}\n\n\t\t\t.order-box-box {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 146rpx;\n\n\t\t\t\t.order-box-box-item {\n\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\theight: 100rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.utils {\n\t\twidth: 100%;\n\t\tmargin-top: 20rpx;\n\n\t\t.utils-box {\n\t\t\twidth: 686rpx;\n\t\t\tbackground-color: #ffffff;\n\t\t\tborder-radius: 24rpx;\n\n\t\t\t.utils-box-item {\n\t\t\t\twidth: 140rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627806\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}