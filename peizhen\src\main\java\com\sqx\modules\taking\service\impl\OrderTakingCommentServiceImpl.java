package com.sqx.modules.taking.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.orders.dao.OrdersDao;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.taking.dao.OrderTakingCommentDao;
import com.sqx.modules.taking.dao.OrderTakingDao;
import com.sqx.modules.taking.entity.CommentFabulous;
import com.sqx.modules.taking.entity.OrderTaking;
import com.sqx.modules.taking.entity.TakingCommnt;
import com.sqx.modules.taking.response.TakingCommentResponse;
import com.sqx.modules.taking.response.TakingDetailsResponse;
import com.sqx.modules.taking.service.OrderTakingCommentService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class OrderTakingCommentServiceImpl extends ServiceImpl<OrderTakingCommentDao, TakingCommnt> implements OrderTakingCommentService {

    @Autowired
    private OrderTakingCommentDao orderTakingCommentDao;
    @Autowired
    private OrderTakingDao orderTakingDao;
    @Autowired
    private OrdersDao ordersDao;

    @Override
    public Result selectOrderTakingComment(Integer page, Integer limit, Long id) {
        Page<TakingDetailsResponse> page1 = new Page<>(page, limit);
        IPage<Map<String,Object>> iPage = orderTakingCommentDao.selectOrderTakingComment(page1, id);
        return Result.success().put("data", new PageUtils(iPage));
    }

    @Override
    public Result selectOrderTakingCommentByUserId(Integer page, Integer limit, Long userId) {
        Page<TakingDetailsResponse> page1 = new Page<>(page, limit);
        IPage<Map<String,Object>> iPage = orderTakingCommentDao.selectOrderTakingCommentByUserId(page1, userId);
        return Result.success().put("data", new PageUtils(iPage));
    }

    @Override
    public Result updateGoodsNum(Long commentId, Long userId) {
        //判断自己是否点过赞
        CommentFabulous commentFabulous = orderTakingCommentDao.selectGoodsNum(commentId, userId);
        if (commentFabulous != null) {
            //有赞则取消点赞
            int i = orderTakingCommentDao.deleteGoodsNum(commentFabulous.getId());
            if (i > 0) {
                return Result.success("取消点赞成功！");
            } else {
                return Result.error("取消点赞失败！");
            }
        } else {
            //无赞 点赞
            int i = orderTakingCommentDao.insertGoodsNum(commentId, userId);
            if (i > 0) {
                return Result.success("点赞成功！");
            } else {
                return Result.error("点赞失败！");
            }
        }
    }

    @Override
    public Result addGoodsNum(Long id, Long userId, String content, Integer score,Long ordersId) {
        int i = selectTakingCommentCount(id, userId);
        if(i>0){
            return Result.error("您已经评价过了！");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建
        TakingCommnt takingCommnt = new TakingCommnt();
        takingCommnt.setContent(content);
        takingCommnt.setOrderTakingId(id);
        takingCommnt.setUserId(userId);
        takingCommnt.setCreateTime(simpleDateFormat.format(new Date()));
        takingCommnt.setScore(score);
        takingCommnt.setOrdersId(ordersId);
        baseMapper.insert(takingCommnt);
        OrderTaking orderTaking = new OrderTaking();
        orderTaking.setId(id);
        orderTaking.setOrderScore(baseMapper.selectAvgScore(id));
        orderTakingDao.updateById(orderTaking);
        return Result.success();
    }

    @Override
    public int selectTakingCommentCount(Long ordersId,Long userId){
        return baseMapper.selectCount(new QueryWrapper<TakingCommnt>().eq("orders_id",ordersId).eq("user_id",userId));
    }

    @Override
    public Result queryMyComment(Long userId) {

        return null;
    }
}
