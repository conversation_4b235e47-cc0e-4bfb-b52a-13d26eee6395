package com.sqx.modules.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
public class SysEvaluate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "evaluate_id", type = IdType.AUTO)
    private Long evaluateId;

    /**
     * 用户id(评分人)
     */
    private Long userId;

    /**
     * 接单人id(被评分人)
     */
    private Long riderUserId;

    /**
     * 订单号
     */
    private String indentNumber;

    /**
     * 评价内容
     */
    private String evaluateMessage;
    /**
     * 评价图片
     */
    private String evaluateImg;

    /**
     * 评级 1:非常差 2:差 3:一般 4:满意 5:非常满意
     */
    private Integer satisfactionFlag;

    /**
     * 评价时间
     */
    private Date createTime;

    /**
     * 是否已删除0否 -1是
     */
    @TableLogic
    private Integer isDelete;
    /**
     * 用户昵称
     */
    @TableField(exist = false)
    private String userName;
    /**
     * 用户头像
     */
   @TableField(exist = false)
    private String userAvatar;
    /**
     * 骑手昵称
     */
    @TableField(exist = false)
    private String rideName;
    /**
     * 骑手头像
     */
    @TableField(exist = false)
    private String rideAvatar;
}
