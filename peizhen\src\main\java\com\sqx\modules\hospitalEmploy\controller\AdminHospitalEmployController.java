package com.sqx.modules.hospitalEmploy.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalEmploy.entity.HospitalEmploy;
import com.sqx.modules.hospitalEmploy.service.HospitalEmployService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * 服务管理-管理端
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@RestController
@Api(value = "服务管理-管理端", tags = {"服务管理-管理端"})
@RequestMapping("/admin/hospitalEmploy/")
public class AdminHospitalEmployController {
    @Autowired
    private HospitalEmployService employService;

    /**
     * 新增服务类型
     *
     * @param hospitalEmploy
     * @return
     */
    @ApiOperation("新增服务类型")
    @PostMapping("addHospitalEmploy")
    public Result addHospitalEmploy(HospitalEmploy hospitalEmploy) {
        return employService.addHospitalEmploy(hospitalEmploy);
    }

    /**
     * 修改服务类型
     *
     * @param hospitalEmploy
     * @return
     */
    @ApiOperation("修改服务类型")
    @PostMapping("updateHospitalEmploy")
    public Result updateHospitalEmploy(HospitalEmploy hospitalEmploy) {
        return employService.updateHospitalEmploy(hospitalEmploy);
    }

    /**
     * 删除服务类型
     *
     * @param serviceId 服务id
     * @return
     */
    @ApiOperation("删除服务类型")
    @GetMapping("deleteHospitalEmploy")
    public Result deleteHospitalEmploy(Long serviceId) {
        return employService.deleteHospitalEmploy(serviceId);
    }

    /**
     * 查看服务详情
     *
     * @param serviceId 服务id
     * @return
     */
    @ApiOperation("查看服务详情")
    @GetMapping("getHospitalEmployInfo")
    public Result getHospitalEmployInfo(Long serviceId) {
        return Result.success().put("data", employService.getHospitalEmployInfo(serviceId));
    }

    /**
     * 查看服务列表
     *
     * @param page
     * @param limit
     * @param hospitalEmploy
     * @return
     */
    @ApiOperation("查看服务列表")
    @GetMapping("getHospitalEmployList")
    public Result getHospitalEmployList(Integer page, Integer limit, HospitalEmploy hospitalEmploy) {
        return Result.success().put("data", employService.getHospitalEmployList(page, limit, hospitalEmploy));
    }

    @ApiOperation("获取服务列表")
    @GetMapping("getEmployList")
    public Result getEmployList(Integer authentication) {
        return Result.success().put("data", employService.getEmployList(authentication));
    }

}

