{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown/u-dropdown.vue?868e", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown/u-dropdown.vue?1543", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown/u-dropdown.vue?a965", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown/u-dropdown.vue?8ae8", "uni-app:///uview-ui/components/u-dropdown/u-dropdown.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown/u-dropdown.vue?7e80", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown/u-dropdown.vue?33ef"], "names": ["name", "props", "activeColor", "type", "default", "inactiveColor", "closeOnClickMask", "closeOnClickSelf", "duration", "height", "borderBottom", "titleSize", "borderRadius", "menuIcon", "menuIconSize", "data", "showDropdown", "menuList", "active", "current", "contentStyle", "zIndex", "opacity", "highlightIndex", "contentHeight", "computed", "popupStyle", "style", "created", "mounted", "methods", "init", "child", "menuClick", "setTimeout", "open", "val", "close", "maskClick", "highlight", "getContentHeight"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoC5wB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,eAiBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;EACA;EACAW;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACA;MACAC;MACA;MACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACAA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC,6BAEA;EACAC;IACAC;MACA;MACA;MACA;MACA;QACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACAC;UACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAe;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAhB;QACAC;MACA;IAEA;IACA;IACAgB;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzOA;AAAA;AAAA;AAAA;AAA+6C,CAAgB,yvCAAG,EAAC,C;;;;;;;;;;;ACAn8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-dropdown/u-dropdown.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-dropdown.vue?vue&type=template&id=0340bb60&scoped=true&\"\nvar renderjs\nimport script from \"./u-dropdown.vue?vue&type=script&lang=js&\"\nexport * from \"./u-dropdown.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-dropdown.vue?vue&type=style&index=0&id=0340bb60&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0340bb60\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-dropdown/u-dropdown.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown.vue?vue&type=template&id=0340bb60&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$u.addUnit(_vm.height)\n  var g1 = _vm.$u.addUnit(_vm.titleSize)\n  var g2 = _vm.$u.addUnit(_vm.menuIconSize)\n  var s0 = _vm.__get_style([\n    _vm.contentStyle,\n    {\n      transition: \"opacity \" + _vm.duration / 1000 + \"s linear\",\n      top: _vm.$u.addUnit(_vm.height),\n      height: _vm.contentHeight + \"px\",\n    },\n  ])\n  var s1 = _vm.__get_style([_vm.popupStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-dropdown\">\n\t\t<view class=\"u-dropdown__menu\" :style=\"{\n\t\t\theight: $u.addUnit(height)\n\t\t}\" :class=\"{\n\t\t\t'u-border-bottom': borderBottom\n\t\t}\">\n\t\t\t<view class=\"u-dropdown__menu__item\" v-for=\"(item, index) in menuList\" :key=\"index\" @tap.stop=\"menuClick(index)\">\n\t\t\t\t<view class=\"u-flex\">\n\t\t\t\t\t<text class=\"u-dropdown__menu__item__text\" :style=\"{\n\t\t\t\t\t\tcolor: item.disabled ? '#c0c4cc' : (index === current || highlightIndex == index) ? activeColor : inactiveColor,\n\t\t\t\t\t\tfontSize: $u.addUnit(titleSize)\n\t\t\t\t\t}\">{{item.title}}</text>\n\t\t\t\t\t<view class=\"u-dropdown__menu__item__arrow\" :class=\"{\n\t\t\t\t\t\t'u-dropdown__menu__item__arrow--rotate': index === current\n\t\t\t\t\t}\">\n\t\t\t\t\t\t<u-icon :custom-style=\"{display: 'flex'}\" :name=\"menuIcon\" :size=\"$u.addUnit(menuIconSize)\" :color=\"index === current || highlightIndex == index ? activeColor : '#c0c4cc'\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"u-dropdown__content\" :style=\"[contentStyle, {\n\t\t\ttransition: `opacity ${duration / 1000}s linear`,\n\t\t\ttop: $u.addUnit(height),\n\t\t\theight: contentHeight + 'px'\n\t\t}]\"\n\t\t @tap=\"maskClick\" @touchmove.stop.prevent>\n\t\t\t<view @tap.stop.prevent class=\"u-dropdown__content__popup\" :style=\"[popupStyle]\">\n\t\t\t\t<slot></slot>\n\t\t\t</view>\n\t\t\t<view class=\"u-dropdown__content__mask\"></view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * dropdown 下拉菜单\n\t * @description 该组件一般用于向下展开菜单，同时可切换多个选项卡的场景\n\t * @tutorial http://uviewui.com/components/dropdown.html\n\t * @property {String} active-color 标题和选项卡选中的颜色（默认#2979ff）\n\t * @property {String} inactive-color 标题和选项卡未选中的颜色（默认#606266）\n\t * @property {Boolean} close-on-click-mask 点击遮罩是否关闭菜单（默认true）\n\t * @property {Boolean} close-on-click-self 点击当前激活项标题是否关闭菜单（默认true）\n\t * @property {String | Number} duration 选项卡展开和收起的过渡时间，单位ms（默认300）\n\t * @property {String | Number} height 标题菜单的高度，单位任意（默认80）\n\t * @property {String | Number} border-radius 菜单展开内容下方的圆角值，单位任意（默认0）\n\t * @property {Boolean} border-bottom 标题菜单是否显示下边框（默认false）\n\t * @property {String | Number} title-size 标题的字体大小，单位任意，数值默认为rpx单位（默认28）\n\t * @event {Function} open 下拉菜单被打开时触发\n\t * @event {Function} close 下拉菜单被关闭时触发\n\t * @example <u-dropdown></u-dropdown>\n\t */\n\texport default {\n\t\tname: 'u-dropdown',\n\t\tprops: {\n\t\t\t// 菜单标题和选项的激活态颜色\n\t\t\tactiveColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#2979ff'\n\t\t\t},\n\t\t\t// 菜单标题和选项的未激活态颜色\n\t\t\tinactiveColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#606266'\n\t\t\t},\n\t\t\t// 点击遮罩是否关闭菜单\n\t\t\tcloseOnClickMask: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 点击当前激活项标题是否关闭菜单\n\t\t\tcloseOnClickSelf: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 过渡时间\n\t\t\tduration: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 300\n\t\t\t},\n\t\t\t// 标题菜单的高度，单位任意，数值默认为rpx单位\n\t\t\theight: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 80\n\t\t\t},\n\t\t\t// 是否显示下边框\n\t\t\tborderBottom: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 标题的字体大小\n\t\t\ttitleSize: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 28\n\t\t\t},\n\t\t\t// 下拉出来的内容部分的圆角值\n\t\t\tborderRadius: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\t// 菜单右侧的icon图标\n\t\t\tmenuIcon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'arrow-down'\n\t\t\t},\n\t\t\t// 菜单右侧图标的大小\n\t\t\tmenuIconSize: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 26\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshowDropdown: true, // 是否打开下来菜单,\n\t\t\t\tmenuList: [], // 显示的菜单\n\t\t\t\tactive: false, // 下拉菜单的状态\n\t\t\t\t// 当前是第几个菜单处于激活状态，小程序中此处不能写成false或者\"\"，否则后续将current赋值为0，\n\t\t\t\t// 无能的TX没有使用===而是使用==判断，导致程序认为前后二者没有变化，从而不会触发视图更新\n\t\t\t\tcurrent: 99999,\n\t\t\t\t// 外层内容的样式，初始时处于底层，且透明\n\t\t\t\tcontentStyle: {\n\t\t\t\t\tzIndex: -1,\n\t\t\t\t\topacity: 0\n\t\t\t\t},\n\t\t\t\t// 让某个菜单保持高亮的状态\n\t\t\t\thighlightIndex: 99999,\n\t\t\t\tcontentHeight: 0\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 下拉出来部分的样式\n\t\t\tpopupStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\t// 进行Y轴位移，展开状态时，恢复原位。收齐状态时，往上位移100%，进行隐藏\n\t\t\t\tstyle.transform = `translateY(${this.active ? 0 : '-100%'})`\n\t\t\t\tstyle['transition-duration'] = this.duration / 1000 + 's';\n\t\t\t\tstyle.borderRadius = `0 0 ${this.$u.addUnit(this.borderRadius)} ${this.$u.addUnit(this.borderRadius)}`;\n\t\t\t\treturn style;\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 引用所有子组件(u-dropdown-item)的this，不能在data中声明变量，否则在微信小程序会造成循环引用而报错\n\t\t\tthis.children = [];\n\t\t},\n\t\tmounted() {\n\t\t\t\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 当某个子组件内容变化时，触发父组件的init，父组件再让每一个子组件重新初始化一遍\n\t\t\t\t// 以保证数据的正确性\n\t\t\t\tthis.menuList = [];\n\t\t\t\tthis.children.map(child => {\n\t\t\t\t\tchild.init();\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 点击菜单\n\t\t\tmenuClick(index) {\n\t\t\t\t//点击菜单的时候给下拉框一个高度，否则会造成下拉弹窗不显示的问题\n\t\t\t\tthis.getContentHeight();\n\t\t\t\t// 判断是否被禁用\n\t\t\t\tif (this.menuList[index].disabled) return;\n\t\t\t\t// 如果点击时的索引和当前激活项索引相同，意味着点击了激活项，需要收起下拉菜单\n\t\t\t\tif (index === this.current && this.closeOnClickSelf) {\n\t\t\t\t\tthis.close();\n\t\t\t\t\t// 等动画结束后，再移除下拉菜单中的内容，否则直接移除，也就没有下拉菜单收起的效果了\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.children[index].active = false;\n\t\t\t\t\t}, this.duration)\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.open(index);\n\t\t\t},\n\t\t\t// 打开下拉菜单\n\t\t\topen(index) {\n\t\t\t\t// 重置高亮索引，否则会造成多个菜单同时高亮\n\t\t\t\t// this.highlightIndex = 9999;\n\t\t\t\t// 展开时，设置下拉内容的样式\n\t\t\t\tthis.contentStyle = {\n\t\t\t\t\tzIndex: 9999999,\n\t\t\t\t}\n\t\t\t\t// 标记展开状态以及当前展开项的索引\n\t\t\t\tthis.active = true;\n\t\t\t\tthis.current = index;\n\t\t\t\t// 历遍所有的子元素，将索引匹配的项标记为激活状态，因为子元素是通过v-if控制切换的\n\t\t\t\t// 之所以不是因display: none，是因为nvue没有display这个属性\n\t\t\t\tthis.children.map((val, idx) => {\n\t\t\t\t\tval.active = index == idx ? true : false;\n\t\t\t\t})\n\t\t\t\tthis.$emit('open', this.current);\n\t\t\t},\n\t\t\t// 设置下拉菜单处于收起状态\n\t\t\tclose() {\n\t\t\t\tthis.$emit('close', this.current);\n\t\t\t\t// 设置为收起状态，同时current归位，设置为空字符串\n\t\t\t\tthis.active = false;\n\t\t\t\tthis.current = 99999;\n\t\t\t\t//当关闭的时候把下拉框的高度设置为0，使下拉框与遮罩隐藏起来\n\t\t\t\tthis.contentHeight = 0\n\t\t\t\t// 下拉内容的样式进行调整，不透明度设置为0\n\t\t\t\tthis.contentStyle = {\n\t\t\t\t\tzIndex: 99999,\n\t\t\t\t\topacity: 0\n\t\t\t\t}\n\t\t\t\t\n\t\t\t},\n\t\t\t// 点击遮罩\n\t\t\tmaskClick() {\n\t\t\t\t// 如果不允许点击遮罩，直接返回\n\t\t\t\tif (!this.closeOnClickMask) return;\n\t\t\t\tthis.close();\n\t\t\t},\n\t\t\t// 外部手动设置某个菜单高亮\n\t\t\thighlight(index = undefined) {\n\t\t\t\tthis.highlightIndex = index !== undefined ? index : 99999;\n\t\t\t},\n\t\t\t// 获取下拉菜单内容的高度\n\t\t\tgetContentHeight() {\n\t\t\t\t// 这里的原理为，因为dropdown组件是相对定位的，它的下拉出来的内容，必须给定一个高度\n\t\t\t\t// 才能让遮罩占满菜单一下，直到屏幕底部的高度\n\t\t\t\t// this.$u.sys()为uView封装的获取设备信息的方法\n\t\t\t\tlet windowHeight = this.$u.sys().windowHeight;\n\t\t\t\tthis.$uGetRect('.u-dropdown__menu').then(res => {\n\t\t\t\t\t// 这里获取的是dropdown的尺寸，在H5上，uniapp获取尺寸是有bug的(以前提出修复过，后来又出现了此bug，目前hx2.8.11版本)\n\t\t\t\t\t// H5端bug表现为元素尺寸的top值为导航栏底部到到元素的上边沿的距离，但是元素的bottom值确是导航栏顶部到元素底部的距离\n\t\t\t\t\t// 二者是互相矛盾的，本质原因是H5端导航栏非原生，uni的开发者大意造成\n\t\t\t\t\t// 这里取菜单栏的botton值合理的，不能用res.top，否则页面会造成滚动\n\t\t\t\t\t// this.contentHeight = windowHeight - res.bottom;\n\t\t\t\t\tthis.contentHeight = windowHeight - res.bottom;\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t@import \"../../libs/css/style.components.scss\";\n\n\t.u-dropdown {\n\t\tflex: 1;\n\t\twidth: 100%;\n\t\tposition: relative;\n\n\t\t&__menu {\n\t\t\t@include vue-flex;\n\t\t\tposition: relative;\n\t\t\tz-index: 11;\n\t\t\theight: 80rpx;\n\n\t\t\t&__item {\n\t\t\t\tflex: 1;\n\t\t\t\t@include vue-flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\n\t\t\t\t&__text {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $u-content-color;\n\t\t\t\t}\n\n\t\t\t\t&__arrow {\n\t\t\t\t\tmargin-left: 6rpx;\n\t\t\t\t\ttransition: transform .3s;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\t@include vue-flex;\n\n\t\t\t\t\t&--rotate {\n\t\t\t\t\t\ttransform: rotate(180deg);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&__content {\n\t\t\tposition: absolute;\n\t\t\tz-index: 8;\n\t\t\twidth: 100%;\n\t\t\tleft: 0px;\n\t\t\tbottom: 0;\n\t\t\toverflow: hidden;\n\t\t\t\n\n\t\t\t&__mask {\n\t\t\t\tposition: absolute;\n\t\t\t\tz-index: 9;\n\t\t\t\tbackground: rgba(0, 0, 0, .3);\n\t\t\t\twidth: 100%;\n\t\t\t\tleft: 0;\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 0;\n\t\t\t}\n\n\t\t\t&__popup {\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 10;\n\t\t\t\ttransition: all 0.3s;\n\t\t\t\ttransform: translate3D(0, -100%, 0);\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\t\t}\n\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown.vue?vue&type=style&index=0&id=0340bb60&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown.vue?vue&type=style&index=0&id=0340bb60&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627540\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}