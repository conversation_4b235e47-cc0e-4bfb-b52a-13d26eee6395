<view class="ai-container data-v-2e7a9c24"><view class="top-bar data-v-2e7a9c24"><view class="page-header data-v-2e7a9c24"><text class="page-title data-v-2e7a9c24">AI助手</text><text class="page-subtitle data-v-2e7a9c24">{{$root.m0}}</text></view><view class="top-actions data-v-2e7a9c24"><view data-event-opts="{{[['tap',[['selectModel',['$event']]]]]}}" class="action-btn model-btn data-v-2e7a9c24" bindtap="__e"><u-icon vue-id="be4d2d3c-1" name="setting" size="18" color="#666" class="data-v-2e7a9c24" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['createNewChat',['$event']]]]]}}" class="action-btn new-chat-btn data-v-2e7a9c24" bindtap="__e"><u-icon vue-id="be4d2d3c-2" name="plus" size="20" color="#fff" class="data-v-2e7a9c24" bind:__l="__l"></u-icon></view></view></view><view class="conversation-list data-v-2e7a9c24"><block wx:if="{{$root.g0}}"><view class="empty-state data-v-2e7a9c24"><view class="empty-icon data-v-2e7a9c24"><u-icon vue-id="be4d2d3c-3" name="chat" size="80" color="#ddd" class="data-v-2e7a9c24" bind:__l="__l"></u-icon></view><text class="empty-title data-v-2e7a9c24">还没有对话记录</text><text class="empty-subtitle data-v-2e7a9c24">开始你的第一次AI对话吧</text><view class="empty-action data-v-2e7a9c24"><view data-event-opts="{{[['tap',[['createNewChat',['$event']]]]]}}" class="start-chat-btn data-v-2e7a9c24" bindtap="__e"><u-icon style="margin-right:8rpx;" vue-id="be4d2d3c-4" name="plus" size="20" color="#fff" class="data-v-2e7a9c24" bind:__l="__l"></u-icon><text class="btn-text data-v-2e7a9c24">开始对话</text></view></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['enterChat',['$0'],[[['conversationList','id',item.$orig.id]]]]]]]}}" class="conversation-item data-v-2e7a9c24" bindtap="__e"><view class="conversation-avatar data-v-2e7a9c24"><view class="avatar-icon data-v-2e7a9c24"><u-icon vue-id="{{'be4d2d3c-5-'+__i0__}}" name="chat" size="24" color="#0175FE" class="data-v-2e7a9c24" bind:__l="__l"></u-icon></view></view><view class="conversation-content data-v-2e7a9c24"><view class="conversation-header data-v-2e7a9c24"><text class="conversation-title data-v-2e7a9c24">{{item.$orig.title||'新对话'}}</text><view class="conversation-meta data-v-2e7a9c24"><text class="conversation-model data-v-2e7a9c24">{{item.m1}}</text><text class="update-time data-v-2e7a9c24">{{item.m2}}</text></view></view><view class="conversation-preview data-v-2e7a9c24"><text class="last-message data-v-2e7a9c24">{{item.$orig.lastMessage||'开始新的对话...'}}</text></view></view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="conversation-actions data-v-2e7a9c24" catchtap="__e"><u-icon vue-id="{{'be4d2d3c-6-'+__i0__}}" name="more-dot-fill" size="20" color="#ccc" data-event-opts="{{[['^click',[['showActions',['$0'],[[['conversationList','id',item.$orig.id]]]]]]]}}" bind:click="__e" class="data-v-2e7a9c24" bind:__l="__l"></u-icon></view></view></block></view><block wx:if="{{$root.g1>0}}"><u-loadmore vue-id="be4d2d3c-7" status="{{loadmoreStatus}}" data-event-opts="{{[['^loadmore',[['loadMore']]]]}}" bind:loadmore="__e" class="data-v-2e7a9c24" bind:__l="__l"></u-loadmore></block><u-action-sheet vue-id="be4d2d3c-8" list="{{actionList}}" value="{{showActionSheet}}" data-event-opts="{{[['^click',[['handleAction']]],['^input',[['__set_model',['','showActionSheet','$event',[]]]]]]}}" bind:click="__e" bind:input="__e" class="data-v-2e7a9c24" bind:__l="__l"></u-action-sheet><u-modal vue-id="be4d2d3c-9" title="编辑标题" show-cancel-button="{{true}}" value="{{showEditTitle}}" data-event-opts="{{[['^confirm',[['updateTitle']]],['^cancel',[['cancelEdit']]],['^input',[['__set_model',['','showEditTitle','$event',[]]]]]]}}" bind:confirm="__e" bind:cancel="__e" bind:input="__e" class="data-v-2e7a9c24" bind:__l="__l" vue-slots="{{['default']}}"><view class="edit-title-content data-v-2e7a9c24"><input class="title-input data-v-2e7a9c24" placeholder="请输入对话标题" maxlength="50" data-event-opts="{{[['input',[['__set_model',['','editTitleValue','$event',[]]]]]]}}" value="{{editTitleValue}}" bindinput="__e"/></view></u-modal></view>