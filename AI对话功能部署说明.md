# AI对话功能部署和使用说明

## 功能概述

为陪诊系统新增了AI对话功能模块，支持用户与AI进行多轮对话，具备上下文记忆能力，支持多个AI模型切换（DeepSeek和Kimi），对话记录持久化存储。

## 技术架构

### 后端架构
- **Spring Boot 2.6.11** - 主框架
- **MyBatis Plus 3.2.0** - 数据访问层
- **MySQL 8.0.17** - 数据存储
- **RestTemplate** - HTTP客户端，调用AI API
- **OpenAI兼容API** - 支持DeepSeek和Kimi

### 前端架构
- **Vue.js 2.5.16** - 管理后台框架
- **Element UI 2.8.2** - UI组件库

## 数据库变更

### 新增数据表

1. **ai_model_config** - AI模型配置表
2. **ai_conversation** - AI对话会话表  
3. **ai_message** - AI对话消息表

### 执行SQL脚本

数据库脚本已添加到 `peizhen/db/peizhen.sql` 文件中，包含：
- 表结构创建
- 默认模型配置数据（DeepSeek和Kimi）

## 部署步骤

### 1. 数据库更新

执行数据库脚本中的AI相关表创建语句：

```sql
-- 执行 peizhen/db/peizhen.sql 中新增的AI相关表
-- ai_model_config, ai_conversation, ai_message
```

### 2. 配置AI模型API密钥

在 `application.yml` 中已添加AI配置项，需要在数据库中配置具体的API密钥：

```sql
-- 更新DeepSeek API密钥
UPDATE ai_model_config SET api_key = '你的DeepSeek API密钥' WHERE model_code = 'deepseek-chat';

-- 更新Kimi API密钥
UPDATE ai_model_config SET api_key = '你的Kimi API密钥' WHERE model_code = 'kimi-chat';
```

**注意：** 所有数据表的主键字段统一使用 `id`，而不是 `xxx_id` 的形式，这样维护起来更简洁方便。

### 3. 后端部署

后端代码已集成到现有项目中，重新编译部署即可：

```bash
cd peizhen
mvn clean package
java -jar target/peizhen.jar
```

### 4. 前端部署

管理后台页面已添加到现有项目中，重新构建前端：

```bash
cd peizhen--admin
npm run build
```

## API接口说明

### 管理后台接口

#### 模型配置管理
- `GET /ai/model/list` - 分页查询模型配置
- `GET /ai/model/enabled` - 查询启用的模型列表
- `GET /ai/model/{modelId}` - 查询模型详情
- `POST /ai/model/save` - 保存模型配置
- `POST /ai/model/update` - 更新模型配置
- `POST /ai/model/delete/{modelId}` - 删除模型配置
- `POST /ai/model/toggle/{modelId}` - 启用/禁用模型

#### 对话记录管理
- `GET /ai/conversation/list` - 分页查询对话列表
- `GET /ai/conversation/{conversationId}` - 查询对话详情
- `POST /ai/conversation/delete/{conversationId}` - 删除对话

### APP端接口

#### 模型和会话管理
- `GET /app/ai/chat/models` - 获取可用模型列表
- `POST /app/ai/chat/conversation/create` - 创建对话会话
- `GET /app/ai/chat/conversation/list` - 获取用户对话列表
- `GET /app/ai/chat/conversation/{conversationId}` - 获取对话详情
- `POST /app/ai/chat/conversation/{conversationId}/title` - 更新对话标题
- `POST /app/ai/chat/conversation/{conversationId}/delete` - 删除对话
- `POST /app/ai/chat/conversation/{conversationId}/switch-model` - 切换模型

#### 聊天功能
- `POST /app/ai/chat/conversation/{conversationId}/send` - 发送消息
- `GET /app/ai/chat/conversation/{conversationId}/history` - 获取对话历史

## 管理后台使用说明

### 1. AI模型配置

访问路径：`/aiModelConfig`

功能：
- 查看所有AI模型配置
- 新增/编辑/删除模型配置
- 启用/禁用模型
- 配置模型参数（温度、最大token等）

### 2. AI对话记录

访问路径：`/aiConversationList`

功能：
- 查看所有用户的对话记录
- 按用户、模型、标题筛选
- 查看对话详情和消息内容
- 删除对话记录
- 查看token消耗统计

## APP端使用流程

### 1. 获取可用模型
```javascript
GET /app/ai/chat/models
```

### 2. 创建对话会话
```javascript
POST /app/ai/chat/conversation/create
{
  "modelCode": "deepseek-chat",
  "title": "新对话"
}
```

### 3. 发送消息
```javascript
POST /app/ai/chat/conversation/{conversationId}/send
{
  "message": "你好，请介绍一下自己",
  "modelCode": "deepseek-chat"  // 可选，切换模型
}
```

### 4. 获取对话历史
```javascript
GET /app/ai/chat/conversation/{conversationId}/history?page=1&limit=50
```

## 注意事项

### 1. API密钥安全
- API密钥存储在数据库中，确保数据库安全
- 建议定期更换API密钥
- 生产环境建议对API密钥进行加密存储

### 2. 成本控制
- 监控token消耗，避免过度使用
- 设置合理的最大token限制
- 可以根据用户等级限制使用频率

### 3. 错误处理
- API调用失败时会记录错误信息
- 超时设置为60秒，可根据需要调整
- 建议添加重试机制

### 4. 性能优化
- 对话历史查询建议分页
- 可以考虑添加缓存机制
- 定期清理过期的对话记录

## 扩展功能建议

### 1. 流式响应
- 当前实现为同步响应
- 可以扩展支持Server-Sent Events实现流式输出

### 2. 多模态支持
- 当前仅支持文本对话
- 可以扩展支持图片、文件等多模态输入

### 3. 对话模板
- 可以预设一些对话模板
- 支持系统提示词配置

### 4. 统计分析
- 添加使用统计功能
- 分析热门话题和用户行为

## 故障排查

### 1. API调用失败
- 检查API密钥是否正确
- 检查网络连接
- 查看错误日志

### 2. 数据库连接问题
- 检查数据库配置
- 确认表结构是否正确创建

### 3. 前端页面异常
- 检查路由配置
- 确认组件文件是否正确放置

---

**开发完成时间：** 2025-08-03  
**版本：** 1.0.0  
**开发者：** AI助手
