package com.sqx.modules.task.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.task.entity.HelpTake;
import com.sqx.modules.utils.excel.ExcelData;

public interface HelpTakeService extends IService<HelpTake> {

    Result selectHelpTake(int page, int limit);

    Result selectRunHelpOrder(int page,int limit,Integer status,Long userId);

    Result selectRunHelpOrder(int page,int limit,Integer status,String phone,String startTime,String endTime);

    ExcelData helpTakeListExcel(Integer status, String phone, String startTime, String endTime);

    HelpTake selectHelpTakeById(Long helpTakeId);

    Integer countHelpTakeByCreateTime(String time,Integer flag);

    Double sumMoneyBySend(String time,Integer flag);

    Result saveBody(HelpTake helpTake);

    Result endHelpTake(Long id);

    Result closeOrder(Long helpTakeId,Long helpOrderId,String code);

    Result closeOrders(Long helpOrderId);

    Result updateHelpTakeById(HelpTake helpTake);

    Result deleteById(Long id);

}
