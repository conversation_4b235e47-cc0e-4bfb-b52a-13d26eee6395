package com.sqx.modules.hospitalEmploy.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 服务管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
public class HospitalEmploy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务id
     */
    @ApiModelProperty("服务id")
    @TableId(type = IdType.AUTO)
    private Long serviceId;

    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    private String serviceName;

    /**
     * 模块id
     */
    @ApiModelProperty("模块id")
    private Long modularId;

    /**
     * 价格
     */
    @ApiModelProperty("money")
    private BigDecimal money;

    /**
     * 单位(1:天 2:次)
     */
    @ApiModelProperty("单位(1:天 2:次)")
    private Integer company;

    /**
     * 标签
     */
    @ApiModelProperty("标签")
    private String tags;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String serviceDescribe;
    /**
     * 图标
     */
    @ApiModelProperty("图标")
    private String img;
    /**
     * 服务内容
     */
    @ApiModelProperty("服务内容")
    private String serviceContent;
    /**
     * 服务须知
     */
    @ApiModelProperty("服务须知")
    private String serviceInstruction;
    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;
    /**
     * 是否启用 0否 1是
     */
    @ApiModelProperty("是否启用 0否 1是")
    private Integer isEnable;
    /**
     * 路由
     */
    @ApiModelProperty("路由")
    private String route;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;
    /**
     * 背景图
     */
    @ApiModelProperty("背景图")
    private String backgroundImg;
    /**
     * 模板类型
     */
    @ApiModelProperty("模板类型")
    private Integer serviceType;
    /**
     * 特殊标志
     */
    @ApiModelProperty("特殊标志")
    private String icon;
    /**
     * 注意事项
     */
    @ApiModelProperty("注意事项")
    private String mattersThing;

    /**
     * 服务类型名称
     */
    @ApiModelProperty("服务类型名称")
    @TableField(exist = false)
    private String modularName;
}
