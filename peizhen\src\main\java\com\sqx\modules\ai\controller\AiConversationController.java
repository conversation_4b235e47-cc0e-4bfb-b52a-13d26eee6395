package com.sqx.modules.ai.controller;

import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.service.AiConversationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * AI对话会话管理Controller
 */
@RestController
@RequestMapping("/ai/conversation")
@Api(value = "AI对话会话管理", tags = {"AI对话会话管理"})
public class AiConversationController {

    @Autowired
    private AiConversationService conversationService;

    /**
     * 分页查询对话会话列表
     */
    @GetMapping("/list")
    @ApiOperation("分页查询对话会话列表")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = conversationService.queryPage(params);
        return Result.success().put("data", page);
    }

    /**
     * 查询对话会话详情
     */
    @GetMapping("/{id}")
    @ApiOperation("查询对话会话详情")
    public Result info(@PathVariable("id") Long id) {
        // 管理后台查看，不需要用户ID验证
        return conversationService.getConversationDetail(id, null);
    }

    /**
     * 删除对话会话
     */
    @PostMapping("/delete/{id}")
    @ApiOperation("删除对话会话")
    public Result delete(@PathVariable("id") Long id) {
        // 管理后台删除，不需要用户ID验证
        return conversationService.deleteConversation(id, null);
    }
}
