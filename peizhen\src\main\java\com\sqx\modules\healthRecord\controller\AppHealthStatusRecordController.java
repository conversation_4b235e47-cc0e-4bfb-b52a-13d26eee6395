package com.sqx.modules.healthRecord.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.healthRecord.entity.HealthStatusRecord;
import com.sqx.modules.healthRecord.service.HealthStatusRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 健康状况记录表-用户端 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@Api(value = "健康记录管理-用户端", tags = {"健康记录管理-用户端"})
@RequestMapping("/app/healthRecord/")
public class AppHealthStatusRecordController {

    @Autowired
    private HealthStatusRecordService healthStatusRecordService;

    @Login
    @ApiOperation("添加或修改健康记录")
    @PostMapping("saveHealthRecord")
    public Result saveHealthRecord(@RequestAttribute("userId") Long userId, @RequestBody HealthStatusRecord healthRecord) {
        // 确保用户只能操作自己的记录
        healthRecord.setUserId(userId);
        healthRecord.setCreatedBy(userId);
        healthRecord.setUpdatedBy(userId);
        return healthStatusRecordService.saveHealthRecord(healthRecord);
    }

    @Login
    @ApiOperation("获取当前用户的健康记录列表")
    @GetMapping("getMyHealthRecords")
    public Result getMyHealthRecords(
            @RequestAttribute("userId") Long userId,
            @ApiParam("页码") Integer page,
            @ApiParam("每页数量") Integer limit,
            @ApiParam("患者ID") Long patientId) {
        if (patientId != null) {
            return Result.success().put("data", healthStatusRecordService.getHealthRecordsByPatientId(page, limit, patientId));
        } else {
            return Result.success().put("data", healthStatusRecordService.getHealthRecordsByUserId(page, limit, userId));
        }
    }

    @Login
    @ApiOperation("获取健康记录详情")
    @GetMapping("getHealthRecordInfo")
    public Result getHealthRecordInfo(
            @RequestAttribute("userId") Long userId,
            @ApiParam("记录ID") Long recordId) {
        HealthStatusRecord healthRecord = healthStatusRecordService.getHealthRecordInfo(recordId);
        if (healthRecord == null) {
            return Result.error("健康记录不存在");
        }
        // 验证用户权限
        if (!userId.equals(healthRecord.getUserId())) {
            return Result.error("无权访问此记录");
        }
        return Result.success().put("data", healthRecord);
    }

    @Login
    @ApiOperation("删除健康记录")
    @GetMapping("deleteHealthRecord")
    public Result deleteHealthRecord(
            @RequestAttribute("userId") Long userId,
            @ApiParam("记录ID") Long recordId) {
        HealthStatusRecord healthRecord = healthStatusRecordService.getHealthRecordInfo(recordId);
        if (healthRecord == null) {
            return Result.error("健康记录不存在");
        }
        // 验证用户权限
        if (!userId.equals(healthRecord.getUserId())) {
            return Result.error("无权删除此记录");
        }
        return healthStatusRecordService.deleteHealthRecord(recordId, userId);
    }
}
