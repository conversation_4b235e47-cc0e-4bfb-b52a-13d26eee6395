package com.sqx.modules.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserCertification;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestAttribute;

public interface UserCertificationService extends IService<UserCertification> {
    Result insert(UserCertification userCertification);

    Result isInsert(Long userId);

    Result queryCertification(Long page, Long limit, String status, String name,Integer authentication);

    Result queryUserCertification(IPage iPage, String name,String phone,Integer authentication);

    Result auditorUserCertification(Integer status, Long id, String remek);

    UserCertification getUserCertification(Long userId);

    Result queryInsert(Long userId);
}
