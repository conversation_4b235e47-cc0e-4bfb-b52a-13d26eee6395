{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown-item/u-dropdown-item.vue?b5a9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown-item/u-dropdown-item.vue?ede2", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown-item/u-dropdown-item.vue?4687", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown-item/u-dropdown-item.vue?8e07", "uni-app:///uview-ui/components/u-dropdown-item/u-dropdown-item.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown-item/u-dropdown-item.vue?6404", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-dropdown-item/u-dropdown-item.vue?a10f"], "names": ["name", "props", "value", "type", "default", "title", "options", "disabled", "height", "data", "active", "activeColor", "inactiveColor", "computed", "props<PERSON><PERSON>e", "watch", "created", "methods", "init", "parent", "cellClick", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4OAEN;AACP,KAAK;AACL;AACA,aAAa,sOAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuBjxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,gBAYA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAD;MACA;MACA;MACA;IACA;EACA;EACAE;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACAC;UACAd;UACAE;QACA;MACA;IACA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAAo7C,CAAgB,8vCAAG,EAAC,C;;;;;;;;;;;ACAx8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-dropdown-item/u-dropdown-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-dropdown-item.vue?vue&type=template&id=43403030&scoped=true&\"\nvar renderjs\nimport script from \"./u-dropdown-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-dropdown-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-dropdown-item.vue?vue&type=style&index=0&id=43403030&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43403030\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-dropdown-item/u-dropdown-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown-item.vue?vue&type=template&id=43403030&scoped=true&\"", "var components\ntry {\n  components = {\n    uCellGroup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-cell-group/u-cell-group\" */ \"@/uview-ui/components/u-cell-group/u-cell-group.vue\"\n      )\n    },\n    uCellItem: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-cell-item/u-cell-item\" */ \"@/uview-ui/components/u-cell-item/u-cell-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.active && !_vm.$slots.default && !_vm.$slots.$default\n      ? _vm.$u.addUnit(_vm.height)\n      : null\n  var l0 =\n    _vm.active && !_vm.$slots.default && !_vm.$slots.$default\n      ? _vm.__map(_vm.options, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var a0 = {\n            color:\n              _vm.value == item.value ? _vm.activeColor : _vm.inactiveColor,\n          }\n          return {\n            $orig: $orig,\n            a0: a0,\n          }\n        })\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown-item.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-dropdown-item\" v-if=\"active\" @touchmove.stop.prevent=\"() => {}\" @tap.stop.prevent=\"() => {}\">\n\t\t<block v-if=\"!$slots.default && !$slots.$default\">\n\t\t\t<scroll-view scroll-y=\"true\" :style=\"{\n\t\t\t\theight: $u.addUnit(height)\n\t\t\t}\">\n\t\t\t\t<view class=\"u-dropdown-item__options\">\n\t\t\t\t\t<u-cell-group>\n\t\t\t\t\t\t<u-cell-item bgColor=\"#ffffff\" @click=\"cellClick(item.value)\" :arrow=\"false\" :title=\"item.label\" v-for=\"(item, index) in options\"\n\t\t\t\t\t\t :key=\"index\" :title-style=\"{\n\t\t\t\t\t\t\tcolor: value == item.value ? activeColor : inactiveColor\n\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t<u-icon v-if=\"value == item.value\" name=\"checkbox-mark\" :color=\"activeColor\" size=\"32\"></u-icon>\n\t\t\t\t\t\t</u-cell-item>\n\t\t\t\t\t</u-cell-group>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</block>\n\t\t<slot v-else />\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * dropdown-item 下拉菜单\n\t * @description 该组件一般用于向下展开菜单，同时可切换多个选项卡的场景\n\t * @tutorial http://uviewui.com/components/dropdown.html\n\t * @property {String | Number} v-model 双向绑定选项卡选择值\n\t * @property {String} title 菜单项标题\n\t * @property {Array[Object]} options 选项数据，如果传入了默认slot，此参数无效\n\t * @property {Boolean} disabled 是否禁用此选项卡（默认false）\n\t * @property {String | Number} duration 选项卡展开和收起的过渡时间，单位ms（默认300）\n\t * @property {String | Number} height 弹窗下拉内容的高度(内容超出将会滚动)（默认auto）\n\t * @example <u-dropdown-item title=\"标题\"></u-dropdown-item>\n\t */\n\texport default {\n\t\tname: 'u-dropdown-item',\n\t\tprops: {\n\t\t\t// 当前选中项的value值\n\t\t\tvalue: {\n\t\t\t\ttype: [Number, String, Array],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 菜单项标题\n\t\t\ttitle: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 选项数据，如果传入了默认slot，此参数无效\n\t\t\toptions: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn []\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 是否禁用此菜单项\n\t\t\tdisabled: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 下拉弹窗的高度\n\t\t\theight: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 'auto'\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tactive: false, // 当前项是否处于展开状态\n\t\t\t\tactiveColor: '#2979ff', // 激活时左边文字和右边对勾图标的颜色\n\t\t\t\tinactiveColor: '#ffffff', // 未激活时左边文字和右边对勾图标的颜色\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 监听props是否发生了变化，有些值需要传递给父组件u-dropdown，无法双向绑定\n\t\t\tpropsChange() {\n\t\t\t\treturn `${this.title}-${this.disabled}`;\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tpropsChange(n) {\n\t\t\t\t// 当值变化时，通知父组件重新初始化，让父组件执行每个子组件的init()方法\n\t\t\t\t// 将所有子组件数据重新整理一遍\n\t\t\t\tif (this.parent) this.parent.init();\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 父组件的实例\n\t\t\tthis.parent = false;\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 获取父组件u-dropdown\n\t\t\t\tlet parent = this.$u.$parent.call(this, 'u-dropdown');\n\t\t\t\tif (parent) {\n\t\t\t\t\tthis.parent = parent;\n\t\t\t\t\t// 将子组件的激活颜色配置为父组件设置的激活和未激活时的颜色\n\t\t\t\t\tthis.activeColor = parent.activeColor;\n\t\t\t\t\tthis.inactiveColor = parent.inactiveColor;\n\t\t\t\t\t// 将本组件的this，放入到父组件的children数组中，让父组件可以操作本(子)组件的方法和属性\n\t\t\t\t\t// push进去前，显判断是否已经存在了本实例，因为在子组件内部数据变化时，会通过父组件重新初始化子组件\n\t\t\t\t\tlet exist = parent.children.find(val => {\n\t\t\t\t\t\treturn this === val;\n\t\t\t\t\t})\n\t\t\t\t\tif (!exist) parent.children.push(this);\n\t\t\t\t\tif (parent.children.length == 1) this.active = true;\n\t\t\t\t\t// 父组件无法监听children的变化，故将子组件的title，传入父组件的menuList数组中\n\t\t\t\t\tparent.menuList.push({\n\t\t\t\t\t\ttitle: this.title,\n\t\t\t\t\t\tdisabled: this.disabled\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// cell被点击\n\t\t\tcellClick(value) {\n\t\t\t\t// 修改通过v-model绑定的值\n\t\t\t\tthis.$emit('input', value);\n\t\t\t\t// 通知父组件(u-dropdown)收起菜单\n\t\t\t\tthis.parent.close();\n\t\t\t\t// 发出事件，抛出当前勾选项的value\n\t\t\t\tthis.$emit('change', value);\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init();\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t@import \"../../libs/css/style.components.scss\";\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown-item.vue?vue&type=style&index=0&id=43403030&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-dropdown-item.vue?vue&type=style&index=0&id=43403030&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627669\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}