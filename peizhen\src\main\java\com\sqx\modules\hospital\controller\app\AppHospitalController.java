package com.sqx.modules.hospital.controller.app;


import com.sqx.common.utils.Result;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.service.HospitalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * 医院管理-用户端
 *
 * <AUTHOR>
 * @since 2022-11-02
 */

@RestController
@Api(value = "医院管理-用户端", tags = {"医院管理"})
@RequestMapping(value = "/app/hospital/")
public class AppHospitalController {
    @Autowired
    private HospitalService hospitalService;

    /**
     * 获取医院列表
     * @param page
     * @param limit
     * @param hospital 医院信息
     * @return
     */
    @ApiOperation("获取医院列表")
    @GetMapping("getHospitalList")
    public Result getHospitalList(Integer page, Integer limit, Hospital hospital) {
        hospital.setIsEnable(1);
        return Result.success().put("data", hospitalService.getHospitalList(page, limit, hospital));
    }

    /**
     * 获取医院详情
     * @param hospitalId 医院id
     * @return
     */
    @ApiOperation("获取医院详情")
    @GetMapping("getHospitalInfo")
    public Result getHospitalInfo(Long hospitalId) {
        return Result.success().put("data", hospitalService.getHospitalInfo(hospitalId));
    }

    /**
     * 获取所有有医院的城市
     * @return
     */
    @ApiOperation("获取所有有医院的城市")
    @GetMapping("getCityList")
    public Result getCityList() {
        return Result.success().put("data", hospitalService.getCityList());
    }
}

