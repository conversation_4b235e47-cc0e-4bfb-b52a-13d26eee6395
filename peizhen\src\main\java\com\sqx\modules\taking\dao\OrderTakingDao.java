package com.sqx.modules.taking.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.taking.entity.OrderTaking;
import com.sqx.modules.taking.response.MyReleaseResponse;
import com.sqx.modules.taking.response.OrderTakingResponse;
import com.sqx.modules.taking.response.TakingDetailsResponse;
import com.sqx.modules.taking.response.TakingResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface OrderTakingDao extends BaseMapper<OrderTaking> {
    IPage<OrderTakingResponse> queryTaking(@Param("iPage") IPage iPage,@Param("city") String city, @Param("longitude") String longitude, @Param("latitude") String latitude, @Param("like") String like, @Param("condition") Integer condition, @Param("bannerid") Long bannerid, @Param("isRecommend") Long isRecommend, @Param("id") String id, @Param("sex") Long sex, @Param("by") String by,@Param("classify") Integer classify,@Param("salesNum") String salesNum);

    List<OrderTakingResponse> queryLowTaking(@Param("city") String city, @Param("longitude") String longitude, @Param("latitude") String latitude, @Param("like") String like, @Param("condition") Integer condition, @Param("bannerid") Long bannerid, @Param("isRecommend") Long isRecommend, @Param("id") String id, @Param("sex") Long sex, @Param("by") String by,@Param("classify") Integer classify,@Param("salesNum") String salesNum);

    IPage<OrderTakingResponse> queryLowTakings(Page<OrderTakingResponse> pages,@Param("city") String city, @Param("longitude") String longitude, @Param("latitude") String latitude, @Param("like") String like, @Param("condition") Integer condition, @Param("bannerid") Long bannerid, @Param("isRecommend") Long isRecommend, @Param("id") String id, @Param("sex") Long sex, @Param("by") String by,@Param("classify") Integer classify,@Param("salesNum") String salesNum);

    TakingDetailsResponse queryTakingDetails(@Param("id") Long id,@Param("longitude") String longitude,@Param("latitude") String latitude);

    IPage<MyReleaseResponse> selectMyRelease(Page<MyReleaseResponse> page, @Param("userId") Long userId, @Param("status") String status);

    IPage<TakingResponse> queryAllOrderTaking(Page<TakingResponse> page,@Param("city") String city, @Param("gameId") String gameId, @Param("status") Long status, @Param("userName") String userName, @Param("userId") Long userId,@Param("classify") Integer classify);

    Integer countGoodsByCreateTime(@Param("time")String time,@Param("flag")Integer flag);

    Double sumGoodsByCreateTime(@Param("time")String time,@Param("flag")Integer flag);

    Integer selectOrderTakingCountByUserId(Long userId,Integer status);

    int updateTakingStatusByUserId(Long userId);

}
