{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/login.vue?5a57", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/login.vue?85ab", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/login.vue?4316", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/login.vue?125c", "uni-app:///pages/public/login.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/login.vue?4482", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/public/login.vue?66ad"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "checked", "mobile", "code", "weixin<PERSON>ogin", "sending", "sendTime", "count", "weixinPhone", "sendDataList", "sessionkey", "phoneNum", "isopen", "onLoad", "onShow", "methods", "selbindwx", "uni", "url", "bingwx", "icon", "position", "title", "window", "that", "<PERSON><PERSON><PERSON><PERSON>", "wxGetUserInfo", "desc", "success", "console", "fail", "login", "provider", "inviterCode", "openId", "unionId", "userName", "avatar", "sex", "duration", "getPhoneNumber", "setPhoneByInsert", "decryptData", "key", "iv", "getWeixinInfo", "phone", "showCancel", "content", "weixinLo", "token", "unionid", "openid", "clientid", "forget", "register", "inputChange", "navBack", "getUserInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoExvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;IACA;IACA;MACA;QACA;UACA;QAAA;MAEA;IACA;IACA;MACA;QACA;UACA;QACA;MACA;IACA;EAIA;EACAC,2BAiBA;EACAC;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA,gGACA;UACA;UACA;YACAb;UACA;YACAA;UACA;UACA;YACAc;YACA;YACA;;YAEA;cACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAA;kBACAC;gBACA;cACA;gBACAD;kBACAC;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAF;UACAG;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;UACA,gGACA;YACA;YACA;cACAnB;YACA;cACAA;YACA;YACA;cACAc;cACA;cACA;;cAEA;gBACA;kBACA;kBACA;kBACA;kBACAA;kBACAA;kBACA;kBACA;kBACA;kBACA;kBACAA;oBACAC;kBACA;gBACA;kBACAD;oBACAC;kBACA;gBACA;cACA;YACA;UACA;YACAK,uBACA,+DACAC,2BACA,mBACAD,qCACA;UACA;QACA;UACA;YACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACAN;gBACAC;cACA;YACA;cACAD;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAO;MACAR;QACAC;MACA;IACA;IACAQ;MAAA;MACA;QACAT;UACAK;UACAF;QACA;QACA;MACA;MACAzB;QACAgC;QACAC;UACAC;UACA;UACA;UACA;UACA;YACA;YACA;UACA;QACA;QACAC;UACAD;QACA;MACA;IACA;IACA;IACAE;MACA;MACA;MACAd;QACAe;QACAJ;UACAC;UACA;YACA1B;UACA;UACAqB;YACA;cACAP;cACAA;cACAA;cACAO;cAEA;cACA;gBACAS;cACA;cACA;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBAAA;gBACAL;cACA;;cACAT;cACAA;cACA;cACAK;cACA;gBACAL;cACA;gBACAA;cACA;YACA;cACAP;gBACAG;gBACAE;gBACAiB;cACA;cACAV;YACA;UACA;QAEA;MACA;IACA;IACA;IACAW;MACA;QACAX;MACA;QACAA;QACAA;QACA;MACA;IACA;IACA;IACAY;MAAA;MACA;QACAC;QACAC;QACAC;MACA;MAEA;QACA;UACA;UACA;QACA;UACA3B;YACAK;YACAF;YACAmB;UACA;QACA;MACA;IACA;IACA;IACAM;MAAA;MACA;MACA5B;QACAK;MACA;MACA;QACAY;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAQ;QACAb;MACA;MACAT;QACAP;QACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UAEA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACAA;QACA;UACAA;YACA8B;YACAzB;YACA0B;UACA;QACA;MACA;IACA;IAEAC;MACA;MACAhC;QACAe;QACAJ;UACAJ;UACAK;UACAL;UACAA;UACAA;UACAA;YACA0B;YACAC;YACAC;UACA;YACAvB;YACA;cACA;gBACA;gBACAL,4DACA6B,yDAEA;cACA;cACA7B;cACAA;cACAA;YACA;cACAP;cACAA;gBACAC;cACA;YACA;UACA;QAGA;MACA;IACA;IAEAoC;MACArC;QACAC;MACA;IACA;IACAqC;MACA;QACAtC;UACAK;UACAF;QACA;QACA;MACA;MACAH;QACAC;MACA;IACA;IACAsC;MACA;MACA;IACA;IACAC;MACAxC;IACA;IAEAyC;MAAA;MACA;QACA;UACA;UACA,2EACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAzC;YACAC;UACA;QACA;UACAD;YACA8B;YACAzB;YACA0B;UACA;UACA;QACA;QACA/B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnfA;AAAA;AAAA;AAAA;AAAu3C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACA34C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/public/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/public/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=2f0f6fbc&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/public/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=2f0f6fbc&\"", "var components\ntry {\n  components = {\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-checkbox/u-checkbox\" */ \"@/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- <image @click=\"navBack\" src=\"../../static/images/index/close.png\" style=\"width: 32upx;height: 32upx;margin-left: 46upx;\"></image> -->\r\n\t\t<!-- 小程序状态下登录 -->\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<view class=\"mp_wxBox\">\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"headers\">\r\n\t\t\t\t\t<image src=\"../../static/logo.png\" style=\"border-radius: 50%;\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view>申请获取以下权限</view>\r\n\t\t\t\t\t<text>获得你的公开信息(昵称，头像、地区等)</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button v-show=\"weixinPhone\" style=\"background: #557EFD;color: #FFFFFF;\" class=\"bottom\"\r\n\t\t\t\t\topen-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">\r\n\t\t\t\t\t授权手机号\r\n\t\t\t\t</button>\r\n\t\t\t\t<button v-show=\"!weixinPhone\" style=\"background: #557EFD;color: #FFFFFF;\" class='bottom'\r\n\t\t\t\t\tbindtap=\"getUserProfile\" @tap=\"wxGetUserInfo\">\r\n\t\t\t\t\t授权登录\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view>\r\n\t\t\t<view class=\"flex justify-center\" style=\"width:100%\">\r\n\t\t\t\t<u-checkbox label-size=\"26rpx\" v-model=\"checked\" shape=\"circle\">\r\n\t\t\t\t\t我已认真阅读\r\n\t\t\t\t\t<text @click.stop=\"gotoxieyi('/my/setting/mimi')\" style=\"color: #4e86f8;\">《隐私政策》</text>\r\n\t\t\t\t\t和\r\n\t\t\t\t\t<text @click.stop=\"gotoxieyi('/my/setting/xieyi')\" style=\"color: #4e86f8;\">《用户服务协议》</text>\r\n\r\n\t\t\t\t</u-checkbox>\r\n\t\t\t\t<!-- <text>登录即代表同意</text>\r\n\t\t\t\t<navigator url=\"/my/setting/mimi\" open-type=\"navigate\">《隐私政策》</navigator>\r\n\t\t\t\t和\r\n\t\t\t\t<navigator url=\"/my/setting/xieyi\" open-type=\"navigate\">《用户服务协议》</navigator> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t<view style=\"text-align: center;\">\r\n\t\t\t<image style=\"width: 120upx;height: 120upx;margin-top: 140upx;border-radius:20upx\"\r\n\t\t\t\tsrc=\"../../static/logo.png\"></image>\r\n\r\n\r\n\t\t\t<!-- <button v-if=\"weixinLogin\" class=\"confirm-btn\" @click=\"weixinLo\">微信登录</button>\r\n\t\t\t<button v-if=\"weixinLogin\" class='confirm-btn-weixin' @click=\"register\">手机号登录</button>\r\n\t\t\t<button v-if=\"!weixinLogin\" class='confirm-btn' @click=\"register\">手机号登录</button> -->\r\n\t\t\t<button class='confirm-btn' v-if=\"!isopen\" @click=\"register\">手机号登录</button>\r\n\t\t\t<button class='confirm-btn' v-if=\"isopen\" @click=\"bingwx\">微信一键登录</button>\r\n\r\n\t\t\t<!-- 底部信息 -->\r\n\t\t\t<view class=\"footer\">\r\n\t\t\t\t<u-checkbox label-size=\"26rpx\" v-model=\"checked\" shape=\"circle\">\r\n\t\t\t\t\t我已认真阅读\r\n\t\t\t\t\t<text @click.stop=\"gotoxieyi('/my/setting/mimi')\" style=\"color: #4e86f8;\">《隐私政策》</text>\r\n\t\t\t\t\t和\r\n\t\t\t\t\t<text @click.stop=\"gotoxieyi('/my/setting/xieyi')\" style=\"color: #4e86f8;\">《用户服务协议》</text>\r\n\r\n\t\t\t\t</u-checkbox>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\r\n\t</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tchecked: false,\r\n\t\t\t\tmobile: '',\r\n\t\t\t\tcode: '',\r\n\t\t\t\tweixinLogin: false,\r\n\t\t\t\tsending: false,\r\n\t\t\t\tsendTime: '获取验证码',\r\n\t\t\t\tcount: 60,\r\n\t\t\t\tweixinPhone: false,\r\n\t\t\t\tsendDataList: {},\r\n\t\t\t\tsessionkey: '',\r\n\t\t\t\tphoneNum: false,\r\n\t\t\t\tisopen: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tif (e.inviterCode) {\r\n\t\t\t\tthis.$queue.setData('inviterCode', e.inviterCode);\r\n\t\t\t}\r\n\t\t\t//微信登录开启\r\n\t\t\tthis.$Request.getT('/app/common/type/53').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tif (res.data && res.data.value && res.data.value == '是') {\r\n\t\t\t\t\t\t// this.weixinLogin = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tthis.$Request.getT('/app/common/type/188').then(res => {\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tif (res.data && res.data.value && res.data.value == '是') {\r\n\t\t\t\t\t\tthis.phoneNum = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.selbindwx()\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\tthis.$Request.getT('/app/common/type/237').then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tif (res.data && res.data.value && res.data.value == '是') {\r\n\t\t\t\t\t\t\tthis.isopen = true;\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthis.isopen = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tthis.isopen = false;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tselbindwx() {\r\n\t\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\t\tlet openid = uni.getStorageSync('openid');\r\n\t\t\t\t\tlet userId = uni.getStorageSync('userId');\r\n\t\t\t\t\tlet that = this;\r\n\t\t\t\t\tif (window.location.href.indexOf('?code=') !== -1 || window.location.href.indexOf('&code=') !==\r\n\t\t\t\t\t\t-1) {\r\n\t\t\t\t\t\tlet code;\r\n\t\t\t\t\t\tif (window.location.href.indexOf('?code=') !== -1) {\r\n\t\t\t\t\t\t\tcode = window.location.href.split('?code=')[1].split('&')[0];\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tcode = window.location.href.split('&code=')[1].split('&')[0];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$Request.get('/app/Login/getOpenId?code=' + code).then(ret => {\r\n\t\t\t\t\t\t\tuni.setStorageSync('openid', ret.data)\r\n\t\t\t\t\t\t\t// uni.setStorageSync('', ret.data.headimgurl)\r\n\t\t\t\t\t\t\t// uni.setStorageSync('openidnickname', ret.data.nickname)\r\n\r\n\t\t\t\t\t\t\tthis.$Request.get('/app/Login/openid/login?openId=' + ret.data.openid).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"phone\", res.user.phone);\r\n\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"userId\", res.user.userId);\r\n\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"token\", res.token);\r\n\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"userName\", res.user.userName);\r\n\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"avatar\", res.user.avatar);\r\n\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"invitationCode\", res.user.invitationCode);\r\n\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"inviterCode\", res.user.inviterCode);\r\n\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/my/index'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/public/bind'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 微信公众号登录\r\n\t\t\tbingwx() {\r\n\t\t\t\tif (this.checked == false) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tposition: 'bottom',\r\n\t\t\t\t\t\ttitle: '请同意《用户协议》和《隐私政策》'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tlet ua = navigator.userAgent.toLowerCase();\r\n\t\t\t\tif (ua.indexOf('micromessenger') !== -1) {\r\n\t\t\t\t\tlet openid = uni.getStorageSync('openid');\r\n\t\t\t\t\tlet userId = uni.getStorageSync('userId');\r\n\t\t\t\t\tlet that = this;\r\n\t\t\t\t\tif (!openid) {\r\n\t\t\t\t\t\tif (window.location.href.indexOf('?code=') !== -1 || window.location.href.indexOf('&code=') !==\r\n\t\t\t\t\t\t\t-1) {\r\n\t\t\t\t\t\t\tlet code;\r\n\t\t\t\t\t\t\tif (window.location.href.indexOf('?code=') !== -1) {\r\n\t\t\t\t\t\t\t\tcode = window.location.href.split('?code=')[1].split('&')[0];\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tcode = window.location.href.split('&code=')[1].split('&')[0];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.$Request.get('/app/Login/getOpenId?code=' + code).then(ret => {\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('openid', ret.data)\r\n\t\t\t\t\t\t\t\t// uni.setStorageSync('openidheadimgurl', ret.data.headimgurl)\r\n\t\t\t\t\t\t\t\t// uni.setStorageSync('openidnickname', ret.data.nickname)\r\n\r\n\t\t\t\t\t\t\t\tthis.$Request.get('/app/Login/openid/login?openId=' + ret.data).then(res => {\r\n\t\t\t\t\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"phone\", res.user.phone);\r\n\t\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"userId\", res.user.userId);\r\n\t\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"token\", res.token);\r\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('sex', res.user.sex)\r\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('openId', res.user.openId)\r\n\t\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"userName\", res.user.userName);\r\n\t\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"avatar\", res.user.avatar);\r\n\t\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"invitationCode\", res.user.invitationCode);\r\n\t\t\t\t\t\t\t\t\t\tthis.$queue.setData(\"inviterCode\", res.user.inviterCode);\r\n\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/public/bind'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\twindow.location.href =\r\n\t\t\t\t\t\t\t\t'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +\r\n\t\t\t\t\t\t\t\tthat.$queue.getWxAppid() +\r\n\t\t\t\t\t\t\t\t'&redirect_uri=' +\r\n\t\t\t\t\t\t\t\twindow.location.href.split('#')[0] +\r\n\t\t\t\t\t\t\t\t'&response_type=code&scope=snsapi_userinfo#wechat_redirect';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$Request.get('/app/Login/openid/login?openId=' + openid).then(res => {\r\n\t\t\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t\t\tthis.$queue.setData(\"phone\", res.user.phone);\r\n\t\t\t\t\t\t\t\tthis.$queue.setData(\"userId\", res.user.userId);\r\n\t\t\t\t\t\t\t\tthis.$queue.setData(\"token\", res.token);\r\n\t\t\t\t\t\t\t\tthis.$queue.setData(\"userName\", res.user.userName);\r\n\t\t\t\t\t\t\t\tthis.$queue.setData(\"avatar\", res.user.avatar);\r\n\t\t\t\t\t\t\t\tthis.$queue.setData(\"invitationCode\", res.user.invitationCode);\r\n\t\t\t\t\t\t\t\tthis.$queue.setData(\"inviterCode\", res.user.inviterCode);\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/bind'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgotoxieyi(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\twxGetUserInfo(e) {\r\n\t\t\t\tif (this.checked == false) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先同意《隐私政策》和《用户服务协议》',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\twx.getUserProfile({\r\n\t\t\t\t\tdesc: '业务需要',\r\n\t\t\t\t\tsuccess: infoRes => {\r\n\t\t\t\t\t\tconsole.log(\"infoRes.encryptedData__________:\" + JSON.stringify(infoRes.userInfo))\r\n\t\t\t\t\t\tlet nickName = infoRes.userInfo.nickName; //昵称\r\n\t\t\t\t\t\tlet avatarUrl = infoRes.userInfo.avatarUrl; //头像\r\n\t\t\t\t\t\tlet sex = infoRes.userInfo.gender; //头像\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tthis.$queue.showLoading('正在登录中...');\r\n\t\t\t\t\t\t\tthis.login(nickName, avatarUrl, sex);\r\n\t\t\t\t\t\t} catch (e) {}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(e) {\r\n\t\t\t\t\t\tconsole.log(e, '1111111')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//登录\r\n\t\t\tlogin(nickName, avatarUrl, sex) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// 1.wx获取登录用户code\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: function(loginRes) {\r\n\t\t\t\t\t\tconsole.log(loginRes, '************')\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\tcode: loginRes.code,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.$Request.get('/app/Login/wxLogin', data).then(res => {\r\n\t\t\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('openId', res.data.open_id)\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('unionId', res.data.unionId)\r\n\t\t\t\t\t\t\t\tthat.sessionkey = res.data.session_key;\r\n\r\n\t\t\t\t\t\t\t\tlet inviterCode = '';\r\n\t\t\t\t\t\t\t\tif (uni.getStorageSync('inviterCode')) {\r\n\t\t\t\t\t\t\t\t\tinviterCode = uni.getStorageSync('inviterCode')\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tlet sendData = {\r\n\t\t\t\t\t\t\t\t\topenId: uni.getStorageSync('openId'),\r\n\t\t\t\t\t\t\t\t\tunionId: uni.getStorageSync('unionId'),\r\n\t\t\t\t\t\t\t\t\tuserName: nickName,\r\n\t\t\t\t\t\t\t\t\tavatar: avatarUrl,\r\n\t\t\t\t\t\t\t\t\tsex: sex, //性别\r\n\t\t\t\t\t\t\t\t\tinviterCode: inviterCode //别人登录进来携带你的邀请码\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\tthat.sendDataList = sendData;\r\n\t\t\t\t\t\t\t\tthat.flag = res.data.flag;\r\n\t\t\t\t\t\t\t\t// 第一次登录获取手机号\r\n\t\t\t\t\t\t\t\tconsole.log(that.flag, '-----------', that.phoneNum)\r\n\t\t\t\t\t\t\t\tif (that.flag == '1' && that.phoneNum) {\r\n\t\t\t\t\t\t\t\t\tthat.weixinPhone = true;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthat.getWeixinInfo(sendData);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tconsole.log(res, '失败')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//小程序微信登录后获取手机号\r\n\t\t\tgetPhoneNumber: function(e) {\r\n\t\t\t\tif (e.detail.errMsg == 'getPhoneNumber:fail user deny') {\r\n\t\t\t\t\tconsole.log('用户拒绝提供手机号');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('用户同意提供手机号');\r\n\t\t\t\t\tconsole.log(e)\r\n\t\t\t\t\tthis.setPhoneByInsert(e.detail.encryptedData, e.detail.iv);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//小程序微信登录后获取手机号\r\n\t\t\tsetPhoneByInsert(decryptData, iv) {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tdecryptData: decryptData,\r\n\t\t\t\t\tkey: this.sessionkey,\r\n\t\t\t\t\tiv: iv\r\n\t\t\t\t};\r\n\r\n\t\t\t\tthis.$Request.postJson('/app/Login/selectPhone', data).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tthis.phone = res.data.phoneNumber;\r\n\t\t\t\t\t\tthis.getWeixinInfo(this.sendDataList);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//获取个人信息\r\n\t\t\tgetWeixinInfo(sendData) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '登录中...'\r\n\t\t\t\t});\r\n\t\t\t\tlet postData = {\r\n\t\t\t\t\topenId: sendData.openId, //小程序openId\r\n\t\t\t\t\tunionId: sendData.unionId, //unionId\r\n\t\t\t\t\tuserName: sendData.userName, //微信名称\r\n\t\t\t\t\tavatar: sendData.avatar, //头像\r\n\t\t\t\t\tsex: sendData.sex, //性别\r\n\t\t\t\t\tphone: that.phone,\r\n\t\t\t\t\tinviterCode: sendData.inviterCode\r\n\t\t\t\t};\r\n\t\t\t\tthat.$Request.postJson('/app/Login/insertWxUser', postData).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tuni.setStorageSync('token', res.token)\r\n\t\t\t\t\t\tuni.setStorageSync('userName', res.user.userName)\r\n\t\t\t\t\t\tuni.setStorageSync('avatar', res.user.avatar)\r\n\t\t\t\t\t\tuni.setStorageSync('phone', res.user.phone)\r\n\t\t\t\t\t\tuni.setStorageSync('invitationCode', res.user.invitationCode)\r\n\t\t\t\t\t\tuni.setStorageSync('sex', res.user.sex)\r\n\t\t\t\t\t\tuni.setStorageSync('userId', res.user.userId)\r\n\t\t\t\t\t\tuni.setStorageSync('openId', res.user.openId)\r\n\r\n\t\t\t\t\t\tthis.$Request.get(\"/app/UserVip/isUserVip\").then(res => {\r\n\t\t\t\t\t\t\tif (res.code == 0 && res.data && res.data.isVip == 2) {\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('isVIP', true)\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('isVIP', false)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\ttitle: '登录失败',\r\n\t\t\t\t\t\t\tcontent: res.msg,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\tweixinLo() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: function(loginRes) {\r\n\t\t\t\t\t\tthat.$queue.showLoading('正在登录中...');\r\n\t\t\t\t\t\tconsole.error(loginRes.authResult);\r\n\t\t\t\t\t\tthat.$queue.setData('weixinToken', loginRes.authResult.access_token);\r\n\t\t\t\t\t\tthat.$queue.setData('unionid', loginRes.authResult.unionid);\r\n\t\t\t\t\t\tthat.$queue.setData('weixinOpenid', loginRes.authResult.openid);\r\n\t\t\t\t\t\tthat.$Request.postJson('/app/login/loginApp', {\r\n\t\t\t\t\t\t\ttoken: loginRes.authResult.access_token,\r\n\t\t\t\t\t\t\tunionid: loginRes.authResult.unionid,\r\n\t\t\t\t\t\t\topenid: loginRes.authResult.openid\r\n\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\tconsole.log(JSON.stringify(res))\r\n\t\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\t\tif (uni.getSystemInfoSync().platform == \"android\") {\r\n\t\t\t\t\t\t\t\t\tlet clientid = plus.push.getClientInfo().clientid;\r\n\t\t\t\t\t\t\t\t\tthat.$Request.postT('/app/login/updateClientId?clientId=' +\r\n\t\t\t\t\t\t\t\t\t\tclientid + '&userId=' + res.userId).then(res => {\r\n\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"token\", res.uuid);\r\n\t\t\t\t\t\t\t\tthat.$queue.setData(\"userId\", res.userId);\r\n\t\t\t\t\t\t\t\tthat.getUserInfo(res.userId, res.token);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/public/wxmobile'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tforget() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/public/pwd'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tregister() {\r\n\t\t\t\tif (!this.checked) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先同意《隐私政策》和《用户服务协议》',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/public/loginphone'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tinputChange(e) {\r\n\t\t\t\tconst key = e.currentTarget.dataset.key;\r\n\t\t\t\tthis[key] = e.detail.value;\r\n\t\t\t},\r\n\t\t\tnavBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\r\n\t\t\tgetUserInfo(userId, token) {\r\n\t\t\t\tthis.$Request.postJson('/app/selectUserById?userId=' + userId).then(res => {\r\n\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\tthis.$queue.setData('token', res.data.uuid);\r\n\t\t\t\t\t\tthis.$queue.setData('image_url', res.data.imageUrl ? res.data.imageUrl :\r\n\t\t\t\t\t\t\t'/static/img/common/logo.jpg');\r\n\t\t\t\t\t\tthis.$queue.setData('inviterCode', res.data.inviterCode);\r\n\t\t\t\t\t\tthis.$queue.setData('invitationCode', res.data.invitationCode);\r\n\t\t\t\t\t\tthis.$queue.setData('grade', res.data.grade);\r\n\t\t\t\t\t\tthis.$queue.setData('mobile', res.data.mobile);\r\n\t\t\t\t\t\tthis.$queue.setData('isInvitation', res.data.isInvitation);\r\n\t\t\t\t\t\tthis.$queue.setData('nickName', res.data.nickName ? res.data.nickName : res.data.phone);\r\n\t\t\t\t\t\tthis.$queue.setData('gender', parseInt(res.data.gender));\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\ttitle: '登录失败',\r\n\t\t\t\t\t\t\tcontent: res.msg\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.$queue.logout();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\theight: 100%;\r\n\t\tbackground: #FFFFFF !important;\r\n\t}\r\n\r\n\t.footer {\r\n\t\tpadding-left: 140upx;\r\n\t\tmargin-top: 32upx;\r\n\t\tfont-size: 24upx;\r\n\t\tcolor: #666666;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\r\n\t.container {\r\n\t\ttop: 0;\r\n\t\tpadding-top: 50px;\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #FFFFFF !important;\r\n\t\tcolor: #FFF;\r\n\t}\r\n\r\n\r\n\r\n\t.confirm-btn-weixin {\r\n\t\twidth: 200px;\r\n\t\theight: 42px;\r\n\t\tline-height: 42px;\r\n\t\tborder-radius: 30px;\r\n\t\tmargin-top: 40upx;\r\n\t\tbackground: -moz-linear-gradient(left, #f15b6c, #e10a07 100%);\r\n\t\tbackground: -webkit-gradient(linear, left top, left right, color-stop(0, #f15b6c), color-stop(100%, #e10a07));\r\n\t\tbackground: -webkit-linear-gradient(left, #f15b6c 0, #e10a07 100%);\r\n\t\tbackground: -o-linear-gradient(left, #f15b6c 0, #e10a07 100%);\r\n\t\tbackground: -ms-linear-gradient(left, #f15b6c 0, #e10a07 100%);\r\n\t\tbackground: linear-gradient(to left, #f15b6c 0, #e10a07 100%);\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32upx;\r\n\r\n\t\t&:after {\r\n\t\t\tborder-radius: 60px;\r\n\t\t}\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\twidth: 200px;\r\n\t\theight: 42px;\r\n\t\tline-height: 42px;\r\n\t\tborder-radius: 30px;\r\n\t\tmargin-top: 300upx;\r\n\t\tbackground: -moz-linear-gradient(left, #f15b6c, #e10a07 100%);\r\n\t\tbackground: -webkit-gradient(linear, left top, left right, color-stop(0, #f15b6c), color-stop(100%, #e10a07));\r\n\t\tbackground: -webkit-linear-gradient(left, #f15b6c 0, #e10a07 100%);\r\n\t\tbackground: -o-linear-gradient(left, #f15b6c 0, #e10a07 100%);\r\n\t\tbackground: -ms-linear-gradient(left, #f15b6c 0, #e10a07 100%);\r\n\t\tbackground: linear-gradient(to left, #f15b6c 0, #e10a07 100%);\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32upx;\r\n\r\n\t\t&:after {\r\n\t\t\tborder-radius: 60px;\r\n\t\t}\r\n\t}\r\n\r\n\t.headers {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.headers>image {\r\n\t\twidth: 400upx;\r\n\t\theight: 400upx;\r\n\t}\r\n\r\n\t.footer {\r\n\t\tpadding-left: 100upx;\r\n\t\tmargin-top: 32upx;\r\n\t\tfont-size: 24upx;\r\n\t\tcolor: #666666;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.send-msg {\r\n\t\tborder-radius: 30px;\r\n\t\tcolor: black;\r\n\t\tbackground: white;\r\n\t\theight: 30px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 30px;\r\n\t}\r\n\r\n\t.container {\r\n\t\ttop: 0;\r\n\t\tpadding-top: 32upx;\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t\tbackground: #ffffff;\r\n\t\tcolor: #FFF;\r\n\r\n\t\t.mp_wxBox {\r\n\t\t\t.headers {\r\n\t\t\t\tmargin: 35% auto 50rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-radius: 60rpx;\r\n\t\t\t\twidth: 650rpx;\r\n\t\t\t\theight: 300rpx;\r\n\t\t\t\tline-height: 450rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 300rpx;\r\n\t\t\t\t\theight: 300rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.content {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\ttext {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tcolor: #9d9d9d;\r\n\t\t\t\tmargin-top: 40rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.bottom {\r\n\t\t\t\tline-height: 80upx;\r\n\t\t\t\tborder-radius: 80upx;\r\n\t\t\t\tmargin: 70rpx 50rpx;\r\n\t\t\t\theight: 80upx;\r\n\t\t\t\tfont-size: 35rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.wrapper {\r\n\t\tposition: relative;\r\n\t\tz-index: 90;\r\n\t\tbackground: #fff;\r\n\t\tpadding-bottom: 20px;\r\n\t}\r\n\r\n\t.input-content {\r\n\t\tpadding: 0 20px;\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\twidth: 300px;\r\n\t\theight: 42px;\r\n\t\tline-height: 42px;\r\n\t\tborder-radius: 30px;\r\n\t\tmargin-top: 80px;\r\n\t\tbackground: linear-gradient(to left, #3f5ecb 0, #5074FF 100%);\r\n\t\tcolor: #fff;\r\n\t\t// font-size: $font-lg;\r\n\r\n\t\t&:after {\r\n\t\t\tborder-radius: 60px;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627797\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}