package com.sqx.modules.department.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.department.dao.DepartmentDao;
import com.sqx.modules.department.entity.Department;
import com.sqx.modules.department.service.DepartmentService;
import com.sqx.modules.department.vo.DepartmentIn;
import com.sqx.modules.department.vo.DepartmentOut;
import com.sqx.modules.hospital.entity.Hospital;
import com.sqx.modules.hospital.service.HospitalService;
import com.sqx.modules.utils.EasyPoi.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Slf4j
@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentDao, Department> implements DepartmentService {
    @Autowired
    private DepartmentDao departmentDao;
    @Autowired
    private HospitalService hospitalService;

    @Override
    public Result addDepartment(Department department) {
        Hospital hospital = hospitalService.getById(department.getHospitalId());
        if (department.getParentId() != 0) {
            Department parent = departmentDao.selectById(department.getParentId());
            if (parent == null) {
                return Result.error("上级分类不存在");
            }
        }
        department.setCreateTime(new Date());
        if (hospital == null) {
            return Result.error("选择的医院不存在!");
        }
        Integer count = departmentDao.selectCount(new QueryWrapper<Department>().eq("department_name", department.getDepartmentName()).eq("hospital_id", hospital.getHospitalId()).eq("parent_id", department.getParentId()));
        if (count > 0) {
            return Result.error("同一医院内科室名称不能重复");
        }
        return Result.upStatus(departmentDao.insert(department));
    }

    @Override
    public Result updateDepartment(Department department) {
        Department depart = departmentDao.selectOne(new QueryWrapper<Department>().eq("department_id", department.getDepartmentId()));
        if (depart == null) {
            return Result.error("当前科室不存在");
        }
        if (department.getParentId() != 0) {
            Department parent = departmentDao.selectById(department.getParentId());
            if (parent == null) {
                return Result.error("上级分类不存在");
            }
        }
        Integer count = departmentDao.selectCount(new QueryWrapper<Department>().eq("department_name", depart.getDepartmentName()).eq("hospital_id", depart.getHospitalId()).ne("department_id", depart.getDepartmentId()).eq("parent_id", department.getParentId()));
        if (count > 0) {
            return Result.error("同一医院内科室名称不能重复");
        }
        return Result.upStatus(departmentDao.updateById(department));
    }

    @Override
    public List<Department> getDepartmentList(Department department) {
        QueryWrapper<Department> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(department.getDepartmentName())) {
            wrapper.like("department_name", department.getDepartmentName());
        }
        if (department.getHospitalId() != null) {
            wrapper.eq("hospital_id", department.getHospitalId());
        }
        wrapper.eq("parent_id", 0);

        wrapper.orderByAsc("sort");
        List<Department> departmentList = baseMapper.selectList(wrapper);
        for (Department department1 : departmentList) {
            department1.setDepartmentsList(baseMapper.selectList(new QueryWrapper<Department>().eq("parent_id", department1.getDepartmentId()).eq("hospital_id", department1.getHospitalId()).orderByAsc("sort")));
        }
        return departmentList;
    }

    @Override
    public Result deleteDepartment(Long departmentId) {
        Department department = departmentDao.selectById(departmentId);
        if (department.getParentId() == 0) {
            int count = departmentDao.selectCount(new QueryWrapper<Department>().eq("parent_id", department.getDepartmentId()));
            if (count > 0) {
                return Result.error("请先删除下级分类");
            }
        }
        log.error("删除科室成功:科室信息:" + department);
        return Result.upStatus(departmentDao.deleteById(departmentId));


    }

    @Override
    public Department getDepartmentInfo(Long departmentId) {

        return departmentDao.selectById(departmentId);

    }

    @Override
    public IPage<Department> getDepartmentPageList(Integer page, Integer limit, Department department) {
        Page<Department> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }

        return baseMapper.selectPage(pages, new QueryWrapper<>(department).orderByAsc("sort"));
    }

    @Override
    public List<DepartmentOut> departmentExcelOut(String startTime, String endTime, Department department) {
        return baseMapper.departmentExcelOut(startTime, endTime, department);
    }

    @Override
    public Result departmentListExcelIn(MultipartFile file, Long hospitalId) throws IOException {
        List<Department> departmentList = ExcelUtils.importExcel(file, 2, 1, Department.class);
        if (CollectionUtils.isEmpty(departmentList)) {
            return Result.error("Excel数据为空，excel转化失败！");
        }
        //当前行索引（Excel的数据从第几行开始,就填写几）
        int index = 4;
        //失败条数
        int repeat = 0;
        //成功条数
        int successIndex = 0;
        for (Department department : departmentList) {
            Hospital hospital;
            if (department.getHospitalId() != null) {
                hospital = hospitalService.getById(department.getHospitalId());
            } else {
                hospital = hospitalService.getById(hospitalId);
            }
            if (department.getParentId() != 0) {
                //查询当前医院下是否有上级科室id
                Department parent = baseMapper.selectOne(new QueryWrapper<Department>().eq("hospital_id", hospital.getHospitalId()).eq("department_id", department.getParentId()));
                if (parent == null) {
                    return Result.error("第【" + index + "】行所属上级科室不存在,请检查【上级科室id】字段");
                }
            }
            index++;
            if (hospital == null) {
                return Result.error("第【" + index + "】行所属医院不存在,请检查【医院id】字段");
            }
        }
        for (Department department : departmentList) {
            Integer count = baseMapper.selectCount(new QueryWrapper<Department>().eq("hospital_id", hospitalId).eq("parent_id", department.getParentId()).eq("department_name", department.getDepartmentName()));
            if (count <= 0) {
                department.setHospitalId(hospitalId);
                department.setCreateTime(new Date());
                if (department.getIsEnable() == null) {
                    department.setIsEnable(1);
                }
                if (department.getSort() == null) {
                    department.setSort(0);
                }
                int result = baseMapper.insert(department);
                if (result > 0) {
                    successIndex++;
                }
            } else {
                repeat++;
            }

        }


        return Result.success("导入成功,共新增【" + successIndex + "】条,失败【" + (departmentList.size() - successIndex) + "】条,其中过滤重复数据【" + repeat + "】条");
    }
}
