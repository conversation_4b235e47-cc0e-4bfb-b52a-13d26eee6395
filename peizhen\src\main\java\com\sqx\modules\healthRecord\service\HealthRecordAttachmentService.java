package com.sqx.modules.healthRecord.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.healthRecord.entity.HealthRecordAttachment;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 健康记录附件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface HealthRecordAttachmentService extends IService<HealthRecordAttachment> {

    /**
     * 上传健康记录附件
     * @param recordId 健康记录ID
     * @param file 上传的文件
     * @param attachmentType 附件类型
     * @param description 附件描述
     * @param uploadedBy 上传人ID
     * @return 操作结果
     * @throws IOException 
     */
    Result uploadAttachment(Long recordId, MultipartFile file, Integer attachmentType, String description, Long uploadedBy) throws IOException;

    /**
     * 根据健康记录ID获取附件列表
     * @param recordId 健康记录ID
     * @return 附件列表
     */
    List<HealthRecordAttachment> getAttachmentsByRecordId(Long recordId);

    /**
     * 删除附件
     * @param attachmentId 附件ID
     * @param operatorId 操作人ID
     * @return 操作结果
     */
    Result deleteAttachment(Long attachmentId, Long operatorId);

    /**
     * 根据健康记录ID删除所有附件
     * @param recordId 健康记录ID
     * @param operatorId 操作人ID
     * @return 操作结果
     */
    Result deleteAttachmentsByRecordId(Long recordId, Long operatorId);

    /**
     * 下载附件
     * @param attachmentId 附件ID
     * @return 文件信息
     */
    HealthRecordAttachment getAttachmentForDownload(Long attachmentId);
}
