package com.sqx.modules.patientInfo.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import com.sqx.modules.patientInfo.service.PatientInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(value = "就诊人信息-管理端", tags = {"就诊人信息-管理端"})
@RequestMapping("/admin/patientInfo/")
public class AdminPatientInfoController {
    @Autowired
    private PatientInfoService patientInfoService;

    @ApiOperation("添加或修改就诊人信息")
    @PostMapping("savePatient")
    public Result savePatient(PatientInfo patientInfo) {
        return patientInfoService.savePatient(patientInfo);
    }

    @ApiOperation("获取指定人的就诊人列表")
    @GetMapping("getPatientList")
    public Result getPatientList(Integer page, Integer limit, PatientInfo patientInfo) {
        return Result.success().put("data", patientInfoService.getPatientList(page, limit, patientInfo));
    }

    @ApiOperation("获取就诊人信息")
    @GetMapping("getPatientInfo")
    public Result getPatientInfo(Long patientId) {
        return Result.success().put("data", patientInfoService.getPatientInfo(patientId));
    }


    @ApiOperation("删除就诊人")
    @GetMapping("deletePatient")
    public Result deletePatient(Long userId ,Long patientId) {
        return  patientInfoService.deletePatient(userId,patientId);
    }
}

