package com.sqx.modules.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.app.entity.UserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户
 */
@Mapper
public interface UserDao extends BaseMapper<UserEntity> {


    IPage<UserEntity> selectUserPage(@Param("page") Page<UserEntity> page, @Param("search") String search, @Param("sex") Integer sex, @Param("platform") String platform, @Param("sysPhone") String sysPhone, @Param("status") Integer status, @Param("isAuthentication") Integer isAuthentication, @Param("isPromotion") Integer isPromotion, @Param("isAgent") Integer isAgent, @Param("userName") String userName, @Param("invitationCode") String invitationCode, @Param("inviterCode") String inviterCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    int queryInviterCount(@Param("inviterCode") String inviterCode);

    int queryUserCount(@Param("type") int type, @Param("date") String date,String platform,Integer isAuthentication);

    Double queryPayMoney(@Param("type") int type, @Param("date") String date);

    IPage<Map<String, Object>> queryCourseOrder(Page iPage, @Param("type") int type, @Param("date") String date);

    int userMessage(String date, int type);

    int insertUser(UserEntity userEntity);

    IPage<Map<String, Object>> takingOrdersMessage(Page<Map<String, Object>> iPage, @Param("type") Long type, @Param("date") String date);

    IPage<UserEntity> getNursingList(@Param("pages") Page<UserEntity> pages, @Param("sex") Integer sex, @Param("workMin") Integer workMin, @Param("workMax") Integer workMax, @Param("ageMin") Integer ageMin, @Param("ageMax") Integer ageMax, @Param("city") String city);

    IPage<UserEntity> getNursingListV5(Page<UserEntity> pages, Integer sex, Integer workMin, Integer workMax, Integer ageMin, Integer ageMax, String city, Integer authentication, Integer orderCount, Integer finalScore, String realName, String isSafetyMoney, String phone);


    Integer cancelArea(@Param("userEntity") UserEntity userEntity);

    List<UserEntity> getUserByCityRider(String city);

    int updateAuthentication(Long userId);

    UserEntity queryAgentUser(String province, String city, String district);

    UserEntity getNursingInfo(@Param("userId") Long userId);

    List<UserEntity> selectUserList(String phone, Integer sex, String platform, String sysPhone, Integer status, Integer isAuthentication, Integer isPromotion, Integer isAgent, String userName, String invitationCode, String inviterCode, String startTime, String endTime, String search);

    int getInviteCount(@Param("invitationCode") String invitationCode);
}
