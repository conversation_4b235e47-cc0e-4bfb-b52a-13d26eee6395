/*
 Navicat Premium Dump SQL

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80017 (8.0.17)
 Source Host           : ************:3306
 Source Schema         : peizhen

 Target Server Type    : MySQL
 Target Server Version : 80017 (8.0.17)
 File Encoding         : 65001

 Date: 05/08/2025 14:33:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for patient_info
-- ----------------------------
DROP TABLE IF EXISTS `patient_info`;
CREATE TABLE `patient_info`  (
  `patient_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '信息id',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `is_under_age` int(1) NULL DEFAULT NULL COMMENT '是否已满18岁(1是 0否)',
  `sex` int(1) NULL DEFAULT NULL COMMENT '性别 1男 2女',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '电话号码',
  `id_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证号码',
  `relationship` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '就诊人关系',
  `is_delete` int(1) NULL DEFAULT 0 COMMENT '0未删除 1删除',
  `emergency_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '紧急联系人',
  `birth_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出生日期',
  `ethnicity` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '民族',
  `marital_status` int(1) NULL DEFAULT NULL COMMENT '婚姻状况(1未婚 2已婚 3离异 4丧偶)',
  `occupation` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职业',
  `employer` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作单位',
  `current_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '现住址',
  `registered_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '户籍地址',
  `insurance_type` int(1) NULL DEFAULT NULL COMMENT '医保类型(1城镇职工 2城镇居民 3新农合 4商业保险 5自费)',
  `insurance_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '医保卡号',
  `primary_contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主要联系人姓名',
  `primary_contact_relation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主要联系人关系',
  `primary_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主要联系人电话',
  `secondary_contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '次要联系人姓名',
  `secondary_contact_relation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '次要联系人关系',
  `secondary_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '次要联系人电话',
  `major_medical_history` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '既往重大疾病史摘要',
  `drug_allergy_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '药物过敏史摘要',
  `current_medication_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '当前主要用药摘要',
  `special_medical_needs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '特殊医疗需求',
  `risk_level` int(1) NULL DEFAULT 1 COMMENT '风险等级(1低风险 2中风险 3高风险)',
  `profile_completeness` int(3) NULL DEFAULT 0 COMMENT '医疗档案完整度(%)',
  `last_medical_update_time` datetime NULL DEFAULT NULL COMMENT '最后更新医疗信息时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`patient_id`) USING BTREE,
  INDEX `idx_patient_risk_completeness`(`risk_level` ASC, `profile_completeness` ASC) USING BTREE,
  INDEX `idx_patient_medical_update`(`last_medical_update_time` ASC) USING BTREE,
  INDEX `idx_patient_phone_name`(`phone` ASC, `real_name` ASC) USING BTREE,
  INDEX `idx_patient_id_number`(`id_number` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (197, '患者风险等级', '患者风险等级', NULL, NULL, 1, '患者医疗风险等级分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (198, '低风险', NULL, '1', '低风险', 1, '低风险', 197);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (199, '中风险', NULL, '2', '中风险', 2, '中风险', 197);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (200, '高风险', NULL, '3', '高风险', 3, '高风险', 197);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (201, '健康状态评级', '健康状态评级', NULL, NULL, 1, '患者整体健康状况评级', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (202, '优秀', NULL, '1', '优秀', 1, '优秀', 201);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (203, '良好', NULL, '2', '良好', 2, '良好', 201);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (204, '一般', NULL, '3', '一般', 3, '一般', 201);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (205, '较差', NULL, '4', '较差', 4, '较差', 201);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (206, '差', NULL, '5', '差', 5, '差', 201);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (207, '病史类型', '病史类型', NULL, NULL, 1, '医疗病史记录类型分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (208, '既往史', NULL, '1', '既往史', 1, '既往史', 207);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (209, '现病史', NULL, '2', '现病史', 2, '现病史', 207);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (210, '家族史', NULL, '3', '家族史', 3, '家族史', 207);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (211, '过敏史', NULL, '4', '过敏史', 4, '过敏史', 207);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (212, '手术史', NULL, '5', '手术史', 5, '手术史', 207);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (213, '过敏类型', '过敏类型', NULL, NULL, 1, '过敏反应类型分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (214, '药物过敏', 'allergy_type', '1', '药物过敏', 1, '药物过敏', 213);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (215, '食物过敏', NULL, '2', '食物过敏', 2, '食物过敏', 213);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (216, '环境过敏', NULL, '3', '环境过敏', 3, '环境过敏', 213);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (217, '接触过敏', NULL, '4', '接触过敏', 4, '接触过敏', 213);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (218, '其他过敏', NULL, '5', '其他过敏', 5, '其他过敏', 213);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (219, '过敏严重程度', '过敏严重程度', NULL, NULL, 1, '过敏反应严重程度分级', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (220, '轻微', NULL, '1', '轻微', 1, '轻微', 219);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (221, '中度', NULL, '2', '中度', 2, '中度', 219);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (222, '严重', NULL, '3', '严重', 3, '严重', 219);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (223, '危及生命', NULL, '4', '危及生命', 4, '危及生命', 219);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (224, '药物分类', '药物分类', NULL, NULL, 1, '药物类型分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (225, '处方药', NULL, '1', '处方药', 1, '处方药', 224);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (226, '非处方药', NULL, '2', '非处方药', 2, '非处方药', 224);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (227, '中药', NULL, '3', '中药', 3, '中药', 224);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (228, '生物制剂', NULL, '4', '生物制剂', 4, '生物制剂', 224);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (229, '疫苗', NULL, '5', '疫苗', 5, '疫苗', 224);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (230, '用药状态', '用药状态', NULL, NULL, 1, '患者用药状态', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (231, '正在使用', NULL, '1', '正在使用', 1, '正在使用', 230);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (232, '已停用', NULL, '2', '已停用', 2, '已停用', 230);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (233, '暂停使用', NULL, '3', '暂停使用', 3, '暂停使用', 230);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (234, '检查类型', '检查类型', NULL, NULL, 1, '医疗检查项目类型', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (235, '血液检查', NULL, '1', '血液检查', 1, '血液检查', 234);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (236, '影像检查', NULL, '2', '影像检查', 2, '影像检查', 234);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (237, '心电检查', NULL, '3', '心电检查', 3, '心电检查', 234);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (238, '内镜检查', NULL, '4', '内镜检查', 4, '内镜检查', 234);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (239, '病理检查', NULL, '5', '病理检查', 5, '病理检查', 234);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (240, '检查结果状态', '检查结果状态', NULL, NULL, 1, '检查结果状态分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (241, '正常', NULL, '1', '正常', 1, '正常', 240);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (242, '异常偏高', NULL, '2', '异常偏高', 2, '异常偏高', 240);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (243, '异常偏低', NULL, '3', '异常偏低', 3, '异常偏低', 240);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (244, '临界值', NULL, '4', '临界值', 4, '临界值', 240);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (245, '严重异常', NULL, '5', '严重异常', 5, '严重异常', 240);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (246, '疫苗类型', '疫苗类型', NULL, NULL, 1, '疫苗种类分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (247, '新冠疫苗', NULL, '1', '新冠疫苗', 1, '新冠疫苗', 246);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (248, '流感疫苗', NULL, '2', '流感疫苗', 2, '流感疫苗', 246);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (249, '乙肝疫苗', NULL, '3', '乙肝疫苗', 3, '乙肝疫苗', 246);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (250, 'HPV疫苗', NULL, '4', 'HPV疫苗', 4, 'HPV疫苗', 246);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (251, '其他疫苗', NULL, '5', '其他疫苗', 5, '其他疫苗', 246);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (252, '接种反应', '接种反应', NULL, NULL, 1, '疫苗接种反应分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (253, '无反应', NULL, '0', '无反应', 1, '无反应', 252);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (254, '轻微反应', NULL, '1', '轻微反应', 2, '轻微反应', 252);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (255, '中度反应', NULL, '2', '中度反应', 3, '中度反应', 252);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (256, '严重反应', NULL, '3', '严重反应', 4, '严重反应', 252);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (257, '治疗方案类型', '治疗方案类型', NULL, NULL, 1, '治疗方案类型分类', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (258, '药物治疗', NULL, '1', '药物治疗', 1, '药物治疗', 257);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (259, '手术治疗', NULL, '2', '手术治疗', 2, '手术治疗', 257);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (260, '物理治疗', NULL, '3', '物理治疗', 3, '物理治疗', 257);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (261, '心理治疗', NULL, '4', '心理治疗', 4, '心理治疗', 257);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (262, '综合治疗', NULL, '5', '综合治疗', 5, '综合治疗', 257);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (263, '治疗方案状态', '治疗方案状态', NULL, NULL, 1, '治疗方案执行状态', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (264, '计划中', NULL, '1', '计划中', 1, '计划中', 263);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (265, '进行中', NULL, '2', '进行中', 2, '进行中', 263);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (266, '已完成', NULL, '3', '已完成', 3, '已完成', 263);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (267, '暂停', NULL, '4', '暂停', 4, '暂停', 263);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (268, '终止', NULL, '5', '终止', 5, '终止', 263);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (269, '婚姻状况', '婚姻状况', NULL, NULL, 1, '患者婚姻状况', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (270, '未婚', NULL, '1', '未婚', 1, '未婚', 269);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (271, '已婚', NULL, '2', '已婚', 2, '已婚', 269);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (272, '离异', NULL, '3', '离异', 3, '离异', 269);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (273, '丧偶', NULL, '4', '丧偶', 4, '丧偶', 269);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (274, '医保类型', '医保类型', NULL, NULL, 1, '医疗保险类型', 0);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (275, '城镇职工', NULL, '1', '城镇职工', 1, '城镇职工', 274);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (276, '城镇居民', NULL, '2', '城镇居民', 2, '城镇居民', 274);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (277, '新农合', NULL, '3', '新农合', 3, '新农合', 274);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (278, '商业保险', NULL, '4', '商业保险', 4, '商业保险', 274);
INSERT INTO `peizhen`.`sys_dict` (`id`, `name`, `type`, `code`, `value`, `order_num`, `remark`, `parent_id`) VALUES (279, '自费', NULL, '5', '自费', 5, '自费', 274);
