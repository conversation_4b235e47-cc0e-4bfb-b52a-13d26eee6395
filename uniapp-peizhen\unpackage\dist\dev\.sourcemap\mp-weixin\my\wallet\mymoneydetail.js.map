{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/mymoneydetail.vue?e3d3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/mymoneydetail.vue?5725", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/mymoneydetail.vue?5a51", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/mymoneydetail.vue?3c3a", "uni-app:///my/wallet/mymoneydetail.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/mymoneydetail.vue?8cc2", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/wallet/mymoneydetail.vue?3e70"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "list", "page", "limit", "tabIndex", "checkReZhiShu", "checkReTuanZhang", "checkReFeiZhiShu", "scrollTop", "contentText", "contentdown", "contentrefresh", "contentnomore", "onLoad", "onPageScroll", "methods", "copyClick", "uni", "success", "title", "icon", "getList", "res", "item", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA4uB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eC0ChwB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;QACAjB;QACAkB;UACAD;YACAC;cACAD;gBACAE;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAnB;QACAC;MACA;MACA;QACA;UACAmB;YACA;cACAC;YACA;cACAA;YACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;QACAN;QACAA;MACA;IACA;EACA;EACAO;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAA+1C,CAAgB,qsCAAG,EAAC,C;;;;;;;;;;;ACAn3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/wallet/mymoneydetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/wallet/mymoneydetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mymoneydetail.vue?vue&type=template&id=6d6134b4&\"\nvar renderjs\nimport script from \"./mymoneydetail.vue?vue&type=script&lang=js&\"\nexport * from \"./mymoneydetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mymoneydetail.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/wallet/mymoneydetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mymoneydetail.vue?vue&type=template&id=6d6134b4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mymoneydetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mymoneydetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view style=\"text-align: left;padding-bottom: 10rpx;\">\n\t\t\t<view v-for=\"(item, index) in list\" :key=\"index\" class=\"item\">\n\t\t\t\t<view>\n\t\t\t\t\t<!-- <view style=\"margin-bottom: 8upx;text-align: right;\">\n\t\t\t\t\t\t<text v-if=\"item.type == 1\" style=\"margin-bottom: 8upx;color: #ecd4b4\">充值</text>\n\t\t\t\t\t\t<text v-if=\"item.type == 2\" style=\"margin-bottom: 8upx;color: #ecd4b4\">提现</text>\n\t\t\t\t\t</view> -->\n\t\t\t\t\t<view style=\"color: #999999;font-size: 28upx;\" >\n\t\t\t\t\t\t<view class=\"flex align-center\" style=\"margin-bottom: 8upx\" >\n\t\t\t\t\t\t{{item.title}}\n\t\t\t\t\t\t\t<image v-if=\"item.show==true\" @click.stop=\"copyClick(item.title)\" src=\"../static/copy.png\" style=\"width: 30rpx;height: 30rpx;margin-left: 5upx;\"\n\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <view v-if=\"item.classify === 2\" style=\"margin-bottom: 8upx\"> 返佣类型：直属返佣</view> -->\n\t\t\t\t\t\t<!-- <view v-if=\"item.classify === 3\" style=\"margin-bottom: 8upx\"> 返佣类型：非直属支付</view> -->\n\t\t\t\t\t\t<view style=\"margin-bottom: 8upx\">{{item.content}}</view>\n\t\t\t\t\t\t<view style=\"margin-bottom: 8upx\"> 创建时间：{{item.createTime}}</view>\n\t\t\t\t\t\t<view style=\"margin-bottom: 8upx;text-align: right;\">\n\t\t\t\t\t\t\t<text v-if=\"item.type == 1\" class=\"text-olive\"\n\t\t\t\t\t\t\t\tstyle=\"font-size: 32upx;font-weight: 600\"><text\n\t\t\t\t\t\t\t\t\tclass=\"text-olive\">+</text>{{item.money}}元</text>\n\t\t\t\t\t\t\t<text v-if=\"item.type == 2\" class=\"text-red\" style=\"font-size: 32upx;font-weight: 600\"><text\n\t\t\t\t\t\t\t\t\tclass=\"text-red\">-</text>{{item.money}}元</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 加载更多提示 -->\n\t\t\t<!-- <view class=\"s-col is-col-24\" v-if=\"list.length > 0\">\n\t\t\t\t<load-more :loadingType=\"loadingType\" :contentText=\"contentText\"></load-more>\n\t\t\t</view> -->\n\t\t\t<!-- 加载更多提示 -->\n\t\t\t<!-- <empty v-if=\"list.length === 0\" des=\"暂无明细数据\" show=\"false\"></empty> -->\n\t\t\t<empty v-if=\"list.length == 0\" content=\"暂无明细\"></empty>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport empty from '@/components/empty.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlist: [],\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10,\n\t\t\t\ttabIndex: 1,\n\t\t\t\tcheckReZhiShu: '否',\n\t\t\t\tcheckReTuanZhang: '否',\n\t\t\t\tcheckReFeiZhiShu: '否',\n\t\t\t\tscrollTop: false,\n\t\t\t\tcontentText: {\n\t\t\t\t\tcontentdown: '上拉显示更多',\n\t\t\t\t\tcontentrefresh: '正在加载...',\n\t\t\t\t\tcontentnomore: '没有更多数据了'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.$queue.showLoading(\"加载中...\");\n\t\t\tthis.getList();\n\t\t},\n\t\tonPageScroll: function(e) {\n\t\t\tthis.scrollTop = e.scrollTop > 200;\n\t\t},\n\t\tmethods: {\n\t\t\tcopyClick(copy) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: copy,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tuni.getClipboardData({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: \"复制成功\",\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetList() {\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tlet data = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit\n\t\t\t\t}\n\t\t\t\tthis.$Request.getT('/app/userMoney/balanceDetailed', data).then(res => {\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tres.data.list.map(item=>{\n\t\t\t\t\t\t\tif((item.title).indexOf('订单')!=-1){\n\t\t\t\t\t\t\t\titem.show = true\n\t\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\t\titem.show = false\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\tif (this.page === 1) {\n\t\t\t\t\t\t\tthis.list = res.data.list;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.list = [...this.list, ...res.data.list];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tthis.page = this.page + 1;\n\t\t\tthis.getList();\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\tthis.getList();\n\t\t}\n\t}\n</script>\n\n<style lang=\"less\">\n\tpage {\n\t\tbackground: #FFFFFF;\n\t}\n\n\t.tui-tab-item-title {\n\t\t// color: #ffffff;\n\t\tfont-size: 30rpx;\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tflex-wrap: nowrap;\n\t\twhite-space: nowrap;\n\t}\n\n\t.tui-tab-item-title-active {\n\t\tborder-bottom: 1px solid #557EFD;\n\t\tcolor: #557EFD;\n\t\tfont-size: 32upx;\n\t\tfont-weight: bold;\n\t\tborder-bottom-width: 6upx;\n\t\ttext-align: center;\n\t}\n\n\t.item {\n\t\tbackground: #FFFFFF;\n\t\tpadding: 32rpx;\n\t\tmargin: 32rpx;\n\t\tfont-size: 28rpx;\n\t\tbox-shadow: 7px 9px 34px rgba(0, 0, 0, 0.1);\n\t\tborder-radius: 16upx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mymoneydetail.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mymoneydetail.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447622574\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}