package com.sqx.modules.patientInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import com.sqx.modules.patientInfo.dao.PatientInfoDao;
import com.sqx.modules.patientInfo.service.PatientInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Service
public class PatientInfoServiceImpl extends ServiceImpl<PatientInfoDao, PatientInfo> implements PatientInfoService {

    @Autowired
    private PatientInfoDao patientInfoDao;
    @Autowired
    private UserService userService;

    @Override
    public Result savePatient(PatientInfo patientInfo) {
        UserEntity userEntity = userService.getById(patientInfo.getUserId());
        if (userEntity == null) {
            return Result.error("当前用户不存在");
        }
        if (patientInfo.getPatientId() == null) {
            Integer selectCount = patientInfoDao.selectCount(new QueryWrapper<PatientInfo>().eq("id_number", patientInfo.getIdNumber()).eq("user_id", patientInfo.getUserId()).eq("is_delete", 0));
            if (selectCount > 0) {
                return Result.error("身份证号码不能重复");
            }
            return Result.upStatus(patientInfoDao.insert(patientInfo));
        } else {
            Integer selectCount = patientInfoDao.selectCount(new QueryWrapper<PatientInfo>().eq("id_number", patientInfo.getIdNumber()).eq("user_id", patientInfo.getUserId()).ne("patient_id", patientInfo.getPatientId()).eq("is_delete", 0));
            if (selectCount > 0) {
                return Result.error("身份证号码不能重复");
            }
            return Result.upStatus(patientInfoDao.updateById(patientInfo));
        }

    }

    @Override
    public IPage<PatientInfo> getPatientList(Integer page, Integer limit, PatientInfo patientInfo) {
        Page<PatientInfo> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
             pages = new Page<>();
             pages.setSize(-1);
        }
        return patientInfoDao.getPatientList(pages, patientInfo);
    }

    @Override
    public PatientInfo getPatientInfo(Long patientId) {

        return patientInfoDao.selectById(patientId);

    }

    @Override
    public Result deletePatient(Long userId,Long patientId) {
        PatientInfo patientInfo = patientInfoDao.selectById(patientId);
        if(!userId.equals(patientInfo.getUserId())){
            return Result.error("无权操作");
        }
        patientInfo.setIsDelete(1);
        return Result.upStatus(patientInfoDao.updateById(patientInfo));


    }
}
