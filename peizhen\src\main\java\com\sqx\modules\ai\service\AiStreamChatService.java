package com.sqx.modules.ai.service;

import com.sqx.common.utils.Result;

/**
 * AI流式聊天服务接口
 */
public interface AiStreamChatService {
    
    /**
     * 发送消息并启动流式响应
     * @param userId 用户ID
     * @param conversationId 对话ID
     * @param message 用户消息
     * @param modelCode 模型代码
     * @return 包含taskId的结果
     */
    Result sendMessageStream(Long userId, Long conversationId, String message, String modelCode);
    
    /**
     * 获取流式消息的增量内容
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param fromIndex 起始分块序号（不包含）
     * @return 增量分块列表
     */
    Result getStreamChunks(Long userId, String taskId, Integer fromIndex);
    
    /**
     * 获取流式任务状态
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Result getStreamStatus(Long userId, String taskId);
    
    /**
     * 取消流式任务
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 操作结果
     */
    Result cancelStreamTask(Long userId, String taskId);

    /**
     * 清理已完成的流式任务
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 操作结果
     */
    Result cleanupTask(Long userId, String taskId);
}
