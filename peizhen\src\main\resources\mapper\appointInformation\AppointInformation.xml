<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.appointInformation.dao.AppointInformationDao">

    <select id="selectByOrdersId" resultType="com.sqx.modules.appointInformation.entity.AppointInformation">
        SELECT
            a.*,
            h.hospital_img,
            he.img,
            he.tags
        FROM
            hospital h,
            hospital_employ he,
            appoint_information a
                LEFT JOIN department d
                          ON d.department_id = a.department_id
        WHERE a.service_id = he.service_id
          AND a.hospital_id = h.hospital_id
          AND a.orders_id = #{ordersId}
    </select>
</mapper>