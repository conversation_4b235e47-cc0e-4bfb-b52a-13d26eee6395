package com.sqx.modules.task.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.task.entity.HelpOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2021/1/6
 */
@Mapper
public interface HelpOrderDao extends BaseMapper<HelpOrder> {

    IPage<HelpOrder> selectNewHelpOrderList(Page<HelpOrder> page,Long gameId,String latitude,String longitude,Integer sort);

    IPage<HelpOrder> selectHelpOrderByClassifyList(Page<HelpOrder> page,@Param("classifyId") Long classifyId,Long gameId);

    IPage<HelpOrder> selectHelpOrderByContentList(Page<HelpOrder> page,@Param("content") String content,Long gameId);

    IPage<HelpOrder> selectStatusHelpOrder(Page<HelpOrder> page,@Param("phone") String phone,@Param("content") String content,@Param("status") Integer status,Long gameId);

    Integer countHelpOrderByCreateTime(@Param("time") String time,@Param("flag") Integer flag);

    Double sumPrice(@Param("time") String time,@Param("flag") Integer flag);




}
