{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-popup/u-popup.vue?5498", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-popup/u-popup.vue?a6c7", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-popup/u-popup.vue?0684", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-popup/u-popup.vue?f7bd", "uni-app:///uview-ui/components/u-popup/u-popup.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-popup/u-popup.vue?3057", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-popup/u-popup.vue?5a83"], "names": ["name", "props", "show", "type", "default", "mode", "mask", "length", "zoom", "safeAreaInsetBottom", "maskCloseAble", "customStyle", "value", "popup", "borderRadius", "zIndex", "closeable", "closeIcon", "closeIconPos", "closeIconColor", "closeIconSize", "width", "height", "negativeTop", "maskCustomStyle", "duration", "data", "visibleSync", "showDrawer", "timer", "closeFromInner", "computed", "style", "transform", "centerStyle", "uZindex", "watch", "mounted", "methods", "getUnitValue", "maskClick", "close", "modeCenterClose", "open", "change"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wMAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAqvB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgDzwB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,gBAuBA;EACAA;EACAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAH;MACAC;IACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;EACA;EACAsB;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAA;UACAX;UACAC;UACAW;QACA;MACA;QACAD;UACAX;UACAC;UACAW;QACA;MACA;MACAD;MACA;MACA;QACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;QAAA;QAEA;QACAA;MACA;MACA;MACA;IACA;IACA;IACAE;MACA;MACAF;MACA;MACAA;MACAA;MACAA;MACA;QACAA;QACA;QACAA;MACA;MACA;IACA;IACA;IACAG;MACA;IACA;EACA;EACAC;IACAxB;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAyB;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA,kDACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;MACA;QAEA;UACA;UACA;QACA;MAQA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/UA;AAAA;AAAA;AAAA;AAA46C,CAAgB,svCAAG,EAAC,C;;;;;;;;;;;ACAh8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-popup/u-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-popup.vue?vue&type=template&id=17becaea&scoped=true&\"\nvar renderjs\nimport script from \"./u-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./u-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-popup.vue?vue&type=style&index=0&id=17becaea&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17becaea\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-popup/u-popup.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-popup.vue?vue&type=template&id=17becaea&scoped=true&\"", "var components\ntry {\n  components = {\n    uMask: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-mask/u-mask\" */ \"@/uview-ui/components/u-mask/u-mask.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.visibleSync\n    ? _vm.__get_style([\n        _vm.customStyle,\n        {\n          zIndex: _vm.uZindex - 1,\n        },\n      ])\n    : null\n  var s1 = _vm.visibleSync ? _vm.__get_style([_vm.style]) : null\n  var s2 =\n    _vm.visibleSync && _vm.mode == \"center\"\n      ? _vm.__get_style([_vm.centerStyle])\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-popup.vue?vue&type=script&lang=js&\"", "<template>\n\t<view v-if=\"visibleSync\" :style=\"[customStyle, {\n\t\tzIndex: uZindex - 1\n\t}]\" class=\"u-drawer\" hover-stop-propagation>\n\t\t<u-mask :duration=\"duration\" :custom-style=\"maskCustomStyle\" :maskClickAble=\"maskCloseAble\" :z-index=\"uZindex - 2\" :show=\"showDrawer && mask\" @click=\"maskClick\"></u-mask>\n\t\t<view\n\t\t\tclass=\"u-drawer-content\"\n\t\t\t@tap=\"modeCenterClose(mode)\"\n\t\t\t:class=\"[\n\t\t\t\tsafeAreaInsetBottom ? 'safe-area-inset-bottom' : '',\n\t\t\t\t'u-drawer-' + mode,\n\t\t\t\tshowDrawer ? 'u-drawer-content-visible' : '',\n\t\t\t\tzoom && mode == 'center' ? 'u-animation-zoom' : ''\n\t\t\t]\"\n\t\t\************************\n\t\t\******************\n\t\t\t:style=\"[style]\"\n\t\t>\n\t\t\t<view class=\"u-mode-center-box\" @tap.stop.prevent @touchmove.stop.prevent v-if=\"mode == 'center'\" :style=\"[centerStyle]\">\n\t\t\t\t<u-icon\n\t\t\t\t\t@click=\"close\"\n\t\t\t\t\tv-if=\"closeable\"\n\t\t\t\t\tclass=\"u-close\"\n\t\t\t\t\t:class=\"['u-close--' + closeIconPos]\"\n\t\t\t\t\t:name=\"closeIcon\"\n\t\t\t\t\t:color=\"closeIconColor\"\n\t\t\t\t\t:size=\"closeIconSize\"\n\t\t\t\t></u-icon>\n\t\t\t\t<scroll-view class=\"u-drawer__scroll-view\" scroll-y=\"true\">\n\t\t\t\t\t<slot />\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\t<scroll-view class=\"u-drawer__scroll-view\" scroll-y=\"true\" v-else>\n\t\t\t\t<slot />\n\t\t\t</scroll-view>\n\t\t\t<view @tap=\"close\" class=\"u-close\" :class=\"['u-close--' + closeIconPos]\">\n\t\t\t\t<u-icon\n\t\t\t\t\tv-if=\"mode != 'center' && closeable\"\n\t\t\t\t\t:name=\"closeIcon\"\n\t\t\t\t\t:color=\"closeIconColor\"\n\t\t\t\t\t:size=\"closeIconSize\"\n\t\t\t\t></u-icon>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n/**\n * popup 弹窗\n * @description 弹出层容器，用于展示弹窗、信息提示等内容，支持上、下、左、右和中部弹出。组件只提供容器，内部内容由用户自定义\n * @tutorial https://www.uviewui.com/components/popup.html\n * @property {String} mode 弹出方向（默认left）\n * @property {Boolean} mask 是否显示遮罩（默认true）\n * @property {Stringr | Number} length mode=left | 见官网说明（默认auto）\n * @property {Boolean} zoom 是否开启缩放动画，只在mode为center时有效（默认true）\n * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配（默认false）\n * @property {Boolean} mask-close-able 点击遮罩是否可以关闭弹出层（默认true）\n * @property {Object} custom-style 用户自定义样式\n * @property {Stringr | Number} negative-top 中部弹出时，往上偏移的值\n * @property {Numberr | String} border-radius 弹窗圆角值（默认0）\n * @property {Numberr | String} z-index 弹出内容的z-index值（默认1075）\n * @property {Boolean} closeable 是否显示关闭图标（默认false）\n * @property {String} close-icon 关闭图标的名称，只能uView的内置图标\n * @property {String} close-icon-pos 自定义关闭图标位置（默认top-right）\n * @property {String} close-icon-color 关闭图标的颜色（默认#909399）\n * @property {Number | String} close-icon-size 关闭图标的大小，单位rpx（默认30）\n * @event {Function} open 弹出层打开\n * @event {Function} close 弹出层收起\n * @example <u-popup v-model=\"show\"><view>出淤泥而不染，濯清涟而不妖</view></u-popup>\n */\nexport default {\n\tname: 'u-popup',\n\tprops: {\n\t\t/**\n\t\t * 显示状态\n\t\t */\n\t\tshow: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t/**\n\t\t * 弹出方向，left|right|top|bottom|center\n\t\t */\n\t\tmode: {\n\t\t\ttype: String,\n\t\t\tdefault: 'left'\n\t\t},\n\t\t/**\n\t\t * 是否显示遮罩\n\t\t */\n\t\tmask: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 抽屉的宽度(mode=left|right)，或者高度(mode=top|bottom)，单位rpx，或者\"auto\"\n\t\t// 或者百分比\"50%\"，表示由内容撑开高度或者宽度\n\t\tlength: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 'auto'\n\t\t},\n\t\t// 是否开启缩放动画，只在mode=center时有效\n\t\tzoom: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否开启底部安全区适配，开启的话，会在iPhoneX机型底部添加一定的内边距\n\t\tsafeAreaInsetBottom: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 是否可以通过点击遮罩进行关闭\n\t\tmaskCloseAble: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 用户自定义样式\n\t\tcustomStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {};\n\t\t\t}\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 此为内部参数，不在文档对外使用，为了解决Picker和keyboard等融合了弹窗的组件\n\t\t// 对v-model双向绑定多层调用造成报错不能修改props值的问题\n\t\tpopup: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 显示显示弹窗的圆角，单位rpx\n\t\tborderRadius: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 0\n\t\t},\n\t\tzIndex: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: ''\n\t\t},\n\t\t// 是否显示关闭图标\n\t\tcloseable: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 关闭图标的名称，只能uView的内置图标\n\t\tcloseIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: 'close'\n\t\t},\n\t\t// 自定义关闭图标位置，top-left为左上角，top-right为右上角，bottom-left为左下角，bottom-right为右下角\n\t\tcloseIconPos: {\n\t\t\ttype: String,\n\t\t\tdefault: 'top-right'\n\t\t},\n\t\t// 关闭图标的颜色\n\t\tcloseIconColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#909399'\n\t\t},\n\t\t// 关闭图标的大小，单位rpx\n\t\tcloseIconSize: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: '30'\n\t\t},\n\t\t// 宽度，只对左，右，中部弹出时起作用，单位rpx，或者\"auto\"\n\t\t// 或者百分比\"50%\"，表示由内容撑开高度或者宽度，优先级高于length参数\n\t\twidth: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 高度，只对上，下，中部弹出时起作用，单位rpx，或者\"auto\"\n\t\t// 或者百分比\"50%\"，表示由内容撑开高度或者宽度，优先级高于length参数\n\t\theight: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 给一个负的margin-top，往上偏移，避免和键盘重合的情况，仅在mode=center时有效\n\t\tnegativeTop: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 0\n\t\t},\n\t\t// 遮罩的样式，一般用于修改遮罩的透明度\n\t\tmaskCustomStyle: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {}\n\t\t\t}\n\t\t},\n\t\t// 遮罩打开或收起的动画过渡时间，单位ms\n\t\tduration: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 250\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tvisibleSync: false,\n\t\t\tshowDrawer: false,\n\t\t\ttimer: null,\n\t\t\tcloseFromInner: false, // value的值改变，是发生在内部还是外部\n\t\t};\n\t},\n\tcomputed: {\n\t\t// 根据mode的位置，设定其弹窗的宽度(mode = left|right)，或者高度(mode = top|bottom)\n\t\tstyle() {\n\t\t\tlet style = {};\n\t\t\t// 如果是左边或者上边弹出时，需要给translate设置为负值，用于隐藏\n\t\t\tif (this.mode == 'left' || this.mode == 'right') {\n\t\t\t\tstyle = {\n\t\t\t\t\twidth: this.width ? this.getUnitValue(this.width) : this.getUnitValue(this.length),\n\t\t\t\t\theight: '100%',\n\t\t\t\t\ttransform: `translate3D(${this.mode == 'left' ? '-100%' : '100%'},0px,0px)`\n\t\t\t\t};\n\t\t\t} else if (this.mode == 'top' || this.mode == 'bottom') {\n\t\t\t\tstyle = {\n\t\t\t\t\twidth: '100%',\n\t\t\t\t\theight: this.height ? this.getUnitValue(this.height) : this.getUnitValue(this.length),\n\t\t\t\t\ttransform: `translate3D(0px,${this.mode == 'top' ? '-100%' : '100%'},0px)`\n\t\t\t\t};\n\t\t\t}\n\t\t\tstyle.zIndex = this.uZindex;\n\t\t\t// 如果用户设置了borderRadius值，添加弹窗的圆角\n\t\t\tif (this.borderRadius) {\n\t\t\t\tswitch (this.mode) {\n\t\t\t\t\tcase 'left':\n\t\t\t\t\t\tstyle.borderRadius = `0 ${this.borderRadius}rpx ${this.borderRadius}rpx 0`;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'top':\n\t\t\t\t\t\tstyle.borderRadius = `0 0 ${this.borderRadius}rpx ${this.borderRadius}rpx`;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'right':\n\t\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx 0 0 ${this.borderRadius}rpx`;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'bottom':\n\t\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx ${this.borderRadius}rpx 0 0`;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t}\n\t\t\t\t// 不加可能圆角无效\n\t\t\t\tstyle.overflow = 'hidden';\n\t\t\t}\n\t\t\tif(this.duration) style.transition = `all ${this.duration / 1000}s linear`;\n\t\t\treturn style;\n\t\t},\n\t\t// 中部弹窗的特有样式\n\t\tcenterStyle() {\n\t\t\tlet style = {};\n\t\t\tstyle.width = this.width ? this.getUnitValue(this.width) : this.getUnitValue(this.length);\n\t\t\t// 中部弹出的模式，如果没有设置高度，就用auto值，由内容撑开高度\n\t\t\tstyle.height = this.height ? this.getUnitValue(this.height) : 'auto';\n\t\t\tstyle.zIndex = this.uZindex;\n\t\t\tstyle.marginTop = `-${this.$u.addUnit(this.negativeTop)}`;\n\t\t\tif (this.borderRadius) {\n\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx`;\n\t\t\t\t// 不加可能圆角无效\n\t\t\t\tstyle.overflow = 'hidden';\n\t\t\t}\n\t\t\treturn style;\n\t\t},\n\t\t// 计算整理后的z-index值\n\t\tuZindex() {\n\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\n\t\t}\n\t},\n\twatch: {\n\t\tvalue(val) {\n\t\t\tif (val) {\n\t\t\t\tthis.open();\n\t\t\t} else if(!this.closeFromInner) {\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t\tthis.closeFromInner = false;\n\t\t}\n\t},\n\tmounted() {\n\t\t// 组件渲染完成时，检查value是否为true，如果是，弹出popup\n\t\tthis.value && this.open();\n\t},\n    methods: {\n\t\t// 判断传入的值，是否带有单位，如果没有，就默认用rpx单位\n\t\tgetUnitValue(val) {\n\t\t\tif(/(%|px|rpx|auto)$/.test(val)) return val;\n\t\t\telse return val + 'rpx'\n\t\t},\n\t\t// 遮罩被点击\n\t\tmaskClick() {\n\t\t\tthis.close();\n\t\t},\n\t\tclose() {\n\t\t\t// 标记关闭是内部发生的，否则修改了value值，导致watch中对value检测，导致再执行一遍close\n\t\t\t// 造成@close事件触发两次\n\t\t\tthis.closeFromInner = true;\n\t\t\tthis.change('showDrawer', 'visibleSync', false);\n\t\t},\n\t\t// 中部弹出时，需要.u-drawer-content将居中内容，此元素会铺满屏幕，点击需要关闭弹窗\n\t\t// 让其只在mode=center时起作用\n\t\tmodeCenterClose(mode) {\n\t\t\tif (mode != 'center' || !this.maskCloseAble) return;\n\t\t\tthis.close();\n\t\t},\n\t\topen() {\n\t\t\tthis.change('visibleSync', 'showDrawer', true);\n\t\t},\n\t\t// 此处的原理是，关闭时先通过动画隐藏弹窗和遮罩，再移除整个组件\n\t\t// 打开时，先渲染组件，延时一定时间再让遮罩和弹窗的动画起作用\n\t\tchange(param1, param2, status) {\n\t\t\t// 如果this.popup为false，意味着为picker，actionsheet等组件调用了popup组件\n\t\t\tif (this.popup == true) {\n\t\t\t\tthis.$emit('input', status);\n\t\t\t}\n\t\t\tthis[param1] = status;\n\t\t\tif(status) {\n\t\t\t\t// #ifdef H5 || MP\n\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\tthis[param2] = status;\n\t\t\t\t\tthis.$emit(status ? 'open' : 'close');\n\t\t\t\t}, 50);\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef H5 || MP\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis[param2] = status;\n\t\t\t\t\tthis.$emit(status ? 'open' : 'close');\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t} else {\n\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\tthis[param2] = status;\n\t\t\t\t\tthis.$emit(status ? 'open' : 'close');\n\t\t\t\t}, this.duration);\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../libs/css/style.components.scss\";\n\n.u-drawer {\n\t/* #ifndef APP-NVUE */\n\tdisplay: block;\n\t/* #endif */\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\toverflow: hidden;\n}\n\n.u-drawer-content {\n\t/* #ifndef APP-NVUE */\n\tdisplay: block;\n\t/* #endif */\n\tposition: absolute;\n\tz-index: 1003;\n\ttransition: all 0.25s linear;\n}\n\n.u-drawer__scroll-view {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.u-drawer-left {\n\ttop: 0;\n\tbottom: 0;\n\tleft: 0;\n\tbackground-color: #ffffff;\n}\n\n.u-drawer-right {\n\tright: 0;\n\ttop: 0;\n\tbottom: 0;\n\tbackground-color: #ffffff;\n}\n\n.u-drawer-top {\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: #ffffff;\n}\n\n.u-drawer-bottom {\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: #ffffff;\n}\n\n.u-drawer-center {\n\t@include vue-flex;\n\tflex-direction: column;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\ttop: 0;\n\tjustify-content: center;\n\talign-items: center;\n\topacity: 0;\n\tz-index: 99999;\n}\n\n.u-mode-center-box {\n\tmin-width: 100rpx;\n\tmin-height: 100rpx;\n\t/* #ifndef APP-NVUE */\n\tdisplay: block;\n\t/* #endif */\n\tposition: relative;\n\t// background-color: #ffffff;\n}\n\n.u-drawer-content-visible.u-drawer-center {\n\ttransform: scale(1);\n\topacity: 1;\n}\n\n.u-animation-zoom {\n\ttransform: scale(1.15);\n}\n\n.u-drawer-content-visible {\n\ttransform: translate3D(0px, 0px, 0px) !important;\n}\n\n.u-close {\n\tposition: absolute;\n\tz-index: 3;\n}\n\n.u-close--top-left {\n\ttop: 30rpx;\n\tleft: 30rpx;\n}\n\n.u-close--top-right {\n\ttop: 30rpx;\n\tright: 30rpx;\n}\n\n.u-close--bottom-left {\n\tbottom: 30rpx;\n\tleft: 30rpx;\n}\n\n.u-close--bottom-right {\n\tright: 30rpx;\n\tbottom: 30rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-popup.vue?vue&type=style&index=0&id=17becaea&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-popup.vue?vue&type=style&index=0&id=17becaea&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627691\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}