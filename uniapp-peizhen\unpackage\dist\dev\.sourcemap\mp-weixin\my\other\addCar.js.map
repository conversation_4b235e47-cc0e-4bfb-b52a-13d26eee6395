{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/addCar.vue?aa41", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/addCar.vue?79bf", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/addCar.vue?3c03", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/addCar.vue?506f", "uni-app:///my/other/addCar.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/addCar.vue?f560", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/other/addCar.vue?df8a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "emergencyPhone", "value", "isUnderAge", "list", "id", "name", "show", "listsex", "label", "sex", "sexTit", "realName", "phone", "idNumber", "relationship", "patientId", "shows", "relationshipList", "onLoad", "onShow", "methods", "getlist", "type", "arr", "selConfirm", "console", "radioChange", "confirm", "navgo", "uni", "url", "submit", "title", "icon", "setTimeout", "getcarDetal"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAquB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoFzvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MAEAC;MACAC;QACAN;QACAO;MACA,GACA;QACAP;QACAO;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;IACA;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;UACA;UACA;YACA;YACAvB;YACAA;YACAwB;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACAF;MACA;MACA;IACA;IACAG;MACAC;QACAC;MACA;IACA;IACAC;MACA;QACAF;UACAG;UACAC;QACA;QACA;MACA;MAEA;QACAJ;UACAG;UACAC;QACA;QACA;MACA;MACA;QACAJ;UACAG;UACAC;QACA;QACA;MACA;MACA;QACAJ;UACAG;UACAC;QACA;QACA;MACA;MACA;QACAJ;UACAG;UACAC;QACA;QACA;MACA;MACA;QACAJ;UACAG;UACAC;QACA;QACA;MACA;MAEA;QACA;UACA/B;UACAO;UACAE;UACAC;UACAC;UACAC;UACAC;UACAf;QACA;QACA;UACA;YACA6B;cACAG;cACAC;YACA;YACAC;cACAL;YACA;UACA;YACAA;cACAG;cACAC;YAEA;UACA;QACA;MACA;QACA;UACA/B;UACAO;UACAE;UACAC;UACAC;UACAC;UACAd;QACA;QACA;UACA;YACA6B;cACAG;cACAC;YACA;YACAC;cACAL;YACA;UAEA;YACAA;cACAG;cACAC;YAEA;UACA;QACA;MACA;IAEA;IACAE;MAAA;MACA;QACApB;MACA;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;YACA;UACA;YACA;UACA;UACA;YACA;UACA;YACA;UACA;QAGA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjTA;AAAA;AAAA;AAAA;AAAw1C,CAAgB,8rCAAG,EAAC,C;;;;;;;;;;;ACA52C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/other/addCar.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/other/addCar.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./addCar.vue?vue&type=template&id=4728c192&\"\nvar renderjs\nimport script from \"./addCar.vue?vue&type=script&lang=js&\"\nexport * from \"./addCar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./addCar.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/other/addCar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addCar.vue?vue&type=template&id=4728c192&\"", "var components\ntry {\n  components = {\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-radio/u-radio\" */ \"@/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSelect: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-select/u-select\" */ \"@/uview-ui/components/u-select/u-select.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.shows = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addCar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addCar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<!-- 表单 -->\n\t\t<view class=\"from\">\n\t\t\t<view class=\"from-box\">\n\t\t\t\t<view class=\"from-box-con\">\n\t\t\t\t\t<view class=\"from-box-con-item\">\n\t\t\t\t\t\t<view class=\"tite\">就诊人是否已满18周岁</view>\n\t\t\t\t\t\t<view class=\"from-box-con-item-r\">\n\t\t\t\t\t\t\t<u-radio-group v-model=\"value\">\n\t\t\t\t\t\t\t\t<u-radio @change=\"radioChange\" v-for=\"(item, index) in list\" :key=\"index\"\n\t\t\t\t\t\t\t\t\t:name=\"item.name\" :disabled=\"item.disabled\">\n\t\t\t\t\t\t\t\t\t{{item.name}}\n\t\t\t\t\t\t\t\t</u-radio>\n\t\t\t\t\t\t\t</u-radio-group>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"from-box-con-item\" >\n\t\t\t\t\t\t<view class=\"tite\">性别</view>\n\t\t\t\t\t\t<view class=\"from-box-con-item-r\">\n\t\t\t\t\t\t\t<u-input placeholder=\"请选择就诊人性别\" @click=\"show=true\" v-model=\"sexTit\" fontSize=\"12\" inputAlign=\"right\"\n\t\t\t\t\t\t\t\t:clearable=\"false\" :disabled=\"true\">\n\t\t\t\t\t\t\t</u-input>\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#CCCCCC\" size=\"28\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"from-box-con-item\">\n\t\t\t\t\t\t<view class=\"tite\">姓名</view>\n\t\t\t\t\t\t<view class=\"from-box-con-item-r\">\n\t\t\t\t\t\t\t<u-input placeholder=\"请输入就诊人姓名\" v-model=\"realName\" style=\"margin-right: 25rpx;\"\n\t\t\t\t\t\t\t\tfontSize=\"12\" inputAlign=\"right\" :clearable=\"false\">\n\t\t\t\t\t\t\t</u-input>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"from-box-con-item\">\n\t\t\t\t\t\t<view class=\"tite\">电话</view>\n\t\t\t\t\t\t<view class=\"from-box-con-item-r\">\n\t\t\t\t\t\t\t<u-input type=\"number\" placeholder=\"请输入就诊电话\" maxlength=\"11\" v-model=\"phone\" style=\"margin-right: 25rpx;\" fontSize=\"12\"\n\t\t\t\t\t\t\t\tinputAlign=\"right\" :clearable=\"false\">\n\t\t\t\t\t\t\t</u-input>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"from-box-con-item\">\n\t\t\t\t\t\t<view class=\"tite\">身份证</view>\n\t\t\t\t\t\t<view class=\"from-box-con-item-r\">\n\t\t\t\t\t\t\t<u-input placeholder=\"请输入身份证\" v-model=\"idNumber\" style=\"margin-right: 25rpx;\" fontSize=\"12\"\n\t\t\t\t\t\t\t\tinputAlign=\"right\" :clearable=\"false\">\n\t\t\t\t\t\t\t</u-input>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"from-box-con-item\" >\n\t\t\t\t\t\t<view class=\"tite\">就诊人关系</view>\n\t\t\t\t\t\t<view class=\"from-box-con-item-r\" >\n\t\t\t\t\t\t\t<u-input placeholder=\"请选择就诊关系\" @click=\"shows = true\" v-model=\"relationship\" \n\t\t\t\t\t\t\t\tfontSize=\"12\" inputAlign=\"right\" :clearable=\"false\" :disabled=\"true\">\n\t\t\t\t\t\t\t</u-input>\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#CCCCCC\" size=\"28\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"from-box-con-item\" style=\"border: none;\">\n\t\t\t\t\t\t<view class=\"tite\">紧急联系人</view>\n\t\t\t\t\t\t<view class=\"from-box-con-item-r\" style=\"width: 80%;\">\n\t\t\t\t\t\t\t<u-input type=\"number\" placeholder=\"请输入紧急联系人电话(选填)\" maxlength=\"11\" v-model=\"emergencyPhone\" style=\"margin-right: 25rpx;\" fontSize=\"12\"\n\t\t\t\t\t\t\t\tinputAlign=\"right\" :clearable=\"false\">\n\t\t\t\t\t\t\t</u-input>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-select v-model=\"show\" :list=\"listsex\" @confirm=\"confirm\"></u-select>\n\n\t\t<u-select v-model=\"shows\" :list=\"relationshipList\" @confirm=\"selConfirm\"></u-select>\n\n\t\t<!-- 添加就诊人 -->\n\t\t<view class=\"addCar\">\n\t\t\t<!-- <view class=\"addCar-box\" @click=\"submit()\" v-if=\"!carId\">保存</view> -->\n\t\t\t<view class=\"addCar-box\" @click=\"submit()\">保存</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\temergencyPhone:'',\n\t\t\t\tvalue: '是',\n\t\t\t\tisUnderAge: '1',\n\t\t\t\tlist: [{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tname: '是'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\tname: '否'\n\t\t\t\t\t}\n\t\t\t\t],\n\n\t\t\t\tshow: false,\n\t\t\t\tlistsex: [{\n\t\t\t\t\t\tvalue: '1',\n\t\t\t\t\t\tlabel: '男'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tvalue: '2',\n\t\t\t\t\t\tlabel: '女'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tsex: '',\n\t\t\t\tsexTit: '',\n\t\t\t\trealName: '',\n\t\t\t\tphone: '',\n\t\t\t\tidNumber: '',\n\t\t\t\trelationship: '',\n\t\t\t\tpatientId: '',\n\t\t\t\tshows: false,\n\t\t\t\trelationshipList: []\n\t\t\t};\n\t\t},\n\t\tonLoad(option) {\n\t\t\tif (option.patientId) {\n\t\t\t\tthis.patientId = option.patientId\n\t\t\t\tthis.getcarDetal()\n\t\t\t}\n\t\t\tthis.userId = this.$queue.getData(\"userId\");\n\t\t\tthis.getlist()\n\t\t},\n\t\tonShow() {\n\n\t\t},\n\t\tmethods: {\n\t\t\tgetlist() {\n\t\t\t\tlet data = {\n\t\t\t\t\ttype: '就诊人关系'\n\t\t\t\t}\n\t\t\t\tthis.$Request.get(\"/app/dict/selectDictList\", data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tlet dictist = res.data\n\t\t\t\t\t\tvar arr = []\n\t\t\t\t\t\tfor (let i = 0; i < dictist.length; i++) {\n\t\t\t\t\t\t\tvar data = {}\n\t\t\t\t\t\t\tdata.label = dictist[i].value\n\t\t\t\t\t\t\tdata.value = i\n\t\t\t\t\t\t\tarr.push(data)\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// console.log(arr)\n\t\t\t\t\t\tthis.relationshipList = arr\n\t\t\t\t\t\t// console.log(this.relationshipList)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tselConfirm(e) {\n\t\t\t\tconsole.log(e, '--------', e[0].label)\n\t\t\t\tthis.relationship = e[0].label\n\t\t\t},\n\t\t\tradioChange(e) {\n\t\t\t\tif (e == '是') {\n\t\t\t\t\tthis.isUnderAge = 1\n\t\t\t\t} else if (e == '否') {\n\t\t\t\t\tthis.isUnderAge = 2\n\t\t\t\t}\n\t\t\t},\n\t\t\tconfirm(e) {\n\t\t\t\tconsole.log(e, '--------')\n\t\t\t\tthis.sexTit = e[0].label\n\t\t\t\tthis.sex = e[0].value\n\t\t\t},\n\t\t\tnavgo() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/my/other/index'\n\t\t\t\t})\n\t\t\t},\n\t\t\tsubmit() {\n\t\t\t\tif (!this.sexTit) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择性别',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!this.realName) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入就诊人姓名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.phone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入就诊电话',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif(this.phone.length != 11) {\n \t\t\t\t\tuni.showToast({\n \t\t\t\t\t\ttitle: '请输入正确就诊电话',\n \t\t\t\t\t\ticon: 'none'\n \t\t\t\t\t})\n \t\t\t\t\treturn\n \t\t\t\t}\n\t\t\t\tif (!this.idNumber) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入身份证',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.relationship) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择就诊关系',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (this.patientId) {\n\t\t\t\t\tlet data = {\n\t\t\t\t\t\tisUnderAge: this.isUnderAge,\n\t\t\t\t\t\tsex: this.sex,\n\t\t\t\t\t\trealName: this.realName,\n\t\t\t\t\t\tphone: this.phone,\n\t\t\t\t\t\tidNumber: this.idNumber,\n\t\t\t\t\t\trelationship: this.relationship,\n\t\t\t\t\t\tpatientId: this.patientId,\n\t\t\t\t\t\temergencyPhone:this.emergencyPhone\n\t\t\t\t\t}\n\t\t\t\t\tthis.$Request.post(\"/app/patientInfo/savePatient\", data).then(res => {\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '修改成功！',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tsetTimeout(()=>{\n\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t},1500)\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle:res.msg,\n\t\t\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tlet data = {\n\t\t\t\t\t\tisUnderAge: this.isUnderAge,\n\t\t\t\t\t\tsex: this.sex,\n\t\t\t\t\t\trealName: this.realName,\n\t\t\t\t\t\tphone: this.phone,\n\t\t\t\t\t\tidNumber: this.idNumber,\n\t\t\t\t\t\trelationship: this.relationship,\n\t\t\t\t\t\temergencyPhone:this.emergencyPhone\n\t\t\t\t\t}\n\t\t\t\t\tthis.$Request.post(\"/app/patientInfo/savePatient\", data).then(res => {\n\t\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '添加成功！',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tsetTimeout(()=>{\n\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t},1500)\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t}else{\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle:res.msg,\n\t\t\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t},\n\t\t\tgetcarDetal() {\n\t\t\t\tlet data = {\n\t\t\t\t\tpatientId: this.patientId\n\t\t\t\t}\n\t\t\t\tthis.$Request.get(\"/app/patientInfo/getPatientInfo\", data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.realName = res.data.realName\n\t\t\t\t\t\tthis.phone = res.data.phone\n\t\t\t\t\t\tthis.idNumber = res.data.idNumber\n\t\t\t\t\t\tthis.relationship = res.data.relationship\n\t\t\t\t\t\tthis.emergencyPhone = res.data.emergencyPhone\n\t\t\t\t\t\tif (res.data.isUnderAge == 1) {\n\t\t\t\t\t\t\tthis.value = '是'\n\t\t\t\t\t\t} else if (res.data.isUnderAge == 2) {\n\t\t\t\t\t\t\tthis.value = '否'\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (res.data.sex == 1) {\n\t\t\t\t\t\t\tthis.sexTit = '男'\n\t\t\t\t\t\t} else if (res.data.sex == 2) {\n\t\t\t\t\t\t\tthis.sexTit = '女'\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"less\">\n\tpage {\n\t\tbackground-color: #F5F5F5;\n\t}\n\n\t.from {\n\t\twidth: 100%;\n\t\theight: auto;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-top: 20rpx;\n\n\t\t.from-box {\n\t\t\twidth: 686rpx;\n\t\t\theight: 100%;\n\t\t\tborder-radius: 24rpx;\n\t\t\tbackground-color: #ffffff;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\n\t\t\t.from-box-con {\n\t\t\t\twidth: 612rpx;\n\t\t\t\theight: 100%;\n\t\t\t}\n\n\t\t\t.from-box-con-item {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 106rpx;\n\t\t\t\tborder-bottom: 1rpx solid #F2F2F2;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\n\n\n\t\t\t\t.from-box-con-item-r {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tfont-size: 28upx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.tite {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: bold;\n\t\twidth: 300upx;\n\t}\n\n\t.addCar {\n\t\twidth: 100%;\n\t\theight: 150rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tbackground-color: #F5F5F5;\n\t\tz-index: 999;\n\n\t\t.addCar-box {\n\t\t\twidth: 686rpx;\n\t\t\theight: 88rpx;\n\t\t\tborder-radius: 44rpx;\n\t\t\tbackground-color: #0175FE;\n\t\t\tcolor: #FFFFFF;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: bold;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t}\n\t}\n\n\t.u-input--border {\n\t\tborder: none !important;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addCar.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addCar.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621954\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}