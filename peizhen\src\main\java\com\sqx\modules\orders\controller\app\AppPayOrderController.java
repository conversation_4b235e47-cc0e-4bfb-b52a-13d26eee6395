package com.sqx.modules.orders.controller.app;

import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.orders.service.PayOrderService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping("/app/appOrder")
@AllArgsConstructor
@Api(value = "app充值订单", tags = {"app充值订单"})
public class AppPayOrderController {
    private PayOrderService orderService;

    /**
     * app充值订单
     *
     * @param userId
     * @param money
     * @return
     */
    @Login
    @RequestMapping(value = "insertOrder", method = RequestMethod.POST)
    public Result insertOrder(@RequestAttribute Long userId,Long payWay, BigDecimal money) {

        return orderService.insertOrder(userId, money);
    }


}
