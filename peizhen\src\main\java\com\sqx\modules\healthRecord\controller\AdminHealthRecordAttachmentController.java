package com.sqx.modules.healthRecord.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.healthRecord.entity.HealthRecordAttachment;
import com.sqx.modules.healthRecord.service.HealthRecordAttachmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 健康记录附件管理-管理端 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@Api(value = "健康记录附件管理-管理端", tags = {"健康记录附件管理-管理端"})
@RequestMapping("/admin/healthRecord/attachment/")
public class AdminHealthRecordAttachmentController {

    @Autowired
    private HealthRecordAttachmentService attachmentService;

    @ApiOperation("上传健康记录附件")
    @PostMapping("upload")
    public Result uploadAttachment(
            @ApiParam("健康记录ID") @RequestParam Long recordId,
            @ApiParam("上传文件") @RequestParam("file") MultipartFile file,
            @ApiParam("附件类型") @RequestParam(required = false, defaultValue = "6") Integer attachmentType,
            @ApiParam("附件描述") @RequestParam(required = false) String description,
            @ApiParam("上传人ID") @RequestParam(required = false, defaultValue = "1") Long uploadedBy) throws IOException {
        
        return attachmentService.uploadAttachment(recordId, file, attachmentType, description, uploadedBy);
    }

    @ApiOperation("获取健康记录附件列表")
    @GetMapping("getAttachmentsByRecordId")
    public Result getAttachmentsByRecordId(@ApiParam("健康记录ID") Long recordId) {
        List<HealthRecordAttachment> attachments = attachmentService.getAttachmentsByRecordId(recordId);
        return Result.success().put("data", attachments);
    }

    @ApiOperation("删除附件")
    @GetMapping("deleteAttachment")
    public Result deleteAttachment(
            @ApiParam("附件ID") Long attachmentId,
            @ApiParam("操作人ID") Long operatorId) {
        return attachmentService.deleteAttachment(attachmentId, operatorId);
    }

    @ApiOperation("根据健康记录ID删除所有附件")
    @GetMapping("deleteAttachmentsByRecordId")
    public Result deleteAttachmentsByRecordId(
            @ApiParam("健康记录ID") Long recordId,
            @ApiParam("操作人ID") Long operatorId) {
        return attachmentService.deleteAttachmentsByRecordId(recordId, operatorId);
    }

    @ApiOperation("下载附件")
    @GetMapping("download")
    public void downloadAttachment(
            @ApiParam("附件ID") Long attachmentId,
            HttpServletResponse response) throws IOException {
        
        HealthRecordAttachment attachment = attachmentService.getAttachmentForDownload(attachmentId);
        if (attachment == null) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }

        // 设置响应头
        response.setContentType(attachment.getMimeType());
        response.setHeader("Content-Disposition", 
            "attachment; filename=\"" + attachment.getFileOriginalName() + "\"");
        
        // 对于 OSS 文件，可以直接重定向到文件URL
        response.sendRedirect(attachment.getFileUrl());
    }
}
