<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.ai.dao.AiModelConfigDao">

    <!-- 查询启用的模型列表 -->
    <select id="selectEnabledModels" resultType="com.sqx.modules.ai.entity.AiModelConfig">
        SELECT id, model_name, model_code, api_url, api_key, model_type,
               max_tokens, temperature, top_p, frequency_penalty, presence_penalty,
               is_enabled, sort_order, create_time, update_time, remark
        FROM ai_model_config
        WHERE is_enabled = 1
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据模型代码查询模型配置 -->
    <select id="selectByModelCode" resultType="com.sqx.modules.ai.entity.AiModelConfig">
        SELECT id, model_name, model_code, api_url, api_key, model_type,
               max_tokens, temperature, top_p, frequency_penalty, presence_penalty,
               is_enabled, sort_order, create_time, update_time, remark
        FROM ai_model_config
        WHERE model_code = #{modelCode}
        AND is_enabled = 1
    </select>

</mapper>
