package com.sqx.modules.app.controller.app;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sqx.common.utils.DateUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.entity.Car;
import com.sqx.modules.app.service.CarService;
import com.sqx.modules.app.utils.HttpUtils;
import com.sqx.modules.common.service.CommonInfoService;
import com.sqx.modules.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 用户车辆信息
 */
@RestController
@RequestMapping("/app/car")
@Api(value = "用户车辆信息", tags = {"用户车辆信息"})
public class AppCarController {

    @Autowired
    private CarService carService;
    @Autowired
    private CommonInfoService commonInfoService;

    @PostMapping("/insertCar")
    @ApiOperation("添加车辆信息")
    @Login
    public Result insertCar(@RequestBody Car car,@RequestAttribute("userId") Long userId){
        car.setUserId(userId);
        car.setCreateTime(DateUtils.format(new Date()));
        carService.save(car);
        return Result.success();
    }

    @PostMapping("/updateCar")
    @ApiOperation("修改车辆信息")
    @Login
    public Result updateCar(@RequestBody Car car){
        carService.updateById(car);
        return Result.success();
    }

    @PostMapping("/deleteCar")
    @ApiOperation("删除车辆信息")
    @Login
    public Result deleteCar(Long carId){
        carService.removeById(carId);
        return Result.success();
    }

    @GetMapping("/selectCarList")
    @ApiOperation("查询车辆信息")
    @Login
    public Result selectCarList(Integer page,Integer limit,@RequestAttribute("userId") Long userId){
        return carService.selectCarList(page, limit, userId, null, null);
    }

    @GetMapping("/selectCarDetails")
    @ApiOperation("查询车辆详细信息")
    @Login
    public Result selectCarDetails(Long carId){
        return Result.success().put("data",carService.getById(carId));
    }

    /**
     * 储存车辆品牌
     */
    private static List<Map<String,Object>> carList;


    @Login
    @GetMapping("/selectCarApi")
    @ApiOperation("查询汽车品牌和车系")
    public Result selectCarApi(){
        if(carList!=null){
            return Result.success().put("data",carList);
        }
        String url="http://apis.juhe.cn/cxdq/brand";
        Map<String,String> param=new HashMap<>();
        param.put("key",commonInfoService.findOne(261).getValue());
        String data = HttpClientUtil.doGet(url, param);
        JSONObject jsonObject = JSONObject.parseObject(data);
        JSONArray result = jsonObject.getJSONArray("result");
        List<Map<String,Object>> carList=new ArrayList<>();
        Map<String,Object> map=new HashMap<>();
        JSONArray jsonArray=new JSONArray();
        String oldL="A";
        for(int i=0;i<result.size();i++){
            JSONObject jsonObject1 = result.getJSONObject(i);
            String l = jsonObject1.getString("first_letter");
            if(oldL.equals(l)){
                jsonArray.add(jsonObject1);
            }else{
                map.put("letter",oldL);
                map.put("carList",jsonArray);
                oldL=l;
                carList.add(map);
                map=new HashMap<>();
                jsonArray=new JSONArray();
                jsonArray.add(jsonObject1);
            }
        }
        map.put("letter",oldL);
        map.put("carList",jsonArray);
        carList.add(map);
        AppCarController.carList=carList;
        return Result.success().put("data",carList);

    }


    /*@Login
    @GetMapping("/selectCarApi")
    @ApiOperation("查询汽车品牌和车系")
    public Result selectCarApi(){
        if(carList==null){
            String host = "https://zca.market.alicloudapi.com";
            String path = "/lifeservice/car/GetSeries";
            String method = "POST";
            String appcode = commonInfoService.findOne(261).getValue();
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Authorization", "APPCODE " + appcode);
            //根据API的要求，定义相对应的Content-Type
            headers.put("Content-Type", "application/json; charset=UTF-8");
            Map<String, String> querys = new HashMap<String, String>();
            String bodys = "";
            try {
                HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, bodys);
                String data = EntityUtils.toString(response.getEntity());
                JSONObject jsonObject = JSONObject.parseObject(data);
                JSONArray result = jsonObject.getJSONArray("result");
                List<Map<String,Object>> carList=new ArrayList<>();
                Map<String,Object> map=new HashMap<>();
                JSONArray jsonArray=new JSONArray();
                String oldL="A";
                for(int i=0;i<result.size();i++){
                    JSONObject jsonObject1 = result.getJSONObject(i);
                    String l = jsonObject1.getString("L");
                    if(oldL.equals(l)){
                        jsonArray.add(jsonObject1);
                    }else{
                        map.put("letter",oldL);
                        map.put("carList",jsonArray);
                        oldL=l;
                        carList.add(map);
                        map=new HashMap<>();
                        jsonArray=new JSONArray();
                        jsonArray.add(jsonObject1);
                    }
                }
                AppCarController.carList=carList;
                return Result.success().put("data",carList);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return Result.error("查询失败！");
        }else{
            return Result.success().put("data",carList);
        }

    }*/

}
