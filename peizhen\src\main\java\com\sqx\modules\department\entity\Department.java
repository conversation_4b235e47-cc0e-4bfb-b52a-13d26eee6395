package com.sqx.modules.department.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.joda.time.DateTime;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
public class Department implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 科室id
     */
    @TableId(value = "department_id", type = IdType.AUTO)
    @Excel(name = "当前科室id", orderNum = "4", width = 15)
    @ApiModelProperty("科室id")
    private Long departmentId;

    /**
     * 医院id
     */
    @Excel(name = "医院id", orderNum = "6")
    @ApiModelProperty("医院id")
    private Long hospitalId;

    /**
     * 科室名称
     */
    @Excel(name = "科室名称", orderNum = "1")
    @TableField(condition = SqlCondition.LIKE)
    @ApiModelProperty("科室名称")
    private String departmentName;

    /**
     * 排序
     */
    @Excel(name = "排序", orderNum = "7")
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", orderNum = "9", format = "yyyy-MM-dd HH:mm:ss", width = 15)
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 是否启用(0否 1是)
     */
    @ApiModelProperty("是否启用(0否 1是)")
    private Integer isEnable;
    /**
     * 所属部门或上级科室
     */
    @ApiModelProperty("所属科室id")
    @Excel(name = "所属科室id", orderNum = "5", width = 15)
    private Long parentId;

    @TableField(exist = false)
    private List<Department> departmentsList;
}
