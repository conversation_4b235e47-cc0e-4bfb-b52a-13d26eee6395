package com.sqx.modules.hospitalEmploy.dao;

import com.sqx.modules.hospitalEmploy.entity.HospitalEmploy;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Mapper
public interface HospitalEmployDao extends BaseMapper<HospitalEmploy> {

    List<HospitalEmploy> getEmployList(@Param("authentication") Integer authentication);
}
