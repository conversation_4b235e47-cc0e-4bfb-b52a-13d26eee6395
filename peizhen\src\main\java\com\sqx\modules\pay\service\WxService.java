package com.sqx.modules.pay.service;

import com.sqx.common.utils.Result;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.task.entity.HelpOrder;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2020/2/26
 */
public interface WxService {



    Result payOrder(Long id, Integer classify,Long payClassifyId, HttpServletRequest request) throws Exception;

    Result wxPayOrder(Long id, Integer classify,HttpServletRequest request) throws Exception;

    Result wxPayHelpOrder(HelpOrder helpOrder, Integer classify,HttpServletRequest request) throws Exception;

    Result wxPaySafetyMoney(Long userId, Integer classify,Integer type,HttpServletRequest request) throws Exception;

    String payBack(String resXml,Integer type);

    boolean refund(String ordersNo,BigDecimal refundMoney);

}