{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-select/u-select.vue?2954", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-select/u-select.vue?be92", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-select/u-select.vue?a4d3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-select/u-select.vue?cf01", "uni-app:///uview-ui/components/u-select/u-select.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-select/u-select.vue?fe52", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-select/u-select.vue?2c15"], "names": ["props", "list", "type", "default", "border", "value", "cancelColor", "confirmColor", "zIndex", "safeAreaInsetBottom", "maskCloseAble", "defaultValue", "mode", "valueName", "labelName", "<PERSON><PERSON><PERSON>", "title", "cancelText", "confirmText", "data", "defaultSelector", "columnData", "selectValue", "lastSelectIndex", "columnNum", "moving", "watch", "immediate", "handler", "computed", "uZIndex", "methods", "pickstart", "pickend", "init", "setDefaultSelector", "setColumnNum", "column", "num", "setColumnData", "setSelectValue", "tmp", "label", "columnChange", "columnIndex", "close", "getResult", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoD1wB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,gBAsBA;EACAA;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;EACA;EACAgB;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACArB;MACAsB;MACAC;QAAA;QACA;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAEA;IAEA;IACA;IACAC;MAEA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MAAA,KACA;MACA;MAAA,KACA;QACA;QACA;QACA;QACA;UACAC;UACAC;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;YACApB;YACAkB;UACA;YACA;YACAlB;YACAkB;UACA;QACA;MACA;QACAlB;MACA;QACAA;MACA;MACA;IACA;IACA;IACAqB;MACA;MACA;QACAC;QACA;UACApC;UACAqC;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;QACA;QACAC;UACA;UACA;YACAvC;YACAqC;UACA;UACA;UACA;UACA;QAEA;QACA;QACA;MACA;QACA;QACA;QACA;UACArC;UACAqC;QACA;QACA;QACA;QACA;MACA;QACA;QACAE;UACA;UACA;UACA;YACAvC;YACAqC;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAG;MACA;IACA;IACA;IACAC;MAAA;MAEA;MAEA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC5VA;AAAA;AAAA;AAAA;AAA66C,CAAgB,uvCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-select/u-select.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-select.vue?vue&type=template&id=a577ac80&scoped=true&\"\nvar renderjs\nimport script from \"./u-select.vue?vue&type=script&lang=js&\"\nexport * from \"./u-select.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-select.vue?vue&type=style&index=0&id=a577ac80&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a577ac80\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-select/u-select.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-select.vue?vue&type=template&id=a577ac80&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-select.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-select.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-select\">\n\t\t<!-- <view class=\"u-select__action\" :class=\"{\n\t\t\t'u-select--border': border\n\t\t}\" @tap.stop=\"selectHandler\">\n\t\t\t<view class=\"u-select__action__icon\" :class=\"{\n\t\t\t\t'u-select__action__icon--reverse': value == true\n\t\t\t}\">\n\t\t\t\t<u-icon name=\"arrow-down-fill\" size=\"26\" color=\"#c0c4cc\"></u-icon>\n\t\t\t</view>\n\t\t</view> -->\n\t\t<u-popup :maskCloseAble=\"maskCloseAble\" mode=\"bottom\" :popup=\"false\" v-model=\"value\" length=\"auto\" :safeAreaInsetBottom=\"safeAreaInsetBottom\" @close=\"close\" :z-index=\"uZIndex\">\n\t\t\t<view class=\"u-select\">\n\t\t\t\t<view class=\"u-select__header\" @touchmove.stop.prevent=\"\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-select__header__cancel u-select__header__btn\"\n\t\t\t\t\t\t:style=\"{ color: cancelColor }\"\n\t\t\t\t\t\thover-class=\"u-hover-class\"\n\t\t\t\t\t\t:hover-stay-time=\"150\"\n\t\t\t\t\t\t@tap=\"getResult('cancel')\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{cancelText}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"u-select__header__title\">\n\t\t\t\t\t\t{{title}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-select__header__confirm u-select__header__btn\"\n\t\t\t\t\t\t:style=\"{ color: moving ? cancelColor : confirmColor }\"\n\t\t\t\t\t\thover-class=\"u-hover-class\"\n\t\t\t\t\t\t:hover-stay-time=\"150\"\n\t\t\t\t\t\****************=\"\"\n\t\t\t\t\t\**********=\"getResult('confirm')\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{confirmText}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-select__body\">\n\t\t\t\t\t<picker-view @change=\"columnChange\" class=\"u-select__body__picker-view\" :value=\"defaultSelector\" @pickstart=\"pickstart\" @pickend=\"pickend\">\n\t\t\t\t\t\t<picker-view-column v-for=\"(item, index) in columnData\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"u-select__body__picker-view__item\" v-for=\"(item1, index1) in item\" :key=\"index1\">\n\t\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item1[labelName] }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t</picker-view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * select 列选择器\n\t * @description 此选择器用于单列，多列，多列联动的选择场景。(从1.3.0版本起，不建议使用Picker组件的单列和多列模式，Select组件是专门为列选择而构造的组件，更简单易用。)\n\t * @tutorial http://uviewui.com/components/select.html\n\t * @property {String} mode 模式选择，\"single-column\"-单列模式，\"mutil-column\"-多列模式，\"mutil-column-auto\"-多列联动模式\n\t * @property {Array} list 列数据，数组形式，见官网说明\n\t * @property {Boolean} v-model 布尔值变量，用于控制选择器的弹出与收起\n\t * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配(默认false)\n\t * @property {String} cancel-color 取消按钮的颜色（默认#606266）\n\t * @property {String} confirm-color 确认按钮的颜色(默认#2979ff)\n\t * @property {String} confirm-text 确认按钮的文字\n\t * @property {String} cancel-text 取消按钮的文字\n\t * @property {String} default-value 提供的默认选中的下标，见官网说明\n\t * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭Picker(默认true)\n\t * @property {String Number} z-index 弹出时的z-index值(默认10075)\n\t * @property {String} value-name 自定义list数据的value属性名 1.3.6\n\t * @property {String} label-name 自定义list数据的label属性名 1.3.6\n\t * @property {String} child-name 自定义list数据的children属性名，只对多列联动模式有效 1.3.7\n\t * @event {Function} confirm 点击确定按钮，返回当前选择的值\n\t * @example <u-select v-model=\"show\" :list=\"list\"></u-select>\n\t */\n\nexport default {\n\tprops: {\n\t\t// 列数据\n\t\tlist: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\t\t// 是否显示边框\n\t\tborder: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 通过双向绑定控制组件的弹出与收起\n\t\tvalue: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// \"取消\"按钮的颜色\n\t\tcancelColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#606266'\n\t\t},\n\t\t// \"确定\"按钮的颜色\n\t\tconfirmColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#2979ff'\n\t\t},\n\t\t// 弹出的z-index值\n\t\tzIndex: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 0\n\t\t},\n\t\tsafeAreaInsetBottom: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 是否允许通过点击遮罩关闭Picker\n\t\tmaskCloseAble: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 提供的默认选中的下标\n\t\tdefaultValue: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn [0];\n\t\t\t}\n\t\t},\n\t\t// 模式选择，single-column-单列，mutil-column-多列，mutil-column-auto-多列联动\n\t\tmode: {\n\t\t\ttype: String,\n\t\t\tdefault: 'single-column'\n\t\t},\n\t\t// 自定义value属性名\n\t\tvalueName: {\n\t\t\ttype: String,\n\t\t\tdefault: 'value'\n\t\t},\n\t\t// 自定义label属性名\n\t\tlabelName: {\n\t\t\ttype: String,\n\t\t\tdefault: 'label'\n\t\t},\n\t\t// 自定义多列联动模式的children属性名\n\t\tchildName: {\n\t\t\ttype: String,\n\t\t\tdefault: 'children'\n\t\t},\n\t\t// 顶部标题\n\t\ttitle: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 取消按钮的文字\n\t\tcancelText: {\n\t\t\ttype: String,\n\t\t\tdefault: '取消'\n\t\t},\n\t\t// 确认按钮的文字\n\t\tconfirmText: {\n\t\t\ttype: String,\n\t\t\tdefault: '确认'\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\t// 用于列改变时，保存当前的索引，下一次变化时比较得出是哪一列发生了变化\n\t\t\tdefaultSelector: [0],\n\t\t\t// picker-view的数据\n\t\t\tcolumnData: [],\n\t\t\t// 每次队列发生变化时，保存选择的结果\n\t\t\tselectValue: [],\n\t\t\t// 上一次列变化时的index\n\t\t\tlastSelectIndex: [],\n\t\t\t// 列数\n\t\t\tcolumnNum: 0,\n\t\t\t// 列是否还在滑动中，微信小程序如果在滑动中就点确定，结果可能不准确\n\t\t\tmoving: false\n\t\t};\n\t},\n\twatch: {\n\t\t// 在select弹起的时候，重新初始化所有数据\n\t\tvalue: {\n\t\t\timmediate: true,\n\t\t\thandler(val) {\n\t\t\t\tif(val) setTimeout(() => this.init(), 10);\n\t\t\t}\n\t\t},\n\t},\n\tcomputed: {\n\t\tuZIndex() {\n\t\t\t// 如果用户有传递z-index值，优先使用\n\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\n\t\t},\n\t},\n\tmethods: {\n\t\t// 标识滑动开始，只有微信小程序才有这样的事件\n\t\tpickstart() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.moving = true;\n\t\t\t// #endif\n\t\t},\n\t\t// 标识滑动结束\n\t\tpickend() {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tthis.moving = false;\n\t\t\t// #endif\n\t\t},\n\t\tinit() {\n\t\t\tthis.setColumnNum();\n\t\t\tthis.setDefaultSelector();\n\t\t\tthis.setColumnData();\n\t\t\tthis.setSelectValue();\n\t\t},\n\t\t// 获取默认选中列下标\n\t\tsetDefaultSelector() {\n\t\t\t// 如果没有传入默认选中的值，生成长度为columnNum，用0填充的数组\n\t\t\tthis.defaultSelector = this.defaultValue.length == this.columnNum ? this.defaultValue : Array(this.columnNum).fill(0);\n\t\t\tthis.lastSelectIndex = this.$u.deepClone(this.defaultSelector);\n\t\t},\n\t\t// 计算列数\n\t\tsetColumnNum() {\n\t\t\t// 单列的列数为1\n\t\t\tif(this.mode == 'single-column') this.columnNum = 1;\n\t\t\t// 多列时，this.list数组长度就是列数\n\t\t\telse if(this.mode == 'mutil-column') this.columnNum = this.list.length;\n\t\t\t// 多列联动时，通过历遍this.list的第一个元素，得出有多少列\n\t\t\telse if(this.mode == 'mutil-column-auto') {\n\t\t\t\tlet num = 1;\n\t\t\t\tlet column = this.list;\n\t\t\t\t// 只要有元素并且第一个元素有children属性，继续历遍\n\t\t\t\twhile(column[0][this.childName]) {\n\t\t\t\t\tcolumn = column[0] ? column[0][this.childName] : {};\n\t\t\t\t\tnum ++;\n\t\t\t\t}\n\t\t\t\tthis.columnNum = num;\n\t\t\t}\n\t\t},\n\t\t// 获取需要展示在picker中的列数据\n\t\tsetColumnData() {\n\t\t\tlet data = [];\n\t\t\tthis.selectValue = [];\n\t\t\tif(this.mode == 'mutil-column-auto') {\n\t\t\t\t// 获得所有数据中的第一个元素\n\t\t\t\tlet column = this.list[this.defaultSelector.length ? this.defaultSelector[0] : 0];\n\t\t\t\t// 通过循环所有的列数，再根据设定列的数组，得出当前需要渲染的整个列数组\n\t\t\t\tfor (let i = 0; i < this.columnNum; i++) {\n\t\t\t\t\t// 第一列默认为整个list数组\n\t\t\t\t\tif (i == 0) {\n\t\t\t\t\t\tdata[i] = this.list;\n\t\t\t\t\t\tcolumn = column[this.childName];\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 大于第一列时，判断是否有默认选中的，如果没有就用该列的第一项\n\t\t\t\t\t\tdata[i] = column;\n\t\t\t\t\t\tcolumn = column[this.defaultSelector[i]][this.childName];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if(this.mode == 'single-column') {\n\t\t\t\tdata[0] = this.list;\n\t\t\t} else {\n\t\t\t\tdata = this.list;\n\t\t\t}\n\t\t\tthis.columnData = data;\n\t\t},\n\t\t// 获取默认选中的值，如果没有设置defaultValue，就默认选中每列的第一个\n\t\tsetSelectValue() {\n\t\t\tlet tmp = null;\n\t\t\tfor(let i = 0; i < this.columnNum; i++) {\n\t\t\t\ttmp = this.columnData[i][this.defaultSelector[i]];\n\t\t\t\tlet data = {\n\t\t\t\t\tvalue: tmp ? tmp[this.valueName] : null,\n\t\t\t\t\tlabel: tmp ? tmp[this.labelName] : null\n\t\t\t\t};\n\t\t\t\t// 判断是否存在额外的参数，如果存在，就返回\n\t\t\t\tif(tmp && tmp.extra) data.extra = tmp.extra;\n\t\t\t\tthis.selectValue.push(data)\n\t\t\t}\n\t\t},\n\t\t// 列选项\n\t\tcolumnChange(e) {\n\t\t\tlet index = null;\n\t\t\tlet columnIndex = e.detail.value;\n\t\t\t// 由于后面是需要push进数组的，所以需要先清空数组\n\t\t\tthis.selectValue = [];\n\t\t\tif(this.mode == 'mutil-column-auto') {\n\t\t\t\t// 对比前后两个数组，寻找变更的是哪一列，如果某一个元素不同，即可判定该列发生了变化\n\t\t\t\tthis.lastSelectIndex.map((val, idx) => {\n\t\t\t\t\tif (val != columnIndex[idx]) index = idx;\n\t\t\t\t});\n\t\t\t\tthis.defaultSelector = columnIndex;\n\t\t\t\tfor (let i = index + 1; i < this.columnNum; i++) {\n\t\t\t\t\t// 当前变化列的下一列的数据，需要获取上一列的数据，同时需要指定是上一列的第几个的children，再往后的\n\t\t\t\t\t// 默认是队列的第一个为默认选项\n\t\t\t\t\tthis.columnData[i] = this.columnData[i - 1][i - 1 == index ? columnIndex[index] : 0][this.childName];\n\t\t\t\t\t// 改变的列之后的所有列，默认选中第一个\n\t\t\t\t\tthis.defaultSelector[i] = 0;\n\t\t\t\t}\n\t\t\t\t// 在历遍的过程中，可能由于上一步修改this.columnData，导致产生连锁反应，程序触发columnChange，会有多次调用\n\t\t\t\t// 只有在最后一次数据稳定后的结果是正确的，此前的历遍中，可能会产生undefined，故需要判断\n\t\t\t\tcolumnIndex.map((item, index) => {\n\t\t\t\t\tlet data = this.columnData[index][columnIndex[index]];\n\t\t\t\t\tlet tmp = {\n\t\t\t\t\t\tvalue: data ? data[this.valueName] : null,\n\t\t\t\t\t\tlabel: data ? data[this.labelName] : null,\n\t\t\t\t\t};\n\t\t\t\t\t// 判断是否有需要额外携带的参数\n\t\t\t\t\tif(data && data.extra !== undefined) tmp.extra = data.extra;\n\t\t\t\t\tthis.selectValue.push(tmp);\n\n\t\t\t\t})\n\t\t\t\t// 保存这一次的结果，用于下次列发生变化时作比较\n\t\t\t\tthis.lastSelectIndex = columnIndex;\n\t\t\t} else if(this.mode == 'single-column') {\n\t\t\t\tlet data = this.columnData[0][columnIndex[0]];\n\t\t\t\t// 初始默认选中值\n\t\t\t\tlet tmp = {\n\t\t\t\t\tvalue: data ? data[this.valueName] : null,\n\t\t\t\t\tlabel: data ? data[this.labelName] : null,\n\t\t\t\t};\n\t\t\t\t// 判断是否有需要额外携带的参数\n\t\t\t\tif(data && data.extra !== undefined) tmp.extra = data.extra;\n\t\t\t\tthis.selectValue.push(tmp);\n\t\t\t} else if(this.mode == 'mutil-column') {\n\t\t\t\t// 初始默认选中值\n\t\t\t\tcolumnIndex.map((item, index) => {\n\t\t\t\t\tlet data = this.columnData[index][columnIndex[index]];\n\t\t\t\t\t// 初始默认选中值\n\t\t\t\t\tlet tmp = {\n\t\t\t\t\t\tvalue: data ? data[this.valueName] : null,\n\t\t\t\t\t\tlabel: data ? data[this.labelName] : null,\n\t\t\t\t\t};\n\t\t\t\t\t// 判断是否有需要额外携带的参数\n\t\t\t\t\tif(data && data.extra !== undefined) tmp.extra = data.extra;\n\t\t\t\t\tthis.selectValue.push(tmp);\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tclose() {\n\t\t\tthis.$emit('input', false);\n\t\t},\n\t\t// 点击确定或者取消\n\t\tgetResult(event = null) {\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tif (this.moving) return;\n\t\t\t// #endif\n\t\t\tif (event) this.$emit(event, this.selectValue);\n\t\t\tthis.close();\n\t\t},\n\t\tselectHandler() {\n\t\t\tthis.$emit('click');\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../libs/css/style.components.scss\";\n\n.u-select {\n\n\t&__action {\n\t\tposition: relative;\n\t\tline-height: $u-form-item-height;\n\t\theight: $u-form-item-height;\n\n\t\t&__icon {\n\t\t\tposition: absolute;\n\t\t\tright: 20rpx;\n\t\t\ttop: 50%;\n\t\t\ttransition: transform .4s;\n\t\t\ttransform: translateY(-50%);\n\t\t\tz-index: 1;\n\n\t\t\t&--reverse {\n\t\t\t\ttransform: rotate(-180deg) translateY(50%);\n\t\t\t}\n\t\t}\n\t}\n\n\t&__hader {\n\t\t&__title {\n\t\t\tcolor: $u-content-color;\n\t\t}\n\t}\n\n\t&--border {\n\t\tborder-radius: 6rpx;\n\t\tborder-radius: 4px;\n\t\tborder: 1px solid $u-form-item-border-color;\n\t}\n\n\t&__header {\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\theight: 80rpx;\n\t\tpadding: 0 40rpx;\n\t}\n\n\t&__body {\n\t\twidth: 100%;\n\t\theight: 500rpx;\n\t\toverflow: hidden;\n\t\tbackground-color: #fff;\n\n\t\t&__picker-view {\n\t\t\theight: 100%;\n\t\t\tbox-sizing: border-box;\n\n\t\t\t&__item {\n\t\t\t\t@include vue-flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: $u-main-color;\n\t\t\t\tpadding: 0 8rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-select.vue?vue&type=style&index=0&id=a577ac80&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-select.vue?vue&type=style&index=0&id=a577ac80&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627598\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}