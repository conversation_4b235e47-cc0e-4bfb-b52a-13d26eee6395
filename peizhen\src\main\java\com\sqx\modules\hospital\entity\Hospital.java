package com.sqx.modules.hospital.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sqx.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Data
public class Hospital implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 医院id
     */
    @NotNull(groups = {UpdateGroup.class}, message = "修改时医院id不能为空")
    @TableId(value = "hospital_id", type = IdType.AUTO)
    @ApiModelProperty("医院id")
    private Long hospitalId;

    /**
     * 医院名称
     */
    @TableField(condition = SqlCondition.LIKE)
    @ApiModelProperty("医院名称")
    private String hospitalName;

    /**
     * 医院图片
     */
    @ApiModelProperty("医院图片")
    private String hospitalImg;

    /**
     * 医院等级
     */
    @ApiModelProperty("医院等级")
    private String hospitalLevel;

    /**
     * 医院类型
     */
    @ApiModelProperty("医院类型")
    private String hospitalType;

    /**
     * 医院经度
     */
    @ApiModelProperty("医院经度")
    private Double hospitalLng;

    /**
     * 医院纬度
     */
    @ApiModelProperty("医院纬度")
    private Double hospitalLat;

    /**
     * 医院简介
     */
    @ApiModelProperty("医院简介")
    private String hospitalDetails;

    /**
     * 省
     */
    @ApiModelProperty("省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty("市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty("区")
    private String district;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String addressDetails;

    /**
     * 科室简介
     */
    @ApiModelProperty("科室简介")
    private String departmentDetails;

    /**
     * 是否启用 (0:否 1:是)
     */
    @ApiModelProperty("是否启用 (0:否 1:是)")
    private Integer isEnable;
    /**
     * 医院logo
     */
    @ApiModelProperty("医院logo")
    private String icon;
    /**
     * 城市首字母
     */
    @ApiModelProperty("城市首字母")
    private String cityInitial;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 待服务订单数
     */
    @TableField(exist = false)
    private Integer ordersCount;

}
