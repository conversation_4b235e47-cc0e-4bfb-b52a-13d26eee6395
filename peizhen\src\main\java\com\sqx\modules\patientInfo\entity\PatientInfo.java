package com.sqx.modules.patientInfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
public class PatientInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 信息id
     */
    @TableId(value = "patient_id", type = IdType.AUTO)
    @ApiModelProperty("信息id")
    private Long patientId;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 是否已满18岁(1是 0否)
     */
    @ApiModelProperty("是否已满18岁(1是 0否)")
    private Integer isUnderAge;

    /**
     * 性别 1男 2女
     */
    @ApiModelProperty("性别 1男 2女")
    private Integer sex;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @TableField(condition = SqlCondition.LIKE)
    private String realName;

    /**
     * 电话号码
     */
    @ApiModelProperty("电话号码")
    private String phone;

    /**
     * 紧急联系人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("紧急联系人")
    private String emergencyPhone;
    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    private String idNumber;
    @ApiModelProperty("是否删除")
    private Integer isDelete;
    @ApiModelProperty("就诊人关系")
    private String relationship;

    @TableField(exist = false)
    private String userName;


}
