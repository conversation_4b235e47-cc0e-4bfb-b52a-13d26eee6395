{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/tousuList.vue?9272", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/tousuList.vue?c8d2", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/tousuList.vue?e31a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/tousuList.vue?5d0d", "uni-app:///my/order/tousuList.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/tousuList.vue?6d7e", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/order/tousuList.vue?a8c7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "page", "limit", "count", "dataList", "onLoad", "onShow", "methods", "copyClick", "uni", "success", "title", "icon", "saveImg", "urls", "current", "tousuList", "state", "item", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAwuB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCwD5vB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACAC;QACAT;QACAU;UACAD;YACAC;cACAD;gBACAE;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ;QACAK;QACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAf;QACAC;QACAe;MACA;MACA;QACA;UAEA;YACA;YACA;cACA;gBACAC;cACA;YACA;UACA;YACA;YACA;cACA;gBACAA;cACA;YACA;UACA;UACA;QACA;MAEA;IACA;EACA;EACAC;IACA;MACAV;QACAE;QACAC;MACA;MACA;IACA;MACA;MACA;IACA;EACA;EACAQ;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAA+iC,CAAgB,08BAAG,EAAC,C;;;;;;;;;;;ACAnkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/order/tousuList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/order/tousuList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tousuList.vue?vue&type=template&id=595b79b8&\"\nvar renderjs\nimport script from \"./tousuList.vue?vue&type=script&lang=js&\"\nexport * from \"./tousuList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tousuList.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/order/tousuList.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tousuList.vue?vue&type=template&id=595b79b8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dataList.length\n  var l0 = _vm.__map(_vm.dataList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g1 = g0 > 0 ? item.image && item.image.length > 0 : null\n    return {\n      $orig: $orig,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.dataList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tousuList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tousuList.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"bonus-main\">\n\t\t\t<view class=\"list-item \" v-if=\"dataList.length>0\" :show-arrow=\"false\" v-for=\"(item, index) in dataList\"\n\t\t\t\t:key=\"index\">\n\t\t\t\t<view class=\"list-item-wrap margin-top-sm\">\n\t\t\t\t\t<view v-if=\"item.image && item.image.length>0\" class=\"padding-tb-sm\">\n\t\t\t\t\t\t<swiper class=\"screen-swiper\" style=\"height: 260rpx;\" :circular=\"true\" :autoplay=\"true\"\n\t\t\t\t\t\t\tduration=\"800\">\n\t\t\t\t\t\t\t<swiper-item v-for=\"(item,index) in item.image\" :key=\"index\"\n\t\t\t\t\t\t\t\t@click=\"saveImg(item.image,index)\">\n\t\t\t\t\t\t\t\t<image :src=\"item\" mode=\"aspectFit\" class=\"radius\"></image>\n\t\t\t\t\t\t\t</swiper-item>\n\t\t\t\t\t\t</swiper>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"padding: 20rpx 30rpx;\">\n\t\t\t\t\t\t<view>投诉标题：{{item.title}}</view>\n\n\t\t\t\t\t\t<view class=\"list-title\" style=\"color: #333;margin-top: 10upx;\" v-if=\"item.content\">投诉内容：{{ item.content }}</view>\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333;\" v-if=\"item.byUserName\">\n\t\t\t\t\t\t\t被投诉人：{{ item.byUserName }}</view>\n\t\t\t\t\t\t<view class=\"margin-top-xs flex align-center\" v-if=\"item.platform==3\">订单投诉：{{item.url}}\t<image src=\"../static/copy.png\" style=\"width: 45rpx;height: 45rpx;margin-left: 5upx;\"\n\t\t\t\t\t\t\t\************=\"copyClick(item.url)\"></image></view>\n\t\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;margin-top: 10rpx;\">\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<text style=\"color: #333;\" v-if=\"item.createAt\">投诉时间：{{ item.createAt }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333;\" v-if=\"item.status==0\">状态：待处理\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333;\" v-if=\"item.status==4\">状态：不处理\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333;\" v-if=\"item.status==1\">状态：已驳回\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333;\" v-if=\"item.status==2\">状态：已封号\n\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333;\" v-if=\"item.status==3\">状态：已处理\n\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333\" v-if=\"item.status==1\">\n\t\t\t\t\t\t\t驳回内容：{{ item.auditContent }}</view>\n\t\t\t\t\t\t<view class=\"list-title\" style=\"margin-top: 10upx;color: #333\" v-if=\"item.status==3\">\n\t\t\t\t\t\t\t处理内容：{{ item.auditContent }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<empty v-if=\"dataList.length === 0\" des=\"暂无记录\" show=\"false\"></empty>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport empty from '../../components/empty.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 10,\n\t\t\t\tcount: 0,\n\t\t\t\tdataList: []\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.tousuList()\n\t\t},\n\t\tonShow() {\n\n\t\t},\n\t\tmethods: {\n\t\t\tcopyClick(copy) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: copy,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tuni.getClipboardData({\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: \"复制成功\",\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 查看图片\n\t\t\tsaveImg(imgs, index) {\n\t\t\t\tif (this.LBSelect) {\n\t\t\t\t\tif (index == this.LBIndex - 1) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// console.log(imgs)\n\t\t\t\tlet that = this;\n\t\t\t\tlet imgArr = imgs\n\t\t\t\t// imgArr.push(imgs);\n\t\t\t\t// //预览图片\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: imgArr,\n\t\t\t\t\tcurrent: imgArr[index]\n\t\t\t\t});\n\t\t\t},\n\t\t\t// get page limit state 3 \n\t\t\ttousuList() {\n\t\t\t\tlet data = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit,\n\t\t\t\t\tstate: 3\n\t\t\t\t}\n\t\t\t\tthis.$Request.get('/app/message/selectMessageByUserId', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\n\t\t\t\t\t\tif (this.page == 1) {\n\t\t\t\t\t\t\tthis.dataList = res.data.list\n\t\t\t\t\t\t\tthis.dataList.map(item=>{\n\t\t\t\t\t\t\t\tif(item.image){\n\t\t\t\t\t\t\t\t\titem.image = item.image.split(',')\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.dataList = [...this.dataList, ...res.data.list]\n\t\t\t\t\t\t\tthis.dataList.map(item=>{\n\t\t\t\t\t\t\t\tif(item.image){\n\t\t\t\t\t\t\t\t\titem.image = item.image.split(',')\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.count = res.data.totalCount\n\t\t\t\t\t}\n\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tonReachBottom: function() {\n\t\t\tif (this.count == this.dataList.length) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已经到底了',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t} else {\n\t\t\t\tthis.page = this.page + 1;\n\t\t\t\tthis.tousuList()\n\t\t\t}\n\t\t},\n\t\tonPullDownRefresh: function() {\n\t\t\tthis.page = 1;\n\t\t\tthis.tousuList()\n\t\t},\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.bg {\n\t\tbackground: #FFFFFF;\n\t}\n\n\t.list-item {\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 16rpx;\n\t\tmargin: 0 20rpx 20rpx 20rpx;\n\t}\n\n\t.img1 {\n\t\twidth: 100%;\n\t\theight: 400rpx;\n\t}\n\n\t.img2 {\n\t\twidth: 210rpx;\n\t\theight: 210rpx;\n\t\tmargin-top: 15rpx;\n\t\tmargin-right: 5rpx;\n\t\tmargin-left: 15rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tousuList.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tousuList.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447624689\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}