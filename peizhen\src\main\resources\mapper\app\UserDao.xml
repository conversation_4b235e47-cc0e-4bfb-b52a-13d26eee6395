<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.app.dao.UserDao">
    <update id="cancelArea">
        update tb_user
        set province=#{userEntity.province},
            city=#{userEntity.city},
            district=#{userEntity.district}
        where user_id = #{userEntity.userId}
    </update>
    <update id="updateAuthentication">
        update tb_user
        set is_authentication= null
        where user_id = #{userId}


    </update>


    <select id="selectUserPage" resultType="com.sqx.modules.app.entity.UserEntity">
        select * from tb_user
        where 1=1
        <if test="search!=null and search!=''">
            and (user_id=#{search} or phone = #{search} or user_name =#{search} )
        </if>
        <if test="userName!=null and userName!=''">
            and user_name like concat('%',#{userName},'%')
        </if>
        <if test="sex!=null and sex!=0">
            and sex=#{sex}
        </if>
        <if test="invitationCode!=null and invitationCode!=''">
            and invitation_code like concat("%",#{invitationCode},"%")
        </if>
        <if test="inviterCode!=null and inviterCode!=''">
            and inviter_code like concat("%",#{inviterCode},"%")
        </if>
        <if test="platform!=null and platform!=''">
            and platform=#{platform}
        </if>
        <if test="sysPhone!=null and sysPhone!=''">
            and sys_phone=#{sysPhone}
        </if>
        <if test="status!=null and status!=0">
            and status=#{status}
        </if>
        <if test="isAuthentication!=null and isAuthentication!=0 and  isAuthentication==1">
            and is_authentication is null
        </if>
        <if test="isAuthentication!=null and isAuthentication!=0 and isAuthentication==2">
            and is_authentication is not null
        </if>
        <if test="isPromotion!=null and isPromotion==0">
            and (is_promotion is null or is_promotion = 0)
        </if>
        <if test="isPromotion!=null and isPromotion==1">
            and is_promotion=1
        </if>
        <if test="isAgent!=null and isAgent==0">
            and (is_agent is null or is_agent = 0)
        </if>
        <if test="isAgent!=null and isAgent==1">
            and is_agent=1
        </if>
        <if test="startTime!=null and startTime!='' and (endTime!=null and endTime!='')">
            and date_format(create_time,'%Y-%m-%d') BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="startTime!=null and startTime!='' and  (endTime==null or endTime=='')">
            and date_format(create_time,'%Y-%m-%d') &gt;= #{startTime}
        </if>
        <if test="endTime!=null and endTime!='' and  (startTime==null or startTime=='')">
            and date_format(create_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>
    <select id="queryInviterCount" resultType="int">
        select count(*)
        from tb_user
        where inviter_code = #{inviterCode}
    </select>
    <select id="queryUserCount" resultType="int">
        select count(*)from tb_user where 1=1
        <if test="type==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{date},'%Y-%m-%d')
        </if>
        <if test="type==2">
            and date_format(create_time,'%Y-%m')=date_format(#{date},'%Y-%m')
        </if>
        <if test="type==3">
            and date_format(create_time,'%Y')=date_format(#{date},'%Y')
        </if>
        <if test="platform!=null">
            and platform=#{platform}
        </if>
        <if test="isAuthentication!=null">
            and is_authentication=#{isAuthentication}
        </if>
    </select>
    <select id="queryPayMoney" resultType="Double">
        select sum(money) from pay_details where `state`=1
        <if test="type==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{date},'%Y-%m-%d')
        </if>
        <if test="type==2">
            and date_format(create_time,'%Y-%m')=date_format(#{date},'%Y-%m')
        </if>
        <if test="type==3">
            and date_format(create_time,'%Y')=date_format(#{date},'%Y')
        </if>
    </select>

    <select id="queryCourseOrder" resultType="com.sqx.modules.app.response.CourseOrderResponse">
        select * from (
        select c.course_id as courseId,sum(o.pay_money) as 'coursemoney' ,count(*) as 'coursenum',any_value(c.title) as
        'coursename' from orders o , course c where o.course_id=c.course_id and o.status=1 and o.orders_type=1
        <if test="type==1">
            and date_format(o.create_time,'%Y-%m-%d')=date_format(#{date},'%Y-%m-%d')
        </if>
        <if test="type==2">
            and date_format(o.create_time,'%Y-%m')=date_format(#{date},'%Y-%m')
        </if>
        <if test="type==3">
            and date_format(o.create_time,'%Y')=date_format(#{date},'%Y')
        </if>
        group by c.course_id
        ) a
        order by a.coursenum desc
    </select>
    <select id="userMessage" resultType="int">
        select count(*) from tb_user t ,user_vip v where t.user_id=v.user_id and is_vip=1
        <if test="type==1">
            and date_format(t.create_time,'%Y-%m-%d')=date_format(#{date},'%Y-%m-%d')
        </if>
        <if test="type==2">
            and date_format(t.create_time,'%Y-%m')=date_format(#{date},'%Y-%m')
        </if>
        <if test="type==3">
            and date_format(t.create_time,'%Y')=date_format(#{date},'%Y')
        </if>
    </select>


    <insert id="insertUser" parameterType="com.sqx.modules.app.entity.UserEntity" useGeneratedKeys="true"
            keyProperty="userId">
        INSERT INTO tb_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != userName and '' != userName">
                user_name,
            </if>
            <if test="null != phone and '' != phone">
                phone,
            </if>
            <if test="null != avatar and '' != avatar">
                avatar,
            </if>
            <if test="null != sex and '' != sex">
                sex,
            </if>
            <if test="null != age and '' != age">
                age,
            </if>
            <if test="null != openId and '' != openId">
                open_id,
            </if>
            <if test="null != wxOpenId and '' != wxOpenId">
                wx_open_id,
            </if>
            <if test="null != password and '' != password">
                password,
            </if>
            <if test="null != createTime and '' != createTime">
                create_time,
            </if>
            <if test="null != updateTime and '' != updateTime">
                update_time,
            </if>
            <if test="null != appleId and '' != appleId">
                apple_id,
            </if>
            <if test="null != sysPhone and '' != sysPhone">
                sys_phone,
            </if>
            <if test="null != status and '' != status">
                status,
            </if>
            <if test="null != platform and '' != platform">
                platform,
            </if>
            <if test="null != jifen and '' != jifen">
                jifen,
            </if>
            <if test="null != invitationCode and '' != invitationCode">
                invitation_code,
            </if>
            <if test="null != inviterCode and '' != inviterCode">
                inviter_code,
            </if>
            <if test="null != clientid and '' != clientid">
                clientid,
            </if>
            <if test="null != zhiFuBaoName and '' != zhiFuBaoName">
                zhi_fu_bao_name,
            </if>
            <if test="null != zhiFuBao and '' != zhiFuBao">
                zhi_fu_bao
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != userName and '' != userName">
                #{userName},
            </if>
            <if test="null != phone and '' != phone">
                #{phone},
            </if>
            <if test="null != avatar and '' != avatar">
                #{avatar},
            </if>
            <if test="null != sex and '' != sex">
                #{sex},
            </if>
            <if test="null != age and '' != age">
                #{age},
            </if>
            <if test="null != openId and '' != openId">
                #{openId},
            </if>
            <if test="null != wxOpenId and '' != wxOpenId">
                #{wxOpenId},
            </if>
            <if test="null != password and '' != password">
                #{password},
            </if>
            <if test="null != createTime and '' != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime and '' != updateTime">
                #{updateTime},
            </if>
            <if test="null != appleId and '' != appleId">
                #{appleId},
            </if>
            <if test="null != sysPhone and '' != sysPhone">
                #{sysPhone},
            </if>
            <if test="null != status and '' != status">
                #{status},
            </if>
            <if test="null != platform and '' != platform">
                #{platform},
            </if>
            <if test="null != jifen and '' != jifen">
                #{jifen},
            </if>
            <if test="null != invitationCode and '' != invitationCode">
                #{invitationCode},
            </if>
            <if test="null != inviterCode and '' != inviterCode">
                #{inviterCode},
            </if>
            <if test="null != clientid and '' != clientid">
                #{clientid},
            </if>
            <if test="null != zhiFuBaoName and '' != zhiFuBaoName">
                #{zhiFuBaoName},
            </if>
            <if test="null != zhiFuBao and '' != zhiFuBao">
                #{zhiFuBao}
            </if>
        </trim>
    </insert>
    <select id="takingOrdersMessage" resultType="com.sqx.modules.app.response.TakingOrderResponse">
        SELECT
        SUM( o.order_number ) AS orderNumber,
        t.game_id as gameName,
        SUM( o.pay_money ) AS payMoney
        FROM
        orders o
        LEFT JOIN order_taking t ON o.order_taking_id = t.id
        WHERE
        o.state = 1
        <if test="type==1">
            and date_format("o.create_time",'%y-%M-%d')=date_format(#{date},'%y-%M-%d')
        </if>
        <if test="type==2">
            and date_format("o.create_time",'%y-%M-%d')=date_format(#{date},'%y-%M-%d')
        </if>
        <if test="type==3">
            and date_format("o.create_time",'%y-%M-%d')=date_format(#{date},'%y-%M-%d')
        </if>

        GROUP BY
        t.game_id
    </select>
    <select id="getNursingList" resultType="com.sqx.modules.app.entity.UserEntity">
        SELECT
        u.*,
        IF((SELECT COUNT(*) FROM orders o,appoint_information a WHERE o.order_taking_user_id = u.user_id AND o.orders_id
        = a.orders_id AND DATE
        (a.hope_time) = CURDATE() AND o.state IN (1, 5)) > 0,1,0) AS workStatus,
        uc.work_age AS workAge,
        uc.name AS realName
        FROM
        tb_user u,
        user_certification uc
        WHERE u.user_id = uc.user_id
        AND u.status = 1
        and uc.status =1
        and uc.authentication = 2
        AND u.age IS NOT NULL
        <if test="city!=null and city!=''">
            and uc.city = #{city}
        </if>
        <if test="workMin!=null">
            and uc.work_age &gt;= #{workMin}
        </if>
        <if test="workMax!=null">
            and uc.work_age &lt;= #{workMax}
        </if>
        <if test="ageMin!=null">
            and u.age &gt;= #{ageMin}
        </if>
        <if test="ageMax!=null">
            and u.age &lt;= #{ageMax}
        </if>
        <if test="sex!=null">
            and u.sex = #{sex}
        </if>
    </select>
    <select id="getNursingListV5" resultType="com.sqx.modules.app.entity.UserEntity">
        SELECT
        u.*,
        IF((SELECT COUNT(*) FROM orders o,appoint_information a WHERE o.order_taking_user_id = u.user_id AND o.orders_id
        = a.orders_id AND DATE
        (a.hope_time) = CURDATE() AND o.state IN (1, 5)) > 0,1,0) AS workStatus,
        uc.work_age AS workAge,
        (select count(0) from orders o where o.order_taking_user_id = u.user_id and o.state = 2) as orderCount
        FROM
        tb_user u,
        user_certification uc
        WHERE u.user_id = uc.user_id
        AND u.status = 1
        and uc.status =1
        <if test="isSafetyMoney=='是'.toString()">
            and u.is_safety_money = 1
        </if>
        and u.shop_state = 1
        <if test="realName!=null and realName!=''">
            and uc.name like concat("%",#{realName},"%")
        </if>
        <if test="phone!=null and phone!=''">
            and uc.phone like concat("%",#{phone},"%")
        </if>
        <if test="authentication!=null">
            and uc.authentication = #{authentication}
        </if>
        <if test="city!=null and city!=''">
            and (uc.city like concat("%",#{city},"%") or uc.province like concat("%",#{city},"%"))
        </if>
        <if test="workMin!=null">
            and uc.work_age &gt;= #{workMin}
        </if>
        <if test="workMax!=null">
            and uc.work_age &lt;= #{workMax}
        </if>
        <if test="ageMin!=null">
            and uc.age &gt;= #{ageMin}
        </if>
        <if test="ageMax!=null">
            and uc.age &lt;= #{ageMax}
        </if>
        <if test="sex!=null">
            and uc.sex = #{sex}
        </if>
        order by
        <if test="orderCount==null">
            orderCount desc,
        </if>
        <if test="orderCount!=null and orderCount==1">
            orderCount desc,
        </if>
        <if test="orderCount!=null and orderCount==2">
            orderCount asc,
        </if>
        <if test="finalScore!=null and finalScore==1">
            u.final_score desc,
        </if>
        <if test="finalScore!=null and finalScore==2">
            u.final_score desc,
        </if>
        u.create_time desc
    </select>
    <select id="getUserByCityRider" resultType="com.sqx.modules.app.entity.UserEntity">
        SELECT u.*
        FROM tb_user u,
             user_certification uc,
             rider_location r
        WHERE u.user_id = uc.user_id
          AND r.user_id = u.user_id
          AND u.is_safety_money = 1
          AND u.status = 1
          AND uc.status = 1
          and u.shop_state = 1
          AND (r.city LIKE CONCAT("%",#{city},"%") OR r.province LIKE CONCAT("%",#{city},"%"))
    </select>
    <select id="queryAgentUser" resultType="com.sqx.modules.app.entity.UserEntity">
        select *
        from tb_user u
        where 1 = 1 and u.is_agent=1
        <if test="province!=null and city==null and district==null">
            and u.province = #{province} and city is null and district is null
        </if>
        <if test="province!=null and city!=null and district==null">
            and u.province = #{province} and city=#{city} and district is null
        </if>
        <if test="province!=null and city!=null and district!=null">
            and u.province = #{province} and city=#{city} and district = #{district}
        </if>
        limit 0,1
    </select>
    <select id="getNursingInfo" resultType="com.sqx.modules.app.entity.UserEntity">
        SELECT u.*,
               IF((SELECT COUNT(*)
                   FROM orders o,
                        appoint_information a
                   WHERE o.order_taking_user_id = u.user_id
                     AND o.orders_id
                       = a.orders_id
                     AND DATE
                   (a.hope_time) = CURDATE() AND o.state IN (1, 5)) > 0,1,0)                            AS workStatus,
               uc.work_age                                                                              AS workAge,
               (select count(0) from orders o where o.order_taking_user_id = u.user_id and o.state = 2) as orderCount

        FROM tb_user u,
             user_certification uc
        WHERE u.user_id = uc.user_id
          and uc.user_id = #{userId}
    </select>
    <select id="selectUserList" resultType="com.sqx.modules.app.entity.UserEntity">
        select * from tb_user
        where 1=1
        <if test="search!=null and search!=''">
            and (user_id=#{search} or phone = #{search} or user_name =#{search} )
        </if>
        <if test="userName!=null and userName!=''">
            and user_name like concat('%',#{userName},'%')
        </if>
        <if test="phone!=null and phone!=''">
            and phone like concat('%',#{phone},'%')
        </if>
        <if test="sex!=null and sex!=0">
            and sex=#{sex}
        </if>
        <if test="invitationCode!=null and invitationCode!=''">
            and invitation_code like concat("%",#{invitationCode},"%")
        </if>
        <if test="inviterCode!=null and inviterCode!=''">
            and inviter_code like concat("%",#{inviterCode},"%")
        </if>
        <if test="platform!=null and platform!=''">
            and platform=#{platform}
        </if>
        <if test="sysPhone!=null and sysPhone!=''">
            and sys_phone=#{sysPhone}
        </if>
        <if test="status!=null and status!=0">
            and status=#{status}
        </if>
        <if test="isAuthentication!=null and isAuthentication!=0 and  isAuthentication==1">
            and is_authentication is null
        </if>
        <if test="isAuthentication!=null and isAuthentication!=0 and isAuthentication==2">
            and is_authentication is not null
        </if>
        <if test="isPromotion!=null and isPromotion==0">
            and (is_promotion is null or is_promotion = 0)
        </if>
        <if test="isPromotion!=null and isPromotion==1">
            and is_promotion=1
        </if>
        <if test="isAgent!=null and isAgent==0">
            and (is_agent is null or is_agent = 0)
        </if>
        <if test="isAgent!=null and isAgent==1">
            and is_agent=1
        </if>
        <if test="startTime!=null and startTime!='' and (endTime!=null and endTime!='')">
            and date_format(create_time,'%Y-%m-%d') BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="startTime!=null and startTime!='' and  (endTime==null or endTime=='')">
            and date_format(create_time,'%Y-%m-%d') &gt;= #{startTime}
        </if>
        <if test="endTime!=null and endTime!='' and  (startTime==null or startTime=='')">
            and date_format(create_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        order by create_time desc
    </select>
    <select id="getInviteCount" resultType="java.lang.Integer">
        select count(0)
        from tb_user u
        where inviter_code = #{invitationCode}
        and (u.is_authentication is not null and u.is_authentication=1)

    </select>


</mapper>