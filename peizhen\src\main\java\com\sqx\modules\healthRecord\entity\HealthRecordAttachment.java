package com.sqx.modules.healthRecord.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 健康记录附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("health_record_attachments")
public class HealthRecordAttachment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 附件ID
     */
    @TableId(value = "attachment_id", type = IdType.AUTO)
    @ApiModelProperty("附件ID")
    private Long attachmentId;

    /**
     * 健康记录ID，关联health_status_record表
     */
    @ApiModelProperty("健康记录ID")
    private Long recordId;

    /**
     * 文件名
     */
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     * 原始文件名
     */
    @ApiModelProperty("原始文件名")
    private String fileOriginalName;

    /**
     * 文件存储路径
     */
    @ApiModelProperty("文件存储路径")
    private String filePath;

    /**
     * 文件访问URL
     */
    @ApiModelProperty("文件访问URL")
    private String fileUrl;

    /**
     * 文件大小(字节)
     */
    @ApiModelProperty("文件大小(字节)")
    private Long fileSize;

    /**
     * 文件类型(image/document/video等)
     */
    @ApiModelProperty("文件类型")
    private String fileType;

    /**
     * 文件扩展名
     */
    @ApiModelProperty("文件扩展名")
    private String fileExtension;

    /**
     * MIME类型
     */
    @ApiModelProperty("MIME类型")
    private String mimeType;

    /**
     * 附件类型(1检查报告 2化验单 3影像资料 4用药照片 5症状照片 6其他)
     */
    @ApiModelProperty("附件类型(1检查报告 2化验单 3影像资料 4用药照片 5症状照片 6其他)")
    private Integer attachmentType;

    /**
     * 附件描述
     */
    @ApiModelProperty("附件描述")
    private String description;

    /**
     * 上传时间
     */
    @ApiModelProperty("上传时间")
    private LocalDateTime uploadTime;

    /**
     * 上传人ID
     */
    @ApiModelProperty("上传人ID")
    private Long uploadedBy;

    /**
     * 是否删除(0未删除 1已删除)
     */
    @ApiModelProperty("是否删除(0未删除 1已删除)")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
