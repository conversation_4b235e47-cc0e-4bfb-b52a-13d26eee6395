package com.sqx.modules.tbCoupon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.Result;
import com.sqx.modules.pay.entity.PayDetails;
import com.sqx.modules.tbCoupon.entity.TbCoupon;
import com.sqx.modules.tbCoupon.entity.TbCouponUser;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public interface TbCouponUserService extends IService<TbCouponUser> {

    IPage<TbCouponUser> getMyCouponList(Integer page, Integer limit, TbCouponUser couponUser);

    Result receiveEnvelope(Long userId, Long couponId,Integer num);

    Result receiveActivity(Long userId, Long couponId);

    void paymentCallback(PayDetails payDetails);

    void giveCoupon(TbCoupon tbCoupon, Long userId);

    Result giveUserCoupon(String userIds, Long couponId, Integer num);
}
