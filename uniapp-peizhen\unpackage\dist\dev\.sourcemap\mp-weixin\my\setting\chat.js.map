{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/chat.vue?2a1a", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/chat.vue?a617", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/chat.vue?a0d3", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/chat.vue?47de", "uni-app:///my/setting/chat.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/chat.vue?6d80", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/setting/chat.vue?92db"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "connected", "connecting", "msg", "type4", "listRight", "chat", "userHead", "content", "sendType", "type", "chatId", "ListItem", "ShopState", "ShopordersId", "Shopimage", "Shopmoney", "ShopTitle", "orderState", "ordersId", "orderimage", "orderNum", "ordermoney", "orderTitle", "orderCreateTime", "className", "Shopsales", "hand", "index", "page", "size", "countDown", "computed", "showMsg", "onUnload", "uni", "onLoad", "onShow", "onHide", "methods", "copy", "title", "showCancel", "cancelText", "confirmText", "success", "h5Copy", "textarea", "document", "getDateDiff", "result", "goDingdanDetail", "url", "goShop", "ShopClose", "orderClose", "goDingdan", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connect", "header", "method", "fail", "console", "close", "getTimeOrListItem1", "res", "d", "time", "setTimeout", "scrollTop", "duration", "getChatSave", "phone", "userId", "userName", "storeId", "storeHead", "storeName", "setChatSave", "avatar", "that", "chooseImage", "count", "sourceType", "filePath", "name", "config", "info", "viewImg", "imgsArray", "current", "urls"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmuB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0IvvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;UACAC;QACA;QACAC;QACAC;QACAC;MACA;MACAF;MACAG;MACAD;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;IACAA;EACA;EACAC;IACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACA,wCAEA;MACA;IACA;EACA;EACAC;IACAH;EACA;EACAI;IACAC;MAAA;MACAL;QACAM;QACAjC;QACAkC;QACAC;QACAC;QACAC;UACA;YACAV;cACAnC;cACA6C;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACAC;MACAA;MACAC;MACAD;MACAA;MACA;MACAA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAGA;QACAC;QACAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QAAA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACAC;MACAhB;QACAiB;MACA;IACA;IACAC;MACAlB;QACAiB;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAvB;UACA3B;UACAkC;QACA;QACA;MACA;MACA;MACA;MACAP;QACAM;MACA;MACAN;QACA;QACA;QACAiB;QACApD;UACA;YACAG;UACA;QACA;QACAwD;UACA;UACA;QACA;QACAC;QACAf;UACA;QAAA,CACA;QACAgB;UACA;QAAA;MAEA;MACA1B;QACA;QACA;QACAA;QACA;QACA;QACA;QACA;QACA2B;MACA;MACA3B;QACA;QACA;QACAA;QACAA;UACA3B;UACAkC;QACA;QACAoB;MACA;MACA3B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA2B;MACA;MACA3B;QACA;QACA;QACA;QACA2B;MACA;IACA;IACAC;MACA5B;IACA;IACA6B;MAAA;MACA,iEACA;QACA;QACA;UACA;UACAC;YACAC;YACA;cACAA;YACA;cACAC;YACA;YACA;cACA;cACA;cACAD;YACA;YACA;cACA;cACAA;YACA;YACA;cACA;cACAA;YACA;YACA;UACA;UACAE;YACAjC;cACAkC;cACAC;YACA;UACA;QACA;QACAnC;MACA;IACA;IACAoC;MAAA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;QACAC;QACAlE;QACAmE;QACAC;QACAC;QACAC;MACA;MACA;QACA;UACA;UACA1C;YACAM;UACA;UACA;QACA;MACA;IACA;IACAqC;MACA;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA,qGACA,6BACA;MACA;MACA;QACAL;QACAjE;QACAG;QACAD;QACAiE;QACAlE;MACA;MACAT;MACA;MACAmC;QACAnC;QACA6C;UACA;UACA;YACAkC;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAX;YACAY;UACA;UACAlB;QACA;QACAD;UACAC;QACA;MACA;MACA;IACA;IACA;IACAmB;MAAA;MAEA9C;QACA+C;QACAC;QACAtC;UACA;YACA;YACAV;cAAA;cACAiB;cAAA;cACAgC;cACAC;cACAxC;gBACA;gBACA;gBACAV;cACA;YACA;UACA;QACA;MACA;IACA;IACAmD;MACA;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;UACA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;MACAtD;QACAuD;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtkBA;AAAA;AAAA;AAAA;AAA0iC,CAAgB,q8BAAG,EAAC,C;;;;;;;;;;;ACA9jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/setting/chat.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/setting/chat.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./chat.vue?vue&type=template&id=53031d1c&\"\nvar renderjs\nimport script from \"./chat.vue?vue&type=script&lang=js&\"\nexport * from \"./chat.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chat.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/setting/chat.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=template&id=53031d1c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view style=\"width: 100%;padding-bottom: 140rpx;\">\n\t\t\t<view style=\"display: flex;flex-direction: column;\" v-for=\"(item,index) in ListItem\" :key='index'>\n\t\t\t\t<view style=\"margin-top: 15rpx;width: 100%;text-align: center;font-size: 26rpx;color: #999999;\">\n\t\t\t\t\t{{item.createTime}}\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"item.sendType === 2\" style=\"width: 83%;margin-right: 15%;\">\n\t\t\t\t\t<view class=\"chat-listitem\" style=\"float: left;margin-left: 10rpx;\">\n\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t<image src=\"../../static/logo.png\" class=\"chat-listitem-image\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"item.content && item.type === 1\" class=\"chat-listitem-text1\"\n\t\t\t\t\t\t\tstyle=\"margin-left: 20rpx;\">{{item.content}}</view>\n\t\t\t\t\t\t<image @tap=\"viewImg(item.content)\" v-if=\"item.content && item.type === 2\" :src=\"item.content\"\n\t\t\t\t\t\t\tstyle=\"height: 200rpx;width: 200rpx;margin-left: 20rpx;\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view v-if=\"item.sendType === 1\" style=\"width: 83%;margin-left: 15%;\">\n\t\t\t\t\t<view class=\"chat-listitem\" style=\"float: right;\">\n\t\t\t\t\t\t<view v-if=\"item.content && item.type === 1\" @longpress=\"copy(item.content)\"\n\t\t\t\t\t\t\tclass=\"chat-listitem-text\" style=\"margin-right: 20rpx;\">{{item.content}}</view>\n\t\t\t\t\t\t<view v-if=\"item.content && item.type === 4\" @click=\"goShop(item.content[3])\"\n\t\t\t\t\t\t\tstyle=\"width: 400rpx;background: #FFFFFF;height: max-content;margin-right: 20rpx;margin-top: 10rpx;border-radius: 20rpx;\">\n\t\t\t\t\t\t\t<image :src=\"item.content[0]\" class=\"chat-listitem-image-type4\"\n\t\t\t\t\t\t\t\tstyle=\"width: 400rpx;height: 350rpx;\"></image>\n\t\t\t\t\t\t\t<view style=\"padding: 10rpx;padding-bottom: 20rpx;\">\n\t\t\t\t\t\t\t\t<view style=\"color: #ed5732;font-size: 34rpx;\"><text style=\"font-size: 22rpx;\">￥ </text>\n\t\t\t\t\t\t\t\t\t{{item.content[2]}}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\tstyle=\"overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;width: 100%;height:  75upx;\">\n\t\t\t\t\t\t\t\t\t{{item.content[1]}}\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"item.content && item.type === 3\"\n\t\t\t\t\t\t\tstyle=\"width: 500rpx;background: #FFFFFF;height: max-content;margin-right: 20rpx;margin-top: 10rpx;border-radius: 20rpx;padding: 15rpx 10rpx;\">\n\t\t\t\t\t\t\t<view style=\"color: #000000;font-weight: 600;margin-left: 10rpx;\">你正在咨询的订单</view>\n\t\t\t\t\t\t\t<view style=\"display: flex;\">\n\t\t\t\t\t\t\t\t<image :src=\"item.content[0]\" class=\"chat-listitem-image-type4\"\n\t\t\t\t\t\t\t\t\tstyle=\"margin-left: 7rpx;margin-top: 20rpx;width: 110rpx;height: 110rpx;\"></image>\n\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\tstyle=\"margin-top: 15rpx;padding: 10rpx 0rpx 5rpx 10rpx;width: 75%;background: #f5f5f5;margin-left: 10rpx;\">\n\t\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\t\tstyle=\"font-size: 28rpx;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;height:  75upx;width: 100%;\">\n\t\t\t\t\t\t\t\t\t\t{{item.content[1]}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view style=\"color: #ed5732;font-size: 28rpx;\"><text style=\"font-size: 22rpx;\">￥\n\t\t\t\t\t\t\t\t\t\t</text>{{item.content[5]}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view style=\"color: #999999;margin-top: 10rpx;margin-left: 12rpx;\">\n\t\t\t\t\t\t\t\t<view>订单编号：{{item.content[3]}}</view>\n\t\t\t\t\t\t\t\t<view style=\"margin-top: 10rpx;\">创建时间：{{item.content[4]}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tstyle=\"float: right;margin-right: 10rpx;margin-top: 15rpx;background: #F9221D;color: #FFFFFF;border-radius: 50rpx;width: 150rpx;height: 50rpx;font-size: 24rpx;text-align: center;line-height: 50rpx;\"\n\t\t\t\t\t\t\t\t@click=\"goDingdanDetail(item.content[2])\">查看</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image @tap=\"viewImg(item.content)\" v-if=\"item.content && item.type === 2\" :src=\"item.content\"\n\t\t\t\t\t\t\tstyle=\"height: 200rpx;width: 200rpx;margin-right: 20rpx;\"></image>\n\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t<image v-if=\"item.chat.userHead\" :src=\"item.chat.userHead\" class=\"chat-listitem-image\">\n\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t<image v-else src=\"../../static/logo.png\" class=\"chat-listitem-image\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- <view v-if=\"item.sendType === 4\" style=\"width: 83%;margin-left: 15%;\">\n\t\t\t\t\t<view class=\"chat-listitem\" style=\"float: right;\">\n\t\t\t\t\t\t<view style=\"height: max-content;\">\n\t\t\t\t\t\t\t<image :src=\"type4[0]\" mode=\"\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image @tap=\"viewImg(item.content)\" v-if=\"item.content && item.type === 2\" :src=\"item.content\" style=\"height: 200rpx;width: 170rpx;margin-right: 20rpx;\"></image>\n\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t<image :src=\"item.chat.userHead\" class=\"chat-listitem-image\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"ShopState\"\n\t\t\tstyle=\"width: 95%;margin-left: 20rpx;height: 150upx;position: fixed;bottom: 120upx;z-index: 99;background-color: #FFFFFF;border-radius: 20rpx;\">\n\t\t\t<view style=\"display: flex;width: 100%;color: #000000;padding: 20rpx;\">\n\t\t\t\t<image :src=\"Shopimage\" style=\"width: 110rpx;height: 110rpx;\"></image>\n\t\t\t\t<view style=\"margin-left: 20rpx;width: 400rpx;\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tstyle=\"font-size: 34rpx;color: #000000;overflow: hidden;text-overflow: ellipsis;flex-wrap: nowrap;white-space: nowrap;width: 98%;\">\n\t\t\t\t\t\t{{ShopTitle}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"margin-top: 20rpx;color: #ed5732;font-size: 34rpx;\">￥{{Shopmoney}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"text-align: right;\">\n\t\t\t\t\t<image @click=\"ShopClose\" src=\"../../static/images/msg/close.png\"\n\t\t\t\t\t\tstyle=\"width: 30rpx;height: 30rpx;\"></image>\n\t\t\t\t\t<view\n\t\t\t\t\t\tstyle=\"margin-top: 20rpx;background: #F9221D;color: #FFFFFF;border-radius: 50rpx;width: 150rpx;height: 50rpx;font-size: 24rpx;text-align: center;line-height: 50rpx;\"\n\t\t\t\t\t\t@click=\"goMaijia\">发送给商家</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-if=\"orderState\"\n\t\t\tstyle=\"width: 95%;margin-left: 20rpx;height: 150upx;position: fixed;bottom: 120upx;z-index: 99;background-color: #FFFFFF;border-radius: 20rpx;\">\n\t\t\t<view style=\"display: flex;width: 100%;color: #000000;padding: 20rpx;\">\n\t\t\t\t<image :src=\"orderimage\" style=\"width: 110rpx;height: 110rpx;\"></image>\n\t\t\t\t<view style=\"margin-left: 20rpx;width: 400rpx;\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tstyle=\"font-size: 34rpx;color: #000000;overflow: hidden;text-overflow: ellipsis;flex-wrap: nowrap;white-space: nowrap;width: 98%;\">\n\t\t\t\t\t\t你可能想咨询该订单</view>\n\t\t\t\t\t<view style=\"margin-top: 20rpx;color: #ed5732;font-size: 34rpx;\">￥{{ordermoney}}</view>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"text-align: right;\">\n\t\t\t\t\t<image @click=\"orderClose\" src=\"../../static/images/msg/close.png\"\n\t\t\t\t\t\tstyle=\"width: 30rpx;height: 30rpx;\"></image>\n\t\t\t\t\t<view\n\t\t\t\t\t\tstyle=\"margin-top: 20rpx;background: #F9221D;color: #FFFFFF;border-radius: 50rpx;width: 150rpx;height: 50rpx;font-size: 24rpx;text-align: center;line-height: 50rpx;\"\n\t\t\t\t\t\t@click=\"goDingdan\">发送订单</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 底部聊天输入框 -->\n\t\t<view class=\"input-box\">\n\t\t\t<view class=\"justify-between padding-lr\"\n\t\t\t\tstyle=\"display: flex;margin-top: 15rpx;width: 100%;background-color: #FFFFFF;padding-top: 4upx;\">\n\t\t\t\t<image src=\"../../static/images/msg/add.png\" @click=\"chooseImage(['album'])\"\n\t\t\t\t\tstyle=\"width: 60rpx;height: 60rpx;margin-top: 8rpx;margin-right: 12rpx;\"></image>\n\t\t\t\t<input confirm-type=\"send\" @confirm='setChatSave(1)' type=\"text\" v-model=\"content\"\n\t\t\t\t\tstyle=\"width: 72%;height: 70rpx;background: #F5F5F5;margin: 4rpx 10rpx 0;border-radius: 70rpx;padding-left: 10rpx;\" />\n\t\t\t\t<view class=\"save\" @tap='setChatSave(1)'>发送</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport configdata from '../../common/config.js';\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tconnected: false,\n\t\t\t\tconnecting: false,\n\t\t\t\tmsg: false,\n\t\t\t\ttype4: [],\n\t\t\t\tlistRight: {\n\t\t\t\t\tchat: {\n\t\t\t\t\t\tuserHead: \"\"\n\t\t\t\t\t},\n\t\t\t\t\tcontent: \"\",\n\t\t\t\t\tsendType: 1,\n\t\t\t\t\ttype: 1\n\t\t\t\t},\n\t\t\t\tcontent: '',\n\t\t\t\tchatId: '',\n\t\t\t\ttype: 1,\n\t\t\t\tListItem: [],\n\t\t\t\tShopState: false,\n\t\t\t\tShopordersId: '',\n\t\t\t\tShopimage: '',\n\t\t\t\tShopmoney: '',\n\t\t\t\tShopTitle: '',\n\t\t\t\torderState: false,\n\t\t\t\tordersId: '',\n\t\t\t\torderimage: '',\n\t\t\t\torderNum: '',\n\t\t\t\tordermoney: '',\n\t\t\t\torderTitle: '',\n\t\t\t\torderCreateTime: '',\n\t\t\t\tclassName: '',\n\t\t\t\tShopsales: '',\n\t\t\t\thand: 1,\n\t\t\t\tindex: 0,\n\t\t\t\tpage: 0,\n\t\t\t\tsize: 1000,\n\t\t\t\tcountDown: ''\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\tshowMsg() {\n\t\t\t\tif (this.connected) {\n\t\t\t\t\tif (this.msg) {\n\t\t\t\t\t\treturn '收到消息：' + this.msg\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn '等待接收消息'\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\treturn '尚未连接'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.closeSocket()\n\t\t\tuni.hideLoading()\n\t\t},\n\t\tonLoad(d) {\n\t\t\tif (d.className) {\n\t\t\t\tthis.className = d.className;\n\t\t\t\tif (d.className === 'shop') {\n\t\t\t\t\tthis.ShopState = true;\n\t\t\t\t\tthis.ShopordersId = d.ordersId;\n\t\t\t\t\tthis.Shopimage = d.image;\n\t\t\t\t\tthis.Shopmoney = d.money;\n\t\t\t\t\tthis.Shopsales = d.sales;\n\t\t\t\t\tthis.ShopTitle = d.title;\n\t\t\t\t} else if (d.className === 'order') {\n\t\t\t\t\tthis.orderState = true;\n\t\t\t\t\tthis.ordersId = d.id;\n\t\t\t\t\tthis.orderimage = d.image;\n\t\t\t\t\tthis.ordermoney = d.money;\n\t\t\t\t\tthis.orderTitle = d.title;\n\t\t\t\t\tthis.orderNum = d.orderNum;\n\t\t\t\t\tthis.orderCreateTime = d.createTime;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.getChatSave();\n\t\t\tthis.connect();\n\t\t},\n\t\tonShow() {\n\t\t\tif (this.connected || this.connecting) {\n\n\t\t\t} else {\n\t\t\t\tthis.connect();\n\t\t\t}\n\t\t},\n\t\tonHide() {\n\t\t\tuni.closeSocket()\n\t\t},\n\t\tmethods: {\n\t\t\tcopy(content) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '温馨提示',\n\t\t\t\t\tcontent: '确认要复制此文字吗？',\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\tdata: content,\n\t\t\t\t\t\t\t\tsuccess: r => {\n\t\t\t\t\t\t\t\t\tthis.$queue.showToast('复制成功');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\th5Copy(content) {\n\t\t\t\tif (!document.queryCommandSupported('copy')) {\n\t\t\t\t\t// 不支持\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tlet textarea = document.createElement(\"textarea\")\n\t\t\t\ttextarea.value = content\n\t\t\t\ttextarea.readOnly = \"readOnly\"\n\t\t\t\tdocument.body.appendChild(textarea)\n\t\t\t\ttextarea.select() // 选择对象\n\t\t\t\ttextarea.setSelectionRange(0, content.length) //核心\n\t\t\t\tlet result = document.execCommand(\"copy\") // 执行浏览器复制命令\n\t\t\t\ttextarea.remove()\n\t\t\t\treturn result\n\t\t\t},\n\t\t\tgetDateDiff(data) {\n\t\t\t\t// 传进来的data必须是日期格式，不能是时间戳\n\t\t\t\t//var str = data;\n\t\t\t\t//将字符串转换成时间格式\n\t\t\t\tvar timePublish = new Date(data);\n\t\t\t\tvar timeNow = new Date();\n\t\t\t\tvar minute = 1000 * 60;\n\t\t\t\tvar hour = minute * 60;\n\t\t\t\tvar day = hour * 24;\n\t\t\t\tvar month = day * 30;\n\t\t\t\tvar result = \"2\";\n\n\t\t\t\tvar diffValue = timeNow - timePublish;\n\t\t\t\tvar diffMonth = diffValue / month;\n\t\t\t\tvar diffWeek = diffValue / (7 * day);\n\t\t\t\tvar diffDay = diffValue / day;\n\t\t\t\tvar diffHour = diffValue / hour;\n\t\t\t\tvar diffMinute = diffValue / minute;\n\n\n\t\t\t\tif (diffMonth > 3) {\n\t\t\t\t\tresult = timePublish.getFullYear() + \"-\";\n\t\t\t\t\tresult += timePublish.getMonth() + \"-\";\n\t\t\t\t\tresult += timePublish.getDate();\n\t\t\t\t} else if (diffMonth > 1) { //月\n\t\t\t\t\tresult = data.substring(0, 10);\n\t\t\t\t} else if (diffWeek > 1) { //周\n\t\t\t\t\tresult = data.substring(0, 10);\n\t\t\t\t} else if (diffDay > 1) { //天\n\t\t\t\t\tresult = data.substring(0, 10);\n\t\t\t\t} else if (diffHour > 1) { //小时\n\t\t\t\t\tresult = parseInt(diffHour) + \"小时前\";\n\t\t\t\t} else if (diffMinute > 1) { //分钟\n\t\t\t\t\tresult = parseInt(diffMinute) + \"分钟前\";\n\t\t\t\t} else {\n\t\t\t\t\tresult = \"刚刚\";\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\tgoDingdanDetail(id) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '../member/orderdetail?id=' + id\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoShop(ordersId) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: './commoditydetail?ordersId=' + ordersId\n\t\t\t\t});\n\t\t\t},\n\t\t\tShopClose() {\n\t\t\t\tthis.ShopState = false;\n\t\t\t},\n\t\t\torderClose() {\n\t\t\t\tthis.orderState = false;\n\t\t\t},\n\t\t\tgoDingdan() {\n\t\t\t\tthis.orderState = false;\n\t\t\t\tthis.setChatSave(3);\n\t\t\t},\n\t\t\tgoMaijia() {\n\t\t\t\tthis.ShopState = false;\n\t\t\t\tthis.setChatSave(4);\n\t\t\t},\n\t\t\tconnect() {\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tif (this.connected || this.connecting) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tcontent: '正在连接或者已经连接，请勿重复连接',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tlet token = uni.getStorageSync('token')\n\t\t\t\tthis.connecting = true\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '连接中...'\n\t\t\t\t})\n\t\t\t\tuni.connectSocket({\n\t\t\t\t\t// url: 'wss://game.shengqianxiong.com.cn/wss/websocket/' + userId,\n\t\t\t\t\t// url: 'ws://************:8180/sqx_fast/websocket/' + userId,\n\t\t\t\t\turl: this.config(\"WSHOST\") + userId,\n\t\t\t\t\tdata() {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tmsg: 'Hello'\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\theader: {\n\t\t\t\t\t\t'content-type': 'application/json',\n\t\t\t\t\t\t'token': token\n\t\t\t\t\t},\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t// 这里是接口调用成功的回调，不是连接成功的回调，请注意\n\t\t\t\t\t},\n\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t// 这里是接口调用失败的回调，不是连接失败的回调，请注意\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tuni.onSocketOpen((res) => {\n\t\t\t\t\tthis.connecting = false\n\t\t\t\t\tthis.connected = true\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t// \ttitle: '连接成功'\n\t\t\t\t\t// })\n\t\t\t\t\tconsole.log('onOpen', res);\n\t\t\t\t})\n\t\t\t\tuni.onSocketError((err) => {\n\t\t\t\t\tthis.connecting = false\n\t\t\t\t\tthis.connected = false\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tcontent: '网络较差，请稍后再试',\n\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t})\n\t\t\t\t\tconsole.log('onError', err);\n\t\t\t\t})\n\t\t\t\tuni.onSocketMessage((res) => {\n\t\t\t\t\t// let that = this;\n\t\t\t\t\t// let datas = JSON.parse(res.data)\n\t\t\t\t\t// let data = {\n\t\t\t\t\t// \tchat: {\n\t\t\t\t\t// \t\tuserHead: '../../static/logo.png'\n\t\t\t\t\t// \t},\n\t\t\t\t\t// \tcontent: datas.content,\n\t\t\t\t\t// \ttype: datas.type,\n\t\t\t\t\t// \tsendType: datas.sendType\n\t\t\t\t\t// }\n\t\t\t\t\t// that.ListItem.push(data);\n\t\t\t\t\tthis.getTimeOrListItem1();\n\t\t\t\t\tconsole.log('onMessage', res)\n\t\t\t\t})\n\t\t\t\tuni.onSocketClose((res) => {\n\t\t\t\t\tthis.connected = false\n\t\t\t\t\tthis.startRecive = false\n\t\t\t\t\tthis.msg = false\n\t\t\t\t\tconsole.log('onClose', res)\n\t\t\t\t})\n\t\t\t},\n\t\t\tclose() {\n\t\t\t\tuni.closeSocket()\n\t\t\t},\n\t\t\tgetTimeOrListItem1() {\n\t\t\t\tthis.$Request.getT('/app/chats/list?chatId=' + this.chatId).then(\n\t\t\t\t\tres => {\n\t\t\t\t\t\tthis.ListItem = [];\n\t\t\t\t\t\tif (res.data) {\n\t\t\t\t\t\t\tvar time = '';\n\t\t\t\t\t\t\tres.data.forEach(d => {\n\t\t\t\t\t\t\t\td.createTime = this.getDateDiff(d.createTime);\n\t\t\t\t\t\t\t\tif (d.createTime === time) {\n\t\t\t\t\t\t\t\t\td.createTime = '';\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\ttime = d.createTime;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (!d.chat.userHead) {\n\t\t\t\t\t\t\t\t\t// d.chat.userHead = '../../static/logo.png';\n\t\t\t\t\t\t\t\t\tlet avatar = this.$queue.getData('avatar');\n\t\t\t\t\t\t\t\t\td.chat.userHead = avatar\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (d.type === 4) {\n\t\t\t\t\t\t\t\t\tlet data = d.content.split(',');\n\t\t\t\t\t\t\t\t\td.content = data;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (d.type === 3) {\n\t\t\t\t\t\t\t\t\tlet data = d.content.split(',');\n\t\t\t\t\t\t\t\t\td.content = data;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis.ListItem.push(d);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\t\tscrollTop: 99999,\n\t\t\t\t\t\t\t\t\tduration: 0\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\tgetChatSave() {\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tlet phone = this.$queue.getData('phone');\n\t\t\t\tlet userName = this.$queue.getData('userName');\n\t\t\t\tif (!phone) {\n\t\t\t\t\tphone = this.$queue.getData('userName');\n\t\t\t\t}\n\t\t\t\tlet avatar = this.$queue.getData('avatar');\n\t\t\t\tlet data = {\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\tuserHead: avatar,\n\t\t\t\t\tuserName: userName,\n\t\t\t\t\tstoreId: '0',\n\t\t\t\t\tstoreHead: '省钱兄电竞',\n\t\t\t\t\tstoreName: ''\n\t\t\t\t}\n\t\t\t\tthis.$Request.postJson('/app/chats/save', data).then(res => {\n\t\t\t\t\tif (res.status === 0) {\n\t\t\t\t\t\tthis.chatId = res.data.chatId;\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.getTimeOrListItem1();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tsetChatSave(type) {\n\t\t\t\t//type:1文字 2图片\n\t\t\t\tif (type === 1 && this.content == '') {\n\t\t\t\t\tthis.$queue.showToast('请输入聊天内容');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (this.chatId == '' || this.chatId == undefined) {\n\t\t\t\t\tthis.$queue.showToast('网络较差，请稍后再试');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tif (type === 4) {\n\t\t\t\t\tthis.content = this.Shopimage + ',' + this.ShopTitle + ',' + this.Shopmoney + ',' + this.ShopordersId;\n\t\t\t\t}\n\t\t\t\tif (type === 3) {\n\t\t\t\t\tthis.content = this.orderimage + ',' + this.orderTitle + ',' + this.ordersId + ',' + this.orderNum +\n\t\t\t\t\t\t',' + this.orderCreateTime +\n\t\t\t\t\t\t',' + this.ordermoney\n\t\t\t\t}\n\t\t\t\tlet data = {\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\tcontent: this.content,\n\t\t\t\t\tchatId: this.chatId,\n\t\t\t\t\ttype: type,\n\t\t\t\t\tstoreId: '0',\n\t\t\t\t\tsendType: '1'\n\t\t\t\t}\n\t\t\t\tdata = JSON.stringify(data);\n\t\t\t\tlet that = this;\n\t\t\t\tuni.sendSocketMessage({\n\t\t\t\t\tdata: data,\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tlet avatar = that.$queue.getData('avatar');\n\t\t\t\t\t\tif (!avatar) {\n\t\t\t\t\t\t\tavatar = '../../static/logo.png';\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// let data = {\n\t\t\t\t\t\t// \tchat: {\n\t\t\t\t\t\t// \t\tuserHead: avatar\n\t\t\t\t\t\t// \t},\n\t\t\t\t\t\t// \tcontent: that.content,\n\t\t\t\t\t\t// \ttype: type,\n\t\t\t\t\t\t// \tsendType: 1\n\t\t\t\t\t\t// }\n\t\t\t\t\t\t// that.ListItem.push(data);\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthat.getTimeOrListItem1();\n\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\tconsole.log(that.content);\n\t\t\t\t\t},\n\t\t\t\t\tfail(err) {\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tthis.content = '';\n\t\t\t},\n\t\t\t//发送图片\n\t\t\tchooseImage(sourceType) {\n\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tfor (let i = 0; i < res.tempFilePaths.length; i++) {\n\t\t\t\t\t\t\tthis.$queue.showLoading(\"上传中...\");\n\t\t\t\t\t\t\tuni.uploadFile({ // 上传接口\n\t\t\t\t\t\t\t\turl: this.config(\"APIHOST1\") + '/alioss/upload', //真实的接口地址\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePaths[i],\n\t\t\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\t\t\tsuccess: (uploadFileRes) => {\n\t\t\t\t\t\t\t\t\tthis.content = JSON.parse(uploadFileRes.data).data;\n\t\t\t\t\t\t\t\t\tthis.setChatSave(2);\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tconfig: function(name) {\n\t\t\t\tvar info = null;\n\t\t\t\tif (name) {\n\t\t\t\t\tvar name2 = name.split(\".\"); //字符分割\n\t\t\t\t\tif (name2.length > 1) {\n\t\t\t\t\t\tinfo = configdata[name2[0]][name2[1]] || null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tinfo = configdata[name] || null;\n\t\t\t\t\t}\n\t\t\t\t\tif (info == null) {\n\t\t\t\t\t\tlet web_config = cache.get(\"web_config\");\n\t\t\t\t\t\tif (web_config) {\n\t\t\t\t\t\t\tif (name2.length > 1) {\n\t\t\t\t\t\t\t\tinfo = web_config[name2[0]][name2[1]] || null;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tinfo = web_config[name] || null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn info;\n\t\t\t},\n\t\t\t//查看大图\n\t\t\tviewImg(item) {\n\t\t\t\tlet imgsArray = [];\n\t\t\t\timgsArray[0] = item;\n\t\t\t\tuni.previewImage({\n\t\t\t\t\tcurrent: 0,\n\t\t\t\t\turls: imgsArray\n\t\t\t\t});\n\t\t\t},\n\t\t},\n\t};\n</script>\n\n<style>\n\tpage {\n\t\tbackground: #F5F5F5;\n\t}\n\n\t.input-box {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\theight: 100rpx;\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tbox-sizing: content-box;\n\t\tz-index: 999;\n\t\t/* background-color: #ececec; */\n\t\t/* padding: 0 5rpx; */\n\t}\n\n\t.chat-listitem {\n\t\tdisplay: flex;\n\t\tmargin-top: 20rpx;\n\t\tpadding: 10rpx;\n\t}\n\n\t.chat-listitem-text {\n\t\tcolor: #FFFFFF;\n\t\tbackground: #557EFD;\n\t\tmargin-top: 10rpx;\n\t\twidth: fit-content;\n\t\tpadding: 15rpx;\n\t\tfont-size: 30rpx;\n\t\theight: max-content;\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.chat-listitem-text1 {\n\t\t/* color: #FFFFFF; */\n\t\tbackground: #FFFFFF;\n\t\tmargin-top: 10rpx;\n\t\twidth: fit-content;\n\t\tpadding: 15rpx;\n\t\tfont-size: 30rpx;\n\t\theight: max-content;\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.chat-listitem-image-type4 {\n\t\t/* color: #FFFFFF; */\n\t\tbackground: #FFFFFF;\n\t\twidth: fit-content;\n\t\tfont-size: 30rpx;\n\t\theight: max-content;\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\t\tborder-top-left-radius: 20rpx;\n\t\tborder-top-right-radius: 20rpx;\n\t}\n\n\t.chat-listitem-image {\n\t\tmargin-top: 5rpx;\n\t\twidth: 75rpx;\n\t\theight: 75rpx;\n\t\tborder-radius: 5rpx;\n\t}\n\n\t.save {\n\t\twidth: 130rpx;\n\t\ttext-align: center;\n\t\tborder-radius: 10rpx;\n\t\theight: 70rpx;\n\t\tcolor: #FFF;\n\t\tbackground: #557EFD;\n\t\tmargin: 5rpx 10rpx 0;\n\t\tline-height: 70rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./chat.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447621949\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}