logging:
  file:
    name: logs/peizhen.log
# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30
    connection-timeout: 5000ms
  port: 7168
  servlet:
    context-path: /sqx_fast

spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  # 环境 dev|test|prod
  profiles:
    active: prod
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10240MB
      max-request-size: 10240MB
      enabled: true
  redis:
    open: false  # 是否开启redis缓存  true开启   false关闭
    database: 2
    host: ************
    port: 6379
    password: <PERSON><PERSON><PERSON><PERSON>@888    # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  mvc:
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ant_path_matcher
#  resources:
#    add-mappings: false

# AI配置
ai:
  # 默认启用的模型
  default-model: deepseek-chat
  # API调用超时时间(秒)
  timeout: 60
  # 最大重试次数
  max-retries: 3

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sqx.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      logic-delete-value: -1
      logic-not-delete-value: 0
      insert-strategy: not_empty
      update-strategy: not_empty
      select-strategy: not_empty
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

sqx:
  redis:
    open: false
  shiro:
    redis: false
  # APP模块，是通过jwt认证的，如果要使用APP模块，则需要修改【加密秘钥】
  jwt:
    # 加密秘钥
    secret: f4e2e52034348f86b67cde581c0f9eb5
    # token有效时长，7天，单位秒
    expire: 2592000
    header: token