{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/citys/citys.vue?256f", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/citys/citys.vue?a0f4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/citys/citys.vue?ff0c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/index/citys/citys.vue?c18e", "uni-app:///pages/index/citys/citys.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formatName", "activeCity", "hotCity", "obtainCitys", "components", "citySelect", "onLoad", "setTimeout", "methods", "cityClick", "uni", "icon", "title", "mask", "console", "success", "beforePage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;;;AAGpD;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACQvwB;;;;;;;;;;;;;eAGA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IAAA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;EACA;EACAC;IACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAH;MACAH;QACA;QACA;QACA;QACAO;QACAJ;UACAK;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/index/citys/citys.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/citys/citys.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./citys.vue?vue&type=template&id=394285ca&\"\nvar renderjs\nimport script from \"./citys.vue?vue&type=script&lang=js&\"\nexport * from \"./citys.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/citys/citys.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./citys.vue?vue&type=template&id=394285ca&\"", "var components\ntry {\n  components = {\n    citySelect: function () {\n      return import(\n        /* webpackChunkName: \"components/city-select/city-select\" */ \"@/components/city-select/city-select.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./citys.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./citys.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<city-select @cityClick=\"cityClick\" :formatName=\"formatName\" :obtainCitys=\"obtainCitys\" :isSearch=\"true\"\n\t\t\tref=\"citys\"></city-select>\n\t</view>\n</template>\n\n<script>\n\timport citys from './citys.js'\n\t// console.log(citys.length)\n\timport citySelect from '@/components/city-select/city-select.vue'\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t//需要构建索引参数的名称（注意：传递的对象里面必须要有这个名称的参数）\n\t\t\t\tformatName: 'title',\n\t\t\t\t//当前城市\n\t\t\t\tactiveCity: {},\n\t\t\t\t//热门城市\n\t\t\t\thotCity: [],\n\t\t\t\t//显示的城市数据\n\t\t\t\tobtainCitys: [],\n\t\t\t}\n\t\t},\n\t\tcomponents: {\n\t\t\tcitySelect\n\t\t},\n\t\tonLoad(e) {\n\n\t\t\t//动态更新数据\n\t\t\tsetTimeout(() => {\n\t\t\t\t//修改数据格式\n\t\t\t\tthis.formatName = 'cityName'\n\t\t\t\t//修改当前城市\n\t\t\t\t// this.activeCity = {\n\t\t\t\t// \tcityName: '南京',\n\t\t\t\t// \tcityCode: 110100\n\t\t\t\t// }\n\t\t\t\t//修改热门城市\n\t\t\t\t// this.hotCity = [\n\t\t\t\t// \t{\n\t\t\t\t// \t\tcityName: '南京',\n\t\t\t\t// \t\tcityCode: 110100\n\t\t\t\t// \t},\n\t\t\t\t// \t{\n\t\t\t\t// \t\tcityName: '北京',\n\t\t\t\t// \t\tcityCode: 110102\n\t\t\t\t// \t}\n\t\t\t\t// ]\n\t\t\t\t//修改构建索引数据\n\t\t\t\tthis.obtainCitys = citys\n\n\t\t\t}, 100)\n\t\t},\n\t\tmethods: {\n\t\t\tcityClick(item) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '修改成功',\n\t\t\t\t\tmask: true\n\t\t\t\t})\n\t\t\t\tuni.setStorageSync('city', item.cityName)\n\t\t\t\tsetTimeout(function() {\n\t\t\t\t\t// uni.navigateBack()\n\t\t\t\t\tlet pages = getCurrentPages(); // 当前页面\n\t\t\t\t\tlet beforePage = pages[pages.length - 2]; // 上一页\n\t\t\t\t\tconsole.log(beforePage)\n\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\tbeforePage.onShow(); // 执行上一页的onShow方法\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}, 1000)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style></style>\n"], "sourceRoot": ""}