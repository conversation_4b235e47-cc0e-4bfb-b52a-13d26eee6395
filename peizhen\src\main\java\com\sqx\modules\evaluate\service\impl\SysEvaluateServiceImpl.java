package com.sqx.modules.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.evaluate.entity.SysEvaluate;
import com.sqx.modules.evaluate.dao.SysEvaluateDao;
import com.sqx.modules.evaluate.service.SysEvaluateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.service.OrdersService;
import com.sqx.modules.utils.SenInfoCheckUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class SysEvaluateServiceImpl extends ServiceImpl<SysEvaluateDao, SysEvaluate> implements SysEvaluateService {
    @Autowired
    SysEvaluateDao evaluateDao;
    @Autowired
    OrdersService ordersService;
    @Autowired
    UserService userService;

    @Override
    public Result addEvaluate(SysEvaluate evaluate) {
        if (!SenInfoCheckUtil.contentFilter(evaluate.getEvaluateMessage())) {
            return Result.error("文本存在违规信息，请检测后重新提交");
        }
        evaluate.setCreateTime(new Date());
        Orders orders = ordersService.getOne(new QueryWrapper<Orders>().eq("orders_no", evaluate.getIndentNumber()));
        if (orders == null) {
            return Result.error("当前订单不存在");
        }
        if (!"2".equals(orders.getState())) {
            return Result.error("当前订单未完成");
        }
        evaluate.setRiderUserId(orders.getOrderTakingUserId());
        evaluateDao.insert(evaluate);
        //更新接单人评分
        UserEntity userEntity = userService.getById(evaluate.getRiderUserId());
        if (userEntity == null) {
            return Result.error("当前用户不存在");
        }
        //总分数
        String totalScore = evaluateDao.getTotalScore(userEntity.getUserId());
        //评论总条数
        Integer count = evaluateDao.selectCount(new QueryWrapper<SysEvaluate>().eq("rider_user_id", userEntity.getUserId()));
        //更新用户评分
        userEntity.setFinalScore(new BigDecimal(totalScore).divide(new BigDecimal(count), 1, BigDecimal.ROUND_DOWN).toString());
        userService.updateById(userEntity);
        return Result.success();

    }

    @Override
    public IPage<SysEvaluate> getUserEvaluateList(Integer page, Integer limit, SysEvaluate evaluate) {
        Page<SysEvaluate> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }

        return evaluateDao.getUserEvaluateList(pages, evaluate);

    }

    @Override
    public Result deleteEvaluate(Long userId, Long evaluateId, Integer auth) {
        SysEvaluate sysEvaluate = evaluateDao.selectById(evaluateId);
        UserEntity userEntity = userService.getById(sysEvaluate.getRiderUserId());

        if (auth != 1) {
            if (!userId.equals(sysEvaluate.getUserId())) {
                return Result.error("您无法操作当前评论");
            }
        }
        evaluateDao.deleteById(evaluateId);

        //总分数
        String totalScore = evaluateDao.getTotalScore(userId);
        if (StringUtils.isBlank(totalScore)) {
            userEntity.setFinalScore("5.0");
        } else {
            //评论总条数
            Integer count = evaluateDao.selectCount(new QueryWrapper<SysEvaluate>().eq("rider_user_id", userEntity.getUserId()));
            //更新用户评分
            userEntity.setFinalScore(new BigDecimal(totalScore).divide(new BigDecimal(count), 1, BigDecimal.ROUND_DOWN).toString());
        }


        return userService.updateById(userEntity) ? Result.success() : Result.error();

    }
}
