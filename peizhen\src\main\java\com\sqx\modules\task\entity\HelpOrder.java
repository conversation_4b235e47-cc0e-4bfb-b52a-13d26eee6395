package com.sqx.modules.task.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sqx.modules.app.entity.UserEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  help_order
 * <AUTHOR> 2021-01-06
 */
@Data
@TableName("help_order")
public class HelpOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 跑腿订单id
     */
    @TableId
    private Long id;

    /**
     *  接单id
     */
    private Long helpTakeId;


    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 内容
     */
    private String content;

    @ApiModelProperty("省")
    private String province;
    @ApiModelProperty("市")
    private String city;
    @ApiModelProperty("区")
    private String district;
    @ApiModelProperty("详细地址")
    private String detailsAddress;
    @ApiModelProperty("纬度")
    private String longitude;
    @ApiModelProperty("经度")
    private String latitude;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("电话")
    private String phone;

    /**
     * 期望送达时间
     */
    private String deliveryTime;

    /**
     * 服务类型
     */
    private Long gameId;

    /**
     * 发单人id
     */
    private Long userId;

    /**
     * 佣金
     */
    private BigDecimal commission;

    /**
     * 实际发布金额
     */
    private BigDecimal money;

    /**
     * 图片
     */
    private String image;

    /**
     * 收货码
     */
    private String code;

    /**
     * 状态（1待审核 2待接单 3待送达 4已完成  5取消）
     */
    private Integer status;


    /**
     * 支付方式 1微信 2支付宝
     */
    private Integer payType;

    /**
     * 创建时间
     */
    private String createTime;

    private String cause;

    /**
     * 支付方式 1零钱 2微信  3支付宝
     */
    private Integer payWay;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private String avatar;

    @TableField(exist = false)
    private UserEntity user;

    @TableField(exist = false)
    private HelpTake helpTake;

    @TableField(exist = false)
    private String serviceName;

    @TableField(exist = false)
    private Double distance;

    @TableField(exist = false)
    private Integer classify;

}
