<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.app.dao.CarDao">

    <select id="selectCarList" resultType="com.sqx.modules.app.entity.Car">
        select c.*,u.user_name as userName,u.phone from car c
        left join tb_user u on u.user_id=c.user_id
        where 1=1
        <if test="userId!=null">
            and c.user_id=#{userId}
        </if>
        <if test="userName!=null and userName!=''">
            and u.user_name like concat('%',#{userName},'%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%',#{phone},'%')
        </if>
        order by c.create_time desc
    </select>

</mapper>