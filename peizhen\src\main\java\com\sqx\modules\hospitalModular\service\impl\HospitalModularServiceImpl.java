package com.sqx.modules.hospitalModular.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.hospitalModular.entity.HospitalModular;
import com.sqx.modules.hospitalModular.dao.HospitalModularDao;
import com.sqx.modules.hospitalModular.service.HospitalModularService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Service
public class HospitalModularServiceImpl extends ServiceImpl<HospitalModularDao, HospitalModular> implements HospitalModularService {

    @Autowired
    private HospitalModularDao modularDao;

    @Override
    public Result addModular(HospitalModular hospitalModular) {
        HospitalModular modular = modularDao.selectOne(new QueryWrapper<HospitalModular>().eq("modular_name", hospitalModular.getModularName()));
        if (modular != null) {
            return Result.error("服务分类不能为空!");
        }
        return Result.upStatus(modularDao.insert(hospitalModular));


    }

    @Override
    public Result updateModular(HospitalModular hospitalModular) {
        HospitalModular modular = modularDao.selectOne(new QueryWrapper<HospitalModular>().eq("modular_name", hospitalModular.getModularName()).ne("modular_id", hospitalModular.getModularId()));
        if (modular != null) {
            return Result.error("服务分类名称不能重复!");
        }
        return Result.upStatus(modularDao.updateById(hospitalModular));

    }

    @Override
    public Result deleteModular(Long modularId) {
        return Result.upStatus(modularDao.deleteById(modularId));

    }

    @Override
    public HospitalModular getModularInfo(Long modularId) {
        return modularDao.selectById(modularId);
    }

    @Override
    public IPage<HospitalModular> getModularList(Integer page, Integer limit, HospitalModular hospitalModular) {
        Page<HospitalModular> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
             pages = new Page<>();
             pages.setSize(-1);
        }
        return modularDao.selectPage(pages,new QueryWrapper<>(hospitalModular));
    }
}
