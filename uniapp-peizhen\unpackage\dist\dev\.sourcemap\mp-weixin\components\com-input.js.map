{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/com-input.vue?22c5", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/com-input.vue?2931", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/com-input.vue?a410", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/com-input.vue?621e", "uni-app:///components/com-input.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/com-input.vue?f8e4", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/com-input.vue?9aff"], "names": ["data", "showPassword", "second", "isRunCode", "typeList", "left", "right", "up", "down", "props", "readOnly", "type", "default", "logo", "value", "placeholder", "isShowCode", "codeText", "setTime", "maxlength", "isShowPass", "icon", "title", "tips", "navigateType", "border", "hoverClass", "iconColor", "mounted", "_this", "clearInterval", "methods", "showPass", "onInput", "setCode", "runCodes", "console", "countDown", "computed", "_type", "_isShowPass", "_isShowCode", "_setTime", "getVerCodeSecond"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAytB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqB7uB;AACA;AACA;AACA;AACA;AAHA,eAIA;EACAA;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAD;IAAA;IACAE;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;MACA;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;IACAe;MACAhB;MACAC;IACA;EACA;EACAgB;IAAA;IACAC;IACA;IACA;MACA;IACA;IACAC;EACA;;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACAC;MACA;MACA;QAEA;QACA;QACAN;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;MACA;;MAEA;MACAO;QACAR;QACA;UACAA;UACAC;QACA;MACA;IACA;EAGA;EACAQ;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/LA;AAAA;AAAA;AAAA;AAAg2C,CAAgB,guCAAG,EAAC,C;;;;;;;;;;;ACAp3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/com-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./com-input.vue?vue&type=template&id=9a63825a&\"\nvar renderjs\nimport script from \"./com-input.vue?vue&type=script&lang=js&\"\nexport * from \"./com-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./com-input.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/com-input.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./com-input.vue?vue&type=template&id=9a63825a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./com-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./com-input.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"mix-list-cell\" :class=\"border\" hover-class=\"cell-hover\" :hover-stay-time=\"50\">\n\t\t\t<text class=\"cell-tit\">{{title}}</text>\n\t\t\t<input class=\"main-input\" :value=\"value\" :type=\"_type\" placeholder-class=\"placeholder-class\"\n\t\t\t\t:maxlength=\"maxlength\" :placeholder=\"placeholder\" :password=\"type==='password'&&!showPassword\"\n\t\t\t\t@input=\"onInput\" :disabled=\"readOnly\" />\n\n\t\t\t<!-- 是否可见密码 -->\n\t\t\t<image v-if=\"_isShowPass&&type==='password'&&!_isShowCode\" class=\"img cuIcon\"\n\t\t\t\t:class=\"showPassword?'cuIcon-attention':'cuIcon-attentionforbid'\" @tap=\"showPass\"></image>\n\t\t\t<!-- 倒计时 -->\n\t\t\t<view v-if=\"_isShowCode&&!_isShowPass\" :class=\"['vercode',{'vercode-run': second>0}]\" @click=\"setCode\">\n\t\t\t\t{{ getVerCodeSecond }}\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\n</template>\n\n<script>\n\tvar _this, countDown;\n\t/**\n\t *  简单封装了下， 应用范围比较狭窄，可以在此基础上进行扩展使用\n\t *  比如加入image， iconSize可控等\n\t */\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshowPassword: false, //是否显示明文\n\t\t\t\tsecond: 0, //倒计时\n\t\t\t\tisRunCode: false, //是否开始倒计时\n\t\t\t\ttypeList: {\n\t\t\t\t\tleft: 'icon-zuo',\n\t\t\t\t\tright: 'icon-you',\n\t\t\t\t\tup: 'icon-shang',\n\t\t\t\t\tdown: 'icon-xia'\n\t\t\t\t},\n\t\t\t}\n\t\t},\n\t\tprops: {\n\t\t\treadOnly: {\n\t\t\t\t//是否显示获取验证码（二选一）\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false,\n\t\t\t},\n\t\t\ttype: String, //类型\n\t\t\tlogo: String, //类型\n\t\t\tvalue: String, //值\n\t\t\tplaceholder: String, //框内提示\n\t\t\tisShowCode: {\n\t\t\t\t//是否显示获取验证码（二选一）\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false,\n\t\t\t},\n\t\t\tcodeText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"获取验证码\",\n\t\t\t},\n\t\t\tsetTime: {\n\t\t\t\t//倒计时时间设置\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 60,\n\t\t\t},\n\t\t\tmaxlength: {\n\t\t\t\t//最大长度\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 30,\n\t\t\t},\n\t\t\tisShowPass: {\n\t\t\t\t//是否显示密码图标（二选一）\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false,\n\t\t\t},\n\t\t\ticon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '标题'\n\t\t\t},\n\t\t\ttips: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tnavigateType: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'right'\n\t\t\t},\n\t\t\tborder: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'b-b'\n\t\t\t},\n\t\t\thoverClass: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'cell-hover'\n\t\t\t},\n\t\t\ticonColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#333'\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\t_this = this\n\t\t\t//准备触发\n\t\t\tthis.$on('runCodes', (val) => {\n\t\t\t\tthis.runCodes(val);\n\t\t\t});\n\t\t\tclearInterval(countDown); //先清理一次循环，避免缓存\n\t\t},\n\t\tmethods: {\n\t\t\tshowPass() {\n\t\t\t\t//是否显示密码\n\t\t\t\tthis.showPassword = !this.showPassword\n\t\t\t},\n\t\t\tonInput(e) {\n\t\t\t\t//传出值\n\t\t\t\tthis.$emit('input', e.target.value)\n\t\t\t},\n\t\t\tsetCode() {\n\t\t\t\t//设置获取验证码的事件\n\t\t\t\tif (this.isRunCode) {\n\t\t\t\t\t//判断是否开始倒计时，避免重复点击\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tthis.$emit('setCode')\n\t\t\t},\n\t\t\trunCodes(val) {\n\t\t\t\tconsole.error(\"runCodes\")\n\t\t\t\t//开始倒计时\n\t\t\t\tif (String(val) == \"0\") {\n\n\t\t\t\t\t//判断是否需要终止循环\n\t\t\t\t\tthis.second = 0; //初始倒计时\n\t\t\t\t\tclearInterval(countDown); //清理循环\n\t\t\t\t\tthis.isRunCode = false; //关闭循环状态\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tif (this.isRunCode) {\n\t\t\t\t\t//判断是否开始倒计时，避免重复点击\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\tthis.isRunCode = true\n\t\t\t\tthis.second = this._setTime //倒数秒数\n\n\t\t\t\tlet _this = this;\n\t\t\t\tcountDown = setInterval(function() {\n\t\t\t\t\t_this.second--\n\t\t\t\t\tif (_this.second == 0) {\n\t\t\t\t\t\t_this.isRunCode = false\n\t\t\t\t\t\tclearInterval(countDown)\n\t\t\t\t\t}\n\t\t\t\t}, 1000)\n\t\t\t}\n\n\n\t\t},\n\t\tcomputed: {\n\t\t\t_type() {\n\t\t\t\t//处理值\n\t\t\t\tconst type = this.type\n\t\t\t\treturn type == 'password' ? 'text' : type\n\t\t\t},\n\t\t\t_isShowPass() {\n\t\t\t\t//处理值\n\t\t\t\treturn String(this.isShowPass) !== 'false'\n\t\t\t},\n\t\t\t_isShowCode() {\n\t\t\t\t//处理值\n\t\t\t\treturn String(this.isShowCode) !== 'false'\n\t\t\t},\n\t\t\t_setTime() {\n\t\t\t\t//处理值\n\t\t\t\tconst setTime = Number(this.setTime)\n\t\t\t\treturn setTime > 0 ? setTime : 60\n\t\t\t},\n\t\t\tgetVerCodeSecond() {\n\t\t\t\t//验证码倒计时计算\n\t\t\t\tif (this.second <= 0) {\n\t\t\t\t\treturn this.codeText;\n\t\t\t\t} else {\n\t\t\t\t\tif (this.second < 10) {\n\t\t\t\t\t\treturn '0' + this.second + \"s\";\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn this.second + \"s\";\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang='scss'>\n\t.main-input {\n\t\tflex: 1;\n\t\ttext-align: left;\n\t\t/* color: white; */\n\t\tfont-size: 16px;\n\t\tpadding-right: 6px;\n\t\tmargin-left: 10px;\n\t\tborder: 1rpx solid #d9d9d9;\n\t\theight: 90rpx;\n\t\tborder-radius: 10rpx;\n\t\tpadding: 0upx 30upx;\n\t}\n\n\t.icon .mix-list-cell.b-b:after {\n\t\tleft: 45px;\n\t}\n\n\t.placeholder-class {\n\t\t/* color: white; */\n\t\topacity: 0.5;\n\t}\n\n\t.mix-list-cell {\n\t\tborder-radius: 32upx;\n\t\tmargin-top: 1px;\n\t\tfont-size: 32upx;\n\t\tbackground: #ffffff;\n\t\ttext-align: left;\n\t\tdisplay: flex;\n\t\tmargin: 32upx;\n\t\t/* padding: 24upx 32upx; */\n\t\tposition: relative;\n\n\t\t&.cell-hover {\n\t\t\tbackground: transparent;\n\t\t}\n\n\t\t&.b-b:after {\n\t\t\tleft: 16px;\n\t\t}\n\n\t\t.cell-icon {\n\t\t\talign-self: center;\n\t\t\twidth: 28px;\n\t\t\tmax-height: 30px;\n\t\t\tfont-size: 18px;\n\t\t}\n\n\t\t.cell-more {\n\t\t\talign-self: center;\n\t\t\tfont-size: 16px;\n\t\t\tcolor: #606266;\n\t\t\tmargin-left: 10px;\n\t\t}\n\n\t\t.cell-tit {\n\t\t\twidth: 80px;\n\t\t\tfont-size: 16px;\n\t\t\t/* color: white; */\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.cell-tip {\n\t\t\tfont-size: 14px;\n\t\t\tcolor: white;\n\t\t}\n\n\t}\n\n\t.items {\n\t\tposition: absolute;\n\t\theight: 48px;\n\t\twidth: 100%;\n\t\tbackground: #FFFFFF;\n\t\t/*opacity:0.05;*/\n\t}\n\n\t.main-list {\n\t\topacity: 0.8;\n\t\tz-index: 88;\n\t\tbackground: white;\n\t\tborder: 1px solid white;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\theight: 18px;\n\t\t/* Input 高度 */\n\t\tcolor: #333333;\n\t\tpadding: 16px;\n\t\tmargin-top: 12px;\n\t\tmargin-bottom: 12px;\n\t}\n\n\t.img {\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\tfont-size: 16px;\n\t}\n\n\t.vercode {\n\t\tcolor: #e10a07;\n\t\tfont-size: 14px;\n\t}\n\n\t.vercode-run {\n\t\tcolor: black !important;\n\t}\n\n\t.oBorder {\n\n\t\tborder-radius: 2.5rem;\n\t\t-webkit-box-shadow: 0 0 30px 0 rgba(43, 86, 112, .1);\n\t\tbox-shadow: 0 0 30px 0 rgba(43, 86, 112, .1);\n\t}\n</style>\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./com-input.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./com-input.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627812\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}