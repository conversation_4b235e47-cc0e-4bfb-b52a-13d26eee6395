package com.sqx.modules.creditRecord.controller;


import com.sqx.common.utils.Result;
import com.sqx.modules.creditRecord.entity.CreditRecord;
import com.sqx.modules.creditRecord.service.CreditRecordService;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * 信用分记录表
 *
 * @since 2023-05-17
 */
@RestController
@RequestMapping("/admin/creditRecord/")
public class CreditRecordController {

    @Autowired
    private CreditRecordService recordService;

    /**
     * 获取信用分记录
     *
     * @param page
     * @param limit
     * @param creditRecord
     * @return
     */
    @GetMapping("getCreditRecordList")
    @ApiModelProperty("获取信用分记录")
    public Result getCreditRecordList(Integer page, Integer limit, CreditRecord creditRecord) {
        return Result.success().put("data", recordService.getCreditRecordList(page, limit, creditRecord));
    }
}

