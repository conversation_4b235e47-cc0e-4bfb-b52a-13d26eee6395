# universalMission页面医生管理功能说明

## 功能概述

在universalMission页面中添加了医生管理功能，包括：
1. 新增"医生管理"tab页
2. 在医院管理操作栏添加"医生列表"按钮
3. 支持从医院列表直接跳转到该医院的医生列表

## 实现的功能

### 1. 新增医生管理Tab

在universalMission页面的tab栏中添加了"医生管理"选项卡，包含：

**搜索功能**：
- 医生姓名模糊搜索
- 医院下拉选择
- 科室下拉选择（级联，根据选择的医院动态加载）
- 查询和重置按钮

**列表显示**：
- 医生编号
- 头像显示
- 医生姓名
- 工号
- 所属医院
- 所属科室
- 职称
- 联系电话
- 性别
- 工作状态（在职/离职/休假）
- 启用状态（开关控制）

**操作功能**：
- 添加医生（弹窗形式）
- 编辑医生（弹窗形式）
- 删除医生（带确认提示）
- 状态切换（启用/禁用）

**分页功能**：
- 支持分页显示
- 可选择每页显示数量（10/20/30/40）
- 页码导航

### 2. 医院管理操作栏增强

在医院管理tab的操作栏中添加了"医生列表"按钮：
- 点击后自动切换到"医生管理"tab
- 自动筛选显示该医院的所有医生
- 预设医院筛选条件，并加载对应的科室选项

### 3. 医生新增/编辑弹窗

**弹窗功能**：
- 完整的医生信息表单
- 医院科室级联选择
- 表单验证（前端验证）
- 支持新增和编辑两种模式

**表单字段**：
- 基本信息：姓名、工号、医院、科室
- 联系方式：电话、邮箱
- 个人信息：性别、出生日期、身份证号
- 职业信息：职称、学历、工作状态
- 专业信息：专业特长、执业范围、执业证书编号
- 其他信息：标签、简介、备注、排序、启用状态

### 4. 数据结构

**新增的数据字段**：
```javascript
// 医生搜索表单
doctorSearchForm: {
    doctorName: '',      // 医生姓名
    hospitalId: '',      // 医院ID
    departmentId: ''     // 科室ID
},

// 医生列表数据
doctorTableData: {
    list: [],           // 医生列表
    totalCount: 0       // 总数量
},

// 分页和加载状态
doctorTableLoading: false,  // 加载状态
doctorPage: 1,             // 当前页
doctorLimit: 10,           // 每页数量

// 下拉选项
hospitalOptions: [],        // 医院选项
departmentOptions: [],      // 科室选项

// 医生弹窗相关
doctorDialogVisible: false,     // 弹窗显示状态
doctorDialogTitle: '添加医生',   // 弹窗标题
doctorFormDepartmentOptions: [], // 表单中的科室选项
doctorForm: { ... },            // 医生表单数据
doctorRules: { ... }            // 表单验证规则
```

### 5. 核心方法

**搜索和数据加载**：
- `searchDoctors()` - 搜索医生列表
- `resetDoctorSearch()` - 重置搜索条件
- `loadHospitalOptions()` - 加载医院选项
- `loadDepartmentOptions(hospitalId)` - 加载科室选项

**分页处理**：
- `handleDoctorSizeChange(val)` - 每页数量变化
- `handleDoctorCurrentChange(val)` - 当前页变化

**操作功能**：
- `addDoctor()` - 添加医生（弹窗形式）
- `editDoctor(row)` - 编辑医生（弹窗形式）
- `deleteDoctor(row)` - 删除医生
- `updateDoctorStatus(row)` - 更新医生状态

**弹窗相关方法**：
- `resetDoctorForm()` - 重置医生表单
- `loadHospitalOptionsForForm()` - 为表单加载医院选项
- `onDoctorFormHospitalChange(hospitalId)` - 表单中医院选择变化处理
- `loadDepartmentOptionsForForm(hospitalId)` - 为表单加载科室选项
- `saveDoctorForm()` - 保存医生表单

**导航功能**：
- `viewDoctors(row)` - 从医院列表查看医生（自动切换tab并筛选）
- `onDoctorHospitalChange(hospitalId)` - 医院选择变化处理

### 5. Tab切换逻辑

修改了`handleClick`方法，添加了医生管理tab的处理：
```javascript
if (tab._props.label == '医生管理') {
    this.doctorPage = 1
    this.doctorLimit = 10
    this.loadHospitalOptions()
    this.searchDoctors()
}
```

### 6. 界面特性

**用户体验优化**：
- 医院科室级联选择
- 实时状态切换
- 加载状态提示
- 操作确认提示
- 成功/失败消息提示

**响应式设计**：
- 表格自适应宽度
- 操作按钮合理布局
- 分页组件居中显示

## 使用流程

### 方式一：直接访问医生管理
1. 在universalMission页面点击"医生管理"tab
2. 使用搜索条件筛选医生
3. 进行相应的管理操作

### 方式二：从医院列表访问
1. 在"医院管理"tab中找到目标医院
2. 点击该医院操作栏的"医生列表"按钮
3. 自动跳转到医生管理tab并显示该医院的医生

## 技术特点

1. **数据联动**：医院和科室的级联选择
2. **状态管理**：实时的启用/禁用状态切换
3. **用户友好**：直观的操作界面和反馈
4. **性能优化**：按需加载数据，避免不必要的请求
5. **代码复用**：复用现有的API接口和组件样式

## 注意事项

1. 添加和编辑医生功能跳转到独立的医生管理页面（doctorList）
2. 删除操作有确认提示，防止误操作
3. 状态切换失败时会自动恢复原状态
4. 医院选择变化时会自动清空科室选择并重新加载科室选项

这个功能完美集成到了现有的universalMission页面中，提供了便捷的医生管理入口。
