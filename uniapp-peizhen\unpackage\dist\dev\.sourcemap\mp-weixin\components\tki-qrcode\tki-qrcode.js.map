{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/tki-qrcode/tki-qrcode.vue?be29", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/tki-qrcode/tki-qrcode.vue?c229", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/tki-qrcode/tki-qrcode.vue?f40e", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/tki-qrcode/tki-qrcode.vue?ec61", "uni-app:///components/tki-qrcode/tki-qrcode.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/tki-qrcode/tki-qrcode.vue?2b51", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/components/tki-qrcode/tki-qrcode.vue?53bc"], "names": ["name", "props", "cid", "type", "default", "size", "unit", "show", "val", "background", "foreground", "pdground", "icon", "iconSize", "lv", "onval", "loadMake", "usingComponents", "showLoading", "loadingText", "data", "result", "methods", "_makeCode", "qrcode", "context", "canvasId", "text", "correctLevel", "image", "imageSize", "cbR<PERSON>ult", "that", "uni", "title", "duration", "_clearCode", "_saveCode", "filePath", "success", "_result", "_empty", "rt", "watch", "setTimeout", "computed", "cpSize", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+I;AAC/I;AAC8D;AACL;AACa;;;AAGtE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,6GAAM;AACR,EAAE,sHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACQ7vB;;;;;;;;;AACA;AAAA,eACA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;IACAe;MACAhB;MACAC;IACA;EACA;EACAgB;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACAC;UAAA;UACAC;UAAA;UACAT;UAAA;UACAE;UAAA;UACAQ;UAAA;UACAtB;UAAA;UACAI;UAAA;UACAC;UAAA;UACAC;UAAA;UACAiB;UAAA;UACAC;UAAA;UACAC;UAAA;UACAC;YAAA;YACAC;UACA;QACA;MACA;QACAC;UACAC;UACAtB;UACAuB;QACA;MACA;IACA;IACAC;MACA;MACAZ;IACA;IACAa;MACA;MACA;QACAJ;UACAK;UACAC;YACAN;cACAC;cACAtB;cACAuB;YACA;UACA;QACA;MACA;IACA;IACAK;MACA;MACA;IACA;IACAC;MACA;QACAC;MACA;QACAA;MACA;QACAA;MACA;QACA;MACA;QACA;MACA;QACAA;MACA;MACA;IACA;EACA;EACAC;IACAtC;MAAA;MACA;QACA;QACA;UACAuC;YACA;UACA;QACA;MACA;IACA;IACApC;MAAA;MACA;QACA;UACAoC;YACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;QACAH;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/LA;AAAA;AAAA;AAAA;AAAgjC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACApkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tki-qrcode/tki-qrcode.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tki-qrcode.vue?vue&type=template&id=143ec824&xlang=wxml&minapp=mpvue&\"\nvar renderjs\nimport script from \"./tki-qrcode.vue?vue&type=script&lang=js&\"\nexport * from \"./tki-qrcode.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tki-qrcode.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tki-qrcode/tki-qrcode.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tki-qrcode.vue?vue&type=template&id=143ec824&xlang=wxml&minapp=mpvue&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tki-qrcode.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tki-qrcode.vue?vue&type=script&lang=js&\"", "<template xlang=\"wxml\" minapp=\"mpvue\">\n\t<view class=\"tki-qrcode\">\n\t\t<canvas class=\"tki-qrcode-canvas\" :canvas-id=\"cid\" :style=\"{width:cpSize+'px',height:cpSize+'px'}\" />\n\t\t<image v-show=\"show\" :src=\"result\" :style=\"{width:cpSize+'px',height:cpSize+'px'}\" />\n\t</view>\n</template>\n\n<script>\nimport QRCode from \"./qrcode.js\"\nlet qrcode\nexport default {\n\tname: \"tki-qrcode\",\n\tprops: {\n\t\tcid: {\n\t\t\ttype: String,\n\t\t\tdefault: 'tki-qrcode-canvas'\n\t\t},\n\t\tsize: {\n\t\t\ttype: Number,\n\t\t\tdefault: 200\n\t\t},\n\t\tunit: {\n\t\t\ttype: String,\n\t\t\tdefault: 'upx'\n\t\t},\n\t\tshow: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tval: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tbackground: {\n\t\t\ttype: String,\n\t\t\tdefault: '#ffffff'\n\t\t},\n\t\tforeground: {\n\t\t\ttype: String,\n\t\t\tdefault: '#000000'\n\t\t},\n\t\tpdground: {\n\t\t\ttype: String,\n\t\t\tdefault: '#000000'\n\t\t},\n\t\ticon: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\ticonSize: {\n\t\t\ttype: Number,\n\t\t\tdefault: 40\n\t\t},\n\t\tlv: {\n\t\t\ttype: Number,\n\t\t\tdefault: 3\n\t\t},\n\t\tonval: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tloadMake: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tusingComponents: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tshowLoading: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tloadingText: {\n\t\t\ttype: String,\n\t\t\tdefault: '二维码生成中'\n\t\t},\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tresult: '',\n\t\t}\n\t},\n\tmethods: {\n\t\t_makeCode() {\n\t\t\tlet that = this\n\t\t\tif (!this._empty(this.val)) {\n\t\t\t\tqrcode = new QRCode({\n\t\t\t\t\tcontext: that, // 上下文环境\n\t\t\t\t\tcanvasId:that.cid, // canvas-id\n\t\t\t\t\tusingComponents: that.usingComponents, // 是否是自定义组件\n\t\t\t\t\tloadingText: that.loadingText, // loading文字\n\t\t\t\t\ttext: that.val, // 生成内容\n\t\t\t\t\tsize: that.cpSize, // 二维码大小\n\t\t\t\t\tbackground: that.background, // 背景色\n\t\t\t\t\tforeground: that.foreground, // 前景色\n\t\t\t\t\tpdground: that.pdground, // 定位角点颜色\n\t\t\t\t\tcorrectLevel: that.lv, // 容错级别\n\t\t\t\t\timage: that.icon, // 二维码图标\n\t\t\t\t\timageSize: that.iconSize,// 二维码图标大小\n\t\t\t\t\tcbResult: function (res) { // 生成二维码的回调\n\t\t\t\t\t\tthat._result(res)\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '二维码内容不能为空',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t_clearCode() {\n\t\t\tthis._result('')\n\t\t\tqrcode.clear()\n\t\t},\n\t\t_saveCode() {\n\t\t\tlet that = this;\n\t\t\tif (this.result != \"\") {\n\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\tfilePath: that.result,\n\t\t\t\t\tsuccess: function () {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '二维码保存成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t_result(res) {\n\t\t\tthis.result = res;\n\t\t\tthis.$emit('result', res)\n\t\t},\n\t\t_empty(v) {\n\t\t\tlet tp = typeof v,\n\t\t\t\trt = false;\n\t\t\tif (tp == \"number\" && String(v) == \"\") {\n\t\t\t\trt = true\n\t\t\t} else if (tp == \"undefined\") {\n\t\t\t\trt = true\n\t\t\t} else if (tp == \"object\") {\n\t\t\t\tif (JSON.stringify(v) == \"{}\" || JSON.stringify(v) == \"[]\" || v == null) rt = true\n\t\t\t} else if (tp == \"string\") {\n\t\t\t\tif (v == \"\" || v == \"undefined\" || v == \"null\" || v == \"{}\" || v == \"[]\") rt = true\n\t\t\t} else if (tp == \"function\") {\n\t\t\t\trt = false\n\t\t\t}\n\t\t\treturn rt\n\t\t}\n\t},\n\twatch: {\n\t\tsize: function (n, o) {\n\t\t\tif (n != o && !this._empty(n)) {\n\t\t\t\tthis.cSize = n\n\t\t\t\tif (!this._empty(this.val)) {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis._makeCode()\n\t\t\t\t\t}, 100);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tval: function (n, o) {\n\t\t\tif (this.onval) {\n\t\t\t\tif (n != o && !this._empty(n)) {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis._makeCode()\n\t\t\t\t\t}, 0);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\tcpSize() {\n\t\t\tif(this.unit == \"upx\"){\n\t\t\t\treturn uni.upx2px(this.size)\n\t\t\t}else{\n\t\t\t\treturn this.size\n\t\t\t}\n\t\t}\n\t},\n\tmounted: function () {\n\t\tif (this.loadMake) {\n\t\t\tif (!this._empty(this.val)) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis._makeCode()\n\t\t\t\t}, 0);\n\t\t\t}\n\t\t}\n\t},\n}\n</script>\n<style>\n.tki-qrcode {\n  position: relative;\n}\n.tki-qrcode-canvas {\n  position: fixed;\n  top: -99999upx;\n  left: -99999upx;\n  z-index: -99999;\n}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tki-qrcode.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tki-qrcode.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447626719\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}