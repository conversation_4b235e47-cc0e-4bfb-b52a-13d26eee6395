{"version": 3, "sources": ["uni-app:///my/components/wangding-pickerAddress/data.js", "uni-app:///my/components/mescroll-uni/components/mescroll-uni/mescroll-mixins.js", "uni-app:///my/components/mescroll-uni/components/mescroll-uni/mescroll-uni.js", "uni-app:///my/components/mescroll-uni/components/mescroll-uni/mescroll-uni-option.js", "uni-app:///my/components/mescroll-uni/components/mescroll-uni/mescroll-i18n.js", "uni-app:///my/components/mescroll-uni/components/mescroll-uni/wxs/mixins.js"], "names": ["MescrollMixin", "data", "mescroll", "onPullDownRefresh", "onPageScroll", "e", "onReachBottom", "methods", "mescrollInit", "mescrollInitByRef", "resetUpScroll", "mescrollRef", "$refs", "downCallback", "optUp", "use", "setTimeout", "endSuccess", "upCallback", "endErr", "mounted", "MeScroll", "options", "isScrollBody", "me", "version", "isDownScrolling", "isUpScrolling", "hasDownCallback", "down", "callback", "initDownScroll", "initUpScroll", "optDown", "native", "auto", "autoShowLoading", "triggerDownScroll", "isUpAutoLoad", "triggerUpScroll", "prototype", "extendDownScroll", "extend", "isLock", "offset", "startTop", "inOffsetRate", "outOffsetRate", "bottomOffset", "minAngle", "textInOffset", "textOutOffset", "textLoading", "textSuccess", "textErr", "beforeEndDelay", "bgColor", "textColor", "inited", "inOffset", "outOffset", "onMoving", "beforeLoading", "showLoading", "afterLoading", "beforeEndDownScroll", "endDownScroll", "afterEndDownScroll", "extendUpScroll", "isBoth", "page", "num", "size", "time", "noMoreSize", "textNoMore", "showNoMore", "hideUpScroll", "errDistance", "toTop", "src", "duration", "btnClick", "onShow", "zIndex", "left", "right", "bottom", "safearea", "width", "radius", "empty", "icon", "tip", "btnText", "fixed", "top", "onScroll", "userOption", "defaultOption", "key", "def", "hasColor", "color", "c", "toLowerCase", "downHight", "touchstartEvent", "startPoint", "getPoint", "getScrollTop", "startAngle", "lastPoint", "maxTouchmoveY", "getBodyHeight", "inTouchend", "touchmoveEvent", "scrollTop", "curPoint", "moveY", "y", "getAngle", "touchendEvent", "preventDefault", "diff", "movetype", "isDownEndSuccess", "isMoveDown", "Math", "round", "rate", "endDownScrollCall", "isScrollUp", "angle", "x", "touches", "pageX", "pageY", "changedTouches", "clientX", "clientY", "p1", "p2", "abs", "z", "sqrt", "asin", "PI", "showDownScroll", "uni", "startPullDownRefresh", "showDownLoadingCall", "stopPullDownRefresh", "endScroll", "setScrollHeight", "scrollTo", "delay", "lockDownScroll", "lockUpScroll", "up", "hasNext", "startNum", "setScrollTop", "showTopBtn", "hideTopBtn", "scroll", "scrollHeight", "preScrollY", "is<PERSON><PERSON><PERSON>", "canUp", "getScrollBottom", "showUpScroll", "endUpScroll", "isShowNoMore", "isShowLoading", "prePageNum", "prePageTime", "removeEmpty", "setPageNum", "setPageSize", "endByPage", "dataSize", "totalPage", "systime", "endBySize", "totalSize", "loadSize", "pageNum", "pageSize", "showEmpty", "allDataSize", "topBtnShow", "t", "myScrollTo", "resetScrollTo", "getScrollHeight", "getClientHeight", "getStep", "star", "end", "count", "step", "i", "timer", "setInterval", "clearInterval", "isReal", "h", "clientHeight", "setClientHeight", "bodyHeight", "setBodyHeight", "cancelable", "defaultPrevented", "GlobalOption", "i18n", "zh", "en", "mescrollI18n", "getType", "getStorageSync", "setType", "type", "setStorageSync", "WxsMixin", "wxsProp", "isUpBoth", "callProp", "callType", "renderBiz", "propObserver", "wxsCall", "msg", "Date", "now", "downLoadType", "$set"], "mappings": ";;;;;;;;;;;;;;;;eAAe,CAAC;EACd,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACR,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AACF,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACR,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM;EAER,CAAC;AACF,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,GAAG,EACH,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,QAAQ,EACR,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,YAAY;EAEd,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,SAAS,EACT,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW;EAEb,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO,EACP,KAAK,EACL,aAAa,EACb,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO;EAET,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,OAAO;EAET,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,OAAO,EACP,OAAO,EACP,MAAM;EAER,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU;EAEZ,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,aAAa;EAEf,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU;EAEZ,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,aAAa;EAEf,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,MAAM;EACd,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,OAAO,EACP,MAAM,EACN,UAAU,EACV,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,YAAY;EAEd,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ;EAEV,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACR,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK;EAEP,CAAC;AACF,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU;EAEZ,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,OAAO;EAET,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,WAAW,EACX,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO;EAET,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW,EACX,SAAS;EAEX,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,CACP,WAAW;EAEb,CAAC,EACD;IACC,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,CACP,WAAW;EAEb,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACR,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,YAAY;EAEd,CAAC;AACF,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,YAAY,EACZ,YAAY;EAEd,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,YAAY;EAEd,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,YAAY,EACZ,UAAU,EACV,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,aAAa;EAEf,CAAC,EACD;IACC,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,WAAW,EACX,WAAW;EAEb,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,WAAW,EACX,cAAc;EAEhB,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,YAAY,EACZ,UAAU,EACV,SAAS,EACT,WAAW,EACX,eAAe,EACf,YAAY,EACZ,cAAc,EACd,UAAU,EACV,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,iBAAiB,EACjB,WAAW,EACX,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,YAAY,EACZ,YAAY;EAEd,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,OAAO,EACP,KAAK,EACL,UAAU;EAEZ,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,WAAW;EAEb,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,aAAa,EACb,SAAS,EACT,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,OAAO;EAET,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU,EACV,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,EACZ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU;EAEZ,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,iBAAiB;EAEnB,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW;EAEb,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,WAAW,EACX,SAAS,EACT,SAAS,EACT,UAAU;EAEZ,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,UAAU;EAEZ,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK;EAEP,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,KAAK,EACL,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO;EAET,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,WAAW;EAEb,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,aAAa;EAEf,CAAC,EACD;IACC,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,CACP,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS;EAEX,CAAC,EACD;IACC,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,UAAU,EACV,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,CACP,MAAM;EAER,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW,EACX,MAAM,EACN,OAAO;EAET,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,CAAC;IACP,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,KAAK,EACL,KAAK,EACL,KAAK;EAEP,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC,EACD;IACC,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,CACP,IAAI;EAEN,CAAC;AAEH,CAAC,EACD;EACC,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,CAAC;IACR,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,CACP,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,QAAQ,EACR,IAAI;EAEN,CAAC;AACF,CAAC,EACD;EACC,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,CAAC;IACR,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,CACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI;EAEN,CAAC;AACF,CAAC,CACD;AAAA,2B;;;;;;;;;;;;;;;;;;ACp0JD;AACA,IAAMA,aAAa,GAAG;EACrBC,IAAI,kBAAG;IACN,OAAO;MACNC,QAAQ,EAAE,IAAI,CAAC;IAChB,CAAC;EACF,CAAC;EACD;EACAC,iBAAiB,+BAAE;IAClB,IAAI,CAACD,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,iBAAiB,EAAE;EACnD,CAAC;EACD;EACAC,YAAY,wBAACC,CAAC,EAAE;IACf,IAAI,CAACH,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACE,YAAY,CAACC,CAAC,CAAC;EAC/C,CAAC;EACD;EACAC,aAAa,2BAAG;IACf,IAAI,CAACJ,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACI,aAAa,EAAE;EAC/C,CAAC;EACDC,OAAO,EAAE;IACR;IACAC,YAAY,wBAACN,QAAQ,EAAE;MACtB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACO,iBAAiB,EAAE,CAAC,CAAC;IAC3B,CAAC;IACD;IACAA,iBAAiB,+BAAG;MACnB,IAAG,CAAC,IAAI,CAACP,QAAQ,IAAI,CAAC,IAAI,CAACA,QAAQ,CAACQ,aAAa,EAAC;QACjD,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACD,WAAW;QACxC,IAAGA,WAAW,EAAE,IAAI,CAACT,QAAQ,GAAGS,WAAW,CAACT,QAAQ;MACrD;IACD,CAAC;IACD;IACAW,YAAY,0BAAG;MAAA;MACd,IAAG,IAAI,CAACX,QAAQ,CAACY,KAAK,CAACC,GAAG,EAAC;QAC1B,IAAI,CAACb,QAAQ,CAACQ,aAAa,EAAE;MAC9B,CAAC,MAAI;QACJM,UAAU,CAAC,YAAI;UACd,KAAI,CAACd,QAAQ,CAACe,UAAU,EAAE;QAC3B,CAAC,EAAE,GAAG,CAAC;MACR;IACD,CAAC;IACD;IACAC,UAAU,wBAAG;MAAA;MACZ;MACAF,UAAU,CAAC,YAAI;QACd,MAAI,CAACd,QAAQ,CAACiB,MAAM,EAAE;MACvB,CAAC,EAAE,GAAG,CAAC;IACR;EACD,CAAC;EACDC,OAAO,qBAAG;IACT,IAAI,CAACX,iBAAiB,EAAE,CAAC,CAAC;EAC3B;AAED,CAAC;AAAA,eAEcT,aAAa;AAAA,2B;;;;;;;;;;;;;;;;;;;;ACxD5B;AACA;AACA;AACA;AACA;;AAEe,SAASqB,QAAQ,CAACC,OAAO,EAAEC,YAAY,EAAE;EACvD,IAAIC,EAAE,GAAG,IAAI;EACbA,EAAE,CAACC,OAAO,GAAG,OAAO,CAAC,CAAC;EACtBD,EAAE,CAACF,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5BE,EAAE,CAACD,YAAY,GAAGA,YAAY,IAAI,KAAK,CAAC,CAAC;;EAEzCC,EAAE,CAACE,eAAe,GAAG,KAAK,CAAC,CAAC;EAC5BF,EAAE,CAACG,aAAa,GAAG,KAAK,CAAC,CAAC;EAC1B,IAAIC,eAAe,GAAGJ,EAAE,CAACF,OAAO,CAACO,IAAI,IAAIL,EAAE,CAACF,OAAO,CAACO,IAAI,CAACC,QAAQ,CAAC,CAAC;;EAEnE;EACAN,EAAE,CAACO,cAAc,EAAE;EACnB;EACAP,EAAE,CAACQ,YAAY,EAAE;;EAEjB;EACAhB,UAAU,CAAC,YAAW;IAAE;IACvB;IACA,IAAI,CAACQ,EAAE,CAACS,OAAO,CAAClB,GAAG,IAAIS,EAAE,CAACS,OAAO,CAACC,MAAM,KAAKV,EAAE,CAACS,OAAO,CAACE,IAAI,IAAIP,eAAe,EAAE;MAChF,IAAIJ,EAAE,CAACS,OAAO,CAACG,eAAe,EAAE;QAC/BZ,EAAE,CAACa,iBAAiB,EAAE,CAAC,CAAC;MACzB,CAAC,MAAM;QACNb,EAAE,CAACS,OAAO,CAACH,QAAQ,IAAIN,EAAE,CAACS,OAAO,CAACH,QAAQ,CAACN,EAAE,CAAC,CAAC,CAAC;MACjD;IACD;IACA;IACA,IAAG,CAACA,EAAE,CAACc,YAAY,EAAC;MAAE;MACrBtB,UAAU,CAAC,YAAU;QACpBQ,EAAE,CAACV,KAAK,CAACC,GAAG,IAAIS,EAAE,CAACV,KAAK,CAACqB,IAAI,IAAI,CAACX,EAAE,CAACc,YAAY,IAAId,EAAE,CAACe,eAAe,EAAE;MAC1E,CAAC,EAAC,GAAG,CAAC;IACP;EACD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACT;;AAEA;AACAlB,QAAQ,CAACmB,SAAS,CAACC,gBAAgB,GAAG,UAASR,OAAO,EAAE;EACvD;EACAZ,QAAQ,CAACqB,MAAM,CAACT,OAAO,EAAE;IACxBlB,GAAG,EAAE,IAAI;IAAE;IACXoB,IAAI,EAAE,IAAI;IAAE;IACZD,MAAM,EAAE,KAAK;IAAE;IACfE,eAAe,EAAE,KAAK;IAAE;IACxBO,MAAM,EAAE,KAAK;IAAE;IACfC,MAAM,EAAE,EAAE;IAAE;IACZC,QAAQ,EAAE,GAAG;IAAE;IACfC,YAAY,EAAE,CAAC;IAAE;IACjBC,aAAa,EAAE,GAAG;IAAE;IACpBC,YAAY,EAAE,EAAE;IAAE;IAClBC,QAAQ,EAAE,EAAE;IAAE;IACdC,YAAY,EAAE,MAAM;IAAE;IACtBC,aAAa,EAAE,MAAM;IAAE;IACvBC,WAAW,EAAE,SAAS;IAAE;IACxBC,WAAW,EAAE,MAAM;IAAE;IACrBC,OAAO,EAAE,MAAM;IAAE;IACjBC,cAAc,EAAE,CAAC;IAAE;IACnBC,OAAO,EAAE,aAAa;IAAE;IACxBC,SAAS,EAAE,MAAM;IAAE;IACnBC,MAAM,EAAE,IAAI;IAAE;IACdC,QAAQ,EAAE,IAAI;IAAE;IAChBC,SAAS,EAAE,IAAI;IAAE;IACjBC,QAAQ,EAAE,IAAI;IAAE;IAChBC,aAAa,EAAE,IAAI;IAAE;IACrBC,WAAW,EAAE,IAAI;IAAE;IACnBC,YAAY,EAAE,IAAI;IAAE;IACpBC,mBAAmB,EAAE,IAAI;IAAE;IAC3BC,aAAa,EAAE,IAAI;IAAE;IACrBC,kBAAkB,EAAE,IAAI;IAAE;IAC1BrC,QAAQ,EAAE,kBAAS5B,QAAQ,EAAE;MAC5B;MACAA,QAAQ,CAACQ,aAAa,EAAE;IACzB;EACD,CAAC,CAAC;AACH,CAAC;;AAED;AACAW,QAAQ,CAACmB,SAAS,CAAC4B,cAAc,GAAG,UAAStD,KAAK,EAAE;EACnD;EACAO,QAAQ,CAACqB,MAAM,CAAC5B,KAAK,EAAE;IACtBC,GAAG,EAAE,IAAI;IAAE;IACXoB,IAAI,EAAE,IAAI;IAAE;IACZQ,MAAM,EAAE,KAAK;IAAE;IACf0B,MAAM,EAAE,IAAI;IAAE;IACdvC,QAAQ,EAAE,IAAI;IAAE;IAChBwC,IAAI,EAAE;MACLC,GAAG,EAAE,CAAC;MAAE;MACRC,IAAI,EAAE,EAAE;MAAE;MACVC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC;;IACDC,UAAU,EAAE,CAAC;IAAE;IACf9B,MAAM,EAAE,GAAG;IAAE;IACbQ,WAAW,EAAE,SAAS;IAAE;IACxBuB,UAAU,EAAE,SAAS;IAAE;IACvBnB,OAAO,EAAE,aAAa;IAAE;IACxBC,SAAS,EAAE,MAAM;IAAE;IACnBC,MAAM,EAAE,IAAI;IAAE;IACdK,WAAW,EAAE,IAAI;IAAE;IACnBa,UAAU,EAAE,IAAI;IAAE;IAClBC,YAAY,EAAE,IAAI;IAAE;IACpBC,WAAW,EAAE,EAAE;IAAE;IACjBC,KAAK,EAAE;MACN;MACAC,GAAG,EAAE,IAAI;MAAE;MACXpC,MAAM,EAAE,IAAI;MAAE;MACdqC,QAAQ,EAAE,GAAG;MAAE;MACfC,QAAQ,EAAE,IAAI;MAAE;MAChBC,MAAM,EAAE,IAAI;MAAE;MACdC,MAAM,EAAE,IAAI;MAAE;MACdC,IAAI,EAAE,IAAI;MAAE;MACZC,KAAK,EAAE,EAAE;MAAE;MACXC,MAAM,EAAE,GAAG;MAAE;MACbC,QAAQ,EAAE,KAAK;MAAE;MACjBC,KAAK,EAAE,EAAE;MAAE;MACXC,MAAM,EAAE,KAAK,CAAC;IACf,CAAC;;IACDC,KAAK,EAAE;MACN5E,GAAG,EAAE,IAAI;MAAE;MACX6E,IAAI,EAAE,IAAI;MAAE;MACZC,GAAG,EAAE,YAAY;MAAE;MACnBC,OAAO,EAAE,EAAE;MAAE;MACbZ,QAAQ,EAAE,IAAI;MAAE;MAChBC,MAAM,EAAE,IAAI;MAAE;MACdY,KAAK,EAAE,KAAK;MAAE;MACdC,GAAG,EAAE,QAAQ;MAAE;MACfZ,MAAM,EAAE,EAAE,CAAC;IACZ,CAAC;;IACDa,QAAQ,EAAE,KAAK,CAAC;EACjB,CAAC,CAAC;AACH,CAAC;;AAED;AACA5E,QAAQ,CAACqB,MAAM,GAAG,UAASwD,UAAU,EAAEC,aAAa,EAAE;EACrD,IAAI,CAACD,UAAU,EAAE,OAAOC,aAAa;EACrC,KAAK,IAAIC,GAAG,IAAID,aAAa,EAAE;IAC9B,IAAID,UAAU,CAACE,GAAG,CAAC,IAAI,IAAI,EAAE;MAC5B,IAAIC,GAAG,GAAGF,aAAa,CAACC,GAAG,CAAC;MAC5B,IAAIC,GAAG,IAAI,IAAI,IAAI,sBAAOA,GAAG,MAAK,QAAQ,EAAE;QAC3CH,UAAU,CAACE,GAAG,CAAC,GAAG/E,QAAQ,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAE2D,GAAG,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM;QACNH,UAAU,CAACE,GAAG,CAAC,GAAGC,GAAG;MACtB;IACD,CAAC,MAAM,IAAI,sBAAOH,UAAU,CAACE,GAAG,CAAC,MAAK,QAAQ,EAAE;MAC/C/E,QAAQ,CAACqB,MAAM,CAACwD,UAAU,CAACE,GAAG,CAAC,EAAED,aAAa,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD;EACD;;EACA,OAAOF,UAAU;AAClB,CAAC;;AAED;AACA7E,QAAQ,CAACmB,SAAS,CAAC8D,QAAQ,GAAG,UAASC,KAAK,EAAE;EAC7C,IAAG,CAACA,KAAK,EAAE,OAAO,KAAK;EACvB,IAAIC,CAAC,GAAGD,KAAK,CAACE,WAAW,EAAE;EAC3B,OAAOD,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,SAAS,IAAIA,CAAC,IAAI,aAAa,IAAIA,CAAC,IAAI,OAAO;AAC3E,CAAC;;AAED;AACAnF,QAAQ,CAACmB,SAAS,CAACT,cAAc,GAAG,YAAW;EAC9C,IAAIP,EAAE,GAAG,IAAI;EACb;EACAA,EAAE,CAACS,OAAO,GAAGT,EAAE,CAACF,OAAO,CAACO,IAAI,IAAI,CAAC,CAAC;EAClC,IAAG,CAACL,EAAE,CAACS,OAAO,CAACwB,SAAS,IAAIjC,EAAE,CAAC8E,QAAQ,CAAC9E,EAAE,CAACS,OAAO,CAACuB,OAAO,CAAC,EAAEhC,EAAE,CAACS,OAAO,CAACwB,SAAS,GAAG,MAAM,CAAC,CAAC;EAC5FjC,EAAE,CAACiB,gBAAgB,CAACjB,EAAE,CAACS,OAAO,CAAC;;EAE/B;EACA,IAAGT,EAAE,CAACD,YAAY,IAAIC,EAAE,CAACS,OAAO,CAACC,MAAM,EAAC;IACvCV,EAAE,CAACS,OAAO,CAAClB,GAAG,GAAG,KAAK;EACvB,CAAC,MAAI;IACJS,EAAE,CAACS,OAAO,CAACC,MAAM,GAAG,KAAK,EAAC;EAC3B;;EAEAV,EAAE,CAACkF,SAAS,GAAG,CAAC,CAAC,CAAC;;EAElB;EACA,IAAIlF,EAAE,CAACS,OAAO,CAAClB,GAAG,IAAIS,EAAE,CAACS,OAAO,CAACyB,MAAM,EAAE;IACxC;IACA1C,UAAU,CAAC,YAAW;MAAE;MACvBQ,EAAE,CAACS,OAAO,CAACyB,MAAM,CAAClC,EAAE,CAAC;IACtB,CAAC,EAAE,CAAC,CAAC;EACN;AACD,CAAC;;AAED;AACAH,QAAQ,CAACmB,SAAS,CAACmE,eAAe,GAAG,UAAStG,CAAC,EAAE;EAChD,IAAI,CAAC,IAAI,CAAC4B,OAAO,CAAClB,GAAG,EAAE;EAEvB,IAAI,CAAC6F,UAAU,GAAG,IAAI,CAACC,QAAQ,CAACxG,CAAC,CAAC,CAAC,CAAC;EACpC,IAAI,CAACwC,QAAQ,GAAG,IAAI,CAACiE,YAAY,EAAE,CAAC,CAAC;EACrC,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,CAAC;EACrB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACJ,UAAU,CAAC,CAAC;EAClC,IAAI,CAACK,aAAa,GAAG,IAAI,CAACC,aAAa,EAAE,GAAG,IAAI,CAACjF,OAAO,CAACe,YAAY,CAAC,CAAC;EACvE,IAAI,CAACmE,UAAU,GAAG,KAAK,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA9F,QAAQ,CAACmB,SAAS,CAAC4E,cAAc,GAAG,UAAS/G,CAAC,EAAE;EAC/C,IAAI,CAAC,IAAI,CAAC4B,OAAO,CAAClB,GAAG,EAAE;EACvB,IAAIS,EAAE,GAAG,IAAI;EAEb,IAAI6F,SAAS,GAAG7F,EAAE,CAACsF,YAAY,EAAE,CAAC,CAAC;EACnC,IAAIQ,QAAQ,GAAG9F,EAAE,CAACqF,QAAQ,CAACxG,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIkH,KAAK,GAAGD,QAAQ,CAACE,CAAC,GAAGhG,EAAE,CAACoF,UAAU,CAACY,CAAC,CAAC,CAAC;;EAE1C;EACA;EACA;EACA;EACA,IAAID,KAAK,GAAG,CAAC,KACV/F,EAAE,CAACD,YAAY,IAAI8F,SAAS,IAAI,CAAC,IAEjC,CAAC7F,EAAE,CAACD,YAAY,KAAK8F,SAAS,IAAI,CAAC,IAAKA,SAAS,IAAI7F,EAAE,CAACS,OAAO,CAACY,QAAQ,IAAIwE,SAAS,KAAK7F,EAAE,CAACqB,QAAS,CAAG,CAC1G,EAAE;IACH;IACA,IAAI,CAACrB,EAAE,CAAC2F,UAAU,IAAI,CAAC3F,EAAE,CAACE,eAAe,IAAI,CAACF,EAAE,CAACS,OAAO,CAACU,MAAM,KAAK,CAACnB,EAAE,CAACG,aAAa,IAAKH,EAAE,CAACG,aAAa,IACxGH,EAAE,CAACV,KAAK,CAACuD,MAAO,CAAC,EAAE;MAEpB;MACA,IAAG,CAAC7C,EAAE,CAACuF,UAAU,EAAEvF,EAAE,CAACuF,UAAU,GAAGvF,EAAE,CAACiG,QAAQ,CAACjG,EAAE,CAACwF,SAAS,EAAEM,QAAQ,CAAC,CAAC,CAAC;MACxE,IAAI9F,EAAE,CAACuF,UAAU,GAAGvF,EAAE,CAACS,OAAO,CAACgB,QAAQ,EAAE,OAAO,CAAC;;MAEjD;MACA,IAAIzB,EAAE,CAACyF,aAAa,GAAG,CAAC,IAAIK,QAAQ,CAACE,CAAC,IAAIhG,EAAE,CAACyF,aAAa,EAAE;QAC3DzF,EAAE,CAAC2F,UAAU,GAAG,IAAI,CAAC,CAAC;QACtB3F,EAAE,CAACkG,aAAa,EAAE,CAAC,CAAC;QACpB;MACD;MAEAlG,EAAE,CAACmG,cAAc,CAACtH,CAAC,CAAC,CAAC,CAAC;;MAEtB,IAAIuH,IAAI,GAAGN,QAAQ,CAACE,CAAC,GAAGhG,EAAE,CAACwF,SAAS,CAACQ,CAAC,CAAC,CAAC;;MAExC;MACA,IAAIhG,EAAE,CAACkF,SAAS,GAAGlF,EAAE,CAACS,OAAO,CAACW,MAAM,EAAE;QACrC,IAAIpB,EAAE,CAACqG,QAAQ,KAAK,CAAC,EAAE;UACtBrG,EAAE,CAACqG,QAAQ,GAAG,CAAC,CAAC,CAAC;UACjBrG,EAAE,CAACsG,gBAAgB,GAAG,IAAI,CAAC,CAAC;UAC5BtG,EAAE,CAACS,OAAO,CAAC0B,QAAQ,IAAInC,EAAE,CAACS,OAAO,CAAC0B,QAAQ,CAACnC,EAAE,CAAC,CAAC,CAAC;UAChDA,EAAE,CAACuG,UAAU,GAAG,IAAI,CAAC,CAAC;QACvB;;QACAvG,EAAE,CAACkF,SAAS,IAAIkB,IAAI,GAAGpG,EAAE,CAACS,OAAO,CAACa,YAAY,CAAC,CAAC;;QAEhD;MACD,CAAC,MAAM;QACN,IAAItB,EAAE,CAACqG,QAAQ,KAAK,CAAC,EAAE;UACtBrG,EAAE,CAACqG,QAAQ,GAAG,CAAC,CAAC,CAAC;UACjBrG,EAAE,CAACS,OAAO,CAAC2B,SAAS,IAAIpC,EAAE,CAACS,OAAO,CAAC2B,SAAS,CAACpC,EAAE,CAAC,CAAC,CAAC;UAClDA,EAAE,CAACuG,UAAU,GAAG,IAAI,CAAC,CAAC;QACvB;;QACA,IAAIH,IAAI,GAAG,CAAC,EAAE;UAAE;UACfpG,EAAE,CAACkF,SAAS,IAAIkB,IAAI,GAAGpG,EAAE,CAACS,OAAO,CAACc,aAAa,CAAC,CAAC;QAClD,CAAC,MAAM;UAAE;UACRvB,EAAE,CAACkF,SAAS,IAAIkB,IAAI,CAAC,CAAC;QACvB;MACD;;MAEApG,EAAE,CAACkF,SAAS,GAAGsB,IAAI,CAACC,KAAK,CAACzG,EAAE,CAACkF,SAAS,CAAC,EAAC;MACxC,IAAIwB,IAAI,GAAG1G,EAAE,CAACkF,SAAS,GAAGlF,EAAE,CAACS,OAAO,CAACW,MAAM,CAAC,CAAC;MAC7CpB,EAAE,CAACS,OAAO,CAAC4B,QAAQ,IAAIrC,EAAE,CAACS,OAAO,CAAC4B,QAAQ,CAACrC,EAAE,EAAE0G,IAAI,EAAE1G,EAAE,CAACkF,SAAS,CAAC,CAAC,CAAC;IACrE;EACD;;EAEAlF,EAAE,CAACwF,SAAS,GAAGM,QAAQ,CAAC,CAAC;AAC1B,CAAC;;AAED;AACAjG,QAAQ,CAACmB,SAAS,CAACkF,aAAa,GAAG,UAASrH,CAAC,EAAE;EAC9C,IAAI,CAAC,IAAI,CAAC4B,OAAO,CAAClB,GAAG,EAAE;EACvB;EACA,IAAI,IAAI,CAACgH,UAAU,EAAE;IACpB,IAAI,IAAI,CAACrB,SAAS,IAAI,IAAI,CAACzE,OAAO,CAACW,MAAM,EAAE;MAC1C;MACA,IAAI,CAACP,iBAAiB,EAAE;IACzB,CAAC,MAAM;MACN;MACA,IAAI,CAACqE,SAAS,GAAG,CAAC;MAClB,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAAC;IAC7B;IACA,IAAI,CAACN,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACE,UAAU,GAAG,KAAK;EACxB,CAAC,MAAM,IAAI,CAAC,IAAI,CAACxG,YAAY,IAAI,IAAI,CAACuF,YAAY,EAAE,KAAK,IAAI,CAACjE,QAAQ,EAAE;IAAE;IACzE,IAAIuF,UAAU,GAAG,IAAI,CAACvB,QAAQ,CAACxG,CAAC,CAAC,CAACmH,CAAC,GAAG,IAAI,CAACZ,UAAU,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D;IACA,IAAIY,UAAU,EAAE;MACf;MACA,IAAIC,KAAK,GAAG,IAAI,CAACZ,QAAQ,CAAC,IAAI,CAACZ,QAAQ,CAACxG,CAAC,CAAC,EAAE,IAAI,CAACuG,UAAU,CAAC,CAAC,CAAC;MAC9D,IAAIyB,KAAK,GAAG,EAAE,EAAE;QACf;QACA,IAAI,CAAC9F,eAAe,CAAC,IAAI,CAAC;MAC3B;IACD;EACD;AACD,CAAC;;AAED;AACAlB,QAAQ,CAACmB,SAAS,CAACqE,QAAQ,GAAG,UAASxG,CAAC,EAAE;EACzC,IAAI,CAACA,CAAC,EAAE;IACP,OAAO;MACNiI,CAAC,EAAE,CAAC;MACJd,CAAC,EAAE;IACJ,CAAC;EACF;EACA,IAAInH,CAAC,CAACkI,OAAO,IAAIlI,CAAC,CAACkI,OAAO,CAAC,CAAC,CAAC,EAAE;IAC9B,OAAO;MACND,CAAC,EAAEjI,CAAC,CAACkI,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK;MACrBhB,CAAC,EAAEnH,CAAC,CAACkI,OAAO,CAAC,CAAC,CAAC,CAACE;IACjB,CAAC;EACF,CAAC,MAAM,IAAIpI,CAAC,CAACqI,cAAc,IAAIrI,CAAC,CAACqI,cAAc,CAAC,CAAC,CAAC,EAAE;IACnD,OAAO;MACNJ,CAAC,EAAEjI,CAAC,CAACqI,cAAc,CAAC,CAAC,CAAC,CAACF,KAAK;MAC5BhB,CAAC,EAAEnH,CAAC,CAACqI,cAAc,CAAC,CAAC,CAAC,CAACD;IACxB,CAAC;EACF,CAAC,MAAM;IACN,OAAO;MACNH,CAAC,EAAEjI,CAAC,CAACsI,OAAO;MACZnB,CAAC,EAAEnH,CAAC,CAACuI;IACN,CAAC;EACF;AACD,CAAC;;AAED;AACAvH,QAAQ,CAACmB,SAAS,CAACiF,QAAQ,GAAG,UAASoB,EAAE,EAAEC,EAAE,EAAE;EAC9C,IAAIR,CAAC,GAAGN,IAAI,CAACe,GAAG,CAACF,EAAE,CAACP,CAAC,GAAGQ,EAAE,CAACR,CAAC,CAAC;EAC7B,IAAId,CAAC,GAAGQ,IAAI,CAACe,GAAG,CAACF,EAAE,CAACrB,CAAC,GAAGsB,EAAE,CAACtB,CAAC,CAAC;EAC7B,IAAIwB,CAAC,GAAGhB,IAAI,CAACiB,IAAI,CAACX,CAAC,GAAGA,CAAC,GAAGd,CAAC,GAAGA,CAAC,CAAC;EAChC,IAAIa,KAAK,GAAG,CAAC;EACb,IAAIW,CAAC,KAAK,CAAC,EAAE;IACZX,KAAK,GAAGL,IAAI,CAACkB,IAAI,CAAC1B,CAAC,GAAGwB,CAAC,CAAC,GAAGhB,IAAI,CAACmB,EAAE,GAAG,GAAG;EACzC;EACA,OAAOd,KAAK;AACb,CAAC;;AAED;AACAhH,QAAQ,CAACmB,SAAS,CAACH,iBAAiB,GAAG,YAAW;EACjD,IAAI,IAAI,CAACJ,OAAO,CAAC6B,aAAa,IAAI,IAAI,CAAC7B,OAAO,CAAC6B,aAAa,CAAC,IAAI,CAAC,EAAE;IACnE;EAAA,CACA,MAAM;IACN,IAAI,CAACsF,cAAc,EAAE,CAAC,CAAC;IACvB,CAAC,IAAI,CAACnH,OAAO,CAACC,MAAM,IAAI,IAAI,CAACD,OAAO,CAACH,QAAQ,IAAI,IAAI,CAACG,OAAO,CAACH,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/E;AACD,CAAC;;AAED;AACAT,QAAQ,CAACmB,SAAS,CAAC4G,cAAc,GAAG,YAAW;EAC9C,IAAI,CAAC1H,eAAe,GAAG,IAAI,CAAC,CAAC;EAC7B,IAAI,IAAI,CAACO,OAAO,CAACC,MAAM,EAAE;IACxBmH,GAAG,CAACC,oBAAoB,EAAE,CAAC,CAAC;IAC5B,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,MAAK;IACL,IAAI,CAAC7C,SAAS,GAAG,IAAI,CAACzE,OAAO,CAACW,MAAM,CAAC,CAAC;IACtC,IAAI,CAAC2G,mBAAmB,CAAC,IAAI,CAAC7C,SAAS,CAAC,CAAC,CAAC;EAC3C;AACD,CAAC;;AAEDrF,QAAQ,CAACmB,SAAS,CAAC+G,mBAAmB,GAAG,UAAS7C,SAAS,EAAE;EAC5D,IAAI,CAACzE,OAAO,CAAC8B,WAAW,IAAI,IAAI,CAAC9B,OAAO,CAAC8B,WAAW,CAAC,IAAI,EAAE2C,SAAS,CAAC,CAAC,CAAC;EACvE,IAAI,CAACzE,OAAO,CAAC+B,YAAY,IAAI,IAAI,CAAC/B,OAAO,CAAC+B,YAAY,CAAC,IAAI,EAAE0C,SAAS,CAAC,CAAC,CAAC;AAC1E,CAAC;;AAED;AACArF,QAAQ,CAACmB,SAAS,CAACrC,iBAAiB,GAAG,YAAW;EACjD,IAAI,CAACuB,eAAe,GAAG,IAAI,CAAC,CAAC;EAC7B,IAAI,CAAC6H,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,CAACtH,OAAO,CAACH,QAAQ,IAAI,IAAI,CAACG,OAAO,CAACH,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACvD,CAAC;;AAED;AACAT,QAAQ,CAACmB,SAAS,CAAC0B,aAAa,GAAG,YAAW;EAC7C,IAAI,IAAI,CAACjC,OAAO,CAACC,MAAM,EAAE;IAAE;IAC1B,IAAI,CAACR,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACyG,iBAAiB,CAAC,IAAI,CAAC;IAC5BkB,GAAG,CAACG,mBAAmB,EAAE;IACzB;EACD;EACA,IAAIhI,EAAE,GAAG,IAAI;EACb;EACA,IAAIiI,SAAS,GAAG,SAAZA,SAAS,GAAc;IAC1BjI,EAAE,CAACkF,SAAS,GAAG,CAAC;IAChBlF,EAAE,CAACE,eAAe,GAAG,KAAK;IAC1BF,EAAE,CAAC2G,iBAAiB,CAAC3G,EAAE,CAAC;IACxB,IAAG,CAACA,EAAE,CAACD,YAAY,EAAC;MACnBC,EAAE,CAACkI,eAAe,CAAC,CAAC,CAAC,EAAC;MACtBlI,EAAE,CAACmI,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC;IAClB;EACD,CAAC;EACD;EACA,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIpI,EAAE,CAACS,OAAO,CAACgC,mBAAmB,EAAE;IACnC2F,KAAK,GAAGpI,EAAE,CAACS,OAAO,CAACgC,mBAAmB,CAACzC,EAAE,CAAC,CAAC,CAAC;IAC5C,IAAGA,EAAE,CAACsG,gBAAgB,IAAI,IAAI,EAAE8B,KAAK,GAAG,CAAC,CAAC,CAAC;EAC5C;;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,CAAC,EAAE;IAC3C5I,UAAU,CAACyI,SAAS,EAAEG,KAAK,CAAC;EAC7B,CAAC,MAAM;IACNH,SAAS,EAAE;EACZ;AACD,CAAC;AAEDpI,QAAQ,CAACmB,SAAS,CAAC2F,iBAAiB,GAAG,YAAW;EACjD,IAAI,CAAClG,OAAO,CAACiC,aAAa,IAAI,IAAI,CAACjC,OAAO,CAACiC,aAAa,CAAC,IAAI,CAAC;EAC9D,IAAI,CAACjC,OAAO,CAACkC,kBAAkB,IAAI,IAAI,CAAClC,OAAO,CAACkC,kBAAkB,CAAC,IAAI,CAAC;AACzE,CAAC;;AAED;AACA9C,QAAQ,CAACmB,SAAS,CAACqH,cAAc,GAAG,UAASlH,MAAM,EAAE;EACpD,IAAIA,MAAM,IAAI,IAAI,EAAEA,MAAM,GAAG,IAAI;EACjC,IAAI,CAACV,OAAO,CAACU,MAAM,GAAGA,MAAM;AAC7B,CAAC;;AAED;AACAtB,QAAQ,CAACmB,SAAS,CAACsH,YAAY,GAAG,UAASnH,MAAM,EAAE;EAClD,IAAIA,MAAM,IAAI,IAAI,EAAEA,MAAM,GAAG,IAAI;EACjC,IAAI,CAAC7B,KAAK,CAAC6B,MAAM,GAAGA,MAAM;AAC3B,CAAC;;AAED;AACAtB,QAAQ,CAACmB,SAAS,CAACR,YAAY,GAAG,YAAW;EAC5C,IAAIR,EAAE,GAAG,IAAI;EACb;EACAA,EAAE,CAACV,KAAK,GAAGU,EAAE,CAACF,OAAO,CAACyI,EAAE,IAAI;IAAChJ,GAAG,EAAE;EAAK,CAAC;EACxC,IAAG,CAACS,EAAE,CAACV,KAAK,CAAC2C,SAAS,IAAIjC,EAAE,CAAC8E,QAAQ,CAAC9E,EAAE,CAACV,KAAK,CAAC0C,OAAO,CAAC,EAAEhC,EAAE,CAACV,KAAK,CAAC2C,SAAS,GAAG,MAAM,CAAC,CAAC;EACtFjC,EAAE,CAAC4C,cAAc,CAAC5C,EAAE,CAACV,KAAK,CAAC;EAE3B,IAAIU,EAAE,CAACV,KAAK,CAACC,GAAG,KAAK,KAAK,EAAE,OAAO,CAAC;EACpCS,EAAE,CAACV,KAAK,CAACkJ,OAAO,GAAG,IAAI,CAAC,CAAC;EACzBxI,EAAE,CAACyI,QAAQ,GAAGzI,EAAE,CAACV,KAAK,CAACwD,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,CAAC;;EAErC;EACA,IAAI/C,EAAE,CAACV,KAAK,CAAC4C,MAAM,EAAE;IACpB1C,UAAU,CAAC,YAAW;MAAE;MACvBQ,EAAE,CAACV,KAAK,CAAC4C,MAAM,CAAClC,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,CAAC;EACN;AACD,CAAC;;AAED;AACAH,QAAQ,CAACmB,SAAS,CAAClC,aAAa,GAAG,YAAW;EAC7C,IAAI,IAAI,CAACiB,YAAY,IAAI,CAAC,IAAI,CAACI,aAAa,EAAE;IAAE;IAC/C,IAAI,CAAC,IAAI,CAACb,KAAK,CAAC6B,MAAM,IAAI,IAAI,CAAC7B,KAAK,CAACkJ,OAAO,EAAE;MAC7C,IAAI,CAACzH,eAAe,EAAE;IACvB;EACD;AACD,CAAC;;AAED;AACAlB,QAAQ,CAACmB,SAAS,CAACpC,YAAY,GAAG,UAASC,CAAC,EAAE;EAC7C,IAAI,CAAC,IAAI,CAACkB,YAAY,EAAE;;EAExB;EACA,IAAI,CAAC2I,YAAY,CAAC7J,CAAC,CAACgH,SAAS,CAAC;;EAE9B;EACA,IAAIhH,CAAC,CAACgH,SAAS,IAAI,IAAI,CAACvG,KAAK,CAACiE,KAAK,CAACnC,MAAM,EAAE;IAC3C,IAAI,CAACuH,UAAU,EAAE;EAClB,CAAC,MAAM;IACN,IAAI,CAACC,UAAU,EAAE;EAClB;AACD,CAAC;;AAED;AACA/I,QAAQ,CAACmB,SAAS,CAAC6H,MAAM,GAAG,UAAShK,CAAC,EAAE4F,QAAQ,EAAE;EACjD;EACA,IAAI,CAACiE,YAAY,CAAC7J,CAAC,CAACgH,SAAS,CAAC;EAC9B;EACA,IAAI,CAACqC,eAAe,CAACrJ,CAAC,CAACiK,YAAY,CAAC;;EAEpC;EACA,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,EAAE,IAAI,CAACA,UAAU,GAAG,CAAC;EAChD,IAAI,CAACnC,UAAU,GAAG/H,CAAC,CAACgH,SAAS,GAAG,IAAI,CAACkD,UAAU,GAAG,CAAC;EACnD,IAAI,CAACA,UAAU,GAAGlK,CAAC,CAACgH,SAAS;;EAE7B;EACA,IAAI,CAACe,UAAU,IAAI,IAAI,CAAC7F,eAAe,CAAC,IAAI,CAAC;;EAE7C;EACA,IAAIlC,CAAC,CAACgH,SAAS,IAAI,IAAI,CAACvG,KAAK,CAACiE,KAAK,CAACnC,MAAM,EAAE;IAC3C,IAAI,CAACuH,UAAU,EAAE;EAClB,CAAC,MAAM;IACN,IAAI,CAACC,UAAU,EAAE;EAClB;;EAEA;EACA,IAAI,CAACtJ,KAAK,CAACmF,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,EAAE;AAC9C,CAAC;;AAED;AACA5E,QAAQ,CAACmB,SAAS,CAACD,eAAe,GAAG,UAASiI,OAAO,EAAE;EACtD,IAAI,CAAC,IAAI,CAAC7I,aAAa,IAAI,IAAI,CAACb,KAAK,CAACC,GAAG,IAAI,IAAI,CAACD,KAAK,CAACgB,QAAQ,EAAE;IACjE;IACA,IAAI0I,OAAO,KAAK,IAAI,EAAE;MACrB,IAAIC,KAAK,GAAG,KAAK;MACjB;MACA,IAAI,IAAI,CAAC3J,KAAK,CAACkJ,OAAO,IAAI,CAAC,IAAI,CAAClJ,KAAK,CAAC6B,MAAM,IAAI,CAAC,IAAI,CAACjB,eAAe,EAAE;QACtE,IAAI,IAAI,CAACgJ,eAAe,EAAE,IAAI,IAAI,CAAC5J,KAAK,CAAC8B,MAAM,EAAE;UAAE;UAClD6H,KAAK,GAAG,IAAI,CAAC,CAAC;QACf;MACD;;MACA,IAAIA,KAAK,KAAK,KAAK,EAAE;IACtB;IACA,IAAI,CAACE,YAAY,EAAE,CAAC,CAAC;IACrB,IAAI,CAAC7J,KAAK,CAACwD,IAAI,CAACC,GAAG,EAAE,CAAC,CAAC;IACvB,IAAI,CAACjC,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACiC,GAAG,GAAG,IAAI,CAACzD,KAAK,CAACwD,IAAI,CAACC,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC1D,KAAK,CAACwD,IAAI,CAACE,IAAI,CAAC,CAAC;IAClC,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC3D,KAAK,CAACwD,IAAI,CAACG,IAAI,CAAC,CAAC;IAClC,IAAI,CAAC3D,KAAK,CAACgB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5B;AACD,CAAC;;AAED;AACAT,QAAQ,CAACmB,SAAS,CAACmI,YAAY,GAAG,YAAW;EAC5C,IAAI,CAAChJ,aAAa,GAAG,IAAI,CAAC,CAAC;EAC3B,IAAI,CAACb,KAAK,CAACiD,WAAW,IAAI,IAAI,CAACjD,KAAK,CAACiD,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AACzD,CAAC;;AAED;AACA1C,QAAQ,CAACmB,SAAS,CAACoC,UAAU,GAAG,YAAW;EAC1C,IAAI,CAAC9D,KAAK,CAACkJ,OAAO,GAAG,KAAK,CAAC,CAAC;EAC5B,IAAI,CAAClJ,KAAK,CAAC8D,UAAU,IAAI,IAAI,CAAC9D,KAAK,CAAC8D,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;AACvD,CAAC;;AAED;AACAvD,QAAQ,CAACmB,SAAS,CAACqC,YAAY,GAAG,YAAW;EAC5C,IAAI,CAAC/D,KAAK,CAAC+D,YAAY,IAAI,IAAI,CAAC/D,KAAK,CAAC+D,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3D,CAAC;;AAED;AACAxD,QAAQ,CAACmB,SAAS,CAACoI,WAAW,GAAG,UAASC,YAAY,EAAE;EACvD,IAAIA,YAAY,IAAI,IAAI,EAAE;IAAE;IAC3B,IAAIA,YAAY,EAAE;MACjB,IAAI,CAACjG,UAAU,EAAE,CAAC,CAAC;IACpB,CAAC,MAAM;MACN,IAAI,CAACC,YAAY,EAAE,CAAC,CAAC;IACtB;EACD;;EACA,IAAI,CAAClD,aAAa,GAAG,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAN,QAAQ,CAACmB,SAAS,CAAC9B,aAAa,GAAG,UAASoK,aAAa,EAAE;EAC1D,IAAI,IAAI,CAAChK,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,GAAG,EAAE;IACjC,IAAIuD,IAAI,GAAG,IAAI,CAACxD,KAAK,CAACwD,IAAI;IAC1B,IAAI,CAACyG,UAAU,GAAGzG,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACyG,WAAW,GAAG1G,IAAI,CAACG,IAAI,CAAC,CAAC;IAC9BH,IAAI,CAACC,GAAG,GAAG,IAAI,CAAC0F,QAAQ,CAAC,CAAC;IAC1B3F,IAAI,CAACG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAAC/C,eAAe,IAAIoJ,aAAa,KAAK,KAAK,EAAE;MAAE;MACvD,IAAIA,aAAa,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACG,WAAW,EAAE,CAAC,CAAC;QACpB,IAAI,CAACN,YAAY,EAAE,CAAC,CAAC;MACtB,CAAC,MAAM;QACN,IAAI,CAACvB,cAAc,EAAE,CAAC,CAAC;MACxB;IACD;;IACA,IAAI,CAAC9G,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACiC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,IAAI,GAAGF,IAAI,CAACE,IAAI,CAAC,CAAC;IACvB,IAAI,CAACC,IAAI,GAAGH,IAAI,CAACG,IAAI,CAAC,CAAC;IACvB,IAAI,CAAC3D,KAAK,CAACgB,QAAQ,IAAI,IAAI,CAAChB,KAAK,CAACgB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACnD;AACD,CAAC;;AAED;AACAT,QAAQ,CAACmB,SAAS,CAAC0I,UAAU,GAAG,UAAS3G,GAAG,EAAE;EAC7C,IAAI,CAACzD,KAAK,CAACwD,IAAI,CAACC,GAAG,GAAGA,GAAG,GAAG,CAAC;AAC9B,CAAC;;AAED;AACAlD,QAAQ,CAACmB,SAAS,CAAC2I,WAAW,GAAG,UAAS3G,IAAI,EAAE;EAC/C,IAAI,CAAC1D,KAAK,CAACwD,IAAI,CAACE,IAAI,GAAGA,IAAI;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAnD,QAAQ,CAACmB,SAAS,CAAC4I,SAAS,GAAG,UAASC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACrE,IAAIvB,OAAO;EACX,IAAI,IAAI,CAAClJ,KAAK,CAACC,GAAG,IAAIuK,SAAS,IAAI,IAAI,EAAEtB,OAAO,GAAG,IAAI,CAAClJ,KAAK,CAACwD,IAAI,CAACC,GAAG,GAAG+G,SAAS,CAAC,CAAC;EACpF,IAAI,CAACrK,UAAU,CAACoK,QAAQ,EAAErB,OAAO,EAAEuB,OAAO,CAAC;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAlK,QAAQ,CAACmB,SAAS,CAACgJ,SAAS,GAAG,UAASH,QAAQ,EAAEI,SAAS,EAAEF,OAAO,EAAE;EACrE,IAAIvB,OAAO;EACX,IAAI,IAAI,CAAClJ,KAAK,CAACC,GAAG,IAAI0K,SAAS,IAAI,IAAI,EAAE;IACxC,IAAIC,QAAQ,GAAG,CAAC,IAAI,CAAC5K,KAAK,CAACwD,IAAI,CAACC,GAAG,GAAG,CAAC,IAAI,IAAI,CAACzD,KAAK,CAACwD,IAAI,CAACE,IAAI,GAAG6G,QAAQ,CAAC,CAAC;IAC5ErB,OAAO,GAAG0B,QAAQ,GAAGD,SAAS,CAAC,CAAC;EACjC;;EACA,IAAI,CAACxK,UAAU,CAACoK,QAAQ,EAAErB,OAAO,EAAEuB,OAAO,CAAC;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAlK,QAAQ,CAACmB,SAAS,CAACvB,UAAU,GAAG,UAASoK,QAAQ,EAAErB,OAAO,EAAEuB,OAAO,EAAE;EACpE,IAAI/J,EAAE,GAAG,IAAI;EACb;EACA,IAAIA,EAAE,CAACE,eAAe,EAAE;IACvBF,EAAE,CAACsG,gBAAgB,GAAG,IAAI;IAC1BtG,EAAE,CAAC0C,aAAa,EAAE;EACnB;;EAEA;EACA,IAAI1C,EAAE,CAACV,KAAK,CAACC,GAAG,EAAE;IACjB,IAAI8J,YAAY,CAAC,CAAC;IAClB,IAAIQ,QAAQ,IAAI,IAAI,EAAE;MACrB,IAAIM,OAAO,GAAGnK,EAAE,CAACV,KAAK,CAACwD,IAAI,CAACC,GAAG,CAAC,CAAC;MACjC,IAAIqH,QAAQ,GAAGpK,EAAE,CAACV,KAAK,CAACwD,IAAI,CAACE,IAAI,CAAC,CAAC;MACnC;MACA,IAAImH,OAAO,KAAK,CAAC,EAAE;QAClB,IAAIJ,OAAO,EAAE/J,EAAE,CAACV,KAAK,CAACwD,IAAI,CAACG,IAAI,GAAG8G,OAAO,CAAC,CAAC;MAC5C;;MACA,IAAIF,QAAQ,GAAGO,QAAQ,IAAI5B,OAAO,KAAK,KAAK,EAAE;QAC7C;QACAxI,EAAE,CAACV,KAAK,CAACkJ,OAAO,GAAG,KAAK;QACxB,IAAIqB,QAAQ,KAAK,CAAC,IAAIM,OAAO,KAAK,CAAC,EAAE;UACpC;UACAd,YAAY,GAAG,KAAK;UACpBrJ,EAAE,CAACqK,SAAS,EAAE;QACf,CAAC,MAAM;UACN;UACA,IAAIC,WAAW,GAAG,CAACH,OAAO,GAAG,CAAC,IAAIC,QAAQ,GAAGP,QAAQ;UACrD,IAAIS,WAAW,GAAGtK,EAAE,CAACV,KAAK,CAAC4D,UAAU,EAAE;YACtCmG,YAAY,GAAG,KAAK;UACrB,CAAC,MAAM;YACNA,YAAY,GAAG,IAAI;UACpB;UACArJ,EAAE,CAACyJ,WAAW,EAAE,CAAC,CAAC;QACnB;MACD,CAAC,MAAM;QACN;QACAJ,YAAY,GAAG,KAAK;QACpBrJ,EAAE,CAACV,KAAK,CAACkJ,OAAO,GAAG,IAAI;QACvBxI,EAAE,CAACyJ,WAAW,EAAE,CAAC,CAAC;MACnB;IACD;;IAEA;IACAzJ,EAAE,CAACoJ,WAAW,CAACC,YAAY,CAAC;EAC7B;AACD,CAAC;;AAED;AACAxJ,QAAQ,CAACmB,SAAS,CAACrB,MAAM,GAAG,UAAS2D,WAAW,EAAE;EACjD;EACA,IAAI,IAAI,CAACpD,eAAe,EAAE;IACzB,IAAI,CAACoG,gBAAgB,GAAG,KAAK;IAC7B,IAAIxD,IAAI,GAAG,IAAI,CAACxD,KAAK,CAACwD,IAAI;IAC1B,IAAIA,IAAI,IAAI,IAAI,CAACyG,UAAU,EAAE;MAC5BzG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACwG,UAAU;MAC1BzG,IAAI,CAACG,IAAI,GAAG,IAAI,CAACuG,WAAW;IAC7B;IACA,IAAI,CAAC9G,aAAa,EAAE;EACrB;EACA;EACA,IAAI,IAAI,CAACvC,aAAa,EAAE;IACvB,IAAI,CAACb,KAAK,CAACwD,IAAI,CAACC,GAAG,EAAE;IACrB,IAAI,CAACqG,WAAW,CAAC,KAAK,CAAC;IACvB;IACA,IAAG,IAAI,CAACrJ,YAAY,IAAIuD,WAAW,KAAK,CAAC,EAAC;MAAE;MAC3C,IAAG,CAACA,WAAW,EAAEA,WAAW,GAAG,IAAI,CAAChE,KAAK,CAACgE,WAAW,CAAC,CAAC;MACvD,IAAI,CAAC6E,QAAQ,CAAC,IAAI,CAAC7C,YAAY,EAAE,GAAGhC,WAAW,EAAE,CAAC,CAAC,EAAC;IACrD;EACD;AACD,CAAC;;AAED;AACAzD,QAAQ,CAACmB,SAAS,CAACqJ,SAAS,GAAG,YAAW;EACzC,IAAI,CAAC/K,KAAK,CAAC6E,KAAK,CAAC5E,GAAG,IAAI,IAAI,CAACD,KAAK,CAAC6E,KAAK,CAACR,MAAM,IAAI,IAAI,CAACrE,KAAK,CAAC6E,KAAK,CAACR,MAAM,CAAC,IAAI,CAAC;AACjF,CAAC;;AAED;AACA9D,QAAQ,CAACmB,SAAS,CAACyI,WAAW,GAAG,YAAW;EAC3C,IAAI,CAACnK,KAAK,CAAC6E,KAAK,CAAC5E,GAAG,IAAI,IAAI,CAACD,KAAK,CAAC6E,KAAK,CAACR,MAAM,IAAI,IAAI,CAACrE,KAAK,CAAC6E,KAAK,CAACR,MAAM,CAAC,KAAK,CAAC;AAClF,CAAC;;AAED;AACA9D,QAAQ,CAACmB,SAAS,CAAC2H,UAAU,GAAG,YAAW;EAC1C,IAAI,CAAC,IAAI,CAAC4B,UAAU,EAAE;IACrB,IAAI,CAACA,UAAU,GAAG,IAAI;IACtB,IAAI,CAACjL,KAAK,CAACiE,KAAK,CAACI,MAAM,IAAI,IAAI,CAACrE,KAAK,CAACiE,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EACzD;AACD,CAAC;;AAED;AACA9D,QAAQ,CAACmB,SAAS,CAAC4H,UAAU,GAAG,YAAW;EAC1C,IAAI,IAAI,CAAC2B,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,IAAI,CAACjL,KAAK,CAACiE,KAAK,CAACI,MAAM,IAAI,IAAI,CAACrE,KAAK,CAACiE,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC;EAC1D;AACD,CAAC;;AAED;AACA9D,QAAQ,CAACmB,SAAS,CAACsE,YAAY,GAAG,YAAW;EAC5C,OAAO,IAAI,CAACO,SAAS,IAAI,CAAC;AAC3B,CAAC;;AAED;AACAhG,QAAQ,CAACmB,SAAS,CAAC0H,YAAY,GAAG,UAAS1C,CAAC,EAAE;EAC7C,IAAI,CAACH,SAAS,GAAGG,CAAC;AACnB,CAAC;;AAED;AACAnG,QAAQ,CAACmB,SAAS,CAACmH,QAAQ,GAAG,UAASnC,CAAC,EAAEwE,CAAC,EAAE;EAC5C,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACzE,CAAC,EAAEwE,CAAC,CAAC,EAAC;AAC1C,CAAC;;AAED;AACA3K,QAAQ,CAACmB,SAAS,CAAC0J,aAAa,GAAG,UAASD,UAAU,EAAE;EACvD,IAAI,CAACA,UAAU,GAAGA,UAAU;AAC7B,CAAC;;AAED;AACA5K,QAAQ,CAACmB,SAAS,CAACkI,eAAe,GAAG,YAAW;EAC/C,OAAO,IAAI,CAACyB,eAAe,EAAE,GAAG,IAAI,CAACC,eAAe,EAAE,GAAG,IAAI,CAACtF,YAAY,EAAE;AAC7E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAzF,QAAQ,CAACmB,SAAS,CAAC6J,OAAO,GAAG,UAASC,IAAI,EAAEC,GAAG,EAAEzK,QAAQ,EAAEkK,CAAC,EAAE9D,IAAI,EAAE;EACnE,IAAIN,IAAI,GAAG2E,GAAG,GAAGD,IAAI,CAAC,CAAC;EACvB,IAAIN,CAAC,KAAK,CAAC,IAAIpE,IAAI,KAAK,CAAC,EAAE;IAC1B9F,QAAQ,IAAIA,QAAQ,CAACyK,GAAG,CAAC;IACzB;EACD;EACAP,CAAC,GAAGA,CAAC,IAAI,GAAG,CAAC,CAAC;EACd9D,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAC,CAAC;EACnB,IAAIsE,KAAK,GAAGR,CAAC,GAAG9D,IAAI,CAAC,CAAC;EACtB,IAAIuE,IAAI,GAAG7E,IAAI,GAAG4E,KAAK,CAAC,CAAC;EACzB,IAAIE,CAAC,GAAG,CAAC,CAAC,CAAC;EACX,IAAIC,KAAK,GAAGC,WAAW,CAAC,YAAW;IAClC,IAAIF,CAAC,GAAGF,KAAK,GAAG,CAAC,EAAE;MAClBF,IAAI,IAAIG,IAAI;MACZ3K,QAAQ,IAAIA,QAAQ,CAACwK,IAAI,EAAEK,KAAK,CAAC;MACjCD,CAAC,EAAE;IACJ,CAAC,MAAM;MACN5K,QAAQ,IAAIA,QAAQ,CAACyK,GAAG,EAAEI,KAAK,CAAC,CAAC,CAAC;MAClCE,aAAa,CAACF,KAAK,CAAC;IACrB;EACD,CAAC,EAAEzE,IAAI,CAAC;AACT,CAAC;;AAED;AACA7G,QAAQ,CAACmB,SAAS,CAAC4J,eAAe,GAAG,UAASU,MAAM,EAAE;EACrD,IAAIC,CAAC,GAAG,IAAI,CAACC,YAAY,IAAI,CAAC;EAC9B,IAAID,CAAC,KAAK,CAAC,IAAID,MAAM,KAAK,IAAI,EAAE;IAAE;IACjCC,CAAC,GAAG,IAAI,CAAC7F,aAAa,EAAE;EACzB;EACA,OAAO6F,CAAC;AACT,CAAC;AACD1L,QAAQ,CAACmB,SAAS,CAACyK,eAAe,GAAG,UAASF,CAAC,EAAE;EAChD,IAAI,CAACC,YAAY,GAAGD,CAAC;AACtB,CAAC;;AAED;AACA1L,QAAQ,CAACmB,SAAS,CAAC2J,eAAe,GAAG,YAAW;EAC/C,OAAO,IAAI,CAAC7B,YAAY,IAAI,CAAC;AAC9B,CAAC;AACDjJ,QAAQ,CAACmB,SAAS,CAACkH,eAAe,GAAG,UAASqD,CAAC,EAAE;EAChD,IAAI,CAACzC,YAAY,GAAGyC,CAAC;AACtB,CAAC;;AAED;AACA1L,QAAQ,CAACmB,SAAS,CAAC0E,aAAa,GAAG,YAAW;EAC7C,OAAO,IAAI,CAACgG,UAAU,IAAI,CAAC;AAC5B,CAAC;AACD7L,QAAQ,CAACmB,SAAS,CAAC2K,aAAa,GAAG,UAASJ,CAAC,EAAE;EAC9C,IAAI,CAACG,UAAU,GAAGH,CAAC;AACpB,CAAC;;AAED;AACA1L,QAAQ,CAACmB,SAAS,CAACmF,cAAc,GAAG,UAAStH,CAAC,EAAE;EAC/C;EACA;EACA;EACA,IAAIA,CAAC,IAAIA,CAAC,CAAC+M,UAAU,IAAI,CAAC/M,CAAC,CAACgN,gBAAgB,EAAEhN,CAAC,CAACsH,cAAc,EAAE;AACjE,CAAC,C;;;;;;;;;;;;;;;;;;;AC9xBD;AACA;AACA,IAAM2F,YAAY,GAAG;EACpBzL,IAAI,EAAE;IACL;IACAe,MAAM,EAAE,EAAE;IAAE;IACZV,MAAM,EAAE,KAAK,CAAC;EACf,CAAC;;EACD6H,EAAE,EAAE;IACH;IACAnH,MAAM,EAAE,GAAG;IAAE;IACbmC,KAAK,EAAE;MACN;MACAC,GAAG,EAAE,iDAAiD;MAAE;MACxDpC,MAAM,EAAE,IAAI;MAAE;MACd0C,KAAK,EAAE,EAAE;MAAE;MACXC,MAAM,EAAE,GAAG;MAAE;MACbE,KAAK,EAAE,EAAE,CAAC;IACX,CAAC;;IACDE,KAAK,EAAE;MACN5E,GAAG,EAAE,IAAI;MAAE;MACX6E,IAAI,EAAE,iDAAiD,CAAC;IACzD;EACD,CAAC;;EACD;EACA2H,IAAI,EAAE;IACL;IACAC,EAAE,EAAE;MACH3L,IAAI,EAAE;QACLqB,YAAY,EAAE,MAAM;QAAE;QACtBC,aAAa,EAAE,MAAM;QAAE;QACvBC,WAAW,EAAE,SAAS;QAAE;QACxBC,WAAW,EAAE,MAAM;QAAE;QACrBC,OAAO,EAAE,MAAM,CAAE;MAClB,CAAC;;MACDyG,EAAE,EAAE;QACH3G,WAAW,EAAE,SAAS;QAAE;QACxBuB,UAAU,EAAE,SAAS;QAAE;QACvBgB,KAAK,EAAE;UACNE,GAAG,EAAE,UAAU,CAAC;QACjB;MACD;IACD,CAAC;;IACD;IACA4H,EAAE,EAAE;MACH5L,IAAI,EAAE;QACLqB,YAAY,EAAE,mBAAmB;QACjCC,aAAa,EAAE,iBAAiB;QAChCC,WAAW,EAAE,aAAa;QAC1BC,WAAW,EAAE,qBAAqB;QAClCC,OAAO,EAAE;MACV,CAAC;MACDyG,EAAE,EAAE;QACH3G,WAAW,EAAE,aAAa;QAC1BuB,UAAU,EAAE,SAAS;QACrBgB,KAAK,EAAE;UACNE,GAAG,EAAE;QACN;MACD;IACD;EACD;AACD,CAAC;AAAA,eAEcyH,YAAY;AAAA,2B;;;;;;;;;;;;;;;;;;AC/D3B;AACA,IAAMI,YAAY,GAAG;EACpB;EACArH,GAAG,EAAE,IAAI;EACT;EACAsH,OAAO,qBAAE;IACR,OAAOtE,GAAG,CAACuE,cAAc,CAAC,eAAe,CAAC,IAAI,IAAI,CAACvH,GAAG;EACvD,CAAC;EACD;EACAwH,OAAO,mBAACC,IAAI,EAAC;IACZzE,GAAG,CAAC0E,cAAc,CAAC,eAAe,EAAED,IAAI,CAAC;EAC1C;AACD,CAAC;AAAA,eAEcJ,YAAY;AAAA,2B;;;;;;;;;;;;;;;;;;;ACd3B;AACA,IAAMM,QAAQ,GAAG;EAChB/N,IAAI,kBAAG;IACN,OAAO;MACN;MACAgO,OAAO,EAAE;QACRhM,OAAO,EAAC,CAAC,CAAC;QAAE;QACZoF,SAAS,EAAC,CAAC;QAAE;QACb6F,UAAU,EAAC,CAAC;QAAE;QACdxL,eAAe,EAAC,KAAK;QAAE;QACvBC,aAAa,EAAC,KAAK;QAAE;QACrBJ,YAAY,EAAC,IAAI;QAAE;QACnB2M,QAAQ,EAAC,IAAI;QAAE;QACflC,CAAC,EAAE,CAAC,CAAC;MACN,CAAC;;MAED;MACAmC,QAAQ,EAAE;QACTC,QAAQ,EAAE,EAAE;QAAE;QACdpC,CAAC,EAAE,CAAC,CAAC;MACN,CAAC;;MAED;;MAoBA;;MAEAqC,SAAS,EAAE;QACVC,YAAY,0BAAE,CAAC,CAAC,CAAC;MAClB;IAED,CAAC;EACF,CAAC;EACD/N,OAAO,EAAE;IACR;IACAgO,OAAO,mBAACC,GAAG,EAAC;MACX,IAAGA,GAAG,CAACV,IAAI,KAAK,YAAY,EAAC;QAC5B;QACA,IAAI,CAACG,OAAO,GAAG;UACdhM,OAAO,EAAE,IAAI,CAAC/B,QAAQ,CAAC+B,OAAO;UAC9BoF,SAAS,EAAE,IAAI,CAACnH,QAAQ,CAAC4G,YAAY,EAAE;UACvCoG,UAAU,EAAE,IAAI,CAAChN,QAAQ,CAACgH,aAAa,EAAE;UACzCxF,eAAe,EAAE,IAAI,CAACxB,QAAQ,CAACwB,eAAe;UAC9CC,aAAa,EAAE,IAAI,CAACzB,QAAQ,CAACyB,aAAa;UAC1CuM,QAAQ,EAAE,IAAI,CAAChO,QAAQ,CAACY,KAAK,CAACuD,MAAM;UACpC9C,YAAY,EAAC,IAAI,CAACrB,QAAQ,CAACqB,YAAY;UACvCyK,CAAC,EAAEyC,IAAI,CAACC,GAAG;QACZ,CAAC;MACF,CAAC,MAAK,IAAGF,GAAG,CAACV,IAAI,KAAK,aAAa,EAAC;QACnC;QACA,IAAI,CAACa,YAAY,GAAGH,GAAG,CAACG,YAAY;QACpC;QACA,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC1O,QAAQ,EAAE,cAAc,EAAE,IAAI,CAACyO,YAAY,CAAC;QAC3D;QACA,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC1O,QAAQ,EAAE,kBAAkB,EAAE,IAAI,CAAC;MACnD,CAAC,MAAK,IAAGsO,GAAG,CAACV,IAAI,KAAK,mBAAmB,EAAC;QACzC;QACA,IAAI,CAAC5N,QAAQ,CAACmC,iBAAiB,EAAE;MAClC,CAAC,MAAK,IAAGmM,GAAG,CAACV,IAAI,KAAK,eAAe,EAAC;QACrC;QACA,IAAI,CAAC5N,QAAQ,CAACgE,aAAa,EAAE;MAC9B,CAAC,MAAK,IAAGsK,GAAG,CAACV,IAAI,KAAK,iBAAiB,EAAC;QACvC;QACA,IAAI,CAAC5N,QAAQ,CAACqC,eAAe,CAAC,IAAI,CAAC;MACpC;IACD;EACD,CAAC;EACDnB,OAAO,qBAAG;IAAA;IAET;IACA,IAAI,CAAClB,QAAQ,CAAC+B,OAAO,CAAC+B,YAAY,GAAG,YAAI;MACxC,KAAI,CAACmK,QAAQ,GAAG;QAACC,QAAQ,EAAE,aAAa;QAAEpC,CAAC,EAAEyC,IAAI,CAACC,GAAG;MAAE,CAAC,EAAC;IAC1D,CAAC;IACD;IACA,IAAI,CAACxO,QAAQ,CAAC+B,OAAO,CAACkC,kBAAkB,GAAG,YAAI;MAC9C,KAAI,CAACgK,QAAQ,GAAG;QAACC,QAAQ,EAAE,eAAe;QAAEpC,CAAC,EAAEyC,IAAI,CAACC,GAAG;MAAE,CAAC,EAAC;MAC3D,IAAI9E,KAAK,GAAG,GAAG,IAAI,KAAI,CAAC1J,QAAQ,CAAC+B,OAAO,CAACsB,cAAc,IAAI,CAAC,CAAC;MAC7DvC,UAAU,CAAC,YAAI;QACd,IAAG,KAAI,CAAC2N,YAAY,KAAK,CAAC,IAAI,KAAI,CAACA,YAAY,KAAK,CAAC,EAAC;UACrD,KAAI,CAACR,QAAQ,GAAG;YAACC,QAAQ,EAAE,gBAAgB;YAAEpC,CAAC,EAAEyC,IAAI,CAACC,GAAG;UAAE,CAAC,EAAC;QAC7D;QACA;QACA,KAAI,CAACE,IAAI,CAAC,KAAI,CAAC1O,QAAQ,EAAE,cAAc,EAAE,KAAI,CAACyO,YAAY,CAAC;MAC5D,CAAC,EAAE/E,KAAK,CAAC;IACV,CAAC;IACD;IACA,IAAI,CAAC2E,OAAO,CAAC;MAACT,IAAI,EAAE;IAAY,CAAC,CAAC;EAEnC;AACD,CAAC;AAAA,eAEcE,QAAQ;AAAA,2B", "file": "my/common/vendor.js", "sourcesContent": ["export default [{\n\t\t\"name\": \"北京市\",\n\t\t\"city\": [{\n\t\t\t\"name\": \"北京市\",\n\t\t\t\"area\": [\n\t\t\t\t\"东城区\",\n\t\t\t\t\"西城区\",\n\t\t\t\t\"崇文区\",\n\t\t\t\t\"宣武区\",\n\t\t\t\t\"朝阳区\",\n\t\t\t\t\"丰台区\",\n\t\t\t\t\"石景山区\",\n\t\t\t\t\"海淀区\",\n\t\t\t\t\"门头沟区\",\n\t\t\t\t\"房山区\",\n\t\t\t\t\"通州区\",\n\t\t\t\t\"顺义区\",\n\t\t\t\t\"昌平区\",\n\t\t\t\t\"大兴区\",\n\t\t\t\t\"平谷区\",\n\t\t\t\t\"怀柔区\",\n\t\t\t\t\"密云县\",\n\t\t\t\t\"延庆县\"\n\t\t\t]\n\t\t}]\n\t},\n\t{\n\t\t\"name\": \"天津市\",\n\t\t\"city\": [{\n\t\t\t\"name\": \"天津市\",\n\t\t\t\"area\": [\n\t\t\t\t\"和平区\",\n\t\t\t\t\"河东区\",\n\t\t\t\t\"河西区\",\n\t\t\t\t\"南开区\",\n\t\t\t\t\"河北区\",\n\t\t\t\t\"红桥区\",\n\t\t\t\t\"塘沽区\",\n\t\t\t\t\"汉沽区\",\n\t\t\t\t\"大港区\",\n\t\t\t\t\"东丽区\",\n\t\t\t\t\"西青区\",\n\t\t\t\t\"津南区\",\n\t\t\t\t\"北辰区\",\n\t\t\t\t\"武清区\",\n\t\t\t\t\"宝坻区\",\n\t\t\t\t\"宁河县\",\n\t\t\t\t\"静海县\",\n\t\t\t\t\"蓟  县\"\n\t\t\t]\n\t\t}]\n\t},\n\t{\n\t\t\"name\": \"河北省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"石家庄市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"长安区\",\n\t\t\t\t\t\"桥东区\",\n\t\t\t\t\t\"桥西区\",\n\t\t\t\t\t\"新华区\",\n\t\t\t\t\t\"郊  区\",\n\t\t\t\t\t\"井陉矿区\",\n\t\t\t\t\t\"井陉县\",\n\t\t\t\t\t\"正定县\",\n\t\t\t\t\t\"栾城县\",\n\t\t\t\t\t\"行唐县\",\n\t\t\t\t\t\"灵寿县\",\n\t\t\t\t\t\"高邑县\",\n\t\t\t\t\t\"深泽县\",\n\t\t\t\t\t\"赞皇县\",\n\t\t\t\t\t\"无极县\",\n\t\t\t\t\t\"平山县\",\n\t\t\t\t\t\"元氏县\",\n\t\t\t\t\t\"赵  县\",\n\t\t\t\t\t\"辛集市\",\n\t\t\t\t\t\"藁\",\n\t\t\t\t\t\"晋州市\",\n\t\t\t\t\t\"新乐市\",\n\t\t\t\t\t\"鹿泉市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"唐山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"路南区\",\n\t\t\t\t\t\"路北区\",\n\t\t\t\t\t\"古冶区\",\n\t\t\t\t\t\"开平区\",\n\t\t\t\t\t\"新  区\",\n\t\t\t\t\t\"丰润县\",\n\t\t\t\t\t\"滦  县\",\n\t\t\t\t\t\"滦南县\",\n\t\t\t\t\t\"乐亭县\",\n\t\t\t\t\t\"迁西县\",\n\t\t\t\t\t\"玉田县\",\n\t\t\t\t\t\"唐海县\",\n\t\t\t\t\t\"遵化市\",\n\t\t\t\t\t\"丰南市\",\n\t\t\t\t\t\"迁安市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"秦皇岛市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海港区\",\n\t\t\t\t\t\"山海关区\",\n\t\t\t\t\t\"北戴河区\",\n\t\t\t\t\t\"青龙满族自治县\",\n\t\t\t\t\t\"昌黎县\",\n\t\t\t\t\t\"抚宁县\",\n\t\t\t\t\t\"卢龙县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"邯郸市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"邯山区\",\n\t\t\t\t\t\"丛台区\",\n\t\t\t\t\t\"复兴区\",\n\t\t\t\t\t\"峰峰矿区\",\n\t\t\t\t\t\"邯郸县\",\n\t\t\t\t\t\"临漳县\",\n\t\t\t\t\t\"成安县\",\n\t\t\t\t\t\"大名县\",\n\t\t\t\t\t\"涉  县\",\n\t\t\t\t\t\"磁  县\",\n\t\t\t\t\t\"肥乡县\",\n\t\t\t\t\t\"永年县\",\n\t\t\t\t\t\"邱  县\",\n\t\t\t\t\t\"鸡泽县\",\n\t\t\t\t\t\"广平县\",\n\t\t\t\t\t\"馆陶县\",\n\t\t\t\t\t\"魏  县\",\n\t\t\t\t\t\"曲周县\",\n\t\t\t\t\t\"武安市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"邢台市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"桥东区\",\n\t\t\t\t\t\"桥西区\",\n\t\t\t\t\t\"邢台县\",\n\t\t\t\t\t\"临城县\",\n\t\t\t\t\t\"内丘县\",\n\t\t\t\t\t\"柏乡县\",\n\t\t\t\t\t\"隆尧县\",\n\t\t\t\t\t\"任  县\",\n\t\t\t\t\t\"南和县\",\n\t\t\t\t\t\"宁晋县\",\n\t\t\t\t\t\"巨鹿县\",\n\t\t\t\t\t\"新河县\",\n\t\t\t\t\t\"广宗县\",\n\t\t\t\t\t\"平乡县\",\n\t\t\t\t\t\"威  县\",\n\t\t\t\t\t\"清河县\",\n\t\t\t\t\t\"临西县\",\n\t\t\t\t\t\"南宫市\",\n\t\t\t\t\t\"沙河市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"保定市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"新市区\",\n\t\t\t\t\t\"北市区\",\n\t\t\t\t\t\"南市区\",\n\t\t\t\t\t\"满城县\",\n\t\t\t\t\t\"清苑县\",\n\t\t\t\t\t\"涞水县\",\n\t\t\t\t\t\"阜平县\",\n\t\t\t\t\t\"徐水县\",\n\t\t\t\t\t\"定兴县\",\n\t\t\t\t\t\"唐  县\",\n\t\t\t\t\t\"高阳县\",\n\t\t\t\t\t\"容城县\",\n\t\t\t\t\t\"涞源县\",\n\t\t\t\t\t\"望都县\",\n\t\t\t\t\t\"安新县\",\n\t\t\t\t\t\"易  县\",\n\t\t\t\t\t\"曲阳县\",\n\t\t\t\t\t\"蠡  县\",\n\t\t\t\t\t\"顺平县\",\n\t\t\t\t\t\"博野\",\n\t\t\t\t\t\"雄县\",\n\t\t\t\t\t\"涿州市\",\n\t\t\t\t\t\"定州市\",\n\t\t\t\t\t\"安国市\",\n\t\t\t\t\t\"高碑店市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"张家口\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"桥东区\",\n\t\t\t\t\t\"桥西区\",\n\t\t\t\t\t\"宣化区\",\n\t\t\t\t\t\"下花园区\",\n\t\t\t\t\t\"宣化县\",\n\t\t\t\t\t\"张北县\",\n\t\t\t\t\t\"康保县\",\n\t\t\t\t\t\"沽源县\",\n\t\t\t\t\t\"尚义县\",\n\t\t\t\t\t\"蔚  县\",\n\t\t\t\t\t\"阳原县\",\n\t\t\t\t\t\"怀安县\",\n\t\t\t\t\t\"万全县\",\n\t\t\t\t\t\"怀来县\",\n\t\t\t\t\t\"涿鹿县\",\n\t\t\t\t\t\"赤城县\",\n\t\t\t\t\t\"崇礼县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"承德市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"双桥区\",\n\t\t\t\t\t\"双滦区\",\n\t\t\t\t\t\"鹰手营子矿区\",\n\t\t\t\t\t\"承德县\",\n\t\t\t\t\t\"兴隆县\",\n\t\t\t\t\t\"平泉县\",\n\t\t\t\t\t\"滦平县\",\n\t\t\t\t\t\"隆化县\",\n\t\t\t\t\t\"丰宁满族自治县\",\n\t\t\t\t\t\"宽城满族自治县\",\n\t\t\t\t\t\"围场满族蒙古族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"沧州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"新华区\",\n\t\t\t\t\t\"运河区\",\n\t\t\t\t\t\"沧  县\",\n\t\t\t\t\t\"青  县\",\n\t\t\t\t\t\"东光县\",\n\t\t\t\t\t\"海兴县\",\n\t\t\t\t\t\"盐山县\",\n\t\t\t\t\t\"肃宁县\",\n\t\t\t\t\t\"南皮县\",\n\t\t\t\t\t\"吴桥县\",\n\t\t\t\t\t\"献  县\",\n\t\t\t\t\t\"孟村回族自治县\",\n\t\t\t\t\t\"泊头市\",\n\t\t\t\t\t\"任丘市\",\n\t\t\t\t\t\"黄骅市\",\n\t\t\t\t\t\"河间市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"廊坊市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"安次区\",\n\t\t\t\t\t\"固安县\",\n\t\t\t\t\t\"永清县\",\n\t\t\t\t\t\"香河县\",\n\t\t\t\t\t\"大城县\",\n\t\t\t\t\t\"文安县\",\n\t\t\t\t\t\"大厂回族自治县\",\n\t\t\t\t\t\"霸州市\",\n\t\t\t\t\t\"三河市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"衡水市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"桃城区\",\n\t\t\t\t\t\"枣强县\",\n\t\t\t\t\t\"武邑县\",\n\t\t\t\t\t\"武强县\",\n\t\t\t\t\t\"饶阳县\",\n\t\t\t\t\t\"安平县\",\n\t\t\t\t\t\"故城县\",\n\t\t\t\t\t\"景  县\",\n\t\t\t\t\t\"阜城县\",\n\t\t\t\t\t\"冀州市\",\n\t\t\t\t\t\"深州市\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"山西省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"太原市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"小店区\",\n\t\t\t\t\t\"迎泽区\",\n\t\t\t\t\t\"杏花岭区\",\n\t\t\t\t\t\"尖草坪区\",\n\t\t\t\t\t\"万柏林区\",\n\t\t\t\t\t\"晋源区\",\n\t\t\t\t\t\"清徐县\",\n\t\t\t\t\t\"阳曲县\",\n\t\t\t\t\t\"娄烦县\",\n\t\t\t\t\t\"古交市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"大同市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城  区\",\n\t\t\t\t\t\"矿  区\",\n\t\t\t\t\t\"南郊区\",\n\t\t\t\t\t\"新荣区\",\n\t\t\t\t\t\"阳高县\",\n\t\t\t\t\t\"天镇县\",\n\t\t\t\t\t\"广灵县\",\n\t\t\t\t\t\"灵丘县\",\n\t\t\t\t\t\"浑源县\",\n\t\t\t\t\t\"左云县\",\n\t\t\t\t\t\"大同县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阳泉市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城  区\",\n\t\t\t\t\t\"矿  区\",\n\t\t\t\t\t\"郊  区\",\n\t\t\t\t\t\"平定县\",\n\t\t\t\t\t\"盂  县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"长治市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城  区\",\n\t\t\t\t\t\"郊  区\",\n\t\t\t\t\t\"长治县\",\n\t\t\t\t\t\"襄垣县\",\n\t\t\t\t\t\"屯留县\",\n\t\t\t\t\t\"平顺县\",\n\t\t\t\t\t\"黎城县\",\n\t\t\t\t\t\"壶关县\",\n\t\t\t\t\t\"长子县\",\n\t\t\t\t\t\"武乡县\",\n\t\t\t\t\t\"沁  县\",\n\t\t\t\t\t\"沁源县\",\n\t\t\t\t\t\"潞城市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"晋城市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城  区\",\n\t\t\t\t\t\"沁水县\",\n\t\t\t\t\t\"阳城县\",\n\t\t\t\t\t\"陵川县\",\n\t\t\t\t\t\"泽州县\",\n\t\t\t\t\t\"高平市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"朔州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"朔城区\",\n\t\t\t\t\t\"平鲁区\",\n\t\t\t\t\t\"山阴县\",\n\t\t\t\t\t\"应  县\",\n\t\t\t\t\t\"右玉县\",\n\t\t\t\t\t\"怀仁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"忻州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"忻府区\",\n\t\t\t\t\t\"原平市\",\n\t\t\t\t\t\"定襄县\",\n\t\t\t\t\t\"五台县\",\n\t\t\t\t\t\"代  县\",\n\t\t\t\t\t\"繁峙县\",\n\t\t\t\t\t\"宁武县\",\n\t\t\t\t\t\"静乐县\",\n\t\t\t\t\t\"神池县\",\n\t\t\t\t\t\"五寨县\",\n\t\t\t\t\t\"岢岚县\",\n\t\t\t\t\t\"河曲县\",\n\t\t\t\t\t\"保德县\",\n\t\t\t\t\t\"偏关县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"吕梁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"离石区\",\n\t\t\t\t\t\"孝义市\",\n\t\t\t\t\t\"汾阳市\",\n\t\t\t\t\t\"文水县\",\n\t\t\t\t\t\"交城县\",\n\t\t\t\t\t\"兴  县\",\n\t\t\t\t\t\"临  县\",\n\t\t\t\t\t\"柳林县\",\n\t\t\t\t\t\"石楼县\",\n\t\t\t\t\t\"岚  县\",\n\t\t\t\t\t\"方山县\",\n\t\t\t\t\t\"中阳县\",\n\t\t\t\t\t\"交口县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"晋中市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"榆次市\",\n\t\t\t\t\t\"介休市\",\n\t\t\t\t\t\"榆社县\",\n\t\t\t\t\t\"左权县\",\n\t\t\t\t\t\"和顺县\",\n\t\t\t\t\t\"昔阳县\",\n\t\t\t\t\t\"寿阳县\",\n\t\t\t\t\t\"太谷县\",\n\t\t\t\t\t\"祁  县\",\n\t\t\t\t\t\"平遥县\",\n\t\t\t\t\t\"灵石县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"临汾市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"临汾市\",\n\t\t\t\t\t\"侯马市\",\n\t\t\t\t\t\"霍州市\",\n\t\t\t\t\t\"曲沃县\",\n\t\t\t\t\t\"翼城县\",\n\t\t\t\t\t\"襄汾县\",\n\t\t\t\t\t\"洪洞县\",\n\t\t\t\t\t\"古  县\",\n\t\t\t\t\t\"安泽县\",\n\t\t\t\t\t\"浮山县\",\n\t\t\t\t\t\"吉  县\",\n\t\t\t\t\t\"乡宁县\",\n\t\t\t\t\t\"蒲  县\",\n\t\t\t\t\t\"大宁县\",\n\t\t\t\t\t\"永和县\",\n\t\t\t\t\t\"隰  县\",\n\t\t\t\t\t\"汾西县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"运城市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"运城市\",\n\t\t\t\t\t\"永济市\",\n\t\t\t\t\t\"河津市\",\n\t\t\t\t\t\"芮城县\",\n\t\t\t\t\t\"临猗县\",\n\t\t\t\t\t\"万荣县\",\n\t\t\t\t\t\"新绛县\",\n\t\t\t\t\t\"稷山县\",\n\t\t\t\t\t\"闻喜县\",\n\t\t\t\t\t\"夏  县\",\n\t\t\t\t\t\"绛  县\",\n\t\t\t\t\t\"平陆县\",\n\t\t\t\t\t\"垣曲县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"内蒙古\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"呼和浩特市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"新城区\",\n\t\t\t\t\t\"回民区\",\n\t\t\t\t\t\"玉泉区\",\n\t\t\t\t\t\"郊  区\",\n\t\t\t\t\t\"土默特左旗\",\n\t\t\t\t\t\"托克托县\",\n\t\t\t\t\t\"和林格尔县\",\n\t\t\t\t\t\"清水河县\",\n\t\t\t\t\t\"武川县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"包头市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东河区\",\n\t\t\t\t\t\"昆都伦区\",\n\t\t\t\t\t\"青山区\",\n\t\t\t\t\t\"石拐矿区\",\n\t\t\t\t\t\"白云矿区\",\n\t\t\t\t\t\"郊  区\",\n\t\t\t\t\t\"土默特右旗\",\n\t\t\t\t\t\"固阳县\",\n\t\t\t\t\t\"达尔罕茂明安联合旗\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"乌海市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海勃湾区\",\n\t\t\t\t\t\"海南区\",\n\t\t\t\t\t\"乌达区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"赤峰市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"红山区\",\n\t\t\t\t\t\"元宝山区\",\n\t\t\t\t\t\"松山区\",\n\t\t\t\t\t\"阿鲁科尔沁旗\",\n\t\t\t\t\t\"巴林左旗\",\n\t\t\t\t\t\"巴林右旗\",\n\t\t\t\t\t\"林西县\",\n\t\t\t\t\t\"克什克腾旗\",\n\t\t\t\t\t\"翁牛特旗\",\n\t\t\t\t\t\"喀喇沁旗\",\n\t\t\t\t\t\"宁城县\",\n\t\t\t\t\t\"敖汉旗\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"呼伦贝尔市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海拉尔市\",\n\t\t\t\t\t\"满洲里市\",\n\t\t\t\t\t\"扎兰屯市\",\n\t\t\t\t\t\"牙克石市\",\n\t\t\t\t\t\"根河市\",\n\t\t\t\t\t\"额尔古纳市\",\n\t\t\t\t\t\"阿荣旗\",\n\t\t\t\t\t\"莫力达瓦达斡尔族自治旗\",\n\t\t\t\t\t\"鄂伦春自治旗\",\n\t\t\t\t\t\"鄂温克族自治旗\",\n\t\t\t\t\t\"新巴尔虎右旗\",\n\t\t\t\t\t\"新巴尔虎左旗\",\n\t\t\t\t\t\"陈巴尔虎旗\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"兴安盟\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"乌兰浩特市\",\n\t\t\t\t\t\"阿尔山市\",\n\t\t\t\t\t\"科尔沁右翼前旗\",\n\t\t\t\t\t\"科尔沁右翼中旗\",\n\t\t\t\t\t\"扎赉特旗\",\n\t\t\t\t\t\"突泉县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"通辽市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"科尔沁区\",\n\t\t\t\t\t\"霍林郭勒市\",\n\t\t\t\t\t\"科尔沁左翼中旗\",\n\t\t\t\t\t\"科尔沁左翼后旗\",\n\t\t\t\t\t\"开鲁县\",\n\t\t\t\t\t\"库伦旗\",\n\t\t\t\t\t\"奈曼旗\",\n\t\t\t\t\t\"扎鲁特旗\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"锡林郭勒盟\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"二连浩特市\",\n\t\t\t\t\t\"锡林浩特市\",\n\t\t\t\t\t\"阿巴嘎旗\",\n\t\t\t\t\t\"苏尼特左旗\",\n\t\t\t\t\t\"苏尼特右旗\",\n\t\t\t\t\t\"东乌珠穆沁旗\",\n\t\t\t\t\t\"西乌珠穆沁旗\",\n\t\t\t\t\t\"太仆寺旗\",\n\t\t\t\t\t\"镶黄旗\",\n\t\t\t\t\t\"正镶白旗\",\n\t\t\t\t\t\"正蓝旗\",\n\t\t\t\t\t\"多伦县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"乌兰察布盟\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"集宁市\",\n\t\t\t\t\t\"丰镇市\",\n\t\t\t\t\t\"卓资县\",\n\t\t\t\t\t\"化德县\",\n\t\t\t\t\t\"商都县\",\n\t\t\t\t\t\"兴和县\",\n\t\t\t\t\t\"凉城县\",\n\t\t\t\t\t\"察哈尔右翼前旗\",\n\t\t\t\t\t\"察哈尔右翼中旗\",\n\t\t\t\t\t\"察哈尔右翼后旗\",\n\t\t\t\t\t\"四子王旗\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"伊克昭盟\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东胜市\",\n\t\t\t\t\t\"达拉特旗\",\n\t\t\t\t\t\"准格尔旗\",\n\t\t\t\t\t\"鄂托克前旗\",\n\t\t\t\t\t\"鄂托克旗\",\n\t\t\t\t\t\"杭锦旗\",\n\t\t\t\t\t\"乌审旗\",\n\t\t\t\t\t\"伊金霍洛旗\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"巴彦淖尔盟\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"临河市\",\n\t\t\t\t\t\"五原县\",\n\t\t\t\t\t\"磴口县\",\n\t\t\t\t\t\"乌拉特前旗\",\n\t\t\t\t\t\"乌拉特中旗\",\n\t\t\t\t\t\"乌拉特后旗\",\n\t\t\t\t\t\"杭锦后旗\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阿拉善盟\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"阿拉善左旗\",\n\t\t\t\t\t\"阿拉善右旗\",\n\t\t\t\t\t\"额济纳旗\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"辽宁省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"沈阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"沈河区\",\n\t\t\t\t\t\"皇姑区\",\n\t\t\t\t\t\"和平区\",\n\t\t\t\t\t\"大东区\",\n\t\t\t\t\t\"铁西区\",\n\t\t\t\t\t\"苏家屯区\",\n\t\t\t\t\t\"东陵区\",\n\t\t\t\t\t\"于洪区\",\n\t\t\t\t\t\"新民市\",\n\t\t\t\t\t\"法库县\",\n\t\t\t\t\t\"辽中县\",\n\t\t\t\t\t\"康平县\",\n\t\t\t\t\t\"新城子区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"大连市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"西岗区\",\n\t\t\t\t\t\"中山区\",\n\t\t\t\t\t\"沙河口区\",\n\t\t\t\t\t\"甘井子区\",\n\t\t\t\t\t\"旅顺口区\",\n\t\t\t\t\t\"金州区\",\n\t\t\t\t\t\"瓦房店市\",\n\t\t\t\t\t\"普兰店市\",\n\t\t\t\t\t\"庄河市\",\n\t\t\t\t\t\"长海县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"鞍山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"铁东区\",\n\t\t\t\t\t\"铁西区\",\n\t\t\t\t\t\"立山区\",\n\t\t\t\t\t\"千山区\",\n\t\t\t\t\t\"海城市\",\n\t\t\t\t\t\"台安县\",\n\t\t\t\t\t\"岫岩满族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"抚顺市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"顺城区\",\n\t\t\t\t\t\"新抚区\",\n\t\t\t\t\t\"东洲区\",\n\t\t\t\t\t\"望花区\",\n\t\t\t\t\t\"抚顺县\",\n\t\t\t\t\t\"清原满族自治县\",\n\t\t\t\t\t\"新宾满族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"本溪市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"平山区\",\n\t\t\t\t\t\"明山区\",\n\t\t\t\t\t\"溪湖区\",\n\t\t\t\t\t\"南芬区\",\n\t\t\t\t\t\"本溪满族自治县\",\n\t\t\t\t\t\"桓仁满族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"丹东市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"振兴区\",\n\t\t\t\t\t\"元宝区\",\n\t\t\t\t\t\"振安区\",\n\t\t\t\t\t\"东港市\",\n\t\t\t\t\t\"凤城市\",\n\t\t\t\t\t\"宽甸满族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"锦州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"太和区\",\n\t\t\t\t\t\"古塔区\",\n\t\t\t\t\t\"凌河区\",\n\t\t\t\t\t\"凌海市\",\n\t\t\t\t\t\"黑山县\",\n\t\t\t\t\t\"义县\",\n\t\t\t\t\t\"北宁市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"营口市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"站前区\",\n\t\t\t\t\t\"西市区\",\n\t\t\t\t\t\"鲅鱼圈区\",\n\t\t\t\t\t\"老边区\",\n\t\t\t\t\t\"大石桥市\",\n\t\t\t\t\t\"盖州市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阜新市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海州区\",\n\t\t\t\t\t\"新邱区\",\n\t\t\t\t\t\"太平区\",\n\t\t\t\t\t\"清河门区\",\n\t\t\t\t\t\"细河区\",\n\t\t\t\t\t\"彰武县\",\n\t\t\t\t\t\"阜新蒙古族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"辽阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"白塔区\",\n\t\t\t\t\t\"文圣区\",\n\t\t\t\t\t\"宏伟区\",\n\t\t\t\t\t\"太子河区\",\n\t\t\t\t\t\"弓长岭区\",\n\t\t\t\t\t\"灯塔市\",\n\t\t\t\t\t\"辽阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"盘锦\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"双台子区\",\n\t\t\t\t\t\"兴隆台区\",\n\t\t\t\t\t\"盘山县\",\n\t\t\t\t\t\"大洼县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"铁岭市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"银州区\",\n\t\t\t\t\t\"清河区\",\n\t\t\t\t\t\"调兵山市\",\n\t\t\t\t\t\"开原市\",\n\t\t\t\t\t\"铁岭县\",\n\t\t\t\t\t\"昌图县\",\n\t\t\t\t\t\"西丰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"朝阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"双塔区\",\n\t\t\t\t\t\"龙城区\",\n\t\t\t\t\t\"凌源市\",\n\t\t\t\t\t\"北票市\",\n\t\t\t\t\t\"朝阳县\",\n\t\t\t\t\t\"建平县\",\n\t\t\t\t\t\"喀喇沁左翼蒙古族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"葫芦岛市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"龙港区\",\n\t\t\t\t\t\"南票区\",\n\t\t\t\t\t\"连山区\",\n\t\t\t\t\t\"兴城市\",\n\t\t\t\t\t\"绥中县\",\n\t\t\t\t\t\"建昌县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"吉林省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"长春市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"朝阳区\",\n\t\t\t\t\t\"宽城区\",\n\t\t\t\t\t\"二道区\",\n\t\t\t\t\t\"南关区\",\n\t\t\t\t\t\"绿园区\",\n\t\t\t\t\t\"双阳区\",\n\t\t\t\t\t\"九台市\",\n\t\t\t\t\t\"榆树市\",\n\t\t\t\t\t\"德惠市\",\n\t\t\t\t\t\"农安县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"吉林市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"船营区\",\n\t\t\t\t\t\"昌邑区\",\n\t\t\t\t\t\"龙潭区\",\n\t\t\t\t\t\"丰满区\",\n\t\t\t\t\t\"舒兰市\",\n\t\t\t\t\t\"桦甸市\",\n\t\t\t\t\t\"蛟河市\",\n\t\t\t\t\t\"磐石市\",\n\t\t\t\t\t\"永吉县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"四平\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"铁西区\",\n\t\t\t\t\t\"铁东区\",\n\t\t\t\t\t\"公主岭市\",\n\t\t\t\t\t\"双辽市\",\n\t\t\t\t\t\"梨树县\",\n\t\t\t\t\t\"伊通满族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"辽源市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"龙山区\",\n\t\t\t\t\t\"西安区\",\n\t\t\t\t\t\"东辽县\",\n\t\t\t\t\t\"东丰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"通化市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东昌区\",\n\t\t\t\t\t\"二道江区\",\n\t\t\t\t\t\"梅河口市\",\n\t\t\t\t\t\"集安市\",\n\t\t\t\t\t\"通化县\",\n\t\t\t\t\t\"辉南县\",\n\t\t\t\t\t\"柳河县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"白山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"八道江区\",\n\t\t\t\t\t\"江源区\",\n\t\t\t\t\t\"临江市\",\n\t\t\t\t\t\"靖宇县\",\n\t\t\t\t\t\"抚松县\",\n\t\t\t\t\t\"长白朝鲜族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"松原市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"宁江区\",\n\t\t\t\t\t\"乾安县\",\n\t\t\t\t\t\"长岭县\",\n\t\t\t\t\t\"扶余县\",\n\t\t\t\t\t\"前郭尔罗斯蒙古族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"白城市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"洮北区\",\n\t\t\t\t\t\"大安市\",\n\t\t\t\t\t\"洮南市\",\n\t\t\t\t\t\"镇赉县\",\n\t\t\t\t\t\"通榆县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"延边朝鲜族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"延吉市\",\n\t\t\t\t\t\"图们市\",\n\t\t\t\t\t\"敦化市\",\n\t\t\t\t\t\"龙井市\",\n\t\t\t\t\t\"珲春市\",\n\t\t\t\t\t\"和龙市\",\n\t\t\t\t\t\"安图县\",\n\t\t\t\t\t\"汪清县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"黑龙江省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"哈尔滨市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"松北区\",\n\t\t\t\t\t\"道里区\",\n\t\t\t\t\t\"南岗区\",\n\t\t\t\t\t\"平房区\",\n\t\t\t\t\t\"香坊区\",\n\t\t\t\t\t\"道外区\",\n\t\t\t\t\t\"呼兰区\",\n\t\t\t\t\t\"阿城区\",\n\t\t\t\t\t\"双城市\",\n\t\t\t\t\t\"尚志市\",\n\t\t\t\t\t\"五常市\",\n\t\t\t\t\t\"宾县\",\n\t\t\t\t\t\"方正县\",\n\t\t\t\t\t\"通河县\",\n\t\t\t\t\t\"巴彦县\",\n\t\t\t\t\t\"延寿县\",\n\t\t\t\t\t\"木兰县\",\n\t\t\t\t\t\"依兰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"齐齐哈尔市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"龙沙区\",\n\t\t\t\t\t\"昂昂溪区\",\n\t\t\t\t\t\"铁锋区\",\n\t\t\t\t\t\"建华区\",\n\t\t\t\t\t\"富拉尔基区\",\n\t\t\t\t\t\"碾子山区\",\n\t\t\t\t\t\"梅里斯达斡尔族区\",\n\t\t\t\t\t\"讷河市\",\n\t\t\t\t\t\"富裕县\",\n\t\t\t\t\t\"拜泉县\",\n\t\t\t\t\t\"甘南县\",\n\t\t\t\t\t\"依安县\",\n\t\t\t\t\t\"克山县\",\n\t\t\t\t\t\"泰来县\",\n\t\t\t\t\t\"克东县\",\n\t\t\t\t\t\"龙江县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"鹤岗市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"兴山区\",\n\t\t\t\t\t\"工农区\",\n\t\t\t\t\t\"南山区\",\n\t\t\t\t\t\"兴安区\",\n\t\t\t\t\t\"向阳区\",\n\t\t\t\t\t\"东山区\",\n\t\t\t\t\t\"萝北县\",\n\t\t\t\t\t\"绥滨县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"双鸭山\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"尖山区\",\n\t\t\t\t\t\"岭东区\",\n\t\t\t\t\t\"四方台区\",\n\t\t\t\t\t\"宝山区\",\n\t\t\t\t\t\"集贤县\",\n\t\t\t\t\t\"宝清县\",\n\t\t\t\t\t\"友谊县\",\n\t\t\t\t\t\"饶河县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"鸡西市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"鸡冠区\",\n\t\t\t\t\t\"恒山区\",\n\t\t\t\t\t\"城子河区\",\n\t\t\t\t\t\"滴道区\",\n\t\t\t\t\t\"梨树区\",\n\t\t\t\t\t\"麻山区\",\n\t\t\t\t\t\"密山市\",\n\t\t\t\t\t\"虎林市\",\n\t\t\t\t\t\"鸡东县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"大庆市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"萨尔图区\",\n\t\t\t\t\t\"红岗区\",\n\t\t\t\t\t\"龙凤区\",\n\t\t\t\t\t\"让胡路区\",\n\t\t\t\t\t\"大同区\",\n\t\t\t\t\t\"林甸县\",\n\t\t\t\t\t\"肇州县\",\n\t\t\t\t\t\"肇源县\",\n\t\t\t\t\t\"杜尔伯特蒙古族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"伊春市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"伊春区\",\n\t\t\t\t\t\"带岭区\",\n\t\t\t\t\t\"南岔区\",\n\t\t\t\t\t\"金山屯区\",\n\t\t\t\t\t\"西林区\",\n\t\t\t\t\t\"美溪区\",\n\t\t\t\t\t\"乌马河区\",\n\t\t\t\t\t\"翠峦区\",\n\t\t\t\t\t\"友好区\",\n\t\t\t\t\t\"上甘岭区\",\n\t\t\t\t\t\"五营区\",\n\t\t\t\t\t\"红星区\",\n\t\t\t\t\t\"新青区\",\n\t\t\t\t\t\"汤旺河区\",\n\t\t\t\t\t\"乌伊岭区\",\n\t\t\t\t\t\"铁力市\",\n\t\t\t\t\t\"嘉荫县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"牡丹江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"爱民区\",\n\t\t\t\t\t\"东安区\",\n\t\t\t\t\t\"阳明区\",\n\t\t\t\t\t\"西安区\",\n\t\t\t\t\t\"绥芬河市\",\n\t\t\t\t\t\"宁安市\",\n\t\t\t\t\t\"海林市\",\n\t\t\t\t\t\"穆棱市\",\n\t\t\t\t\t\"林口县\",\n\t\t\t\t\t\"东宁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"佳木斯市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"向阳区\",\n\t\t\t\t\t\"前进区\",\n\t\t\t\t\t\"东风区\",\n\t\t\t\t\t\"郊区\",\n\t\t\t\t\t\"同江市\",\n\t\t\t\t\t\"富锦市\",\n\t\t\t\t\t\"桦川县\",\n\t\t\t\t\t\"抚远县\",\n\t\t\t\t\t\"桦南县\",\n\t\t\t\t\t\"汤原县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"七台河市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"桃山区\",\n\t\t\t\t\t\"新兴区\",\n\t\t\t\t\t\"茄子河区\",\n\t\t\t\t\t\"勃利县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黑河市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"爱辉区\",\n\t\t\t\t\t\"北安市\",\n\t\t\t\t\t\"五大连池市\",\n\t\t\t\t\t\"逊克县\",\n\t\t\t\t\t\"嫩江县\",\n\t\t\t\t\t\"孙吴县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"绥化市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"北林区\",\n\t\t\t\t\t\"安达市\",\n\t\t\t\t\t\"肇东市\",\n\t\t\t\t\t\"海伦市\",\n\t\t\t\t\t\"绥棱县\",\n\t\t\t\t\t\"兰西县\",\n\t\t\t\t\t\"明水县\",\n\t\t\t\t\t\"青冈县\",\n\t\t\t\t\t\"庆安县\",\n\t\t\t\t\t\"望奎县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"大兴安岭地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"呼玛县\",\n\t\t\t\t\t\"塔河县\",\n\t\t\t\t\t\"漠河县\",\n\t\t\t\t\t\"大兴安岭辖区\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"上海市\",\n\t\t\"city\": [{\n\t\t\t\"name\": \"上海市\",\n\t\t\t\"area\": [\n\t\t\t\t\"黄浦区\",\n\t\t\t\t\"卢湾区\",\n\t\t\t\t\"徐汇区\",\n\t\t\t\t\"长宁区\",\n\t\t\t\t\"静安区\",\n\t\t\t\t\"普陀区\",\n\t\t\t\t\"闸北区\",\n\t\t\t\t\"虹口区\",\n\t\t\t\t\"杨浦区\",\n\t\t\t\t\"宝山区\",\n\t\t\t\t\"闵行区\",\n\t\t\t\t\"嘉定区\",\n\t\t\t\t\"松江区\",\n\t\t\t\t\"金山区\",\n\t\t\t\t\"青浦区\",\n\t\t\t\t\"南汇区\",\n\t\t\t\t\"奉贤区\",\n\t\t\t\t\"浦东新区\",\n\t\t\t\t\"崇明县\"\n\t\t\t]\n\t\t}]\n\t},\n\t{\n\t\t\"name\": \"江苏省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"南京市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"玄武区\",\n\t\t\t\t\t\"白下区\",\n\t\t\t\t\t\"秦淮区\",\n\t\t\t\t\t\"建邺区\",\n\t\t\t\t\t\"鼓楼区\",\n\t\t\t\t\t\"下关区\",\n\t\t\t\t\t\"栖霞区\",\n\t\t\t\t\t\"雨花台区\",\n\t\t\t\t\t\"浦口区\",\n\t\t\t\t\t\"江宁区\",\n\t\t\t\t\t\"六合区\",\n\t\t\t\t\t\"溧水县\",\n\t\t\t\t\t\"高淳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"苏州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"金阊区\",\n\t\t\t\t\t\"平江区\",\n\t\t\t\t\t\"沧浪区\",\n\t\t\t\t\t\"虎丘区\",\n\t\t\t\t\t\"吴中区\",\n\t\t\t\t\t\"相城区\",\n\t\t\t\t\t\"常熟市\",\n\t\t\t\t\t\"张家港市\",\n\t\t\t\t\t\"昆山市\",\n\t\t\t\t\t\"吴江市\",\n\t\t\t\t\t\"太仓市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"无锡市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"崇安区\",\n\t\t\t\t\t\"南长区\",\n\t\t\t\t\t\"北塘区\",\n\t\t\t\t\t\"滨湖区\",\n\t\t\t\t\t\"锡山区\",\n\t\t\t\t\t\"惠山区\",\n\t\t\t\t\t\"江阴市\",\n\t\t\t\t\t\"宜兴市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"常州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"钟楼区\",\n\t\t\t\t\t\"天宁区\",\n\t\t\t\t\t\"戚墅堰区\",\n\t\t\t\t\t\"新北区\",\n\t\t\t\t\t\"武进区\",\n\t\t\t\t\t\"金坛市\",\n\t\t\t\t\t\"溧阳市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"镇江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"京口区\",\n\t\t\t\t\t\"润州区\",\n\t\t\t\t\t\"丹徒区\",\n\t\t\t\t\t\"丹阳市\",\n\t\t\t\t\t\"扬中市\",\n\t\t\t\t\t\"句容市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"南通市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"崇川区\",\n\t\t\t\t\t\"港闸区\",\n\t\t\t\t\t\"通州市\",\n\t\t\t\t\t\"如皋市\",\n\t\t\t\t\t\"海门市\",\n\t\t\t\t\t\"启东市\",\n\t\t\t\t\t\"海安县\",\n\t\t\t\t\t\"如东县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"泰州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海陵区\",\n\t\t\t\t\t\"高港区\",\n\t\t\t\t\t\"姜堰市\",\n\t\t\t\t\t\"泰兴市\",\n\t\t\t\t\t\"靖江市\",\n\t\t\t\t\t\"兴化市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"扬州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"广陵区\",\n\t\t\t\t\t\"维扬区\",\n\t\t\t\t\t\"邗江区\",\n\t\t\t\t\t\"江都市\",\n\t\t\t\t\t\"仪征市\",\n\t\t\t\t\t\"高邮市\",\n\t\t\t\t\t\"宝应县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"盐城市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"亭湖区\",\n\t\t\t\t\t\"盐都区\",\n\t\t\t\t\t\"大丰市\",\n\t\t\t\t\t\"东台市\",\n\t\t\t\t\t\"建湖县\",\n\t\t\t\t\t\"射阳县\",\n\t\t\t\t\t\"阜宁县\",\n\t\t\t\t\t\"滨海县\",\n\t\t\t\t\t\"响水县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"连云港市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"新浦区\",\n\t\t\t\t\t\"海州区\",\n\t\t\t\t\t\"连云区\",\n\t\t\t\t\t\"东海县\",\n\t\t\t\t\t\"灌云县\",\n\t\t\t\t\t\"赣榆县\",\n\t\t\t\t\t\"灌南县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"徐州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"云龙区\",\n\t\t\t\t\t\"鼓楼区\",\n\t\t\t\t\t\"九里区\",\n\t\t\t\t\t\"泉山区\",\n\t\t\t\t\t\"贾汪区\",\n\t\t\t\t\t\"邳州市\",\n\t\t\t\t\t\"新沂市\",\n\t\t\t\t\t\"铜山县\",\n\t\t\t\t\t\"睢宁县\",\n\t\t\t\t\t\"沛县\",\n\t\t\t\t\t\"丰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"淮安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"清河区\",\n\t\t\t\t\t\"清浦区\",\n\t\t\t\t\t\"楚州区\",\n\t\t\t\t\t\"淮阴区\",\n\t\t\t\t\t\"涟水县\",\n\t\t\t\t\t\"洪泽县\",\n\t\t\t\t\t\"金湖县\",\n\t\t\t\t\t\"盱眙县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宿迁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"宿城区\",\n\t\t\t\t\t\"宿豫区\",\n\t\t\t\t\t\"沭阳县\",\n\t\t\t\t\t\"泗阳县\",\n\t\t\t\t\t\"泗洪县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"浙江省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"杭州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"拱墅区\",\n\t\t\t\t\t\"西湖区\",\n\t\t\t\t\t\"上城区\",\n\t\t\t\t\t\"下城区\",\n\t\t\t\t\t\"江干区\",\n\t\t\t\t\t\"滨江区\",\n\t\t\t\t\t\"余杭区\",\n\t\t\t\t\t\"萧山区\",\n\t\t\t\t\t\"建德市\",\n\t\t\t\t\t\"富阳市\",\n\t\t\t\t\t\"临安市\",\n\t\t\t\t\t\"桐庐县\",\n\t\t\t\t\t\"淳安县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宁波市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海曙区\",\n\t\t\t\t\t\"江东区\",\n\t\t\t\t\t\"江北区\",\n\t\t\t\t\t\"镇海区\",\n\t\t\t\t\t\"北仑区\",\n\t\t\t\t\t\"鄞州区\",\n\t\t\t\t\t\"余姚市\",\n\t\t\t\t\t\"慈溪市\",\n\t\t\t\t\t\"奉化市\",\n\t\t\t\t\t\"宁海县\",\n\t\t\t\t\t\"象山县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"温州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"鹿城区\",\n\t\t\t\t\t\"龙湾区\",\n\t\t\t\t\t\"瓯海区\",\n\t\t\t\t\t\"瑞安市\",\n\t\t\t\t\t\"乐清市\",\n\t\t\t\t\t\"永嘉县\",\n\t\t\t\t\t\"洞头县\",\n\t\t\t\t\t\"平阳县\",\n\t\t\t\t\t\"苍南县\",\n\t\t\t\t\t\"文成县\",\n\t\t\t\t\t\"泰顺县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"嘉兴市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"秀城区\",\n\t\t\t\t\t\"秀洲区\",\n\t\t\t\t\t\"海宁市\",\n\t\t\t\t\t\"平湖市\",\n\t\t\t\t\t\"桐乡市\",\n\t\t\t\t\t\"嘉善县\",\n\t\t\t\t\t\"海盐县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"湖州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"吴兴区\",\n\t\t\t\t\t\"南浔区\",\n\t\t\t\t\t\"长兴县\",\n\t\t\t\t\t\"德清县\",\n\t\t\t\t\t\"安吉县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"绍兴市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"越城区\",\n\t\t\t\t\t\"诸暨市\",\n\t\t\t\t\t\"上虞市\",\n\t\t\t\t\t\"嵊州市\",\n\t\t\t\t\t\"绍兴县\",\n\t\t\t\t\t\"新昌县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"金华市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"婺城区\",\n\t\t\t\t\t\"金东区\",\n\t\t\t\t\t\"兰溪市\",\n\t\t\t\t\t\"义乌市\",\n\t\t\t\t\t\"东阳市\",\n\t\t\t\t\t\"永康市\",\n\t\t\t\t\t\"武义县\",\n\t\t\t\t\t\"浦江县\",\n\t\t\t\t\t\"磐安县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"衢州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"柯城区\",\n\t\t\t\t\t\"衢江区\",\n\t\t\t\t\t\"江山市\",\n\t\t\t\t\t\"龙游县\",\n\t\t\t\t\t\"常山县\",\n\t\t\t\t\t\"开化县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"舟山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"定海区\",\n\t\t\t\t\t\"普陀区\",\n\t\t\t\t\t\"岱山县\",\n\t\t\t\t\t\"嵊泗县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"台州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"椒江区\",\n\t\t\t\t\t\"黄岩区\",\n\t\t\t\t\t\"路桥区\",\n\t\t\t\t\t\"临海市\",\n\t\t\t\t\t\"温岭市\",\n\t\t\t\t\t\"玉环县\",\n\t\t\t\t\t\"天台县\",\n\t\t\t\t\t\"仙居县\",\n\t\t\t\t\t\"三门县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"丽水市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"莲都区\",\n\t\t\t\t\t\"龙泉市\",\n\t\t\t\t\t\"缙云县\",\n\t\t\t\t\t\"青田县\",\n\t\t\t\t\t\"云和县\",\n\t\t\t\t\t\"遂昌县\",\n\t\t\t\t\t\"松阳县\",\n\t\t\t\t\t\"庆元县\",\n\t\t\t\t\t\"景宁畲族自治县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"安徽省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"合肥市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"庐阳区\",\n\t\t\t\t\t\"瑶海区\",\n\t\t\t\t\t\"蜀山区\",\n\t\t\t\t\t\"包河区\",\n\t\t\t\t\t\"长丰县\",\n\t\t\t\t\t\"肥东县\",\n\t\t\t\t\t\"肥西县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"芜湖市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"镜湖区\",\n\t\t\t\t\t\"弋江区\",\n\t\t\t\t\t\"鸠江区\",\n\t\t\t\t\t\"三山区\",\n\t\t\t\t\t\"芜湖县\",\n\t\t\t\t\t\"南陵县\",\n\t\t\t\t\t\"繁昌县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"蚌埠市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"蚌山区\",\n\t\t\t\t\t\"龙子湖区\",\n\t\t\t\t\t\"禹会区\",\n\t\t\t\t\t\"淮上区\",\n\t\t\t\t\t\"怀远县\",\n\t\t\t\t\t\"固镇县\",\n\t\t\t\t\t\"五河县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"淮南市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"田家庵区\",\n\t\t\t\t\t\"大通区\",\n\t\t\t\t\t\"谢家集区\",\n\t\t\t\t\t\"八公山区\",\n\t\t\t\t\t\"潘集区\",\n\t\t\t\t\t\"凤台县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"马鞍山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"雨山区\",\n\t\t\t\t\t\"花山区\",\n\t\t\t\t\t\"金家庄区\",\n\t\t\t\t\t\"当涂县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"淮北市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"相山区\",\n\t\t\t\t\t\"杜集区\",\n\t\t\t\t\t\"烈山区\",\n\t\t\t\t\t\"濉溪县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"铜陵市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"铜官山区\",\n\t\t\t\t\t\"狮子山区\",\n\t\t\t\t\t\"郊区\",\n\t\t\t\t\t\"铜陵县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"安庆市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"迎江区\",\n\t\t\t\t\t\"大观区\",\n\t\t\t\t\t\"宜秀区\",\n\t\t\t\t\t\"桐城市\",\n\t\t\t\t\t\"宿松县\",\n\t\t\t\t\t\"枞阳县\",\n\t\t\t\t\t\"太湖县\",\n\t\t\t\t\t\"怀宁县\",\n\t\t\t\t\t\"岳西县\",\n\t\t\t\t\t\"望江县\",\n\t\t\t\t\t\"潜山县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黄山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"屯溪区\",\n\t\t\t\t\t\"黄山区\",\n\t\t\t\t\t\"徽州区\",\n\t\t\t\t\t\"休宁县\",\n\t\t\t\t\t\"歙县\",\n\t\t\t\t\t\"祁门县\",\n\t\t\t\t\t\"黟县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"滁州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"琅琊区\",\n\t\t\t\t\t\"南谯区\",\n\t\t\t\t\t\"天长市\",\n\t\t\t\t\t\"明光市\",\n\t\t\t\t\t\"全椒县\",\n\t\t\t\t\t\"来安县\",\n\t\t\t\t\t\"定远县\",\n\t\t\t\t\t\"凤阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阜阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"颍州区\",\n\t\t\t\t\t\"颍东区\",\n\t\t\t\t\t\"颍泉区\",\n\t\t\t\t\t\"界首市\",\n\t\t\t\t\t\"临泉县\",\n\t\t\t\t\t\"颍上县\",\n\t\t\t\t\t\"阜南县\",\n\t\t\t\t\t\"太和县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宿州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"埇桥区\",\n\t\t\t\t\t\"萧县\",\n\t\t\t\t\t\"泗县\",\n\t\t\t\t\t\"砀山县\",\n\t\t\t\t\t\"灵璧县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"巢湖市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"居巢区\",\n\t\t\t\t\t\"含山县\",\n\t\t\t\t\t\"无为县\",\n\t\t\t\t\t\"庐江县\",\n\t\t\t\t\t\"和县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"六安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"金安区\",\n\t\t\t\t\t\"裕安区\",\n\t\t\t\t\t\"寿县\",\n\t\t\t\t\t\"霍山县\",\n\t\t\t\t\t\"霍邱县\",\n\t\t\t\t\t\"舒城县\",\n\t\t\t\t\t\"金寨县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"亳州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"谯城区\",\n\t\t\t\t\t\"利辛县\",\n\t\t\t\t\t\"涡阳县\",\n\t\t\t\t\t\"蒙城县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"池州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"贵池区\",\n\t\t\t\t\t\"东至县\",\n\t\t\t\t\t\"石台县\",\n\t\t\t\t\t\"青阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宣城市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"宣州区\",\n\t\t\t\t\t\"宁国市\",\n\t\t\t\t\t\"广德县\",\n\t\t\t\t\t\"郎溪县\",\n\t\t\t\t\t\"泾县\",\n\t\t\t\t\t\"旌德县\",\n\t\t\t\t\t\"绩溪县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"福建省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"福州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"鼓楼区\",\n\t\t\t\t\t\"台江区\",\n\t\t\t\t\t\"仓山区\",\n\t\t\t\t\t\"马尾区\",\n\t\t\t\t\t\"晋安区\",\n\t\t\t\t\t\"福清市\",\n\t\t\t\t\t\"长乐市\",\n\t\t\t\t\t\"闽侯县\",\n\t\t\t\t\t\"闽清县\",\n\t\t\t\t\t\"永泰县\",\n\t\t\t\t\t\"连江县\",\n\t\t\t\t\t\"罗源县\",\n\t\t\t\t\t\"平潭县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"厦门市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"思明区\",\n\t\t\t\t\t\"海沧区\",\n\t\t\t\t\t\"湖里区\",\n\t\t\t\t\t\"集美区\",\n\t\t\t\t\t\"同安区\",\n\t\t\t\t\t\"翔安区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"莆田市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城厢区\",\n\t\t\t\t\t\"涵江区\",\n\t\t\t\t\t\"荔城区\",\n\t\t\t\t\t\"秀屿区\",\n\t\t\t\t\t\"仙游县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"三明市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"梅列区\",\n\t\t\t\t\t\"三元区\",\n\t\t\t\t\t\"永安市\",\n\t\t\t\t\t\"明溪县\",\n\t\t\t\t\t\"将乐县\",\n\t\t\t\t\t\"大田县\",\n\t\t\t\t\t\"宁化县\",\n\t\t\t\t\t\"建宁县\",\n\t\t\t\t\t\"沙县\",\n\t\t\t\t\t\"尤溪县\",\n\t\t\t\t\t\"清流县\",\n\t\t\t\t\t\"泰宁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"泉州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"鲤城区\",\n\t\t\t\t\t\"丰泽区\",\n\t\t\t\t\t\"洛江区\",\n\t\t\t\t\t\"泉港区\",\n\t\t\t\t\t\"石狮市\",\n\t\t\t\t\t\"晋江市\",\n\t\t\t\t\t\"南安市\",\n\t\t\t\t\t\"惠安县\",\n\t\t\t\t\t\"永春县\",\n\t\t\t\t\t\"安溪县\",\n\t\t\t\t\t\"德化县\",\n\t\t\t\t\t\"金门县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"漳州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"芗城区\",\n\t\t\t\t\t\"龙文区\",\n\t\t\t\t\t\"龙海市\",\n\t\t\t\t\t\"平和县\",\n\t\t\t\t\t\"南靖县\",\n\t\t\t\t\t\"诏安县\",\n\t\t\t\t\t\"漳浦县\",\n\t\t\t\t\t\"华安县\",\n\t\t\t\t\t\"东山县\",\n\t\t\t\t\t\"长泰县\",\n\t\t\t\t\t\"云霄县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"南平市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"延平区\",\n\t\t\t\t\t\"建瓯市\",\n\t\t\t\t\t\"邵武市\",\n\t\t\t\t\t\"武夷山市\",\n\t\t\t\t\t\"建阳市\",\n\t\t\t\t\t\"松溪县\",\n\t\t\t\t\t\"光泽县\",\n\t\t\t\t\t\"顺昌县\",\n\t\t\t\t\t\"浦城县\",\n\t\t\t\t\t\"政和县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"龙岩市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"新罗区\",\n\t\t\t\t\t\"漳平市\",\n\t\t\t\t\t\"长汀县\",\n\t\t\t\t\t\"武平县\",\n\t\t\t\t\t\"上杭县\",\n\t\t\t\t\t\"永定县\",\n\t\t\t\t\t\"连城县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宁德市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"蕉城区\",\n\t\t\t\t\t\"福安市\",\n\t\t\t\t\t\"福鼎市\",\n\t\t\t\t\t\"寿宁县\",\n\t\t\t\t\t\"霞浦县\",\n\t\t\t\t\t\"柘荣县\",\n\t\t\t\t\t\"屏南县\",\n\t\t\t\t\t\"古田县\",\n\t\t\t\t\t\"周宁县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"江西省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"南昌市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东湖区\",\n\t\t\t\t\t\"西湖区\",\n\t\t\t\t\t\"青云谱区\",\n\t\t\t\t\t\"湾里区\",\n\t\t\t\t\t\"青山湖区\",\n\t\t\t\t\t\"新建县\",\n\t\t\t\t\t\"南昌县\",\n\t\t\t\t\t\"进贤县\",\n\t\t\t\t\t\"安义县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"景德镇市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"珠山区\",\n\t\t\t\t\t\"昌江区\",\n\t\t\t\t\t\"乐平市\",\n\t\t\t\t\t\"浮梁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"萍乡市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"安源区\",\n\t\t\t\t\t\"湘东区\",\n\t\t\t\t\t\"莲花县\",\n\t\t\t\t\t\"上栗县\",\n\t\t\t\t\t\"芦溪县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"九江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"浔阳区\",\n\t\t\t\t\t\"庐山区\",\n\t\t\t\t\t\"瑞昌市\",\n\t\t\t\t\t\"九江县\",\n\t\t\t\t\t\"星子县\",\n\t\t\t\t\t\"武宁县\",\n\t\t\t\t\t\"彭泽县\",\n\t\t\t\t\t\"永修县\",\n\t\t\t\t\t\"修水县\",\n\t\t\t\t\t\"湖口县\",\n\t\t\t\t\t\"德安县\",\n\t\t\t\t\t\"都昌县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"新余市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"渝水区\",\n\t\t\t\t\t\"分宜县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"鹰潭市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"月湖区\",\n\t\t\t\t\t\"贵溪市\",\n\t\t\t\t\t\"余江县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"赣州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"章贡区\",\n\t\t\t\t\t\"瑞金市\",\n\t\t\t\t\t\"南康市\",\n\t\t\t\t\t\"石城县\",\n\t\t\t\t\t\"安远县\",\n\t\t\t\t\t\"赣县\",\n\t\t\t\t\t\"宁都县\",\n\t\t\t\t\t\"寻乌县\",\n\t\t\t\t\t\"兴国县\",\n\t\t\t\t\t\"定南县\",\n\t\t\t\t\t\"上犹县\",\n\t\t\t\t\t\"于都县\",\n\t\t\t\t\t\"龙南县\",\n\t\t\t\t\t\"崇义县\",\n\t\t\t\t\t\"信丰县\",\n\t\t\t\t\t\"全南县\",\n\t\t\t\t\t\"大余县\",\n\t\t\t\t\t\"会昌县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"吉安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"吉州区\",\n\t\t\t\t\t\"青原区\",\n\t\t\t\t\t\"井冈山市\",\n\t\t\t\t\t\"吉安县\",\n\t\t\t\t\t\"永丰县\",\n\t\t\t\t\t\"永新县\",\n\t\t\t\t\t\"新干县\",\n\t\t\t\t\t\"泰和县\",\n\t\t\t\t\t\"峡江县\",\n\t\t\t\t\t\"遂川县\",\n\t\t\t\t\t\"安福县\",\n\t\t\t\t\t\"吉水县\",\n\t\t\t\t\t\"万安县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宜春市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"袁州区\",\n\t\t\t\t\t\"丰城市\",\n\t\t\t\t\t\"樟树市\",\n\t\t\t\t\t\"高安市\",\n\t\t\t\t\t\"铜鼓县\",\n\t\t\t\t\t\"靖安县\",\n\t\t\t\t\t\"宜丰县\",\n\t\t\t\t\t\"奉新县\",\n\t\t\t\t\t\"万载县\",\n\t\t\t\t\t\"上高县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"抚州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"临川区\",\n\t\t\t\t\t\"南丰县\",\n\t\t\t\t\t\"乐安县\",\n\t\t\t\t\t\"金溪县\",\n\t\t\t\t\t\"南城县\",\n\t\t\t\t\t\"东乡县\",\n\t\t\t\t\t\"资溪县\",\n\t\t\t\t\t\"宜黄县\",\n\t\t\t\t\t\"广昌县\",\n\t\t\t\t\t\"黎川县\",\n\t\t\t\t\t\"崇仁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"上饶市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"信州区\",\n\t\t\t\t\t\"德兴市\",\n\t\t\t\t\t\"上饶县\",\n\t\t\t\t\t\"广丰县\",\n\t\t\t\t\t\"鄱阳县\",\n\t\t\t\t\t\"婺源县\",\n\t\t\t\t\t\"铅山县\",\n\t\t\t\t\t\"余干县\",\n\t\t\t\t\t\"横峰县\",\n\t\t\t\t\t\"弋阳县\",\n\t\t\t\t\t\"玉山县\",\n\t\t\t\t\t\"万年县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"山东省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"济南市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市中区\",\n\t\t\t\t\t\"历下区\",\n\t\t\t\t\t\"天桥区\",\n\t\t\t\t\t\"槐荫区\",\n\t\t\t\t\t\"历城区\",\n\t\t\t\t\t\"长清区\",\n\t\t\t\t\t\"章丘市\",\n\t\t\t\t\t\"平阴县\",\n\t\t\t\t\t\"济阳县\",\n\t\t\t\t\t\"商河县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"青岛市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市南区\",\n\t\t\t\t\t\"市北区\",\n\t\t\t\t\t\"城阳区\",\n\t\t\t\t\t\"四方区\",\n\t\t\t\t\t\"李沧区\",\n\t\t\t\t\t\"黄岛区\",\n\t\t\t\t\t\"崂山区\",\n\t\t\t\t\t\"胶南市\",\n\t\t\t\t\t\"胶州市\",\n\t\t\t\t\t\"平度市\",\n\t\t\t\t\t\"莱西市\",\n\t\t\t\t\t\"即墨市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"淄博市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"张店区\",\n\t\t\t\t\t\"临淄区\",\n\t\t\t\t\t\"淄川区\",\n\t\t\t\t\t\"博山区\",\n\t\t\t\t\t\"周村区\",\n\t\t\t\t\t\"桓台县\",\n\t\t\t\t\t\"高青县\",\n\t\t\t\t\t\"沂源县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"枣庄市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市中区\",\n\t\t\t\t\t\"山亭区\",\n\t\t\t\t\t\"峄城区\",\n\t\t\t\t\t\"台儿庄区\",\n\t\t\t\t\t\"薛城区\",\n\t\t\t\t\t\"滕州市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"东营市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东营区\",\n\t\t\t\t\t\"河口区\",\n\t\t\t\t\t\"垦利县\",\n\t\t\t\t\t\"广饶县\",\n\t\t\t\t\t\"利津县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"烟台市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"芝罘区\",\n\t\t\t\t\t\"福山区\",\n\t\t\t\t\t\"牟平区\",\n\t\t\t\t\t\"莱山区\",\n\t\t\t\t\t\"龙口市\",\n\t\t\t\t\t\"莱阳市\",\n\t\t\t\t\t\"莱州市\",\n\t\t\t\t\t\"招远市\",\n\t\t\t\t\t\"蓬莱市\",\n\t\t\t\t\t\"栖霞市\",\n\t\t\t\t\t\"海阳市\",\n\t\t\t\t\t\"长岛县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"潍坊市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"潍城区\",\n\t\t\t\t\t\"寒亭区\",\n\t\t\t\t\t\"坊子区\",\n\t\t\t\t\t\"奎文区\",\n\t\t\t\t\t\"青州市\",\n\t\t\t\t\t\"诸城市\",\n\t\t\t\t\t\"寿光市\",\n\t\t\t\t\t\"安丘市\",\n\t\t\t\t\t\"高密市\",\n\t\t\t\t\t\"昌邑市\",\n\t\t\t\t\t\"昌乐县\",\n\t\t\t\t\t\"临朐县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"济宁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市中区\",\n\t\t\t\t\t\"任城区\",\n\t\t\t\t\t\"曲阜市\",\n\t\t\t\t\t\"兖州市\",\n\t\t\t\t\t\"邹城市\",\n\t\t\t\t\t\"鱼台县\",\n\t\t\t\t\t\"金乡县\",\n\t\t\t\t\t\"嘉祥县\",\n\t\t\t\t\t\"微山县\",\n\t\t\t\t\t\"汶上县\",\n\t\t\t\t\t\"泗水县\",\n\t\t\t\t\t\"梁山县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"泰安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"泰山区\",\n\t\t\t\t\t\"岱岳区\",\n\t\t\t\t\t\"新泰市\",\n\t\t\t\t\t\"肥城市\",\n\t\t\t\t\t\"宁阳县\",\n\t\t\t\t\t\"东平县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"威海市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"环翠区\",\n\t\t\t\t\t\"乳山市\",\n\t\t\t\t\t\"文登市\",\n\t\t\t\t\t\"荣成市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"日照市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东港区\",\n\t\t\t\t\t\"岚山区\",\n\t\t\t\t\t\"五莲县\",\n\t\t\t\t\t\"莒县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"莱芜市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"莱城区\",\n\t\t\t\t\t\"钢城区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"临沂市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"兰山区\",\n\t\t\t\t\t\"罗庄区\",\n\t\t\t\t\t\"河东区\",\n\t\t\t\t\t\"沂南县\",\n\t\t\t\t\t\"郯城县\",\n\t\t\t\t\t\"沂水县\",\n\t\t\t\t\t\"苍山县\",\n\t\t\t\t\t\"费县\",\n\t\t\t\t\t\"平邑县\",\n\t\t\t\t\t\"莒南县\",\n\t\t\t\t\t\"蒙阴县\",\n\t\t\t\t\t\"临沭县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"德州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"德城区\",\n\t\t\t\t\t\"乐陵市\",\n\t\t\t\t\t\"禹城市\",\n\t\t\t\t\t\"陵县\",\n\t\t\t\t\t\"宁津县\",\n\t\t\t\t\t\"齐河县\",\n\t\t\t\t\t\"武城县\",\n\t\t\t\t\t\"庆云县\",\n\t\t\t\t\t\"平原县\",\n\t\t\t\t\t\"夏津县\",\n\t\t\t\t\t\"临邑县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"聊城市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东昌府区\",\n\t\t\t\t\t\"临清市\",\n\t\t\t\t\t\"高唐县\",\n\t\t\t\t\t\"阳谷县\",\n\t\t\t\t\t\"茌平县\",\n\t\t\t\t\t\"莘县\",\n\t\t\t\t\t\"东阿县\",\n\t\t\t\t\t\"冠县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"滨州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"滨城区\",\n\t\t\t\t\t\"邹平县\",\n\t\t\t\t\t\"沾化县\",\n\t\t\t\t\t\"惠民县\",\n\t\t\t\t\t\"博兴县\",\n\t\t\t\t\t\"阳信县\",\n\t\t\t\t\t\"无棣县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"菏泽市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"牡丹区\",\n\t\t\t\t\t\"鄄城县\",\n\t\t\t\t\t\"单县\",\n\t\t\t\t\t\"郓城县\",\n\t\t\t\t\t\"曹县\",\n\t\t\t\t\t\"定陶县\",\n\t\t\t\t\t\"巨野县\",\n\t\t\t\t\t\"东明县\",\n\t\t\t\t\t\"成武县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"河南省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"郑州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"中原区\",\n\t\t\t\t\t\"金水区\",\n\t\t\t\t\t\"二七区\",\n\t\t\t\t\t\"管城回族区\",\n\t\t\t\t\t\"上街区\",\n\t\t\t\t\t\"惠济区\",\n\t\t\t\t\t\"巩义市\",\n\t\t\t\t\t\"新郑市\",\n\t\t\t\t\t\"新密市\",\n\t\t\t\t\t\"登封市\",\n\t\t\t\t\t\"荥阳市\",\n\t\t\t\t\t\"中牟县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"开封市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"鼓楼区\",\n\t\t\t\t\t\"龙亭区\",\n\t\t\t\t\t\"顺河回族区\",\n\t\t\t\t\t\"禹王台区\",\n\t\t\t\t\t\"金明区\",\n\t\t\t\t\t\"开封县\",\n\t\t\t\t\t\"尉氏县\",\n\t\t\t\t\t\"兰考县\",\n\t\t\t\t\t\"杞县\",\n\t\t\t\t\t\"通许县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"洛阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"西工区\",\n\t\t\t\t\t\"老城区\",\n\t\t\t\t\t\"涧西区\",\n\t\t\t\t\t\"瀍河回族区\",\n\t\t\t\t\t\"洛龙区\",\n\t\t\t\t\t\"吉利区\",\n\t\t\t\t\t\"偃师市\",\n\t\t\t\t\t\"孟津县\",\n\t\t\t\t\t\"汝阳县\",\n\t\t\t\t\t\"伊川县\",\n\t\t\t\t\t\"洛宁县\",\n\t\t\t\t\t\"嵩县\",\n\t\t\t\t\t\"宜阳县\",\n\t\t\t\t\t\"新安县\",\n\t\t\t\t\t\"栾川县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"平顶山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"新华区\",\n\t\t\t\t\t\"卫东区\",\n\t\t\t\t\t\"湛河区\",\n\t\t\t\t\t\"石龙区\",\n\t\t\t\t\t\"汝州市\",\n\t\t\t\t\t\"舞钢市\",\n\t\t\t\t\t\"宝丰县\",\n\t\t\t\t\t\"叶县\",\n\t\t\t\t\t\"郏县\",\n\t\t\t\t\t\"鲁山县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"安阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"北关区\",\n\t\t\t\t\t\"文峰区\",\n\t\t\t\t\t\"殷都区\",\n\t\t\t\t\t\"龙安区\",\n\t\t\t\t\t\"林州市\",\n\t\t\t\t\t\"安阳县\",\n\t\t\t\t\t\"滑县\",\n\t\t\t\t\t\"内黄县\",\n\t\t\t\t\t\"汤阴县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"鹤壁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"淇滨区\",\n\t\t\t\t\t\"山城区\",\n\t\t\t\t\t\"鹤山区\",\n\t\t\t\t\t\"浚县\",\n\t\t\t\t\t\"淇县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"新乡市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"卫滨区\",\n\t\t\t\t\t\"红旗区\",\n\t\t\t\t\t\"凤泉区\",\n\t\t\t\t\t\"牧野区\",\n\t\t\t\t\t\"卫辉市\",\n\t\t\t\t\t\"辉县市\",\n\t\t\t\t\t\"新乡县\",\n\t\t\t\t\t\"获嘉县\",\n\t\t\t\t\t\"原阳县\",\n\t\t\t\t\t\"长垣县\",\n\t\t\t\t\t\"封丘县\",\n\t\t\t\t\t\"延津县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"焦作市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"解放区\",\n\t\t\t\t\t\"中站区\",\n\t\t\t\t\t\"马村区\",\n\t\t\t\t\t\"山阳区\",\n\t\t\t\t\t\"沁阳市\",\n\t\t\t\t\t\"孟州市\",\n\t\t\t\t\t\"修武县\",\n\t\t\t\t\t\"温县\",\n\t\t\t\t\t\"武陟县\",\n\t\t\t\t\t\"博爱县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"濮阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"华龙区\",\n\t\t\t\t\t\"濮阳县\",\n\t\t\t\t\t\"南乐县\",\n\t\t\t\t\t\"台前县\",\n\t\t\t\t\t\"清丰县\",\n\t\t\t\t\t\"范县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"许昌市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"魏都区\",\n\t\t\t\t\t\"禹州市\",\n\t\t\t\t\t\"长葛市\",\n\t\t\t\t\t\"许昌县\",\n\t\t\t\t\t\"鄢陵县\",\n\t\t\t\t\t\"襄城县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"漯河市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"源汇区\",\n\t\t\t\t\t\"郾城区\",\n\t\t\t\t\t\"召陵区\",\n\t\t\t\t\t\"临颍县\",\n\t\t\t\t\t\"舞阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"三门峡市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"湖滨区\",\n\t\t\t\t\t\"义马市\",\n\t\t\t\t\t\"灵宝市\",\n\t\t\t\t\t\"渑池县\",\n\t\t\t\t\t\"卢氏县\",\n\t\t\t\t\t\"陕县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"南阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"卧龙区\",\n\t\t\t\t\t\"宛城区\",\n\t\t\t\t\t\"邓州市\",\n\t\t\t\t\t\"桐柏县\",\n\t\t\t\t\t\"方城县\",\n\t\t\t\t\t\"淅川县\",\n\t\t\t\t\t\"镇平县\",\n\t\t\t\t\t\"唐河县\",\n\t\t\t\t\t\"南召县\",\n\t\t\t\t\t\"内乡县\",\n\t\t\t\t\t\"新野县\",\n\t\t\t\t\t\"社旗县\",\n\t\t\t\t\t\"西峡县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"商丘市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"梁园区\",\n\t\t\t\t\t\"睢阳区\",\n\t\t\t\t\t\"永城市\",\n\t\t\t\t\t\"宁陵县\",\n\t\t\t\t\t\"虞城县\",\n\t\t\t\t\t\"民权县\",\n\t\t\t\t\t\"夏邑县\",\n\t\t\t\t\t\"柘城县\",\n\t\t\t\t\t\"睢县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"信阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"浉河区\",\n\t\t\t\t\t\"平桥区\",\n\t\t\t\t\t\"潢川县\",\n\t\t\t\t\t\"淮滨县\",\n\t\t\t\t\t\"息县\",\n\t\t\t\t\t\"新县\",\n\t\t\t\t\t\"商城县\",\n\t\t\t\t\t\"固始县\",\n\t\t\t\t\t\"罗山县\",\n\t\t\t\t\t\"光山县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"周口市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"川汇区\",\n\t\t\t\t\t\"项城市\",\n\t\t\t\t\t\"商水县\",\n\t\t\t\t\t\"淮阳县\",\n\t\t\t\t\t\"太康县\",\n\t\t\t\t\t\"鹿邑县\",\n\t\t\t\t\t\"西华县\",\n\t\t\t\t\t\"扶沟县\",\n\t\t\t\t\t\"沈丘县\",\n\t\t\t\t\t\"郸城县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"驻马店市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"驿城区\",\n\t\t\t\t\t\"确山县\",\n\t\t\t\t\t\"新蔡县\",\n\t\t\t\t\t\"上蔡县\",\n\t\t\t\t\t\"西平县\",\n\t\t\t\t\t\"泌阳县\",\n\t\t\t\t\t\"平舆县\",\n\t\t\t\t\t\"汝南县\",\n\t\t\t\t\t\"遂平县\",\n\t\t\t\t\t\"正阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"焦作市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"济源市\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"湖北省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"武汉市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"江岸区\",\n\t\t\t\t\t\"武昌区\",\n\t\t\t\t\t\"江汉区\",\n\t\t\t\t\t\"硚口区\",\n\t\t\t\t\t\"汉阳区\",\n\t\t\t\t\t\"青山区\",\n\t\t\t\t\t\"洪山区\",\n\t\t\t\t\t\"东西湖区\",\n\t\t\t\t\t\"汉南区\",\n\t\t\t\t\t\"蔡甸区\",\n\t\t\t\t\t\"江夏区\",\n\t\t\t\t\t\"黄陂区\",\n\t\t\t\t\t\"新洲区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黄石市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"黄石港区\",\n\t\t\t\t\t\"西塞山区\",\n\t\t\t\t\t\"下陆区\",\n\t\t\t\t\t\"铁山区\",\n\t\t\t\t\t\"大冶市\",\n\t\t\t\t\t\"阳新县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"十堰市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"张湾区\",\n\t\t\t\t\t\"茅箭区\",\n\t\t\t\t\t\"丹江口市\",\n\t\t\t\t\t\"郧县\",\n\t\t\t\t\t\"竹山县\",\n\t\t\t\t\t\"房县\",\n\t\t\t\t\t\"郧西县\",\n\t\t\t\t\t\"竹溪县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"荆州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"沙市区\",\n\t\t\t\t\t\"荆州区\",\n\t\t\t\t\t\"洪湖市\",\n\t\t\t\t\t\"石首市\",\n\t\t\t\t\t\"松滋市\",\n\t\t\t\t\t\"监利县\",\n\t\t\t\t\t\"公安县\",\n\t\t\t\t\t\"江陵县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宜昌市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"西陵区\",\n\t\t\t\t\t\"伍家岗区\",\n\t\t\t\t\t\"点军区\",\n\t\t\t\t\t\"猇亭区\",\n\t\t\t\t\t\"夷陵区\",\n\t\t\t\t\t\"宜都市\",\n\t\t\t\t\t\"当阳市\",\n\t\t\t\t\t\"枝江市\",\n\t\t\t\t\t\"秭归县\",\n\t\t\t\t\t\"远安县\",\n\t\t\t\t\t\"兴山县\",\n\t\t\t\t\t\"五峰土家族自治县\",\n\t\t\t\t\t\"长阳土家族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"襄樊市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"襄城区\",\n\t\t\t\t\t\"樊城区\",\n\t\t\t\t\t\"襄阳区\",\n\t\t\t\t\t\"老河口市\",\n\t\t\t\t\t\"枣阳市\",\n\t\t\t\t\t\"宜城市\",\n\t\t\t\t\t\"南漳县\",\n\t\t\t\t\t\"谷城县\",\n\t\t\t\t\t\"保康县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"鄂州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"鄂城区\",\n\t\t\t\t\t\"华容区\",\n\t\t\t\t\t\"梁子湖区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"荆门市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东宝区\",\n\t\t\t\t\t\"掇刀区\",\n\t\t\t\t\t\"钟祥市\",\n\t\t\t\t\t\"京山县\",\n\t\t\t\t\t\"沙洋县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"孝感市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"孝南区\",\n\t\t\t\t\t\"应城市\",\n\t\t\t\t\t\"安陆市\",\n\t\t\t\t\t\"汉川市\",\n\t\t\t\t\t\"云梦县\",\n\t\t\t\t\t\"大悟县\",\n\t\t\t\t\t\"孝昌县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黄冈市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"黄州区\",\n\t\t\t\t\t\"麻城市\",\n\t\t\t\t\t\"武穴市\",\n\t\t\t\t\t\"红安县\",\n\t\t\t\t\t\"罗田县\",\n\t\t\t\t\t\"浠水县\",\n\t\t\t\t\t\"蕲春县\",\n\t\t\t\t\t\"黄梅县\",\n\t\t\t\t\t\"英山县\",\n\t\t\t\t\t\"团风县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"咸宁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"咸安区\",\n\t\t\t\t\t\"赤壁市\",\n\t\t\t\t\t\"嘉鱼县\",\n\t\t\t\t\t\"通山县\",\n\t\t\t\t\t\"崇阳县\",\n\t\t\t\t\t\"通城县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"随州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"曾都区\",\n\t\t\t\t\t\"广水市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"恩施土家族苗族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"恩施市\",\n\t\t\t\t\t\"利川市\",\n\t\t\t\t\t\"建始县\",\n\t\t\t\t\t\"来凤县\",\n\t\t\t\t\t\"巴东县\",\n\t\t\t\t\t\"鹤峰县\",\n\t\t\t\t\t\"宣恩县\",\n\t\t\t\t\t\"咸丰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"仙桃市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"仙桃\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"天门市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"天门\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"潜江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"潜江\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"神农架林区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"神农架林区\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"湖南省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"长沙市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"岳麓区\",\n\t\t\t\t\t\"芙蓉区\",\n\t\t\t\t\t\"天心区\",\n\t\t\t\t\t\"开福区\",\n\t\t\t\t\t\"雨花区\",\n\t\t\t\t\t\"浏阳市\",\n\t\t\t\t\t\"长沙县\",\n\t\t\t\t\t\"望城县\",\n\t\t\t\t\t\"宁乡县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"株洲市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"天元区\",\n\t\t\t\t\t\"荷塘区\",\n\t\t\t\t\t\"芦淞区\",\n\t\t\t\t\t\"石峰区\",\n\t\t\t\t\t\"醴陵市\",\n\t\t\t\t\t\"株洲县\",\n\t\t\t\t\t\"炎陵县\",\n\t\t\t\t\t\"茶陵县\",\n\t\t\t\t\t\"攸县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"湘潭市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"岳塘区\",\n\t\t\t\t\t\"雨湖区\",\n\t\t\t\t\t\"湘乡市\",\n\t\t\t\t\t\"韶山市\",\n\t\t\t\t\t\"湘潭县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"衡阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"雁峰区\",\n\t\t\t\t\t\"珠晖区\",\n\t\t\t\t\t\"石鼓区\",\n\t\t\t\t\t\"蒸湘区\",\n\t\t\t\t\t\"南岳区\",\n\t\t\t\t\t\"耒阳市\",\n\t\t\t\t\t\"常宁市\",\n\t\t\t\t\t\"衡阳县\",\n\t\t\t\t\t\"衡东县\",\n\t\t\t\t\t\"衡山县\",\n\t\t\t\t\t\"衡南县\",\n\t\t\t\t\t\"祁东县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"邵阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"双清区\",\n\t\t\t\t\t\"大祥区\",\n\t\t\t\t\t\"北塔区\",\n\t\t\t\t\t\"武冈市\",\n\t\t\t\t\t\"邵东县\",\n\t\t\t\t\t\"洞口县\",\n\t\t\t\t\t\"新邵县\",\n\t\t\t\t\t\"绥宁县\",\n\t\t\t\t\t\"新宁县\",\n\t\t\t\t\t\"邵阳县\",\n\t\t\t\t\t\"隆回县\",\n\t\t\t\t\t\"城步苗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"岳阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"岳阳楼区\",\n\t\t\t\t\t\"云溪区\",\n\t\t\t\t\t\"君山区\",\n\t\t\t\t\t\"临湘市\",\n\t\t\t\t\t\"汨罗市\",\n\t\t\t\t\t\"岳阳县\",\n\t\t\t\t\t\"湘阴县\",\n\t\t\t\t\t\"平江县\",\n\t\t\t\t\t\"华容县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"常德市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"武陵区\",\n\t\t\t\t\t\"鼎城区\",\n\t\t\t\t\t\"津市市\",\n\t\t\t\t\t\"澧县\",\n\t\t\t\t\t\"临澧县\",\n\t\t\t\t\t\"桃源县\",\n\t\t\t\t\t\"汉寿县\",\n\t\t\t\t\t\"安乡县\",\n\t\t\t\t\t\"石门县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"张家界市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"永定区\",\n\t\t\t\t\t\"武陵源区\",\n\t\t\t\t\t\"慈利县\",\n\t\t\t\t\t\"桑植县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"益阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"赫山区\",\n\t\t\t\t\t\"资阳区\",\n\t\t\t\t\t\"沅江市\",\n\t\t\t\t\t\"桃江县\",\n\t\t\t\t\t\"南县\",\n\t\t\t\t\t\"安化县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"郴州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"北湖区\",\n\t\t\t\t\t\"苏仙区\",\n\t\t\t\t\t\"资兴市\",\n\t\t\t\t\t\"宜章县\",\n\t\t\t\t\t\"汝城县\",\n\t\t\t\t\t\"安仁县\",\n\t\t\t\t\t\"嘉禾县\",\n\t\t\t\t\t\"临武县\",\n\t\t\t\t\t\"桂东县\",\n\t\t\t\t\t\"永兴县\",\n\t\t\t\t\t\"桂阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"永州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"冷水滩区\",\n\t\t\t\t\t\"零陵区\",\n\t\t\t\t\t\"祁阳县\",\n\t\t\t\t\t\"蓝山县\",\n\t\t\t\t\t\"宁远县\",\n\t\t\t\t\t\"新田县\",\n\t\t\t\t\t\"东安县\",\n\t\t\t\t\t\"江永县\",\n\t\t\t\t\t\"道县\",\n\t\t\t\t\t\"双牌县\",\n\t\t\t\t\t\"江华瑶族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"怀化市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"鹤城区\",\n\t\t\t\t\t\"洪江市\",\n\t\t\t\t\t\"会同县\",\n\t\t\t\t\t\"沅陵县\",\n\t\t\t\t\t\"辰溪县\",\n\t\t\t\t\t\"溆浦县\",\n\t\t\t\t\t\"中方县\",\n\t\t\t\t\t\"新晃侗族自治县\",\n\t\t\t\t\t\"芷江侗族自治县\",\n\t\t\t\t\t\"通道侗族自治县\",\n\t\t\t\t\t\"靖州苗族侗族自治县\",\n\t\t\t\t\t\"麻阳苗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"娄底市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"娄星区\",\n\t\t\t\t\t\"冷水江市\",\n\t\t\t\t\t\"涟源市\",\n\t\t\t\t\t\"新化县\",\n\t\t\t\t\t\"双峰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"湘西土家族苗族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"吉首市\",\n\t\t\t\t\t\"古丈县\",\n\t\t\t\t\t\"龙山县\",\n\t\t\t\t\t\"永顺县\",\n\t\t\t\t\t\"凤凰县\",\n\t\t\t\t\t\"泸溪县\",\n\t\t\t\t\t\"保靖县\",\n\t\t\t\t\t\"花垣县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"广东省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"广州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"越秀区\",\n\t\t\t\t\t\"荔湾区\",\n\t\t\t\t\t\"海珠区\",\n\t\t\t\t\t\"天河区\",\n\t\t\t\t\t\"白云区\",\n\t\t\t\t\t\"黄埔区\",\n\t\t\t\t\t\"番禺区\",\n\t\t\t\t\t\"花都区\",\n\t\t\t\t\t\"南沙区\",\n\t\t\t\t\t\"萝岗区\",\n\t\t\t\t\t\"增城市\",\n\t\t\t\t\t\"从化市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"深圳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"罗湖区\",\n\t\t\t\t\t\"福田区\",\n\t\t\t\t\t\"南山区\",\n\t\t\t\t\t\"宝安区\",\n\t\t\t\t\t\"龙岗区\",\n\t\t\t\t\t\"盐田区\",\n\t\t\t\t\t\"龙华区\",\n\t\t\t\t\t\"坪山区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"东莞市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东城街道\",\n\t\t\t\t\t\"南城街道\",\n\t\t\t\t\t\"万江街道\",\n\t\t\t\t\t\"莞城街道\",\n\t\t\t\t\t\"石碣镇\",\n\t\t\t\t\t\"石龙镇\",\n\t\t\t\t\t\"茶山镇\",\n\t\t\t\t\t\"石排镇\",\n\t\t\t\t\t\"企石镇\",\n\t\t\t\t\t\"横沥镇\",\n\t\t\t\t\t\"桥头镇\",\n\t\t\t\t\t\"谢岗镇\",\n\t\t\t\t\t\"东坑镇\",\n\t\t\t\t\t\"常平镇\",\n\t\t\t\t\t\"寮步镇\",\n\t\t\t\t\t\"樟木头镇\",\n\t\t\t\t\t\"大朗镇\",\n\t\t\t\t\t\"黄江镇\",\n\t\t\t\t\t\"清溪镇\",\n\t\t\t\t\t\"塘厦镇\",\n\t\t\t\t\t\"凤岗镇\",\n\t\t\t\t\t\"大岭山镇\",\n\t\t\t\t\t\"长安镇\",\n\t\t\t\t\t\"虎门镇\",\n\t\t\t\t\t\"厚街镇\",\n\t\t\t\t\t\"沙田镇\",\n\t\t\t\t\t\"道滘镇\",\n\t\t\t\t\t\"洪梅镇\",\n\t\t\t\t\t\"麻涌镇\",\n\t\t\t\t\t\"望牛墩镇\",\n\t\t\t\t\t\"中堂镇\",\n\t\t\t\t\t\"高埗镇\",\n\t\t\t\t\t\"松山湖\",\n\t\t\t\t\t\"东莞港\",\n\t\t\t\t\t\"东莞生态园\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"中山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"石岐街道\",\n\t\t\t\t\t\"东区街道\",\n\t\t\t\t\t\"中山港街道\",\n\t\t\t\t\t\"西区街道\",\n\t\t\t\t\t\"南区街道\",\n\t\t\t\t\t\"五桂山街道\",\n\t\t\t\t\t\"小榄镇\",\n\t\t\t\t\t\"黄圃镇\",\n\t\t\t\t\t\"民众镇\",\n\t\t\t\t\t\"东凤镇\",\n\t\t\t\t\t\"东升镇\",\n\t\t\t\t\t\"古镇镇\",\n\t\t\t\t\t\"沙溪镇\",\n\t\t\t\t\t\"坦洲镇\",\n\t\t\t\t\t\"港口镇\",\n\t\t\t\t\t\"三角镇\",\n\t\t\t\t\t\"横栏镇\",\n\t\t\t\t\t\"南头镇\",\n\t\t\t\t\t\"阜沙镇\",\n\t\t\t\t\t\"南朗镇\",\n\t\t\t\t\t\"三乡镇\",\n\t\t\t\t\t\"板芙镇\",\n\t\t\t\t\t\"大涌镇\",\n\t\t\t\t\t\"神湾镇\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"潮州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市辖区\",\n\t\t\t\t\t\"湘桥区\",\n\t\t\t\t\t\"潮安县\",\n\t\t\t\t\t\"饶平县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"揭阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"榕城区\",\n\t\t\t\t\t\"揭东县\",\n\t\t\t\t\t\"揭西县\",\n\t\t\t\t\t\"惠来县\",\n\t\t\t\t\t\"普宁市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"云浮市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"云城区\",\n\t\t\t\t\t\"新兴县\",\n\t\t\t\t\t\"郁南县\",\n\t\t\t\t\t\"云安县\",\n\t\t\t\t\t\"罗定市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"珠海市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"香洲区\",\n\t\t\t\t\t\"斗门区\",\n\t\t\t\t\t\"金湾区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"汕头市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"金平区\",\n\t\t\t\t\t\"濠江区\",\n\t\t\t\t\t\"龙湖区\",\n\t\t\t\t\t\"潮阳区\",\n\t\t\t\t\t\"潮南区\",\n\t\t\t\t\t\"澄海区\",\n\t\t\t\t\t\"南澳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"韶关市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"浈江区\",\n\t\t\t\t\t\"武江区\",\n\t\t\t\t\t\"曲江区\",\n\t\t\t\t\t\"乐昌市\",\n\t\t\t\t\t\"南雄市\",\n\t\t\t\t\t\"始兴县\",\n\t\t\t\t\t\"仁化县\",\n\t\t\t\t\t\"翁源县\",\n\t\t\t\t\t\"新丰县\",\n\t\t\t\t\t\"乳源瑶族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"佛山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"禅城区\",\n\t\t\t\t\t\"南海区\",\n\t\t\t\t\t\"顺德区\",\n\t\t\t\t\t\"三水区\",\n\t\t\t\t\t\"高明区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"江门市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"蓬江区\",\n\t\t\t\t\t\"江海区\",\n\t\t\t\t\t\"新会区\",\n\t\t\t\t\t\"恩平市\",\n\t\t\t\t\t\"台山市\",\n\t\t\t\t\t\"开平市\",\n\t\t\t\t\t\"鹤山市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"湛江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"赤坎区\",\n\t\t\t\t\t\"霞山区\",\n\t\t\t\t\t\"坡头区\",\n\t\t\t\t\t\"麻章区\",\n\t\t\t\t\t\"吴川市\",\n\t\t\t\t\t\"廉江市\",\n\t\t\t\t\t\"雷州市\",\n\t\t\t\t\t\"遂溪县\",\n\t\t\t\t\t\"徐闻县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"茂名市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"茂南区\",\n\t\t\t\t\t\"茂港区\",\n\t\t\t\t\t\"化州市\",\n\t\t\t\t\t\"信宜市\",\n\t\t\t\t\t\"高州市\",\n\t\t\t\t\t\"电白县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"肇庆市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"端州区\",\n\t\t\t\t\t\"鼎湖区\",\n\t\t\t\t\t\"高要市\",\n\t\t\t\t\t\"四会市\",\n\t\t\t\t\t\"广宁县\",\n\t\t\t\t\t\"怀集县\",\n\t\t\t\t\t\"封开县\",\n\t\t\t\t\t\"德庆县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"惠州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"惠城区\",\n\t\t\t\t\t\"惠阳区\",\n\t\t\t\t\t\"博罗县\",\n\t\t\t\t\t\"惠东县\",\n\t\t\t\t\t\"龙门县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"梅州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"梅江区\",\n\t\t\t\t\t\"兴宁市\",\n\t\t\t\t\t\"梅县\",\n\t\t\t\t\t\"大埔县\",\n\t\t\t\t\t\"丰顺县\",\n\t\t\t\t\t\"五华县\",\n\t\t\t\t\t\"平远县\",\n\t\t\t\t\t\"蕉岭县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"汕尾市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城区\",\n\t\t\t\t\t\"陆丰市\",\n\t\t\t\t\t\"海丰县\",\n\t\t\t\t\t\"陆河县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"河源市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"源城区\",\n\t\t\t\t\t\"紫金县\",\n\t\t\t\t\t\"龙川县\",\n\t\t\t\t\t\"连平县\",\n\t\t\t\t\t\"和平县\",\n\t\t\t\t\t\"东源县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阳江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"江城区\",\n\t\t\t\t\t\"阳春市\",\n\t\t\t\t\t\"阳西县\",\n\t\t\t\t\t\"阳东县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"清远市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"清城区\",\n\t\t\t\t\t\"英德市\",\n\t\t\t\t\t\"连州市\",\n\t\t\t\t\t\"佛冈县\",\n\t\t\t\t\t\"阳山县\",\n\t\t\t\t\t\"清新县\",\n\t\t\t\t\t\"连山壮族瑶族自治县\",\n\t\t\t\t\t\"连南瑶族自治县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"广西\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"南宁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"青秀区\",\n\t\t\t\t\t\"兴宁区\",\n\t\t\t\t\t\"西乡塘区\",\n\t\t\t\t\t\"良庆区\",\n\t\t\t\t\t\"江南区\",\n\t\t\t\t\t\"邕宁区\",\n\t\t\t\t\t\"武鸣县\",\n\t\t\t\t\t\"隆安县\",\n\t\t\t\t\t\"马山县\",\n\t\t\t\t\t\"上林县\",\n\t\t\t\t\t\"宾阳县\",\n\t\t\t\t\t\"横县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"柳州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城中区\",\n\t\t\t\t\t\"鱼峰区\",\n\t\t\t\t\t\"柳北区\",\n\t\t\t\t\t\"柳南区\",\n\t\t\t\t\t\"柳江县\",\n\t\t\t\t\t\"柳城县\",\n\t\t\t\t\t\"鹿寨县\",\n\t\t\t\t\t\"融安县\",\n\t\t\t\t\t\"融水苗族自治县\",\n\t\t\t\t\t\"三江侗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"桂林市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"象山区\",\n\t\t\t\t\t\"秀峰区\",\n\t\t\t\t\t\"叠彩区\",\n\t\t\t\t\t\"七星区\",\n\t\t\t\t\t\"雁山区\",\n\t\t\t\t\t\"阳朔县\",\n\t\t\t\t\t\"临桂县\",\n\t\t\t\t\t\"灵川县\",\n\t\t\t\t\t\"全州县\",\n\t\t\t\t\t\"平乐县\",\n\t\t\t\t\t\"兴安县\",\n\t\t\t\t\t\"灌阳县\",\n\t\t\t\t\t\"荔浦县\",\n\t\t\t\t\t\"资源县\",\n\t\t\t\t\t\"永福县\",\n\t\t\t\t\t\"龙胜各族自治县\",\n\t\t\t\t\t\"恭城瑶族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"梧州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"万秀区\",\n\t\t\t\t\t\"蝶山区\",\n\t\t\t\t\t\"长洲区\",\n\t\t\t\t\t\"岑溪市\",\n\t\t\t\t\t\"苍梧县\",\n\t\t\t\t\t\"藤县\",\n\t\t\t\t\t\"蒙山县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"北海市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海城区\",\n\t\t\t\t\t\"银海区\",\n\t\t\t\t\t\"铁山港区\",\n\t\t\t\t\t\"合浦县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"防城港市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"港口区\",\n\t\t\t\t\t\"防城区\",\n\t\t\t\t\t\"东兴市\",\n\t\t\t\t\t\"上思县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"钦州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"钦南区\",\n\t\t\t\t\t\"钦北区\",\n\t\t\t\t\t\"灵山县\",\n\t\t\t\t\t\"浦北县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"贵港市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"港北区\",\n\t\t\t\t\t\"港南区\",\n\t\t\t\t\t\"覃塘区\",\n\t\t\t\t\t\"桂平市\",\n\t\t\t\t\t\"平南县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"玉林市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"玉州区\",\n\t\t\t\t\t\"北流市\",\n\t\t\t\t\t\"容县\",\n\t\t\t\t\t\"陆川县\",\n\t\t\t\t\t\"博白县\",\n\t\t\t\t\t\"兴业县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"百色市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"右江区\",\n\t\t\t\t\t\"凌云县\",\n\t\t\t\t\t\"平果县\",\n\t\t\t\t\t\"西林县\",\n\t\t\t\t\t\"乐业县\",\n\t\t\t\t\t\"德保县\",\n\t\t\t\t\t\"田林县\",\n\t\t\t\t\t\"田阳县\",\n\t\t\t\t\t\"靖西县\",\n\t\t\t\t\t\"田东县\",\n\t\t\t\t\t\"那坡县\",\n\t\t\t\t\t\"隆林各族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"贺州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"八步区\",\n\t\t\t\t\t\"钟山县\",\n\t\t\t\t\t\"昭平县\",\n\t\t\t\t\t\"富川瑶族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"河池市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"金城江区\",\n\t\t\t\t\t\"宜州市\",\n\t\t\t\t\t\"天峨县\",\n\t\t\t\t\t\"凤山县\",\n\t\t\t\t\t\"南丹县\",\n\t\t\t\t\t\"东兰县\",\n\t\t\t\t\t\"都安瑶族自治县\",\n\t\t\t\t\t\"罗城仫佬族自治县\",\n\t\t\t\t\t\"巴马瑶族自治县\",\n\t\t\t\t\t\"环江毛南族自治县\",\n\t\t\t\t\t\"大化瑶族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"来宾市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"兴宾区\",\n\t\t\t\t\t\"合山市\",\n\t\t\t\t\t\"象州县\",\n\t\t\t\t\t\"武宣县\",\n\t\t\t\t\t\"忻城县\",\n\t\t\t\t\t\"金秀瑶族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"崇左市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"江州区\",\n\t\t\t\t\t\"凭祥市\",\n\t\t\t\t\t\"宁明县\",\n\t\t\t\t\t\"扶绥县\",\n\t\t\t\t\t\"龙州县\",\n\t\t\t\t\t\"大新县\",\n\t\t\t\t\t\"天等县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"海南省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"海口市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"龙华区\",\n\t\t\t\t\t\"秀英区\",\n\t\t\t\t\t\"琼山区\",\n\t\t\t\t\t\"美兰区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"三亚市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市辖区\",\n\t\t\t\t\t\"海棠区\",\n\t\t\t\t\t\"吉阳区\",\n\t\t\t\t\t\"天涯区\",\n\t\t\t\t\t\"崖州区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"五指山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"五指山\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"琼海市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"琼海\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"儋州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"儋州\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"文昌市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"文昌\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"万宁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"万宁\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"东方市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东方\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"澄迈县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"澄迈县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"定安县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"定安县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"屯昌县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"屯昌县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"临高县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"临高县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"白沙黎族自治县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"白沙黎族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"昌江黎族自治县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"昌江黎族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"乐东黎族自治县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"乐东黎族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"陵水黎族自治县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"陵水黎族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"保亭黎族苗族自治县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"保亭黎族苗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"琼中黎族苗族自治县\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"琼中黎族苗族自治县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"重庆市\",\n\t\t\"city\": [{\n\t\t\t\"name\": \"重庆市\",\n\t\t\t\"area\": [\n\t\t\t\t\"渝中区\",\n\t\t\t\t\"大渡口区\",\n\t\t\t\t\"江北区\",\n\t\t\t\t\"南岸区\",\n\t\t\t\t\"北碚区\",\n\t\t\t\t\"渝北区\",\n\t\t\t\t\"巴南区\",\n\t\t\t\t\"长寿区\",\n\t\t\t\t\"双桥区\",\n\t\t\t\t\"沙坪坝区\",\n\t\t\t\t\"万盛区\",\n\t\t\t\t\"万州区\",\n\t\t\t\t\"涪陵区\",\n\t\t\t\t\"黔江区\",\n\t\t\t\t\"永川区\",\n\t\t\t\t\"合川区\",\n\t\t\t\t\"江津区\",\n\t\t\t\t\"九龙坡区\",\n\t\t\t\t\"南川区\",\n\t\t\t\t\"綦江县\",\n\t\t\t\t\"潼南县\",\n\t\t\t\t\"荣昌县\",\n\t\t\t\t\"璧山县\",\n\t\t\t\t\"大足县\",\n\t\t\t\t\"铜梁县\",\n\t\t\t\t\"梁平县\",\n\t\t\t\t\"开县\",\n\t\t\t\t\"忠县\",\n\t\t\t\t\"城口县\",\n\t\t\t\t\"垫江县\",\n\t\t\t\t\"武隆县\",\n\t\t\t\t\"丰都县\",\n\t\t\t\t\"奉节县\",\n\t\t\t\t\"云阳县\",\n\t\t\t\t\"巫溪县\",\n\t\t\t\t\"巫山县\",\n\t\t\t\t\"石柱土家族自治县\",\n\t\t\t\t\"秀山土家族苗族自治县\",\n\t\t\t\t\"酉阳土家族苗族自治县\",\n\t\t\t\t\"彭水苗族土家族自治县\"\n\t\t\t]\n\t\t}]\n\t},\n\t{\n\t\t\"name\": \"四川省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"成都市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"青羊区\",\n\t\t\t\t\t\"锦江区\",\n\t\t\t\t\t\"金牛区\",\n\t\t\t\t\t\"武侯区\",\n\t\t\t\t\t\"成华区\",\n\t\t\t\t\t\"龙泉驿区\",\n\t\t\t\t\t\"青白江区\",\n\t\t\t\t\t\"新都区\",\n\t\t\t\t\t\"温江区\",\n\t\t\t\t\t\"都江堰市\",\n\t\t\t\t\t\"彭州市\",\n\t\t\t\t\t\"邛崃市\",\n\t\t\t\t\t\"崇州市\",\n\t\t\t\t\t\"金堂县\",\n\t\t\t\t\t\"郫县\",\n\t\t\t\t\t\"新津县\",\n\t\t\t\t\t\"双流县\",\n\t\t\t\t\t\"蒲江县\",\n\t\t\t\t\t\"大邑县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"自贡市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"大安区\",\n\t\t\t\t\t\"自流井区\",\n\t\t\t\t\t\"贡井区\",\n\t\t\t\t\t\"沿滩区\",\n\t\t\t\t\t\"荣县\",\n\t\t\t\t\t\"富顺县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"攀枝花市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"仁和区\",\n\t\t\t\t\t\"米易县\",\n\t\t\t\t\t\"盐边县\",\n\t\t\t\t\t\"东区\",\n\t\t\t\t\t\"西区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"泸州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"江阳区\",\n\t\t\t\t\t\"纳溪区\",\n\t\t\t\t\t\"龙马潭区\",\n\t\t\t\t\t\"泸县\",\n\t\t\t\t\t\"合江县\",\n\t\t\t\t\t\"叙永县\",\n\t\t\t\t\t\"古蔺县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"德阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"旌阳区\",\n\t\t\t\t\t\"广汉市\",\n\t\t\t\t\t\"什邡市\",\n\t\t\t\t\t\"绵竹市\",\n\t\t\t\t\t\"罗江县\",\n\t\t\t\t\t\"中江县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"绵阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"涪城区\",\n\t\t\t\t\t\"游仙区\",\n\t\t\t\t\t\"江油市\",\n\t\t\t\t\t\"盐亭县\",\n\t\t\t\t\t\"三台县\",\n\t\t\t\t\t\"平武县\",\n\t\t\t\t\t\"安县\",\n\t\t\t\t\t\"梓潼县\",\n\t\t\t\t\t\"北川羌族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"广元市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"元坝区\",\n\t\t\t\t\t\"朝天区\",\n\t\t\t\t\t\"青川县\",\n\t\t\t\t\t\"旺苍县\",\n\t\t\t\t\t\"剑阁县\",\n\t\t\t\t\t\"苍溪县\",\n\t\t\t\t\t\"市中区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"遂宁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"船山区\",\n\t\t\t\t\t\"安居区\",\n\t\t\t\t\t\"射洪县\",\n\t\t\t\t\t\"蓬溪县\",\n\t\t\t\t\t\"大英县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"内江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市中区\",\n\t\t\t\t\t\"东兴区\",\n\t\t\t\t\t\"资中县\",\n\t\t\t\t\t\"隆昌县\",\n\t\t\t\t\t\"威远县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"乐山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"市中区\",\n\t\t\t\t\t\"五通桥区\",\n\t\t\t\t\t\"沙湾区\",\n\t\t\t\t\t\"金口河区\",\n\t\t\t\t\t\"峨眉山市\",\n\t\t\t\t\t\"夹江县\",\n\t\t\t\t\t\"井研县\",\n\t\t\t\t\t\"犍为县\",\n\t\t\t\t\t\"沐川县\",\n\t\t\t\t\t\"马边彝族自治县\",\n\t\t\t\t\t\"峨边彝族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"南充\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"顺庆区\",\n\t\t\t\t\t\"高坪区\",\n\t\t\t\t\t\"嘉陵区\",\n\t\t\t\t\t\"阆中市\",\n\t\t\t\t\t\"营山县\",\n\t\t\t\t\t\"蓬安县\",\n\t\t\t\t\t\"仪陇县\",\n\t\t\t\t\t\"南部县\",\n\t\t\t\t\t\"西充县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"眉山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"东坡区\",\n\t\t\t\t\t\"仁寿县\",\n\t\t\t\t\t\"彭山县\",\n\t\t\t\t\t\"洪雅县\",\n\t\t\t\t\t\"丹棱县\",\n\t\t\t\t\t\"青神县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宜宾市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"翠屏区\",\n\t\t\t\t\t\"宜宾县\",\n\t\t\t\t\t\"兴文县\",\n\t\t\t\t\t\"南溪县\",\n\t\t\t\t\t\"珙县\",\n\t\t\t\t\t\"长宁县\",\n\t\t\t\t\t\"高县\",\n\t\t\t\t\t\"江安县\",\n\t\t\t\t\t\"筠连县\",\n\t\t\t\t\t\"屏山县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"广安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"广安区\",\n\t\t\t\t\t\"华蓥市\",\n\t\t\t\t\t\"岳池县\",\n\t\t\t\t\t\"邻水县\",\n\t\t\t\t\t\"武胜县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"达州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"通川区\",\n\t\t\t\t\t\"万源市\",\n\t\t\t\t\t\"达县\",\n\t\t\t\t\t\"渠县\",\n\t\t\t\t\t\"宣汉县\",\n\t\t\t\t\t\"开江县\",\n\t\t\t\t\t\"大竹县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"雅安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"雨城区\",\n\t\t\t\t\t\"芦山县\",\n\t\t\t\t\t\"石棉县\",\n\t\t\t\t\t\"名山县\",\n\t\t\t\t\t\"天全县\",\n\t\t\t\t\t\"荥经县\",\n\t\t\t\t\t\"宝兴县\",\n\t\t\t\t\t\"汉源县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"巴中市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"巴州区\",\n\t\t\t\t\t\"南江县\",\n\t\t\t\t\t\"平昌县\",\n\t\t\t\t\t\"通江县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"资阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"雁江区\",\n\t\t\t\t\t\"简阳市\",\n\t\t\t\t\t\"安岳县\",\n\t\t\t\t\t\"乐至县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阿坝藏族羌族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"马尔康县\",\n\t\t\t\t\t\"九寨沟县\",\n\t\t\t\t\t\"红原县\",\n\t\t\t\t\t\"汶川县\",\n\t\t\t\t\t\"阿坝县\",\n\t\t\t\t\t\"理县\",\n\t\t\t\t\t\"若尔盖县\",\n\t\t\t\t\t\"小金县\",\n\t\t\t\t\t\"黑水县\",\n\t\t\t\t\t\"金川县\",\n\t\t\t\t\t\"松潘县\",\n\t\t\t\t\t\"壤塘县\",\n\t\t\t\t\t\"茂县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"甘孜藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"康定县\",\n\t\t\t\t\t\"丹巴县\",\n\t\t\t\t\t\"炉霍县\",\n\t\t\t\t\t\"九龙县\",\n\t\t\t\t\t\"甘孜县\",\n\t\t\t\t\t\"雅江县\",\n\t\t\t\t\t\"新龙县\",\n\t\t\t\t\t\"道孚县\",\n\t\t\t\t\t\"白玉县\",\n\t\t\t\t\t\"理塘县\",\n\t\t\t\t\t\"德格县\",\n\t\t\t\t\t\"乡城县\",\n\t\t\t\t\t\"石渠县\",\n\t\t\t\t\t\"稻城县\",\n\t\t\t\t\t\"色达县\",\n\t\t\t\t\t\"巴塘县\",\n\t\t\t\t\t\"泸定县\",\n\t\t\t\t\t\"得荣县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"凉山彝族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"西昌市\",\n\t\t\t\t\t\"美姑县\",\n\t\t\t\t\t\"昭觉县\",\n\t\t\t\t\t\"金阳县\",\n\t\t\t\t\t\"甘洛县\",\n\t\t\t\t\t\"布拖县\",\n\t\t\t\t\t\"雷波县\",\n\t\t\t\t\t\"普格县\",\n\t\t\t\t\t\"宁南县\",\n\t\t\t\t\t\"喜德县\",\n\t\t\t\t\t\"会东县\",\n\t\t\t\t\t\"越西县\",\n\t\t\t\t\t\"会理县\",\n\t\t\t\t\t\"盐源县\",\n\t\t\t\t\t\"德昌县\",\n\t\t\t\t\t\"冕宁县\",\n\t\t\t\t\t\"木里藏族自治县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"贵州省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"贵阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"南明区\",\n\t\t\t\t\t\"云岩区\",\n\t\t\t\t\t\"花溪区\",\n\t\t\t\t\t\"乌当区\",\n\t\t\t\t\t\"白云区\",\n\t\t\t\t\t\"小河区\",\n\t\t\t\t\t\"清镇市\",\n\t\t\t\t\t\"开阳县\",\n\t\t\t\t\t\"修文县\",\n\t\t\t\t\t\"息烽县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"六盘水市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"钟山区\",\n\t\t\t\t\t\"水城县\",\n\t\t\t\t\t\"盘县\",\n\t\t\t\t\t\"六枝特区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"遵义市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"红花岗区\",\n\t\t\t\t\t\"汇川区\",\n\t\t\t\t\t\"赤水市\",\n\t\t\t\t\t\"仁怀市\",\n\t\t\t\t\t\"遵义县\",\n\t\t\t\t\t\"绥阳县\",\n\t\t\t\t\t\"桐梓县\",\n\t\t\t\t\t\"习水县\",\n\t\t\t\t\t\"凤冈县\",\n\t\t\t\t\t\"正安县\",\n\t\t\t\t\t\"余庆县\",\n\t\t\t\t\t\"湄潭县\",\n\t\t\t\t\t\"道真仡佬族苗族自治县\",\n\t\t\t\t\t\"务川仡佬族苗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"安顺市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"西秀区\",\n\t\t\t\t\t\"普定县\",\n\t\t\t\t\t\"平坝县\",\n\t\t\t\t\t\"镇宁布依族苗族自治县\",\n\t\t\t\t\t\"紫云苗族布依族自治县\",\n\t\t\t\t\t\"关岭布依族苗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"铜仁地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"铜仁市\",\n\t\t\t\t\t\"德江县\",\n\t\t\t\t\t\"江口县\",\n\t\t\t\t\t\"思南县\",\n\t\t\t\t\t\"石阡县\",\n\t\t\t\t\t\"玉屏侗族自治县\",\n\t\t\t\t\t\"松桃苗族自治县\",\n\t\t\t\t\t\"印江土家族苗族自治县\",\n\t\t\t\t\t\"沿河土家族自治县\",\n\t\t\t\t\t\"万山特区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"毕节地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"毕节市\",\n\t\t\t\t\t\"黔西县\",\n\t\t\t\t\t\"大方县\",\n\t\t\t\t\t\"织金县\",\n\t\t\t\t\t\"金沙县\",\n\t\t\t\t\t\"赫章县\",\n\t\t\t\t\t\"纳雍县\",\n\t\t\t\t\t\"威宁彝族回族苗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黔西南布依族苗族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"兴义市\",\n\t\t\t\t\t\"望谟县\",\n\t\t\t\t\t\"兴仁县\",\n\t\t\t\t\t\"普安县\",\n\t\t\t\t\t\"册亨县\",\n\t\t\t\t\t\"晴隆县\",\n\t\t\t\t\t\"贞丰县\",\n\t\t\t\t\t\"安龙县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黔东南苗族侗族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"凯里市\",\n\t\t\t\t\t\"施秉县\",\n\t\t\t\t\t\"从江县\",\n\t\t\t\t\t\"锦屏县\",\n\t\t\t\t\t\"镇远县\",\n\t\t\t\t\t\"麻江县\",\n\t\t\t\t\t\"台江县\",\n\t\t\t\t\t\"天柱县\",\n\t\t\t\t\t\"黄平县\",\n\t\t\t\t\t\"榕江县\",\n\t\t\t\t\t\"剑河县\",\n\t\t\t\t\t\"三穗县\",\n\t\t\t\t\t\"雷山县\",\n\t\t\t\t\t\"黎平县\",\n\t\t\t\t\t\"岑巩县\",\n\t\t\t\t\t\"丹寨县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黔南布依族苗族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"都匀市\",\n\t\t\t\t\t\"福泉市\",\n\t\t\t\t\t\"贵定县\",\n\t\t\t\t\t\"惠水县\",\n\t\t\t\t\t\"罗甸县\",\n\t\t\t\t\t\"瓮安县\",\n\t\t\t\t\t\"荔波县\",\n\t\t\t\t\t\"龙里县\",\n\t\t\t\t\t\"平塘县\",\n\t\t\t\t\t\"长顺县\",\n\t\t\t\t\t\"独山县\",\n\t\t\t\t\t\"三都水族自治县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"云南省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"昆明市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"盘龙区\",\n\t\t\t\t\t\"五华区\",\n\t\t\t\t\t\"官渡区\",\n\t\t\t\t\t\"西山区\",\n\t\t\t\t\t\"东川区\",\n\t\t\t\t\t\"安宁市\",\n\t\t\t\t\t\"呈贡县\",\n\t\t\t\t\t\"晋宁县\",\n\t\t\t\t\t\"富民县\",\n\t\t\t\t\t\"宜良县\",\n\t\t\t\t\t\"嵩明县\",\n\t\t\t\t\t\"石林彝族自治县\",\n\t\t\t\t\t\"禄劝彝族苗族自治县\",\n\t\t\t\t\t\"寻甸回族彝族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"曲靖市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"麒麟区\",\n\t\t\t\t\t\"宣威市\",\n\t\t\t\t\t\"马龙县\",\n\t\t\t\t\t\"沾益县\",\n\t\t\t\t\t\"富源县\",\n\t\t\t\t\t\"罗平县\",\n\t\t\t\t\t\"师宗县\",\n\t\t\t\t\t\"陆良县\",\n\t\t\t\t\t\"会泽县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"玉溪市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"红塔区\",\n\t\t\t\t\t\"江川县\",\n\t\t\t\t\t\"澄江县\",\n\t\t\t\t\t\"通海县\",\n\t\t\t\t\t\"华宁县\",\n\t\t\t\t\t\"易门县\",\n\t\t\t\t\t\"峨山彝族自治县\",\n\t\t\t\t\t\"新平彝族傣族自治县\",\n\t\t\t\t\t\"元江哈尼族彝族傣族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"保山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"隆阳区\",\n\t\t\t\t\t\"施甸县\",\n\t\t\t\t\t\"腾冲县\",\n\t\t\t\t\t\"龙陵县\",\n\t\t\t\t\t\"昌宁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"昭通市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"昭阳区\",\n\t\t\t\t\t\"鲁甸县\",\n\t\t\t\t\t\"巧家县\",\n\t\t\t\t\t\"盐津县\",\n\t\t\t\t\t\"大关县\",\n\t\t\t\t\t\"永善县\",\n\t\t\t\t\t\"绥江县\",\n\t\t\t\t\t\"镇雄县\",\n\t\t\t\t\t\"彝良县\",\n\t\t\t\t\t\"威信县\",\n\t\t\t\t\t\"水富县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"丽江市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"古城区\",\n\t\t\t\t\t\"永胜县\",\n\t\t\t\t\t\"华坪县\",\n\t\t\t\t\t\"玉龙纳西族自治县\",\n\t\t\t\t\t\"宁蒗彝族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"普洱市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"思茅区\",\n\t\t\t\t\t\"普洱哈尼族彝族自治县\",\n\t\t\t\t\t\"墨江哈尼族自治县\",\n\t\t\t\t\t\"景东彝族自治县\",\n\t\t\t\t\t\"景谷傣族彝族自治县\",\n\t\t\t\t\t\"镇沅彝族哈尼族拉祜族自治县\",\n\t\t\t\t\t\"江城哈尼族彝族自治县\",\n\t\t\t\t\t\"孟连傣族拉祜族佤族自治县\",\n\t\t\t\t\t\"澜沧拉祜族自治县\",\n\t\t\t\t\t\"西盟佤族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"临沧市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"临翔区\",\n\t\t\t\t\t\"凤庆县\",\n\t\t\t\t\t\"云县\",\n\t\t\t\t\t\"永德县\",\n\t\t\t\t\t\"镇康县\",\n\t\t\t\t\t\"双江拉祜族佤族布朗族傣族自治县\",\n\t\t\t\t\t\"耿马傣族佤族自治县\",\n\t\t\t\t\t\"沧源佤族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"德宏傣族景颇族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"潞西市\",\n\t\t\t\t\t\"瑞丽市\",\n\t\t\t\t\t\"梁河县\",\n\t\t\t\t\t\"盈江县\",\n\t\t\t\t\t\"陇川县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"怒江傈僳族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"泸水县\",\n\t\t\t\t\t\"福贡县\",\n\t\t\t\t\t\"贡山独龙族怒族自治县\",\n\t\t\t\t\t\"兰坪白族普米族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"迪庆藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"香格里拉县\",\n\t\t\t\t\t\"德钦县\",\n\t\t\t\t\t\"维西傈僳族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"大理白族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"大理市\",\n\t\t\t\t\t\"祥云县\",\n\t\t\t\t\t\"宾川县\",\n\t\t\t\t\t\"弥渡县\",\n\t\t\t\t\t\"永平县\",\n\t\t\t\t\t\"云龙县\",\n\t\t\t\t\t\"洱源县\",\n\t\t\t\t\t\"剑川县\",\n\t\t\t\t\t\"鹤庆县\",\n\t\t\t\t\t\"漾濞彝族自治县\",\n\t\t\t\t\t\"南涧彝族自治县\",\n\t\t\t\t\t\"巍山彝族回族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"楚雄彝族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"楚雄市\",\n\t\t\t\t\t\"双柏县\",\n\t\t\t\t\t\"牟定县\",\n\t\t\t\t\t\"南华县\",\n\t\t\t\t\t\"姚安县\",\n\t\t\t\t\t\"大姚县\",\n\t\t\t\t\t\"永仁县\",\n\t\t\t\t\t\"元谋县\",\n\t\t\t\t\t\"武定县\",\n\t\t\t\t\t\"禄丰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"红河哈尼族彝族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"蒙自县\",\n\t\t\t\t\t\"个旧市\",\n\t\t\t\t\t\"开远市\",\n\t\t\t\t\t\"绿春县\",\n\t\t\t\t\t\"建水县\",\n\t\t\t\t\t\"石屏县\",\n\t\t\t\t\t\"弥勒县\",\n\t\t\t\t\t\"泸西县\",\n\t\t\t\t\t\"元阳县\",\n\t\t\t\t\t\"红河县\",\n\t\t\t\t\t\"金平苗族瑶族傣族自治县\",\n\t\t\t\t\t\"河口瑶族自治县\",\n\t\t\t\t\t\"屏边苗族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"文山壮族苗族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"文山县\",\n\t\t\t\t\t\"砚山县\",\n\t\t\t\t\t\"西畴县\",\n\t\t\t\t\t\"麻栗坡县\",\n\t\t\t\t\t\"马关县\",\n\t\t\t\t\t\"丘北县\",\n\t\t\t\t\t\"广南县\",\n\t\t\t\t\t\"富宁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"西双版纳傣族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"景洪市\",\n\t\t\t\t\t\"勐海县\",\n\t\t\t\t\t\"勐腊县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"西藏\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"拉萨市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城关区\",\n\t\t\t\t\t\"林周县\",\n\t\t\t\t\t\"当雄县\",\n\t\t\t\t\t\"尼木县\",\n\t\t\t\t\t\"曲水县\",\n\t\t\t\t\t\"堆龙德庆县\",\n\t\t\t\t\t\"达孜县\",\n\t\t\t\t\t\"墨竹工卡县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"那曲地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"那曲县\",\n\t\t\t\t\t\"嘉黎县\",\n\t\t\t\t\t\"比如县\",\n\t\t\t\t\t\"聂荣县\",\n\t\t\t\t\t\"安多县\",\n\t\t\t\t\t\"申扎县\",\n\t\t\t\t\t\"索县\",\n\t\t\t\t\t\"班戈县\",\n\t\t\t\t\t\"巴青县\",\n\t\t\t\t\t\"尼玛县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"昌都地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"昌都县\",\n\t\t\t\t\t\"江达县\",\n\t\t\t\t\t\"贡觉县\",\n\t\t\t\t\t\"类乌齐县\",\n\t\t\t\t\t\"丁青县\",\n\t\t\t\t\t\"察雅县\",\n\t\t\t\t\t\"八宿县\",\n\t\t\t\t\t\"左贡县\",\n\t\t\t\t\t\"芒康县\",\n\t\t\t\t\t\"洛隆县\",\n\t\t\t\t\t\"边坝县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"林芝地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"林芝县\",\n\t\t\t\t\t\"工布江达县\",\n\t\t\t\t\t\"米林县\",\n\t\t\t\t\t\"墨脱县\",\n\t\t\t\t\t\"波密县\",\n\t\t\t\t\t\"察隅县\",\n\t\t\t\t\t\"朗县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"山南地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"乃东县\",\n\t\t\t\t\t\"扎囊县\",\n\t\t\t\t\t\"贡嘎县\",\n\t\t\t\t\t\"桑日县\",\n\t\t\t\t\t\"琼结县\",\n\t\t\t\t\t\"曲松县\",\n\t\t\t\t\t\"措美县\",\n\t\t\t\t\t\"洛扎县\",\n\t\t\t\t\t\"加查县\",\n\t\t\t\t\t\"隆子县\",\n\t\t\t\t\t\"错那县\",\n\t\t\t\t\t\"浪卡子县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"日喀则地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"日喀则市\",\n\t\t\t\t\t\"南木林县\",\n\t\t\t\t\t\"江孜县\",\n\t\t\t\t\t\"定日县\",\n\t\t\t\t\t\"萨迦县\",\n\t\t\t\t\t\"拉孜县\",\n\t\t\t\t\t\"昂仁县\",\n\t\t\t\t\t\"谢通门县\",\n\t\t\t\t\t\"白朗县\",\n\t\t\t\t\t\"仁布县\",\n\t\t\t\t\t\"康马县\",\n\t\t\t\t\t\"定结县\",\n\t\t\t\t\t\"仲巴县\",\n\t\t\t\t\t\"亚东县\",\n\t\t\t\t\t\"吉隆县\",\n\t\t\t\t\t\"聂拉木县\",\n\t\t\t\t\t\"萨嘎县\",\n\t\t\t\t\t\"岗巴县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阿里地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"噶尔县\",\n\t\t\t\t\t\"普兰县\",\n\t\t\t\t\t\"札达县\",\n\t\t\t\t\t\"日土县\",\n\t\t\t\t\t\"革吉县\",\n\t\t\t\t\t\"改则县\",\n\t\t\t\t\t\"措勤县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"陕西省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"西安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"莲湖区\",\n\t\t\t\t\t\"新城区\",\n\t\t\t\t\t\"碑林区\",\n\t\t\t\t\t\"雁塔区\",\n\t\t\t\t\t\"灞桥区\",\n\t\t\t\t\t\"未央区\",\n\t\t\t\t\t\"阎良区\",\n\t\t\t\t\t\"临潼区\",\n\t\t\t\t\t\"长安区\",\n\t\t\t\t\t\"高陵县\",\n\t\t\t\t\t\"蓝田县\",\n\t\t\t\t\t\"户县\",\n\t\t\t\t\t\"周至县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"铜川市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"耀州区\",\n\t\t\t\t\t\"王益区\",\n\t\t\t\t\t\"印台区\",\n\t\t\t\t\t\"宜君县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"宝鸡市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"渭滨区\",\n\t\t\t\t\t\"金台区\",\n\t\t\t\t\t\"陈仓区\",\n\t\t\t\t\t\"岐山县\",\n\t\t\t\t\t\"凤翔县\",\n\t\t\t\t\t\"陇县\",\n\t\t\t\t\t\"太白县\",\n\t\t\t\t\t\"麟游县\",\n\t\t\t\t\t\"扶风县\",\n\t\t\t\t\t\"千阳县\",\n\t\t\t\t\t\"眉县\",\n\t\t\t\t\t\"凤县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"咸阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"秦都区\",\n\t\t\t\t\t\"渭城区\",\n\t\t\t\t\t\"杨陵区\",\n\t\t\t\t\t\"兴平市\",\n\t\t\t\t\t\"礼泉县\",\n\t\t\t\t\t\"泾阳县\",\n\t\t\t\t\t\"永寿县\",\n\t\t\t\t\t\"三原县\",\n\t\t\t\t\t\"彬县\",\n\t\t\t\t\t\"旬邑县\",\n\t\t\t\t\t\"长武县\",\n\t\t\t\t\t\"乾县\",\n\t\t\t\t\t\"武功县\",\n\t\t\t\t\t\"淳化县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"渭南市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"临渭区\",\n\t\t\t\t\t\"韩城市\",\n\t\t\t\t\t\"华阴市\",\n\t\t\t\t\t\"蒲城县\",\n\t\t\t\t\t\"潼关县\",\n\t\t\t\t\t\"白水县\",\n\t\t\t\t\t\"澄城县\",\n\t\t\t\t\t\"华县\",\n\t\t\t\t\t\"合阳县\",\n\t\t\t\t\t\"富平县\",\n\t\t\t\t\t\"大荔县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"延安市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"宝塔区\",\n\t\t\t\t\t\"安塞县\",\n\t\t\t\t\t\"洛川县\",\n\t\t\t\t\t\"子长县\",\n\t\t\t\t\t\"黄陵县\",\n\t\t\t\t\t\"延川县\",\n\t\t\t\t\t\"富县\",\n\t\t\t\t\t\"延长县\",\n\t\t\t\t\t\"甘泉县\",\n\t\t\t\t\t\"宜川县\",\n\t\t\t\t\t\"志丹县\",\n\t\t\t\t\t\"黄龙县\",\n\t\t\t\t\t\"吴起县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"汉中市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"汉台区\",\n\t\t\t\t\t\"留坝县\",\n\t\t\t\t\t\"镇巴县\",\n\t\t\t\t\t\"城固县\",\n\t\t\t\t\t\"南郑县\",\n\t\t\t\t\t\"洋县\",\n\t\t\t\t\t\"宁强县\",\n\t\t\t\t\t\"佛坪县\",\n\t\t\t\t\t\"勉县\",\n\t\t\t\t\t\"西乡县\",\n\t\t\t\t\t\"略阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"榆林市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"榆阳区\",\n\t\t\t\t\t\"清涧县\",\n\t\t\t\t\t\"绥德县\",\n\t\t\t\t\t\"神木县\",\n\t\t\t\t\t\"佳县\",\n\t\t\t\t\t\"府谷县\",\n\t\t\t\t\t\"子洲县\",\n\t\t\t\t\t\"靖边县\",\n\t\t\t\t\t\"横山县\",\n\t\t\t\t\t\"米脂县\",\n\t\t\t\t\t\"吴堡县\",\n\t\t\t\t\t\"定边县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"安康市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"汉滨区\",\n\t\t\t\t\t\"紫阳县\",\n\t\t\t\t\t\"岚皋县\",\n\t\t\t\t\t\"旬阳县\",\n\t\t\t\t\t\"镇坪县\",\n\t\t\t\t\t\"平利县\",\n\t\t\t\t\t\"石泉县\",\n\t\t\t\t\t\"宁陕县\",\n\t\t\t\t\t\"白河县\",\n\t\t\t\t\t\"汉阴县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"商洛市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"商州区\",\n\t\t\t\t\t\"镇安县\",\n\t\t\t\t\t\"山阳县\",\n\t\t\t\t\t\"洛南县\",\n\t\t\t\t\t\"商南县\",\n\t\t\t\t\t\"丹凤县\",\n\t\t\t\t\t\"柞水县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"甘肃省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"兰州市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城关区\",\n\t\t\t\t\t\"七里河区\",\n\t\t\t\t\t\"西固区\",\n\t\t\t\t\t\"安宁区\",\n\t\t\t\t\t\"红古区\",\n\t\t\t\t\t\"永登县\",\n\t\t\t\t\t\"皋兰县\",\n\t\t\t\t\t\"榆中县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"嘉峪关市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"嘉峪关市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"金昌市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"金川区\",\n\t\t\t\t\t\"永昌县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"白银市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"白银区\",\n\t\t\t\t\t\"平川区\",\n\t\t\t\t\t\"靖远县\",\n\t\t\t\t\t\"会宁县\",\n\t\t\t\t\t\"景泰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"天水市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"清水县\",\n\t\t\t\t\t\"秦安县\",\n\t\t\t\t\t\"甘谷县\",\n\t\t\t\t\t\"武山县\",\n\t\t\t\t\t\"张家川回族自治县\",\n\t\t\t\t\t\"北道区\",\n\t\t\t\t\t\"秦城区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"武威市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"凉州区\",\n\t\t\t\t\t\"民勤县\",\n\t\t\t\t\t\"古浪县\",\n\t\t\t\t\t\"天祝藏族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"酒泉市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"肃州区\",\n\t\t\t\t\t\"玉门市\",\n\t\t\t\t\t\"敦煌市\",\n\t\t\t\t\t\"金塔县\",\n\t\t\t\t\t\"肃北蒙古族自治县\",\n\t\t\t\t\t\"阿克塞哈萨克族自治县\",\n\t\t\t\t\t\"安西县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"张掖市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"甘州区\",\n\t\t\t\t\t\"民乐县\",\n\t\t\t\t\t\"临泽县\",\n\t\t\t\t\t\"高台县\",\n\t\t\t\t\t\"山丹县\",\n\t\t\t\t\t\"肃南裕固族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"庆阳市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"西峰区\",\n\t\t\t\t\t\"庆城县\",\n\t\t\t\t\t\"环县\",\n\t\t\t\t\t\"华池县\",\n\t\t\t\t\t\"合水县\",\n\t\t\t\t\t\"正宁县\",\n\t\t\t\t\t\"宁县\",\n\t\t\t\t\t\"镇原县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"平凉市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"崆峒区\",\n\t\t\t\t\t\"泾川县\",\n\t\t\t\t\t\"灵台县\",\n\t\t\t\t\t\"崇信县\",\n\t\t\t\t\t\"华亭县\",\n\t\t\t\t\t\"庄浪县\",\n\t\t\t\t\t\"静宁县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"定西市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"安定区\",\n\t\t\t\t\t\"通渭县\",\n\t\t\t\t\t\"临洮县\",\n\t\t\t\t\t\"漳县\",\n\t\t\t\t\t\"岷县\",\n\t\t\t\t\t\"渭源县\",\n\t\t\t\t\t\"陇西县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"陇南市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"武都区\",\n\t\t\t\t\t\"成县\",\n\t\t\t\t\t\"宕昌县\",\n\t\t\t\t\t\"康县\",\n\t\t\t\t\t\"文县\",\n\t\t\t\t\t\"西和县\",\n\t\t\t\t\t\"礼县\",\n\t\t\t\t\t\"两当县\",\n\t\t\t\t\t\"徽县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"临夏回族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"临夏市\",\n\t\t\t\t\t\"临夏县\",\n\t\t\t\t\t\"康乐县\",\n\t\t\t\t\t\"永靖县\",\n\t\t\t\t\t\"广河县\",\n\t\t\t\t\t\"和政县\",\n\t\t\t\t\t\"东乡族自治县\",\n\t\t\t\t\t\"积石山保安族东乡族撒拉族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"甘南藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"合作市\",\n\t\t\t\t\t\"临潭县\",\n\t\t\t\t\t\"卓尼县\",\n\t\t\t\t\t\"舟曲县\",\n\t\t\t\t\t\"迭部县\",\n\t\t\t\t\t\"玛曲县\",\n\t\t\t\t\t\"碌曲县\",\n\t\t\t\t\t\"夏河县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"青海省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"西宁市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"城中区\",\n\t\t\t\t\t\"城东区\",\n\t\t\t\t\t\"城西区\",\n\t\t\t\t\t\"城北区\",\n\t\t\t\t\t\"湟源县\",\n\t\t\t\t\t\"湟中县\",\n\t\t\t\t\t\"大通回族土族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"海东地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"平安县\",\n\t\t\t\t\t\"乐都县\",\n\t\t\t\t\t\"民和回族土族自治县\",\n\t\t\t\t\t\"互助土族自治县\",\n\t\t\t\t\t\"化隆回族自治县\",\n\t\t\t\t\t\"循化撒拉族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"海北藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"海晏县\",\n\t\t\t\t\t\"祁连县\",\n\t\t\t\t\t\"刚察县\",\n\t\t\t\t\t\"门源回族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"海南藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"共和县\",\n\t\t\t\t\t\"同德县\",\n\t\t\t\t\t\"贵德县\",\n\t\t\t\t\t\"兴海县\",\n\t\t\t\t\t\"贵南县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"黄南藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"同仁县\",\n\t\t\t\t\t\"尖扎县\",\n\t\t\t\t\t\"泽库县\",\n\t\t\t\t\t\"河南蒙古族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"果洛藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"玛沁县\",\n\t\t\t\t\t\"班玛县\",\n\t\t\t\t\t\"甘德县\",\n\t\t\t\t\t\"达日县\",\n\t\t\t\t\t\"久治县\",\n\t\t\t\t\t\"玛多县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"玉树藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"玉树县\",\n\t\t\t\t\t\"杂多县\",\n\t\t\t\t\t\"称多县\",\n\t\t\t\t\t\"治多县\",\n\t\t\t\t\t\"囊谦县\",\n\t\t\t\t\t\"曲麻莱县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"海西蒙古族藏族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"德令哈市\",\n\t\t\t\t\t\"格尔木市\",\n\t\t\t\t\t\"乌兰县\",\n\t\t\t\t\t\"都兰县\",\n\t\t\t\t\t\"天峻县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"宁夏\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"银川市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"兴庆区\",\n\t\t\t\t\t\"西夏区\",\n\t\t\t\t\t\"金凤区\",\n\t\t\t\t\t\"灵武市\",\n\t\t\t\t\t\"永宁县\",\n\t\t\t\t\t\"贺兰县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"石嘴山市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"大武口区\",\n\t\t\t\t\t\"惠农区\",\n\t\t\t\t\t\"平罗县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"吴忠市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"利通区\",\n\t\t\t\t\t\"青铜峡市\",\n\t\t\t\t\t\"盐池县\",\n\t\t\t\t\t\"同心县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"固原市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"原州区\",\n\t\t\t\t\t\"西吉县\",\n\t\t\t\t\t\"隆德县\",\n\t\t\t\t\t\"泾源县\",\n\t\t\t\t\t\"彭阳县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"中卫市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"沙坡头区\",\n\t\t\t\t\t\"中宁县\",\n\t\t\t\t\t\"海原县\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"新疆\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"乌鲁木齐市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"天山区\",\n\t\t\t\t\t\"沙依巴克区\",\n\t\t\t\t\t\"新市区\",\n\t\t\t\t\t\"水磨沟区\",\n\t\t\t\t\t\"头屯河区\",\n\t\t\t\t\t\"达坂城区\",\n\t\t\t\t\t\"东山区\",\n\t\t\t\t\t\"乌鲁木齐县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"克拉玛依市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"克拉玛依区\",\n\t\t\t\t\t\"独山子区\",\n\t\t\t\t\t\"白碱滩区\",\n\t\t\t\t\t\"乌尔禾区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"吐鲁番地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"吐鲁番市\",\n\t\t\t\t\t\"托克逊县\",\n\t\t\t\t\t\"鄯善县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"哈密地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"哈密市\",\n\t\t\t\t\t\"伊吾县\",\n\t\t\t\t\t\"巴里坤哈萨克自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"和田地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"和田市\",\n\t\t\t\t\t\"和田县\",\n\t\t\t\t\t\"洛浦县\",\n\t\t\t\t\t\"民丰县\",\n\t\t\t\t\t\"皮山县\",\n\t\t\t\t\t\"策勒县\",\n\t\t\t\t\t\"于田县\",\n\t\t\t\t\t\"墨玉县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阿克苏地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"阿克苏市\",\n\t\t\t\t\t\"温宿县\",\n\t\t\t\t\t\"沙雅县\",\n\t\t\t\t\t\"拜城县\",\n\t\t\t\t\t\"阿瓦提县\",\n\t\t\t\t\t\"库车县\",\n\t\t\t\t\t\"柯坪县\",\n\t\t\t\t\t\"新和县\",\n\t\t\t\t\t\"乌什县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"喀什地区\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"喀什市\",\n\t\t\t\t\t\"巴楚县\",\n\t\t\t\t\t\"泽普县\",\n\t\t\t\t\t\"伽师县\",\n\t\t\t\t\t\"叶城县\",\n\t\t\t\t\t\"岳普湖县\",\n\t\t\t\t\t\"疏勒县\",\n\t\t\t\t\t\"麦盖提县\",\n\t\t\t\t\t\"英吉沙县\",\n\t\t\t\t\t\"莎车县\",\n\t\t\t\t\t\"疏附县\",\n\t\t\t\t\t\"塔什库尔干塔吉克自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"克孜勒苏柯尔克孜自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"阿图什市\",\n\t\t\t\t\t\"阿合奇县\",\n\t\t\t\t\t\"乌恰县\",\n\t\t\t\t\t\"阿克陶县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"巴音郭楞蒙古自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"库尔勒市\",\n\t\t\t\t\t\"和静县\",\n\t\t\t\t\t\"尉犁县\",\n\t\t\t\t\t\"和硕县\",\n\t\t\t\t\t\"且末县\",\n\t\t\t\t\t\"博湖县\",\n\t\t\t\t\t\"轮台县\",\n\t\t\t\t\t\"若羌县\",\n\t\t\t\t\t\"焉耆回族自治县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"昌吉回族自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"昌吉市\",\n\t\t\t\t\t\"阜康市\",\n\t\t\t\t\t\"奇台县\",\n\t\t\t\t\t\"玛纳斯县\",\n\t\t\t\t\t\"吉木萨尔县\",\n\t\t\t\t\t\"呼图壁县\",\n\t\t\t\t\t\"木垒哈萨克自治县\",\n\t\t\t\t\t\"米泉市\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"博尔塔拉蒙古自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"博乐市\",\n\t\t\t\t\t\"精河县\",\n\t\t\t\t\t\"温泉县\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"石河子\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"石河子\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"阿拉尔\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"阿拉尔\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"图木舒克\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"图木舒克\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"五家渠\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"五家渠\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"伊犁哈萨克自治州\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"伊宁市\",\n\t\t\t\t\t\"奎屯市\",\n\t\t\t\t\t\"伊宁县\",\n\t\t\t\t\t\"特克斯县\",\n\t\t\t\t\t\"尼勒克县\",\n\t\t\t\t\t\"昭苏县\",\n\t\t\t\t\t\"新源县\",\n\t\t\t\t\t\"霍城县\",\n\t\t\t\t\t\"巩留县\",\n\t\t\t\t\t\"察布查尔锡伯自治县\",\n\t\t\t\t\t\"塔城地区\",\n\t\t\t\t\t\"阿勒泰地区\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"台湾省\",\n\t\t\"city\": [{\n\t\t\t\t\"name\": \"台北市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"内湖区\",\n\t\t\t\t\t\"南港区\",\n\t\t\t\t\t\"中正区\",\n\t\t\t\t\t\"万华区\",\n\t\t\t\t\t\"大同区\",\n\t\t\t\t\t\"中山区\",\n\t\t\t\t\t\"松山区\",\n\t\t\t\t\t\"大安区\",\n\t\t\t\t\t\"信义区\",\n\t\t\t\t\t\"文山区\",\n\t\t\t\t\t\"士林区\",\n\t\t\t\t\t\"北投区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"新北市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"板桥区\",\n\t\t\t\t\t\"汐止区\",\n\t\t\t\t\t\"新店区\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"桃园市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"其他\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"台中市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"其他\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"台南市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"其他\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"name\": \"高雄市\",\n\t\t\t\t\"area\": [\n\t\t\t\t\t\"其他\"\n\t\t\t\t]\n\t\t\t}\n\t\t]\n\t},\n\t{\n\t\t\"name\": \"澳门\",\n\t\t\"city\": [{\n\t\t\t\"name\": \"澳门\",\n\t\t\t\"area\": [\n\t\t\t\t\"花地玛堂区\",\n\t\t\t\t\"圣安多尼堂区\",\n\t\t\t\t\"大堂区\",\n\t\t\t\t\"望德堂区\",\n\t\t\t\t\"风顺堂区\",\n\t\t\t\t\"嘉模堂区\",\n\t\t\t\t\"圣方济各堂区\",\n\t\t\t\t\"路凼\"\n\t\t\t]\n\t\t}]\n\t},\n\t{\n\t\t\"name\": \"香港\",\n\t\t\"city\": [{\n\t\t\t\"name\": \"香港\",\n\t\t\t\"area\": [\n\t\t\t\t\"深水埗区\",\n\t\t\t\t\"油尖旺区\",\n\t\t\t\t\"九龙城区\",\n\t\t\t\t\"黄大仙区\",\n\t\t\t\t\"观塘区\",\n\t\t\t\t\"北区\",\n\t\t\t\t\"大埔区\",\n\t\t\t\t\"沙田区\",\n\t\t\t\t\"西贡区\",\n\t\t\t\t\"元朗区\",\n\t\t\t\t\"屯门区\",\n\t\t\t\t\"荃湾区\",\n\t\t\t\t\"葵青区\",\n\t\t\t\t\"离岛区\",\n\t\t\t\t\"中西区\",\n\t\t\t\t\"湾仔区\",\n\t\t\t\t\"东区\",\n\t\t\t\t\"南区\"\n\t\t\t]\n\t\t}]\n\t}\n]\n", "// mescroll-body 和 mescroll-uni 通用\nconst MescrollMixin = {\n\tdata() {\n\t\treturn {\n\t\t\tmescroll: null //mescroll实例对象\n\t\t}\n\t},\n\t// 注册系统自带的下拉刷新 (配置down.native为true时生效, 还需在pages配置enablePullDownRefresh:true;详请参考mescroll-native的案例)\n\tonPullDownRefresh(){\n\t\tthis.mescroll && this.mescroll.onPullDownRefresh();\n\t},\n\t// 注册列表滚动事件,用于判定在顶部可下拉刷新,在指定位置可显示隐藏回到顶部按钮 (此方法为页面生命周期,无法在子组件中触发, 仅在mescroll-body生效)\n\tonPageScroll(e) {\n\t\tthis.mescroll && this.mescroll.onPageScroll(e);\n\t},\n\t// 注册滚动到底部的事件,用于上拉加载 (此方法为页面生命周期,无法在子组件中触发, 仅在mescroll-body生效)\n\tonReachBottom() {\n\t\tthis.mescroll && this.mescroll.onReachBottom();\n\t},\n\tmethods: {\n\t\t// mescroll组件初始化的回调,可获取到mescroll对象\n\t\tmescrollInit(mescroll) {\n\t\t\tthis.mescroll = mescroll;\n\t\t\tthis.mescrollInitByRef(); // 兼容字节跳动小程序\n\t\t},\n\t\t// 以ref的方式初始化mescroll对象 (兼容字节跳动小程序)\n\t\tmescrollInitByRef() {\n\t\t\tif(!this.mescroll || !this.mescroll.resetUpScroll){\n\t\t\t\tlet mescrollRef = this.$refs.mescrollRef;\n\t\t\t\tif(mescrollRef) this.mescroll = mescrollRef.mescroll\n\t\t\t}\n\t\t},\n\t\t// 下拉刷新的回调 (mixin默认resetUpScroll)\n\t\tdownCallback() {\n\t\t\tif(this.mescroll.optUp.use){\n\t\t\t\tthis.mescroll.resetUpScroll()\n\t\t\t}else{\n\t\t\t\tsetTimeout(()=>{\n\t\t\t\t\tthis.mescroll.endSuccess();\n\t\t\t\t}, 500)\n\t\t\t}\n\t\t},\n\t\t// 上拉加载的回调\n\t\tupCallback() {\n\t\t\t// mixin默认延时500自动结束加载\n\t\t\tsetTimeout(()=>{\n\t\t\t\tthis.mescroll.endErr();\n\t\t\t}, 500)\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.mescrollInitByRef(); // 兼容字节跳动小程序, 避免未设置@init或@init此时未能取到ref的情况\n\t}\n\t\n}\n\nexport default MescrollMixin;\n", "/* mescroll\n * version 1.3.7\n * 2021-04-12 wenju\n * https://www.mescroll.com\n */\n\nexport default function MeScroll(options, isScrollBody) {\n\tlet me = this;\n\tme.version = '1.3.7'; // mescroll版本号\n\tme.options = options || {}; // 配置\n\tme.isScrollBody = isScrollBody || false; // 滚动区域是否为原生页面滚动; 默认为scroll-view\n\n\tme.isDownScrolling = false; // 是否在执行下拉刷新的回调\n\tme.isUpScrolling = false; // 是否在执行上拉加载的回调\n\tlet hasDownCallback = me.options.down && me.options.down.callback; // 是否配置了down的callback\n\n\t// 初始化下拉刷新\n\tme.initDownScroll();\n\t// 初始化上拉加载,则初始化\n\tme.initUpScroll();\n\n\t// 自动加载\n\tsetTimeout(function() { // 待主线程执行完毕再执行,避免new MeScroll未初始化,在回调获取不到mescroll的实例\n\t\t// 自动触发下拉刷新 (只有配置了down的callback才自动触发下拉刷新)\n\t\tif ((me.optDown.use || me.optDown.native) && me.optDown.auto && hasDownCallback) {\n\t\t\tif (me.optDown.autoShowLoading) {\n\t\t\t\tme.triggerDownScroll(); // 显示下拉进度,执行下拉回调\n\t\t\t} else {\n\t\t\t\tme.optDown.callback && me.optDown.callback(me); // 不显示下拉进度,直接执行下拉回调\n\t\t\t}\n\t\t}\n\t\t// 自动触发上拉加载\n\t\tif(!me.isUpAutoLoad){ // 部分小程序(头条小程序)emit是异步, 会导致isUpAutoLoad判断有误, 先延时确保先执行down的callback,再执行up的callback\n\t\t\tsetTimeout(function(){\n\t\t\t\tme.optUp.use && me.optUp.auto && !me.isUpAutoLoad && me.triggerUpScroll();\n\t\t\t},100)\n\t\t}\n\t}, 30); // 需让me.optDown.inited和me.optUp.inited先执行\n}\n\n/* 配置参数:下拉刷新 */\nMeScroll.prototype.extendDownScroll = function(optDown) {\n\t// 下拉刷新的配置\n\tMeScroll.extend(optDown, {\n\t\tuse: true, // 是否启用下拉刷新; 默认true\n\t\tauto: true, // 是否在初始化完毕之后自动执行下拉刷新的回调; 默认true\n\t\tnative: false, // 是否使用系统自带的下拉刷新; 默认false; 仅mescroll-body生效 (值为true时,还需在pages配置enablePullDownRefresh:true;详请参考mescroll-native的案例)\n\t\tautoShowLoading: false, // 如果设置auto=true(在初始化完毕之后自动执行下拉刷新的回调),那么是否显示下拉刷新的进度; 默认false\n\t\tisLock: false, // 是否锁定下拉刷新,默认false;\n\t\toffset: 80, // 在列表顶部,下拉大于80px,松手即可触发下拉刷新的回调\n\t\tstartTop: 100, // scroll-view快速滚动到顶部时,此时的scroll-top可能大于0, 此值用于控制最大的误差\n\t\tinOffsetRate: 1, // 在列表顶部,下拉的距离小于offset时,改变下拉区域高度比例;值小于1且越接近0,高度变化越小,表现为越往下越难拉\n\t\toutOffsetRate: 0.2, // 在列表顶部,下拉的距离大于offset时,改变下拉区域高度比例;值小于1且越接近0,高度变化越小,表现为越往下越难拉\n\t\tbottomOffset: 20, // 当手指touchmove位置在距离body底部20px范围内的时候结束上拉刷新,避免Webview嵌套导致touchend事件不执行\n\t\tminAngle: 45, // 向下滑动最少偏移的角度,取值区间  [0,90];默认45度,即向下滑动的角度大于45度则触发下拉;而小于45度,将不触发下拉,避免与左右滑动的轮播等组件冲突;\n\t\ttextInOffset: '下拉刷新', // 下拉的距离在offset范围内的提示文本\n\t\ttextOutOffset: '释放更新', // 下拉的距离大于offset范围的提示文本\n\t\ttextLoading: '加载中 ...', // 加载中的提示文本\n\t\ttextSuccess: '加载成功', // 加载成功的文本\n\t\ttextErr: '加载失败', // 加载失败的文本\n\t\tbeforeEndDelay: 0, // 延时结束的时长 (显示加载成功/失败的时长, android小程序设置此项结束下拉会卡顿, 配置后请注意测试)\n\t\tbgColor: \"transparent\", // 背景颜色 (建议在pages.json中再设置一下backgroundColorTop)\n\t\ttextColor: \"gray\", // 文本颜色 (当bgColor配置了颜色,而textColor未配置时,则textColor会默认为白色)\n\t\tinited: null, // 下拉刷新初始化完毕的回调\n\t\tinOffset: null, // 下拉的距离进入offset范围内那一刻的回调\n\t\toutOffset: null, // 下拉的距离大于offset那一刻的回调\n\t\tonMoving: null, // 下拉过程中的回调,滑动过程一直在执行; rate下拉区域当前高度与指定距离的比值(inOffset: rate<1; outOffset: rate>=1); downHight当前下拉区域的高度\n\t\tbeforeLoading: null, // 准备触发下拉刷新的回调: 如果return true,将不触发showLoading和callback回调; 常用来完全自定义下拉刷新, 参考案例【淘宝 v6.8.0】\n\t\tshowLoading: null, // 显示下拉刷新进度的回调\n\t\tafterLoading: null, // 显示下拉刷新进度的回调之后,马上要执行的代码 (如: 在wxs中使用)\n\t\tbeforeEndDownScroll: null, // 准备结束下拉的回调. 返回结束下拉的延时执行时间,默认0ms; 常用于结束下拉之前再显示另外一小段动画,才去隐藏下拉刷新的场景, 参考案例【dotJump】\n\t\tendDownScroll: null, // 结束下拉刷新的回调\n\t\tafterEndDownScroll: null, // 结束下拉刷新的回调,马上要执行的代码 (如: 在wxs中使用)\n\t\tcallback: function(mescroll) {\n\t\t\t// 下拉刷新的回调;默认重置上拉加载列表为第一页\n\t\t\tmescroll.resetUpScroll();\n\t\t}\n\t})\n}\n\n/* 配置参数:上拉加载 */\nMeScroll.prototype.extendUpScroll = function(optUp) {\n\t// 上拉加载的配置\n\tMeScroll.extend(optUp, {\n\t\tuse: true, // 是否启用上拉加载; 默认true\n\t\tauto: true, // 是否在初始化完毕之后自动执行上拉加载的回调; 默认true\n\t\tisLock: false, // 是否锁定上拉加载,默认false;\n\t\tisBoth: true, // 上拉加载时,如果滑动到列表顶部是否可以同时触发下拉刷新;默认true,两者可同时触发;\n\t\tcallback: null, // 上拉加载的回调;function(page,mescroll){ }\n\t\tpage: {\n\t\t\tnum: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始\n\t\t\tsize: 10, // 每页数据的数量\n\t\t\ttime: null // 加载第一页数据服务器返回的时间; 防止用户翻页时,后台新增了数据从而导致下一页数据重复;\n\t\t},\n\t\tnoMoreSize: 5, // 如果列表已无数据,可设置列表的总数量要大于等于5条才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看\n\t\toffset: 150, // 距底部多远时,触发upCallback,仅mescroll-uni生效 ( mescroll-body配置的是pages.json的 onReachBottomDistance )\n\t\ttextLoading: '加载中 ...', // 加载中的提示文本\n\t\ttextNoMore: '没有更多数据了', // 没有更多数据的提示文本\n\t\tbgColor: \"transparent\", // 背景颜色 (建议在pages.json中再设置一下backgroundColorBottom)\n\t\ttextColor: \"gray\", // 文本颜色 (当bgColor配置了颜色,而textColor未配置时,则textColor会默认为白色)\n\t\tinited: null, // 初始化完毕的回调\n\t\tshowLoading: null, // 显示加载中的回调\n\t\tshowNoMore: null, // 显示无更多数据的回调\n\t\thideUpScroll: null, // 隐藏上拉加载的回调\n\t\terrDistance: 60, // endErr的时候需往上滑动一段距离,使其往下滑动时再次触发onReachBottom,仅mescroll-body生效\n\t\ttoTop: {\n\t\t\t// 回到顶部按钮,需配置src才显示\n\t\t\tsrc: null, // 图片路径,默认null (绝对路径或网络图)\n\t\t\toffset: 1000, // 列表滚动多少距离才显示回到顶部按钮,默认1000\n\t\t\tduration: 300, // 回到顶部的动画时长,默认300ms (当值为0或300则使用系统自带回到顶部,更流畅; 其他值则通过step模拟,部分机型可能不够流畅,所以非特殊情况不建议修改此项)\n\t\t\tbtnClick: null, // 点击按钮的回调\n\t\t\tonShow: null, // 是否显示的回调\n\t\t\tzIndex: 9990, // fixed定位z-index值\n\t\t\tleft: null, // 到左边的距离, 默认null. 此项有值时,right不生效. (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx)\n\t\t\tright: 20, // 到右边的距离, 默认20 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx)\n\t\t\tbottom: 120, // 到底部的距离, 默认120 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx)\n\t\t\tsafearea: false, // bottom的偏移量是否加上底部安全区的距离, 默认false, 需要适配iPhoneX时使用 (具体的界面如果不配置此项,则取本vue的safearea值)\n\t\t\twidth: 72, // 回到顶部图标的宽度, 默认72 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx)\n\t\t\tradius: \"50%\" // 圆角, 默认\"50%\" (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx)\n\t\t},\n\t\tempty: {\n\t\t\tuse: true, // 是否显示空布局\n\t\t\ticon: null, // 图标路径\n\t\t\ttip: '~ 暂无相关数据 ~', // 提示\n\t\t\tbtnText: '', // 按钮\n\t\t\tbtnClick: null, // 点击按钮的回调\n\t\t\tonShow: null, // 是否显示的回调\n\t\t\tfixed: false, // 是否使用fixed定位,默认false; 配置fixed为true,以下的top和zIndex才生效 (transform会使fixed失效,最终会降级为absolute)\n\t\t\ttop: \"100rpx\", // fixed定位的top值 (完整的单位值,如 \"10%\"; \"100rpx\")\n\t\t\tzIndex: 99 // fixed定位z-index值\n\t\t},\n\t\tonScroll: false // 是否监听滚动事件\n\t})\n}\n\n/* 配置参数 */\nMeScroll.extend = function(userOption, defaultOption) {\n\tif (!userOption) return defaultOption;\n\tfor (let key in defaultOption) {\n\t\tif (userOption[key] == null) {\n\t\t\tlet def = defaultOption[key];\n\t\t\tif (def != null && typeof def === 'object') {\n\t\t\t\tuserOption[key] = MeScroll.extend({}, def); // 深度匹配\n\t\t\t} else {\n\t\t\t\tuserOption[key] = def;\n\t\t\t}\n\t\t} else if (typeof userOption[key] === 'object') {\n\t\t\tMeScroll.extend(userOption[key], defaultOption[key]); // 深度匹配\n\t\t}\n\t}\n\treturn userOption;\n}\n\n/* 简单判断是否配置了颜色 (非透明,非白色) */\nMeScroll.prototype.hasColor = function(color) {\n\tif(!color) return false;\n\tlet c = color.toLowerCase();\n\treturn c != \"#fff\" && c != \"#ffffff\" && c != \"transparent\" && c != \"white\"\n}\n\n/* -------初始化下拉刷新------- */\nMeScroll.prototype.initDownScroll = function() {\n\tlet me = this;\n\t// 配置参数\n\tme.optDown = me.options.down || {};\n\tif(!me.optDown.textColor && me.hasColor(me.optDown.bgColor)) me.optDown.textColor = \"#fff\"; // 当bgColor有值且textColor未设置,则textColor默认白色\n\tme.extendDownScroll(me.optDown);\n\t\n\t// 如果是mescroll-body且配置了native,则禁止自定义的下拉刷新\n\tif(me.isScrollBody && me.optDown.native){\n\t\tme.optDown.use = false\n\t}else{\n\t\tme.optDown.native = false // 仅mescroll-body支持,mescroll-uni不支持\n\t}\n\t\n\tme.downHight = 0; // 下拉区域的高度\n\n\t// 在页面中加入下拉布局\n\tif (me.optDown.use && me.optDown.inited) {\n\t\t// 初始化完毕的回调\n\t\tsetTimeout(function() { // 待主线程执行完毕再执行,避免new MeScroll未初始化,在回调获取不到mescroll的实例\n\t\t\tme.optDown.inited(me);\n\t\t}, 0)\n\t}\n}\n\n/* 列表touchstart事件 */\nMeScroll.prototype.touchstartEvent = function(e) {\n\tif (!this.optDown.use) return;\n\n\tthis.startPoint = this.getPoint(e); // 记录起点\n\tthis.startTop = this.getScrollTop(); // 记录此时的滚动条位置\n\tthis.startAngle = 0; // 初始角度\n\tthis.lastPoint = this.startPoint; // 重置上次move的点\n\tthis.maxTouchmoveY = this.getBodyHeight() - this.optDown.bottomOffset; // 手指触摸的最大范围(写在touchstart避免body获取高度为0的情况)\n\tthis.inTouchend = false; // 标记不是touchend\n}\n\n/* 列表touchmove事件 */\nMeScroll.prototype.touchmoveEvent = function(e) {\n\tif (!this.optDown.use) return;\n\tlet me = this;\n\n\tlet scrollTop = me.getScrollTop(); // 当前滚动条的距离\n\tlet curPoint = me.getPoint(e); // 当前点\n\n\tlet moveY = curPoint.y - me.startPoint.y; // 和起点比,移动的距离,大于0向下拉,小于0向上拉\n\n\t// 向下拉 && 在顶部\n\t// mescroll-body,直接判定在顶部即可\n\t// scroll-view在滚动时不会触发touchmove,当触顶/底/左/右时,才会触发touchmove\n\t// scroll-view滚动到顶部时,scrollTop不一定为0,也有可能大于0; 在iOS的APP中scrollTop可能为负数,不一定和startTop相等\n\tif (moveY > 0 && (\n\t\t\t(me.isScrollBody && scrollTop <= 0)\n\t\t\t||\n\t\t\t(!me.isScrollBody && (scrollTop <= 0 || (scrollTop <= me.optDown.startTop && scrollTop === me.startTop)) )\n\t\t)) {\n\t\t// 可下拉的条件\n\t\tif (!me.inTouchend && !me.isDownScrolling && !me.optDown.isLock && (!me.isUpScrolling || (me.isUpScrolling &&\n\t\t\t\tme.optUp.isBoth))) {\n\n\t\t\t// 下拉的初始角度是否在配置的范围内\n\t\t\tif(!me.startAngle) me.startAngle = me.getAngle(me.lastPoint, curPoint); // 两点之间的角度,区间 [0,90]\n\t\t\tif (me.startAngle < me.optDown.minAngle) return; // 如果小于配置的角度,则不往下执行下拉刷新\n\n\t\t\t// 如果手指的位置超过配置的距离,则提前结束下拉,避免Webview嵌套导致touchend无法触发\n\t\t\tif (me.maxTouchmoveY > 0 && curPoint.y >= me.maxTouchmoveY) {\n\t\t\t\tme.inTouchend = true; // 标记执行touchend\n\t\t\t\tme.touchendEvent(); // 提前触发touchend\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tme.preventDefault(e); // 阻止默认事件\n\n\t\t\tlet diff = curPoint.y - me.lastPoint.y; // 和上次比,移动的距离 (大于0向下,小于0向上)\n\n\t\t\t// 下拉距离  < 指定距离\n\t\t\tif (me.downHight < me.optDown.offset) {\n\t\t\t\tif (me.movetype !== 1) {\n\t\t\t\t\tme.movetype = 1; // 加入标记,保证只执行一次\n\t\t\t\t\tme.isDownEndSuccess = null; // 重置是否加载成功的状态 (wxs执行的是wxs.wxs)\n\t\t\t\t\tme.optDown.inOffset && me.optDown.inOffset(me); // 进入指定距离范围内那一刻的回调,只执行一次\n\t\t\t\t\tme.isMoveDown = true; // 标记下拉区域高度改变,在touchend重置回来\n\t\t\t\t}\n\t\t\t\tme.downHight += diff * me.optDown.inOffsetRate; // 越往下,高度变化越小\n\n\t\t\t\t// 指定距离  <= 下拉距离\n\t\t\t} else {\n\t\t\t\tif (me.movetype !== 2) {\n\t\t\t\t\tme.movetype = 2; // 加入标记,保证只执行一次\n\t\t\t\t\tme.optDown.outOffset && me.optDown.outOffset(me); // 下拉超过指定距离那一刻的回调,只执行一次\n\t\t\t\t\tme.isMoveDown = true; // 标记下拉区域高度改变,在touchend重置回来\n\t\t\t\t}\n\t\t\t\tif (diff > 0) { // 向下拉\n\t\t\t\t\tme.downHight += diff * me.optDown.outOffsetRate; // 越往下,高度变化越小\n\t\t\t\t} else { // 向上收\n\t\t\t\t\tme.downHight += diff; // 向上收回高度,则向上滑多少收多少高度\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tme.downHight = Math.round(me.downHight) // 取整\n\t\t\tlet rate = me.downHight / me.optDown.offset; // 下拉区域当前高度与指定距离的比值\n\t\t\tme.optDown.onMoving && me.optDown.onMoving(me, rate, me.downHight); // 下拉过程中的回调,一直在执行\n\t\t}\n\t}\n\n\tme.lastPoint = curPoint; // 记录本次移动的点\n}\n\n/* 列表touchend事件 */\nMeScroll.prototype.touchendEvent = function(e) {\n\tif (!this.optDown.use) return;\n\t// 如果下拉区域高度已改变,则需重置回来\n\tif (this.isMoveDown) {\n\t\tif (this.downHight >= this.optDown.offset) {\n\t\t\t// 符合触发刷新的条件\n\t\t\tthis.triggerDownScroll();\n\t\t} else {\n\t\t\t// 不符合的话 则重置\n\t\t\tthis.downHight = 0;\n\t\t\tthis.endDownScrollCall(this);\n\t\t}\n\t\tthis.movetype = 0;\n\t\tthis.isMoveDown = false;\n\t} else if (!this.isScrollBody && this.getScrollTop() === this.startTop) { // scroll-view到顶/左/右/底的滑动事件\n\t\tlet isScrollUp = this.getPoint(e).y - this.startPoint.y < 0; // 和起点比,移动的距离,大于0向下拉,小于0向上拉\n\t\t// 上滑\n\t\tif (isScrollUp) {\n\t\t\t// 需检查滑动的角度\n\t\t\tlet angle = this.getAngle(this.getPoint(e), this.startPoint); // 两点之间的角度,区间 [0,90]\n\t\t\tif (angle > 80) {\n\t\t\t\t// 检查并触发上拉\n\t\t\t\tthis.triggerUpScroll(true);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* 根据点击滑动事件获取第一个手指的坐标 */\nMeScroll.prototype.getPoint = function(e) {\n\tif (!e) {\n\t\treturn {\n\t\t\tx: 0,\n\t\t\ty: 0\n\t\t}\n\t}\n\tif (e.touches && e.touches[0]) {\n\t\treturn {\n\t\t\tx: e.touches[0].pageX,\n\t\t\ty: e.touches[0].pageY\n\t\t}\n\t} else if (e.changedTouches && e.changedTouches[0]) {\n\t\treturn {\n\t\t\tx: e.changedTouches[0].pageX,\n\t\t\ty: e.changedTouches[0].pageY\n\t\t}\n\t} else {\n\t\treturn {\n\t\t\tx: e.clientX,\n\t\t\ty: e.clientY\n\t\t}\n\t}\n}\n\n/* 计算两点之间的角度: 区间 [0,90]*/\nMeScroll.prototype.getAngle = function(p1, p2) {\n\tlet x = Math.abs(p1.x - p2.x);\n\tlet y = Math.abs(p1.y - p2.y);\n\tlet z = Math.sqrt(x * x + y * y);\n\tlet angle = 0;\n\tif (z !== 0) {\n\t\tangle = Math.asin(y / z) / Math.PI * 180;\n\t}\n\treturn angle\n}\n\n/* 触发下拉刷新 */\nMeScroll.prototype.triggerDownScroll = function() {\n\tif (this.optDown.beforeLoading && this.optDown.beforeLoading(this)) {\n\t\t//return true则处于完全自定义状态\n\t} else {\n\t\tthis.showDownScroll(); // 下拉刷新中...\n\t\t!this.optDown.native && this.optDown.callback && this.optDown.callback(this); // 执行回调,联网加载数据\n\t}\n}\n\n/* 显示下拉进度布局 */\nMeScroll.prototype.showDownScroll = function() {\n\tthis.isDownScrolling = true; // 标记下拉中\n\tif (this.optDown.native) {\n\t\tuni.startPullDownRefresh(); // 系统自带的下拉刷新\n\t\tthis.showDownLoadingCall(0); // 仍触发showLoading,因为上拉加载用到\n\t} else{\n\t\tthis.downHight = this.optDown.offset; // 更新下拉区域高度\n\t\tthis.showDownLoadingCall(this.downHight); // 下拉刷新中...\n\t}\n}\n\nMeScroll.prototype.showDownLoadingCall = function(downHight) {\n\tthis.optDown.showLoading && this.optDown.showLoading(this, downHight); // 下拉刷新中...\n\tthis.optDown.afterLoading && this.optDown.afterLoading(this, downHight); // 下拉刷新中...触发之后马上要执行的代码\n}\n\n/* 显示系统自带的下拉刷新时需要处理的业务 */\nMeScroll.prototype.onPullDownRefresh = function() {\n\tthis.isDownScrolling = true; // 标记下拉中\n\tthis.showDownLoadingCall(0); // 仍触发showLoading,因为上拉加载用到\n\tthis.optDown.callback && this.optDown.callback(this); // 执行回调,联网加载数据\n}\n\n/* 结束下拉刷新 */\nMeScroll.prototype.endDownScroll = function() {\n\tif (this.optDown.native) { // 结束原生下拉刷新\n\t\tthis.isDownScrolling = false;\n\t\tthis.endDownScrollCall(this);\n\t\tuni.stopPullDownRefresh();\n\t\treturn\n\t}\n\tlet me = this;\n\t// 结束下拉刷新的方法\n\tlet endScroll = function() {\n\t\tme.downHight = 0;\n\t\tme.isDownScrolling = false;\n\t\tme.endDownScrollCall(me);\n\t\tif(!me.isScrollBody){\n\t\t\tme.setScrollHeight(0) // scroll-view重置滚动区域,使数据不满屏时仍可检查触发翻页\n\t\t\tme.scrollTo(0,0) // scroll-view需重置滚动条到顶部,避免startTop大于0时,对下拉刷新的影响\n\t\t}\n\t}\n\t// 结束下拉刷新时的回调\n\tlet delay = 0;\n\tif (me.optDown.beforeEndDownScroll) {\n\t\tdelay = me.optDown.beforeEndDownScroll(me); // 结束下拉刷新的延时,单位ms\n\t\tif(me.isDownEndSuccess == null) delay = 0; // 没有执行加载中,则不延时\n\t}\n\tif (typeof delay === 'number' && delay > 0) {\n\t\tsetTimeout(endScroll, delay);\n\t} else {\n\t\tendScroll();\n\t}\n}\n\nMeScroll.prototype.endDownScrollCall = function() {\n\tthis.optDown.endDownScroll && this.optDown.endDownScroll(this);\n\tthis.optDown.afterEndDownScroll && this.optDown.afterEndDownScroll(this);\n}\n\n/* 锁定下拉刷新:isLock=ture,null锁定;isLock=false解锁 */\nMeScroll.prototype.lockDownScroll = function(isLock) {\n\tif (isLock == null) isLock = true;\n\tthis.optDown.isLock = isLock;\n}\n\n/* 锁定上拉加载:isLock=ture,null锁定;isLock=false解锁 */\nMeScroll.prototype.lockUpScroll = function(isLock) {\n\tif (isLock == null) isLock = true;\n\tthis.optUp.isLock = isLock;\n}\n\n/* -------初始化上拉加载------- */\nMeScroll.prototype.initUpScroll = function() {\n\tlet me = this;\n\t// 配置参数\n\tme.optUp = me.options.up || {use: false}\n\tif(!me.optUp.textColor && me.hasColor(me.optUp.bgColor)) me.optUp.textColor = \"#fff\"; // 当bgColor有值且textColor未设置,则textColor默认白色\n\tme.extendUpScroll(me.optUp);\n\n\tif (me.optUp.use === false) return; // 配置不使用上拉加载时,则不初始化上拉布局\n\tme.optUp.hasNext = true; // 如果使用上拉,则默认有下一页\n\tme.startNum = me.optUp.page.num + 1; // 记录page开始的页码\n\n\t// 初始化完毕的回调\n\tif (me.optUp.inited) {\n\t\tsetTimeout(function() { // 待主线程执行完毕再执行,避免new MeScroll未初始化,在回调获取不到mescroll的实例\n\t\t\tme.optUp.inited(me);\n\t\t}, 0)\n\t}\n}\n\n/*滚动到底部的事件 (仅mescroll-body生效)*/\nMeScroll.prototype.onReachBottom = function() {\n\tif (this.isScrollBody && !this.isUpScrolling) { // 只能支持下拉刷新的时候同时可以触发上拉加载,否则滚动到底部就需要上滑一点才能触发onReachBottom\n\t\tif (!this.optUp.isLock && this.optUp.hasNext) {\n\t\t\tthis.triggerUpScroll();\n\t\t}\n\t}\n}\n\n/*列表滚动事件 (仅mescroll-body生效)*/\nMeScroll.prototype.onPageScroll = function(e) {\n\tif (!this.isScrollBody) return;\n\t\n\t// 更新滚动条的位置 (主要用于判断下拉刷新时,滚动条是否在顶部)\n\tthis.setScrollTop(e.scrollTop);\n\n\t// 顶部按钮的显示隐藏\n\tif (e.scrollTop >= this.optUp.toTop.offset) {\n\t\tthis.showTopBtn();\n\t} else {\n\t\tthis.hideTopBtn();\n\t}\n}\n\n/*列表滚动事件*/\nMeScroll.prototype.scroll = function(e, onScroll) {\n\t// 更新滚动条的位置\n\tthis.setScrollTop(e.scrollTop);\n\t// 更新滚动内容高度\n\tthis.setScrollHeight(e.scrollHeight);\n\n\t// 向上滑还是向下滑动\n\tif (this.preScrollY == null) this.preScrollY = 0;\n\tthis.isScrollUp = e.scrollTop - this.preScrollY > 0;\n\tthis.preScrollY = e.scrollTop;\n\n\t// 上滑 && 检查并触发上拉\n\tthis.isScrollUp && this.triggerUpScroll(true);\n\n\t// 顶部按钮的显示隐藏\n\tif (e.scrollTop >= this.optUp.toTop.offset) {\n\t\tthis.showTopBtn();\n\t} else {\n\t\tthis.hideTopBtn();\n\t}\n\n\t// 滑动监听\n\tthis.optUp.onScroll && onScroll && onScroll()\n}\n\n/* 触发上拉加载 */\nMeScroll.prototype.triggerUpScroll = function(isCheck) {\n\tif (!this.isUpScrolling && this.optUp.use && this.optUp.callback) {\n\t\t// 是否校验在底部; 默认不校验\n\t\tif (isCheck === true) {\n\t\t\tlet canUp = false;\n\t\t\t// 还有下一页 && 没有锁定 && 不在下拉中\n\t\t\tif (this.optUp.hasNext && !this.optUp.isLock && !this.isDownScrolling) {\n\t\t\t\tif (this.getScrollBottom() <= this.optUp.offset) { // 到底部\n\t\t\t\t\tcanUp = true; // 标记可上拉\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (canUp === false) return;\n\t\t}\n\t\tthis.showUpScroll(); // 上拉加载中...\n\t\tthis.optUp.page.num++; // 预先加一页,如果失败则减回\n\t\tthis.isUpAutoLoad = true; // 标记上拉已经自动执行过,避免初始化时多次触发上拉回调\n\t\tthis.num = this.optUp.page.num; // 把最新的页数赋值在mescroll上,避免对page的影响\n\t\tthis.size = this.optUp.page.size; // 把最新的页码赋值在mescroll上,避免对page的影响\n\t\tthis.time = this.optUp.page.time; // 把最新的页码赋值在mescroll上,避免对page的影响\n\t\tthis.optUp.callback(this); // 执行回调,联网加载数据\n\t}\n}\n\n/* 显示上拉加载中 */\nMeScroll.prototype.showUpScroll = function() {\n\tthis.isUpScrolling = true; // 标记上拉加载中\n\tthis.optUp.showLoading && this.optUp.showLoading(this); // 回调\n}\n\n/* 显示上拉无更多数据 */\nMeScroll.prototype.showNoMore = function() {\n\tthis.optUp.hasNext = false; // 标记无更多数据\n\tthis.optUp.showNoMore && this.optUp.showNoMore(this); // 回调\n}\n\n/* 隐藏上拉区域**/\nMeScroll.prototype.hideUpScroll = function() {\n\tthis.optUp.hideUpScroll && this.optUp.hideUpScroll(this); // 回调\n}\n\n/* 结束上拉加载 */\nMeScroll.prototype.endUpScroll = function(isShowNoMore) {\n\tif (isShowNoMore != null) { // isShowNoMore=null,不处理下拉状态,下拉刷新的时候调用\n\t\tif (isShowNoMore) {\n\t\t\tthis.showNoMore(); // isShowNoMore=true,显示无更多数据\n\t\t} else {\n\t\t\tthis.hideUpScroll(); // isShowNoMore=false,隐藏上拉加载\n\t\t}\n\t}\n\tthis.isUpScrolling = false; // 标记结束上拉加载\n}\n\n/* 重置上拉加载列表为第一页\n *isShowLoading 是否显示进度布局;\n * 1.默认null,不传参,则显示上拉加载的进度布局\n * 2.传参true, 则显示下拉刷新的进度布局\n * 3.传参false,则不显示上拉和下拉的进度 (常用于静默更新列表数据)\n */\nMeScroll.prototype.resetUpScroll = function(isShowLoading) {\n\tif (this.optUp && this.optUp.use) {\n\t\tlet page = this.optUp.page;\n\t\tthis.prePageNum = page.num; // 缓存重置前的页码,加载失败可退回\n\t\tthis.prePageTime = page.time; // 缓存重置前的时间,加载失败可退回\n\t\tpage.num = this.startNum; // 重置为第一页\n\t\tpage.time = null; // 重置时间为空\n\t\tif (!this.isDownScrolling && isShowLoading !== false) { // 如果不是下拉刷新触发的resetUpScroll并且不配置列表静默更新,则显示进度;\n\t\t\tif (isShowLoading == null) {\n\t\t\t\tthis.removeEmpty(); // 移除空布局\n\t\t\t\tthis.showUpScroll(); // 不传参,默认显示上拉加载的进度布局\n\t\t\t} else {\n\t\t\t\tthis.showDownScroll(); // 传true,显示下拉刷新的进度布局,不清空列表\n\t\t\t}\n\t\t}\n\t\tthis.isUpAutoLoad = true; // 标记上拉已经自动执行过,避免初始化时多次触发上拉回调\n\t\tthis.num = page.num; // 把最新的页数赋值在mescroll上,避免对page的影响\n\t\tthis.size = page.size; // 把最新的页码赋值在mescroll上,避免对page的影响\n\t\tthis.time = page.time; // 把最新的页码赋值在mescroll上,避免对page的影响\n\t\tthis.optUp.callback && this.optUp.callback(this); // 执行上拉回调\n\t}\n}\n\n/* 设置page.num的值 */\nMeScroll.prototype.setPageNum = function(num) {\n\tthis.optUp.page.num = num - 1;\n}\n\n/* 设置page.size的值 */\nMeScroll.prototype.setPageSize = function(size) {\n\tthis.optUp.page.size = size;\n}\n\n/* 联网回调成功,结束下拉刷新和上拉加载\n * dataSize: 当前页的数据量(必传)\n * totalPage: 总页数(必传)\n * systime: 服务器时间 (可空)\n */\nMeScroll.prototype.endByPage = function(dataSize, totalPage, systime) {\n\tlet hasNext;\n\tif (this.optUp.use && totalPage != null) hasNext = this.optUp.page.num < totalPage; // 是否还有下一页\n\tthis.endSuccess(dataSize, hasNext, systime);\n}\n\n/* 联网回调成功,结束下拉刷新和上拉加载\n * dataSize: 当前页的数据量(必传)\n * totalSize: 列表所有数据总数量(必传)\n * systime: 服务器时间 (可空)\n */\nMeScroll.prototype.endBySize = function(dataSize, totalSize, systime) {\n\tlet hasNext;\n\tif (this.optUp.use && totalSize != null) {\n\t\tlet loadSize = (this.optUp.page.num - 1) * this.optUp.page.size + dataSize; // 已加载的数据总数\n\t\thasNext = loadSize < totalSize; // 是否还有下一页\n\t}\n\tthis.endSuccess(dataSize, hasNext, systime);\n}\n\n/* 联网回调成功,结束下拉刷新和上拉加载\n * dataSize: 当前页的数据个数(不是所有页的数据总和),用于上拉加载判断是否还有下一页.如果不传,则会判断还有下一页\n * hasNext: 是否还有下一页,布尔类型;用来解决这个小问题:比如列表共有20条数据,每页加载10条,共2页.如果只根据dataSize判断,则需翻到第三页才会知道无更多数据,如果传了hasNext,则翻到第二页即可显示无更多数据.\n * systime: 服务器时间(可空);用来解决这个小问题:当准备翻下一页时,数据库新增了几条记录,此时翻下一页,前面的几条数据会和上一页的重复;这里传入了systime,那么upCallback的page.time就会有值,把page.time传给服务器,让后台过滤新加入的那几条记录\n */\nMeScroll.prototype.endSuccess = function(dataSize, hasNext, systime) {\n\tlet me = this;\n\t// 结束下拉刷新\n\tif (me.isDownScrolling) {\n\t\tme.isDownEndSuccess = true\n\t\tme.endDownScroll();\n\t}\n\n\t// 结束上拉加载\n\tif (me.optUp.use) {\n\t\tlet isShowNoMore; // 是否已无更多数据\n\t\tif (dataSize != null) {\n\t\t\tlet pageNum = me.optUp.page.num; // 当前页码\n\t\t\tlet pageSize = me.optUp.page.size; // 每页长度\n\t\t\t// 如果是第一页\n\t\t\tif (pageNum === 1) {\n\t\t\t\tif (systime) me.optUp.page.time = systime; // 设置加载列表数据第一页的时间\n\t\t\t}\n\t\t\tif (dataSize < pageSize || hasNext === false) {\n\t\t\t\t// 返回的数据不满一页时,则说明已无更多数据\n\t\t\t\tme.optUp.hasNext = false;\n\t\t\t\tif (dataSize === 0 && pageNum === 1) {\n\t\t\t\t\t// 如果第一页无任何数据且配置了空布局\n\t\t\t\t\tisShowNoMore = false;\n\t\t\t\t\tme.showEmpty();\n\t\t\t\t} else {\n\t\t\t\t\t// 总列表数少于配置的数量,则不显示无更多数据\n\t\t\t\t\tlet allDataSize = (pageNum - 1) * pageSize + dataSize;\n\t\t\t\t\tif (allDataSize < me.optUp.noMoreSize) {\n\t\t\t\t\t\tisShowNoMore = false;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tisShowNoMore = true;\n\t\t\t\t\t}\n\t\t\t\t\tme.removeEmpty(); // 移除空布局\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 还有下一页\n\t\t\t\tisShowNoMore = false;\n\t\t\t\tme.optUp.hasNext = true;\n\t\t\t\tme.removeEmpty(); // 移除空布局\n\t\t\t}\n\t\t}\n\n\t\t// 隐藏上拉\n\t\tme.endUpScroll(isShowNoMore);\n\t}\n}\n\n/* 回调失败,结束下拉刷新和上拉加载 */\nMeScroll.prototype.endErr = function(errDistance) {\n\t// 结束下拉,回调失败重置回原来的页码和时间\n\tif (this.isDownScrolling) {\n\t\tthis.isDownEndSuccess = false\n\t\tlet page = this.optUp.page;\n\t\tif (page && this.prePageNum) {\n\t\t\tpage.num = this.prePageNum;\n\t\t\tpage.time = this.prePageTime;\n\t\t}\n\t\tthis.endDownScroll();\n\t}\n\t// 结束上拉,回调失败重置回原来的页码\n\tif (this.isUpScrolling) {\n\t\tthis.optUp.page.num--;\n\t\tthis.endUpScroll(false);\n\t\t// 如果是mescroll-body,则需往回滚一定距离\n\t\tif(this.isScrollBody && errDistance !== 0){ // 不处理0\n\t\t\tif(!errDistance) errDistance = this.optUp.errDistance; // 不传,则取默认\n\t\t\tthis.scrollTo(this.getScrollTop() - errDistance, 0) // 往上回滚的距离\n\t\t}\n\t}\n}\n\n/* 显示空布局 */\nMeScroll.prototype.showEmpty = function() {\n\tthis.optUp.empty.use && this.optUp.empty.onShow && this.optUp.empty.onShow(true)\n}\n\n/* 移除空布局 */\nMeScroll.prototype.removeEmpty = function() {\n\tthis.optUp.empty.use && this.optUp.empty.onShow && this.optUp.empty.onShow(false)\n}\n\n/* 显示回到顶部的按钮 */\nMeScroll.prototype.showTopBtn = function() {\n\tif (!this.topBtnShow) {\n\t\tthis.topBtnShow = true;\n\t\tthis.optUp.toTop.onShow && this.optUp.toTop.onShow(true);\n\t}\n}\n\n/* 隐藏回到顶部的按钮 */\nMeScroll.prototype.hideTopBtn = function() {\n\tif (this.topBtnShow) {\n\t\tthis.topBtnShow = false;\n\t\tthis.optUp.toTop.onShow && this.optUp.toTop.onShow(false);\n\t}\n}\n\n/* 获取滚动条的位置 */\nMeScroll.prototype.getScrollTop = function() {\n\treturn this.scrollTop || 0\n}\n\n/* 记录滚动条的位置 */\nMeScroll.prototype.setScrollTop = function(y) {\n\tthis.scrollTop = y;\n}\n\n/* 滚动到指定位置 */\nMeScroll.prototype.scrollTo = function(y, t) {\n\tthis.myScrollTo && this.myScrollTo(y, t) // scrollview需自定义回到顶部方法\n}\n\n/* 自定义scrollTo */\nMeScroll.prototype.resetScrollTo = function(myScrollTo) {\n\tthis.myScrollTo = myScrollTo\n}\n\n/* 滚动条到底部的距离 */\nMeScroll.prototype.getScrollBottom = function() {\n\treturn this.getScrollHeight() - this.getClientHeight() - this.getScrollTop()\n}\n\n/* 计步器\n star: 开始值\n end: 结束值\n callback(step,timer): 回调step值,计步器timer,可自行通过window.clearInterval(timer)结束计步器;\n t: 计步时长,传0则直接回调end值;不传则默认300ms\n rate: 周期;不传则默认30ms计步一次\n * */\nMeScroll.prototype.getStep = function(star, end, callback, t, rate) {\n\tlet diff = end - star; // 差值\n\tif (t === 0 || diff === 0) {\n\t\tcallback && callback(end);\n\t\treturn;\n\t}\n\tt = t || 300; // 时长 300ms\n\trate = rate || 30; // 周期 30ms\n\tlet count = t / rate; // 次数\n\tlet step = diff / count; // 步长\n\tlet i = 0; // 计数\n\tlet timer = setInterval(function() {\n\t\tif (i < count - 1) {\n\t\t\tstar += step;\n\t\t\tcallback && callback(star, timer);\n\t\t\ti++;\n\t\t} else {\n\t\t\tcallback && callback(end, timer); // 最后一次直接设置end,避免计算误差\n\t\t\tclearInterval(timer);\n\t\t}\n\t}, rate);\n}\n\n/* 滚动容器的高度 */\nMeScroll.prototype.getClientHeight = function(isReal) {\n\tlet h = this.clientHeight || 0\n\tif (h === 0 && isReal !== true) { // 未获取到容器的高度,可临时取body的高度 (可能会有误差)\n\t\th = this.getBodyHeight()\n\t}\n\treturn h\n}\nMeScroll.prototype.setClientHeight = function(h) {\n\tthis.clientHeight = h;\n}\n\n/* 滚动内容的高度 */\nMeScroll.prototype.getScrollHeight = function() {\n\treturn this.scrollHeight || 0;\n}\nMeScroll.prototype.setScrollHeight = function(h) {\n\tthis.scrollHeight = h;\n}\n\n/* body的高度 */\nMeScroll.prototype.getBodyHeight = function() {\n\treturn this.bodyHeight || 0;\n}\nMeScroll.prototype.setBodyHeight = function(h) {\n\tthis.bodyHeight = h;\n}\n\n/* 阻止浏览器默认滚动事件 */\nMeScroll.prototype.preventDefault = function(e) {\n\t// 小程序不支持e.preventDefault, 已在wxs中禁止\n\t// app的bounce只能通过配置pages.json的style.app-plus.bounce为\"none\"来禁止, 或使用renderjs禁止\n\t// cancelable:是否可以被禁用; defaultPrevented:是否已经被禁用\n\tif (e && e.cancelable && !e.defaultPrevented) e.preventDefault()\n}", "// 全局配置\n// mescroll-body 和 mescroll-uni 通用\nconst GlobalOption = {\n\tdown: {\n\t\t// 其他down的配置参数也可以写,这里只展示了常用的配置:\n\t\toffset: 80, // 在列表顶部,下拉大于80px,松手即可触发下拉刷新的回调\n\t\tnative: false // 是否使用系统自带的下拉刷新; 默认false; 仅在mescroll-body生效 (值为true时,还需在pages配置enablePullDownRefresh:true;详请参考mescroll-native的案例)\n\t},\n\tup: {\n\t\t// 其他up的配置参数也可以写,这里只展示了常用的配置:\n\t\toffset: 150, // 距底部多远时,触发upCallback,仅mescroll-uni生效 ( mescroll-body配置的是pages.json的 onReachBottomDistance )\n\t\ttoTop: {\n\t\t\t// 回到顶部按钮,需配置src才显示\n\t\t\tsrc: \"https://www.mescroll.com/img/mescroll-totop.png\", // 图片路径 (建议放入static目录, 如 /static/img/mescroll-totop.png )\n\t\t\toffset: 1000, // 列表滚动多少距离才显示回到顶部按钮,默认1000px\n\t\t\tright: 20, // 到右边的距离, 默认20 (支持\"20rpx\", \"20px\", \"20%\"格式的值, 纯数字则默认单位rpx)\n\t\t\tbottom: 120, // 到底部的距离, 默认120 (支持\"20rpx\", \"20px\", \"20%\"格式的值, 纯数字则默认单位rpx)\n\t\t\twidth: 72 // 回到顶部图标的宽度, 默认72 (支持\"20rpx\", \"20px\", \"20%\"格式的值, 纯数字则默认单位rpx)\n\t\t},\n\t\tempty: {\n\t\t\tuse: true, // 是否显示空布局\n\t\t\ticon: \"https://www.mescroll.com/img/mescroll-empty.png\" // 图标路径 (建议放入static目录, 如 /static/img/mescroll-empty.png )\n\t\t}\n\t},\n\t// 国际化配置\n\ti18n: {\n\t\t// 中文\n\t\tzh: {\n\t\t\tdown: {\n\t\t\t\ttextInOffset: '下拉刷新', // 下拉的距离在offset范围内的提示文本\n\t\t\t\ttextOutOffset: '释放更新', // 下拉的距离大于offset范围的提示文本\n\t\t\t\ttextLoading: '加载中 ...', // 加载中的提示文本\n\t\t\t\ttextSuccess: '加载成功', // 加载成功的文本\n\t\t\t\ttextErr: '加载失败', // 加载失败的文本\n\t\t\t},\n\t\t\tup: {\n\t\t\t\ttextLoading: '加载中 ...', // 加载中的提示文本\n\t\t\t\ttextNoMore: '没有更多数据了', // 没有更多数据的提示文本\n\t\t\t\tempty: {\n\t\t\t\t\ttip: '~ 空空如也 ~' // 空提示\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 英文\n\t\ten: {\n\t\t\tdown: {\n\t\t\t\ttextInOffset: 'drop down refresh',\n\t\t\t\ttextOutOffset: 'release updates',\n\t\t\t\ttextLoading: 'loading ...',\n\t\t\t\ttextSuccess: 'loaded successfully',\n\t\t\t\ttextErr: 'loading failed'\n\t\t\t},\n\t\t\tup: {\n\t\t\t\ttextLoading: 'loading ...',\n\t\t\t\ttextNoMore: '没有更多数据了',\n\t\t\t\tempty: {\n\t\t\t\t\ttip: '~ absolutely empty ~'\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport default GlobalOption\n", "// 国际化工具类\nconst mescrollI18n = {\n\t// 默认语言\n\tdef: \"zh\",\n\t// 获取当前语言类型\n\tgetType(){\n\t\treturn uni.getStorageSync(\"mescroll-i18n\") || this.def\n\t},\n\t// 设置当前语言类型\n\tsetType(type){\n\t\tuni.setStorageSync(\"mescroll-i18n\", type)\n\t}\n}\n\nexport default mescrollI18n\n", "// 定义在wxs (含renderjs) 逻辑层的数据和方法, 与视图层相互通信\nconst WxsMixin = {\n\tdata() {\n\t\treturn {\n\t\t\t// 传入wxs视图层的数据 (响应式)\n\t\t\twxsProp: {\n\t\t\t\toptDown:{}, // 下拉刷新的配置\n\t\t\t\tscrollTop:0, // 滚动条的距离\n\t\t\t\tbodyHeight:0, // body的高度\n\t\t\t\tisDownScrolling:false, // 是否正在下拉刷新中\n\t\t\t\tisUpScrolling:false, // 是否正在上拉加载中\n\t\t\t\tisScrollBody:true, // 是否为mescroll-body滚动\n\t\t\t\tisUpBoth:true, // 上拉加载时,是否同时可以下拉刷新\n\t\t\t\tt: 0 // 数据更新的标记 (只有数据更新了,才会触发wxs的Observer)\n\t\t\t},\n\t\t\t\n\t\t\t// 标记调用wxs视图层的方法\n\t\t\tcallProp: {\n\t\t\t\tcallType: '', // 方法名\n\t\t\t\tt: 0 // 数据更新的标记 (只有数据更新了,才会触发wxs的Observer)\n\t\t\t},\n\t\t\t\n\t\t\t// 不用wxs的平台使用此处的wxsBiz对象,抹平wxs的写法 (微信小程序和APP使用的wxsBiz对象是./wxs/wxs.wxs)\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\t\t\t\n\t\t\t// 不用renderjs的平台使用此处的renderBiz对象,抹平renderjs的写法 (app 和 h5 使用的renderBiz对象是./wxs/renderjs.js)\n\n\t\t\trenderBiz: {\n\t\t\t\tpropObserver(){} // 抹平renderjs的写法\n\t\t\t}\n\n\t\t}\n\t},\n\tmethods: {\n\t\t// wxs视图层调用逻辑层的回调\n\t\twxsCall(msg){\n\t\t\tif(msg.type === 'setWxsProp'){\n\t\t\t\t// 更新wxsProp数据 (值改变才触发更新)\n\t\t\t\tthis.wxsProp = {\n\t\t\t\t\toptDown: this.mescroll.optDown,\n\t\t\t\t\tscrollTop: this.mescroll.getScrollTop(),\n\t\t\t\t\tbodyHeight: this.mescroll.getBodyHeight(),\n\t\t\t\t\tisDownScrolling: this.mescroll.isDownScrolling,\n\t\t\t\t\tisUpScrolling: this.mescroll.isUpScrolling,\n\t\t\t\t\tisUpBoth: this.mescroll.optUp.isBoth,\n\t\t\t\t\tisScrollBody:this.mescroll.isScrollBody,\n\t\t\t\t\tt: Date.now()\n\t\t\t\t}\n\t\t\t}else if(msg.type === 'setLoadType'){\n\t\t\t\t// 设置inOffset,outOffset的状态\n\t\t\t\tthis.downLoadType = msg.downLoadType\n\t\t\t\t// 状态挂载到mescroll对象, 以便在其他组件中使用, 比如<me-video>中\n\t\t\t\tthis.$set(this.mescroll, 'downLoadType', this.downLoadType)\n\t\t\t\t// 重置是否加载成功的状态\n\t\t\t\tthis.$set(this.mescroll, 'isDownEndSuccess', null)\n\t\t\t}else if(msg.type === 'triggerDownScroll'){\n\t\t\t\t// 主动触发下拉刷新\n\t\t\t\tthis.mescroll.triggerDownScroll();\n\t\t\t}else if(msg.type === 'endDownScroll'){\n\t\t\t\t// 结束下拉刷新\n\t\t\t\tthis.mescroll.endDownScroll();\n\t\t\t}else if(msg.type === 'triggerUpScroll'){\n\t\t\t\t// 主动触发上拉加载\n\t\t\t\tthis.mescroll.triggerUpScroll(true);\n\t\t\t}\n\t\t}\n\t},\n\tmounted() {\n\n\t\t// 配置主动触发wxs显示加载进度的回调\n\t\tthis.mescroll.optDown.afterLoading = ()=>{\n\t\t\tthis.callProp = {callType: \"showLoading\", t: Date.now()} // 触发wxs的方法 (值改变才触发更新)\n\t\t}\n\t\t// 配置主动触发wxs隐藏加载进度的回调\n\t\tthis.mescroll.optDown.afterEndDownScroll = ()=>{\n\t\t\tthis.callProp = {callType: \"endDownScroll\", t: Date.now()} // 触发wxs的方法 (值改变才触发更新)\n\t\t\tlet delay = 300 + (this.mescroll.optDown.beforeEndDelay || 0)\n\t\t\tsetTimeout(()=>{\n\t\t\t\tif(this.downLoadType === 4 || this.downLoadType === 0){\n\t\t\t\t\tthis.callProp = {callType: \"clearTransform\", t: Date.now()} // 触发wxs的方法 (值改变才触发更新)\n\t\t\t\t}\n\t\t\t\t// 状态挂载到mescroll对象, 以便在其他组件中使用, 比如<me-video>中\n\t\t\t\tthis.$set(this.mescroll, 'downLoadType', this.downLoadType)\n\t\t\t}, delay)\n\t\t}\n\t\t// 初始化wxs的数据\n\t\tthis.wxsCall({type: 'setWxsProp'})\n\n\t}\n}\n\nexport default WxsMixin;\n"], "sourceRoot": ""}