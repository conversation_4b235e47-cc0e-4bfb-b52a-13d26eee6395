package com.sqx.modules.department.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sqx.modules.department.entity.Department;
import lombok.Data;

import java.io.Serializable;

/**
 * 导出模板
 */
@Data
public class DepartmentOut extends Department implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 医院名称
     */

    @Excel(name = "所属医院",width = 25,orderNum = "3")
    private String hospitalName;

    /**
     * 所属科室
     */
    @Excel(name = "所属科室",width = 20,orderNum = "2")
    private String parentName;

}
