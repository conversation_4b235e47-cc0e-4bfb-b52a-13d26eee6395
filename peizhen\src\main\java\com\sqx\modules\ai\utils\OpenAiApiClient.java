package com.sqx.modules.ai.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sqx.modules.ai.entity.AiModelConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * OpenAI兼容API客户端
 * 支持DeepSeek和Kimi等兼容OpenAI格式的API
 */
@Slf4j
@Component
public class OpenAiApiClient {

    @Autowired
    @Qualifier("aiRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 发送聊天请求
     */
    public ChatResponse sendChatRequest(AiModelConfig modelConfig, List<ChatMessage> messages) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelConfig.getModelCode());
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", modelConfig.getMaxTokens());
            requestBody.put("temperature", modelConfig.getTemperature());
            requestBody.put("top_p", modelConfig.getTopP());
            requestBody.put("frequency_penalty", modelConfig.getFrequencyPenalty());
            requestBody.put("presence_penalty", modelConfig.getPresencePenalty());
            requestBody.put("stream", false);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(modelConfig.getApiKey());

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            long startTime = System.currentTimeMillis();
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                modelConfig.getApiUrl(),
                HttpMethod.POST,
                entity,
                String.class
            );

            long responseTime = System.currentTimeMillis() - startTime;

            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK) {
                return parseSuccessResponse(response.getBody(), responseTime);
            } else {
                return ChatResponse.error("API请求失败: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("AI API调用异常", e);
            return ChatResponse.error("API调用异常: " + e.getMessage());
        }
    }

    /**
     * 发送流式聊天请求
     */
    public void sendStreamChatRequest(AiModelConfig modelConfig, List<ChatMessage> messages, StreamCallback callback) {
        try {
            log.info("开始流式API调用，模型: {}, 消息数: {}", modelConfig.getModelCode(), messages.size());

            // 构建请求体 - 与非流式版本保持一致
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelConfig.getModelCode());
            requestBody.put("messages", messages);

            // 只添加非null的参数
            if (modelConfig.getMaxTokens() != null && modelConfig.getMaxTokens() > 0) {
                requestBody.put("max_tokens", modelConfig.getMaxTokens());
            }
            if (modelConfig.getTemperature() != null) {
                requestBody.put("temperature", modelConfig.getTemperature());
            }
            if (modelConfig.getTopP() != null) {
                requestBody.put("top_p", modelConfig.getTopP());
            }
            if (modelConfig.getFrequencyPenalty() != null) {
                requestBody.put("frequency_penalty", modelConfig.getFrequencyPenalty());
            }
            if (modelConfig.getPresencePenalty() != null) {
                requestBody.put("presence_penalty", modelConfig.getPresencePenalty());
            }
            requestBody.put("stream", true); // 启用流式输出

            String jsonBody = JSON.toJSONString(requestBody);
            log.info("流式请求体: {}", jsonBody);

            // 创建连接
            URL url = new URL(modelConfig.getApiUrl());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Authorization", "Bearer " + modelConfig.getApiKey());
            connection.setRequestProperty("Accept", "text/event-stream");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 设置超时
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(300000);   // 5分钟读取超时

            // 发送请求体
            try (java.io.OutputStream os = connection.getOutputStream()) {
                os.write(jsonBody.getBytes("UTF-8"));
                os.flush();
            }

            // 检查响应状态
            int responseCode = connection.getResponseCode();
            log.info("流式API响应状态: {}", responseCode);

            if (responseCode != 200) {
                // 读取错误响应
                String errorResponse = "";
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "UTF-8"))) {
                    StringBuilder errorBuilder = new StringBuilder();
                    String errorLine;
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorBuilder.append(errorLine);
                    }
                    errorResponse = errorBuilder.toString();
                } catch (Exception e) {
                    errorResponse = "无法读取错误响应";
                }

                String errorMsg = String.format("流式API调用失败，状态码: %d, 错误信息: %s", responseCode, errorResponse);
                log.error(errorMsg);
                callback.onError(errorMsg);
                return;
            }

            // 读取流式响应
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                String line;
                StringBuilder fullContent = new StringBuilder();

                while ((line = reader.readLine()) != null) {
                    log.debug("收到流式数据行: {}", line);

                    if (line.startsWith("data: ")) {
                        String data = line.substring(6).trim();

                        // 检查是否为结束标记
                        if ("[DONE]".equals(data)) {
                            log.info("流式输出完成，总字符数: {}", fullContent.length());
                            callback.onComplete(fullContent.toString());
                            break;
                        }

                        if (data.isEmpty()) {
                            continue; // 跳过空数据行
                        }

                        try {
                            // 解析流式数据
                            JSONObject jsonData = JSON.parseObject(data);
                            JSONArray choices = jsonData.getJSONArray("choices");

                            if (choices != null && !choices.isEmpty()) {
                                JSONObject firstChoice = choices.getJSONObject(0);
                                JSONObject delta = firstChoice.getJSONObject("delta");

                                if (delta != null && delta.containsKey("content")) {
                                    String content = delta.getString("content");
                                    if (content != null && !content.isEmpty()) {
                                        fullContent.append(content);
                                        callback.onChunk(content, fullContent.toString());
                                        log.info("处理内容块: '{}', 累积长度: {}", content, fullContent.length());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.warn("解析流式数据失败: {}", data, e);
                        }
                    }
                }
            }

            connection.disconnect();

        } catch (Exception e) {
            log.error("流式API调用异常", e);
            callback.onError("流式API调用异常: " + e.getMessage());
        }
    }

    /**
     * 流式回调接口
     */
    public interface StreamCallback {
        /**
         * 接收到新的内容块
         * @param chunk 新的内容块
         * @param accumulated 累积的完整内容
         */
        void onChunk(String chunk, String accumulated);

        /**
         * 流式输出完成
         * @param fullContent 完整内容
         */
        void onComplete(String fullContent);

        /**
         * 发生错误
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * 解析成功响应
     */
    private ChatResponse parseSuccessResponse(String responseBody, long responseTime) {
        try {
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            
            // 检查是否有错误
            if (jsonResponse.containsKey("error")) {
                JSONObject error = jsonResponse.getJSONObject("error");
                return ChatResponse.error("API错误: " + error.getString("message"));
            }

            // 解析正常响应
            JSONArray choices = jsonResponse.getJSONArray("choices");
            if (choices == null || choices.isEmpty()) {
                return ChatResponse.error("API响应格式错误: 没有choices");
            }

            JSONObject firstChoice = choices.getJSONObject(0);
            JSONObject message = firstChoice.getJSONObject("message");
            String content = message.getString("content");

            // 解析token使用情况
            JSONObject usage = jsonResponse.getJSONObject("usage");
            int promptTokens = usage != null ? usage.getIntValue("prompt_tokens") : 0;
            int completionTokens = usage != null ? usage.getIntValue("completion_tokens") : 0;
            int totalTokens = usage != null ? usage.getIntValue("total_tokens") : 0;

            return ChatResponse.success(content, promptTokens, completionTokens, totalTokens, (int) responseTime);

        } catch (Exception e) {
            log.error("解析API响应异常", e);
            return ChatResponse.error("解析响应异常: " + e.getMessage());
        }
    }

    /**
     * 聊天消息类
     */
    public static class ChatMessage {
        private String role;
        private String content;

        public ChatMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }

        // getters and setters
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }

    /**
     * 聊天响应类
     */
    public static class ChatResponse {
        private boolean success;
        private String content;
        private String errorMessage;
        private int promptTokens;
        private int completionTokens;
        private int totalTokens;
        private int responseTime;

        public static ChatResponse success(String content, int promptTokens, int completionTokens, int totalTokens, int responseTime) {
            ChatResponse response = new ChatResponse();
            response.success = true;
            response.content = content;
            response.promptTokens = promptTokens;
            response.completionTokens = completionTokens;
            response.totalTokens = totalTokens;
            response.responseTime = responseTime;
            return response;
        }

        public static ChatResponse error(String errorMessage) {
            ChatResponse response = new ChatResponse();
            response.success = false;
            response.errorMessage = errorMessage;
            return response;
        }

        // getters and setters
        public boolean isSuccess() { return success; }
        public String getContent() { return content; }
        public String getErrorMessage() { return errorMessage; }
        public int getPromptTokens() { return promptTokens; }
        public int getCompletionTokens() { return completionTokens; }
        public int getTotalTokens() { return totalTokens; }
        public int getResponseTime() { return responseTime; }
    }
}
