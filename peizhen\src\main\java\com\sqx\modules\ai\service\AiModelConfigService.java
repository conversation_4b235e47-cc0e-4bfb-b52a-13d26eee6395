package com.sqx.modules.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.entity.AiModelConfig;

import java.util.Map;

/**
 * AI模型配置服务接口
 */
public interface AiModelConfigService extends IService<AiModelConfig> {

    /**
     * 分页查询模型配置列表
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 查询启用的模型列表
     */
    Result getEnabledModels();

    /**
     * 根据模型代码获取模型配置
     */
    AiModelConfig getByModelCode(String modelCode);

    /**
     * 保存模型配置
     */
    Result saveModelConfig(AiModelConfig modelConfig);

    /**
     * 更新模型配置
     */
    Result updateModelConfig(AiModelConfig modelConfig);

    /**
     * 删除模型配置
     */
    Result deleteModelConfig(Long modelId);

    /**
     * 启用/禁用模型
     */
    Result toggleModelStatus(Long modelId, Integer status);
}
