<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sqx.modules.taking.dao.OrderTakingDao">

    <select id="queryTaking" resultType="com.sqx.modules.taking.response.OrderTakingResponse">
        select * from (
        SELECT
        t.user_id,
        t.user_name,
        t.avatar,
        t.sex,
        t.age,
        o.id,
        o.sec,
        o.classify,
        o.city,
        o.unit,
        o.game_id as gameName,
        o.my_level,
        o.homepage_img,
        o.money,
        o.old_money,
        o.member_money,
        o.unit_type,
        o.count,
        o.sales_num,
        o.order_score,
        o.min_num,
        o.region,
        o.detailadd,
        o.longitude,
        o.latitude,
        o.service_name as serviceName,
        o.car_type as carType,
        o.safeguard as safeguard,
        o.create_time,
        (st_distance (point (o.longitude, o.latitude),point(#{longitude},#{latitude}) ) *111195) as distance
        FROM order_taking o
        LEFT JOIN tb_user t ON t.user_id = o.user_id
        left join user_certification uc on uc.user_id=o.user_id
        WHERE
        1=1
        <if test="bannerid!=null">
            and g.banner_id = #{bannerid}
        </if>
        <if test="id!=null">
            and o.game_id like concat("%",#{id},'%')
        </if>
        <if test="isRecommend!=null and isRecommend==0 ">
            and o.is_recommend=#{isRecommend}
        </if>
        <if test="sex!=null and sex!=0">
            and t.sex=#{sex}
        </if>
        <if test="city!=null and city!='' ">
            and (o.region like concat('%',#{city},'%') or o.region is null or o.region='')
        </if>
        <if test="like!=null and like!=''">
            and (t.user_name like concat('%',#{like},'%') or o.game_id like concat('%',#{like},'%') or o.my_level like  concat('%',#{like},'%'))
        </if>
        <if test="classify!=null">
            and o.classify=#{classify}
        </if>
        AND o.status =0
        ) b
        order by
        <if test="condition!=null and condition==2 ">
            `count` desc,
        </if>
        <if test="condition!=null and condition==3">
            distance asc,
        </if>
        <if test="salesNum!=null and salesNum !=''">
            sales_num ${salesNum},
        </if>
        <if test="by!=null and by !='' ">
            money ${by},
        </if>
        create_time desc
    </select>
    <select id="queryLowTaking" resultType="com.sqx.modules.taking.response.OrderTakingResponse">
        select * from (
       SELECT
        t.user_id,
        t.user_name,
        t.sex,
        t.age,
        t.avatar,
        o.city,
        o.id,
        o.sec,
        o.unit,
        o.classify,
        o.game_id as gameName,
        o.money,
        o.old_money,
        o.member_money,
        o.unit_type,
        o.count,
        o.sales_num,
        o.min_num,
        o.order_score,
        o.homepage_img,
        o.my_level,
        o.region,
        o.detailadd,
        o.longitude,
        o.latitude,
        o.service_name as serviceName,
        o.car_type as carType,
        o.safeguard as safeguard,
        o.create_time,
        (st_distance (point (o.longitude, o.latitude),point(#{longitude},#{latitude}) ) *111195) as distance
        FROM
        order_taking o
        LEFT JOIN tb_user t ON t.user_id = o.user_id
        left join user_certification uc on uc.user_id=o.user_id
        WHERE
        1=1
        <if test="bannerid!=null">
            and g.banner_id = #{bannerid}
        </if>
        <if test="id!=null">
            and o.game_id like concat("%",#{id},'%')
        </if>
        <if test="isRecommend!=null and isRecommend==0 ">
            and o.is_recommend=#{isRecommend}
        </if>
        <if test="sex!=null and sex!=0">
            and t.sex=#{sex}
        </if>
        <if test="city!=null and city!='' and condition!=null and condition==1 ">
            and (o.region like concat('%',#{city},'%') or o.region is null or o.region='')
        </if>
        <if test="like!=null and like!=''">
            and (t.user_name like concat('%',#{like},'%') or o.game_id like concat('%',#{like},'%') or o.my_level like  concat('%',#{like},'%'))
        </if>
        <if test="classify!=null">
            and o.classify=#{classify}
        </if>
        AND o.status =0
        ) b
        order by
        <if test="condition!=null and condition==2 ">
            `count` desc,
        </if>
        <if test="condition!=null and condition==3">
            distance asc,
        </if>
        <if test="salesNum!=null and salesNum !=''">
            sales_num ${salesNum},
        </if>
        <if test="by!=null and by !='' ">
            money ${by},
        </if>
        create_time desc
            limit 3
    </select>
    <select id="queryLowTakings" resultType="com.sqx.modules.taking.response.OrderTakingResponse">
        select * from (
        SELECT
        t.user_id,
        t.avatar,
        t.age,
        t.sex,
        t.user_name,
        o.city,
        o.id,
        o.sec,
        o.unit,
        o.classify,
        o.game_id as gameName,
        o.money,
        o.old_money,
        o.member_money,
        o.unit_type,
        o.count,
        o.sales_num,
        o.min_num,
        o.order_score,
        o.homepage_img,
        o.my_level,
        o.region,
        o.detailadd,
        o.longitude,
        o.latitude,
        o.service_name as serviceName,
        o.car_type as carType,
        o.safeguard as safeguard,
        o.create_time,
        (st_distance (point (o.longitude, o.latitude),point(#{longitude},#{latitude}) ) *111195) as distance
        FROM
        order_taking o
        LEFT JOIN tb_user t ON t.user_id = o.user_id
        left join user_certification uc on uc.user_id=o.user_id
        WHERE
        1=1
        <if test="bannerid!=null">
            and g.banner_id = #{bannerid}
        </if>
        <if test="id!=null">
            and o.game_id like concat("%",#{id},'%')
        </if>
        <if test="isRecommend!=null and isRecommend==0 ">
            and o.is_recommend=#{isRecommend}
        </if>
        <if test="sex!=null and sex!=0">
            and t.sex=#{sex}
        </if>
        <if test="city!=null and city!='' and condition!=null and condition==1 ">
            and (o.region like concat('%',#{city},'%') or o.region is null or o.region='')
        </if>
        <if test="like!=null and like!=''">
            and (t.user_name like concat('%',#{like},'%') or o.game_id like concat('%',#{like},'%') or o.my_level like
            concat('%',#{like},'%'))
        </if>
        <if test="classify!=null">
            and o.classify=#{classify}
        </if>
        AND o.status =0
        ) b
        order by
        <if test="condition!=null and condition==2 ">
            `count` desc,
        </if>
        <if test="condition!=null and condition==3">
            distance asc,
        </if>
        <if test="salesNum!=null and salesNum !=''">
            sales_num ${salesNum},
        </if>
        <if test="by!=null and by !='' ">
            money ${by},
        </if>
        create_time desc

    </select>

    <select id="queryTakingDetails" resultType="com.sqx.modules.taking.response.TakingDetailsResponse">
         SELECT
        t.user_id,
        o.id,
        o.homepage_img,
        o.details_img,
        t.avatar,
        t.user_name,
        o.city,
        o.game_id as gameName,
        t.sex,
        o.create_time,
        o.unit,
        t.age,
        o.money,
        o.classify,
        o.old_money,
        o.member_money,
        o.unit_type,
        o.order_score,
        o.count,
        o.sec,
        o.sales_num,
        o.voice_introduce,
        o.order_taking_time,
        o.order_taking_area,
        o.order_level,
        o.my_level,
        o.details_img,
        o.min_num,
        o.region,
        o.longitude,
        o.service_name as serviceName,
        o.car_type as carType,
        o.safeguard as safeguard,
        o.latitude,
        o.detailadd,
        (st_distance (point (o.longitude, o.latitude),point(#{longitude},#{latitude}) ) *111195) as distance
        FROM
        order_taking o
        LEFT JOIN tb_user t ON t.user_id = o.user_id
        left join user_certification uc on uc.user_id=o.user_id
        WHERE
        o.id=#{id}
    </select>
    <select id="selectMyRelease" resultType="com.sqx.modules.taking.response.MyReleaseResponse">
        SELECT
        o.id,
        o.`status`,
        o.update_time,
        o.game_id as gameName,
        o.city,
        o.my_level as myLevel,
        o.homepage_img as homepageImg,
        o.money,
        o.sec,
        o.classify,
        o.old_money,
        o.unit,
        o.sales_num,
        o.member_money,
        o.unit_type,
        o.content,
        o.create_time,
        o.min_num,
        o.region,
        o.detailadd,
        o.longitude,
        o.latitude,
        o.service_name as serviceName,
        o.car_type as carType,
        o.safeguard as safeguard,
        o.order_taking_time
        FROM
        order_taking o
        left join user_certification uc on uc.user_id=o.user_id
        WHERE
        o.user_id = #{userId} and o.isdelete=0
        <if test="status!=null and status!=''">
            and o.status =#{status}
        </if>
        order by o.update_time desc
    </select>
    <select id="queryAllOrderTaking" resultType="com.sqx.modules.taking.response.TakingResponse">
        SELECT
        o.id,
        o.game_id as gameName,
        o.my_level,
        o.unit,
        o.order_level,
        o.order_taking_time,
        o.order_taking_area,
        o.money,
        o.sec,
        o.sales_num,
        o.classify,
        o.old_money,
        o.create_time,
        o.member_money,
        o.voice_introduce,
        o.homepage_img,
        o.details_img,
        o.status,
        o.is_recommend,
        o.user_id,
        t.user_name,
        t.avatar,
        t.age,
        t.sex,
        o.city,
        o.count,
        o.order_score,
        o.content,
        o.region,
        o.min_num,
        o.detailadd,
        o.longitude,
        o.latitude,
        o.service_name as serviceName,
        o.car_type as carType,
        o.safeguard as safeguard
        FROM
        order_taking o
        left join tb_user t on o.user_id= t.user_id
        left join user_certification uc on uc.user_id=o.user_id
        where 1=1
        <if test="city!=null and city!=''">
            and (o.region like concat('%',#{city},'%') or o.region is null or o.region='')
        </if>
        <if test="gameId!=null">
            and o.game_id like concat('%',#{gameId},'%')
        </if>
        <if test="status!=null">
            and o.status=#{status}
        </if>
        <if test="userName!=null and userName!=''">
            and t.user_name like #{userName}
        </if>
        <if test="userId!=null">
            and o.user_id=#{userId}
        </if>
        <if test="classify!=null">
            and o.classify=#{classify}
        </if>
        and o.is_delete != -1
        order by o.update_time  desc
    </select>



    <select id="countGoodsByCreateTime" resultType="Integer">
        select count(*) from order_taking where status=0
        <if test="flag!=null and flag==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>

    <select id="sumGoodsByCreateTime" resultType="Double">
        select sum(money) from order_taking where status=0
        <if test="flag!=null and flag==1">
            and date_format(create_time,'%Y-%m-%d')=date_format(#{time},'%Y-%m-%d')
        </if>
        <if test="flag!=null and flag==2">
            and date_format(create_time,'%Y-%m')=date_format(#{time},'%Y-%m')
        </if>
        <if test="flag!=null and flag==3">
            and date_format(create_time,'%Y')=date_format(#{time},'%Y')
        </if>
    </select>

    <select id="selectOrderTakingCountByUserId" resultType="Integer">
        select count(*) from order_taking where status=#{status} and user_id=#{userId}
    </select>

    <update id="updateTakingStatusByUserId">
        update order_taking set status=2 where user_id=#{userId}
    </update>

</mapper>