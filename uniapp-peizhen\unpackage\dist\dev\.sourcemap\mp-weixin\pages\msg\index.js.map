{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/index.vue?d3ff", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/index.vue?f43d", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/index.vue?6699", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/index.vue?db74", "uni-app:///pages/msg/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/index.vue?4651", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/pages/msg/index.vue?de91"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "empty", "data", "page", "limit", "chatList", "userId", "msgList", "time", "messageCount", "onLoad", "onShow", "that", "onHide", "clearInterval", "methods", "getChatList", "getMsgList", "goIM", "uni", "url", "goMsg"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAouB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCqExvB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;IACA;MACAA;QACAA;QACAA;QACAA;UACAA;QACA;MAEA;IAEA;MACAA;MACAA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MAAA;MACA;QACAb;QACAC;MACA;QACA;UACA;QACA;MACA;IACA;IACAa;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACAZ;MACA;QACAA;MACA;MACAa;QACAC;MACA;IACA;IACAC;MACAF;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAA2iC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACA/jC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/msg/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/msg/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2d66829b&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/msg/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2d66829b&\"", "var components\ntry {\n  components = {\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-image/u-image\" */ \"@/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.msgList.length\n  var g1 = _vm.chatList.length\n  var g2 = !_vm.chatList.length && !_vm.msgList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"\">\n\t\t<view v-if=\"msgList.length\" class=\"margin-topW\">\n\t\t\t<view class=\"flex padding-tb radius padding-lr-sm bg\" @click=\"goMsg\" v-for=\"(item,index) in msgList\"\n\t\t\t\t:key='index'>\n\t\t\t\t<view>\n\t\t\t\t\t<image style=\"width: 80rpx;height: 80rpx;border-radius: 80rpx;\"\n\t\t\t\t\t\tsrc=\"../../static/images/msg/msg.png\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex-sub margin-left-sm\">\n\t\t\t\t\t<view class=\"flex justify-between\">\n\t\t\t\t\t\t<view class=\"text-white\">{{item.title?item.title: '系统消息'}}</view>\n\t\t\t\t\t\t<view v-if=\"messageCount>0\"\n\t\t\t\t\t\t\tstyle=\"height: 32rpx;width: 32rpx;border-radius: 100rpx;background-color: red;color: #FFF;text-align: center;\">\n\t\t\t\t\t\t\t{{messageCount}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<view class=\"text-grey\">{{item.content}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"chatList.length\" class=\"margin-top-sm  content \">\n\t\t\t<view class=\"radius padding-lr-sm bg\" style=\"margin-top: 4rpx;\" @click=\"goIM(item)\"\n\t\t\t\tv-for=\"(item,index) in chatList\" :key='index'>\n\t\t\t\t<view class=\"flex padding-tb \" v-if=\"userId == item.userId\">\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<u-image shape=\"circle\" width='80rpx' height=\"80rpx\" :src=\"item.byAvatar\"></u-image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-sub margin-left-sm\">\n\t\t\t\t\t\t<view class=\"flex justify-between\">\n\t\t\t\t\t\t\t<view class=\"text-white\">{{item.byUserName}}</view>\n\t\t\t\t\t\t\t<view class=\"text-grey\">{{item.messageTime}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex justify-between\">\n\t\t\t\t\t\t\t<view class=\"text-grey\">{{ item.messageType == 1? item.content : '[图片]'}}</view>\n\t\t\t\t\t\t\t<view v-if=\"item.contentCount\"\n\t\t\t\t\t\t\t\tstyle=\"height: 32rpx;width: 32rpx;border-radius: 100rpx;background-color: red;color: #FFF;text-align: center;\">\n\t\t\t\t\t\t\t\t{{item.contentCount}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex padding-tb\" v-else>\n\t\t\t\t\t<view>\n\t\t\t\t\t\t<u-image shape=\"circle\" width='80rpx' height=\"80rpx\" :src=\"item.avatar\"></u-image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-sub margin-left-sm\">\n\t\t\t\t\t\t<view class=\"flex justify-between\">\n\t\t\t\t\t\t\t<view class=\"text-white\">{{item.userName}}</view>\n\t\t\t\t\t\t\t<view class=\"text-grey\">{{item.messageTime}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"flex justify-between\">\n\t\t\t\t\t\t\t<view class=\"text-grey\">{{ item.messageType == 1? item.content : '[图片]'}}</view>\n\t\t\t\t\t\t\t<view v-if=\"item.contentCount\"\n\t\t\t\t\t\t\t\tstyle=\"height: 32rpx;width: 32rpx;border-radius: 100rpx;background-color: red;color: #FFF;text-align: center;\">\n\t\t\t\t\t\t\t\t{{item.contentCount}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<empty v-if=\"!chatList.length && !msgList.length\" content='暂无消息'></empty>\n\t</view>\n</template>\n\n<script>\n\timport empty from '../../components/empty.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\tempty\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 100,\n\t\t\t\tchatList: [],\n\t\t\t\tuserId: '',\n\t\t\t\tmsgList: [],\n\t\t\t\ttime: '',\n\t\t\t\tmessageCount: 0\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getChatList()\n\t\t},\n\t\tonShow() {\n\t\t\tlet that = this\n\t\t\tthat.userId = uni.getStorageSync('userId')\n\t\t\tif (that.userId) {\n\t\t\t\tthat.time = setInterval(function() {\n\t\t\t\t\tthat.getChatList()\n\t\t\t\t\tthat.getMsgList()\n\t\t\t\t\tthat.$nextTick(function() {\n\t\t\t\t\t\tthat.messageCount = uni.getStorageSync('messageCount')\n\t\t\t\t\t})\n\n\t\t\t\t}, 1000)\n\n\t\t\t} else {\n\t\t\t\tthat.chatList = []\n\t\t\t\tthat.msgList = []\n\t\t\t}\n\t\t},\n\t\tonHide() {\n\t\t\tclearInterval(this.time)\n\t\t},\n\t\tmethods: {\n\t\t\tgetChatList() {\n\t\t\t\tthis.$Request.get(\"/app/chat/selectChatConversationPage\", {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.limit\n\t\t\t\t}).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.chatList = res.data.list\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetMsgList() {\n\t\t\t\tthis.$Request.get(\"/app/message/selectMessageByUserIdLimit1\").then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.msgList = res.data.list\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoIM(e) {\n\t\t\t\tlet userId = this.$queue.getData('userId');\n\t\t\t\tif (e.userId == userId) {\n\t\t\t\t\tuserId = e.byUserId\n\t\t\t\t} else {\n\t\t\t\t\tuserId = e.userId\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/msg/im?chatConversationId=' + e.chatConversationId + '&byUserId=' + userId\n\t\t\t\t})\n\t\t\t},\n\t\t\tgoMsg() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/msg/message'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\n\t.bg {\n\t\tbackground: #FFFFFF;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447626804\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}