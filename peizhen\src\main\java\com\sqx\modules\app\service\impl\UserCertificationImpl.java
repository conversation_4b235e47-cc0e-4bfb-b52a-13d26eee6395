package com.sqx.modules.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.DateUtils;
import com.sqx.common.utils.PageUtils;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.dao.UserCertificationDao;
import com.sqx.modules.app.dao.UserDao;
import com.sqx.modules.app.entity.UserCertification;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserCertificationService;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.catalina.User;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
public class UserCertificationImpl extends ServiceImpl<UserCertificationDao, UserCertification> implements UserCertificationService {
    private UserDao userDao;

    @Override
    public Result insert(UserCertification userCertification) {
        //查询身份证是否被绑定
        UserCertification oldUserCertification = baseMapper.selectOne(new QueryWrapper<UserCertification>().eq("id_number", userCertification.getIdNumber()).eq("status", 1));
        if (oldUserCertification != null) {
            return Result.error("身份证号已经被绑定");
        } else {
            //计算出年龄
            Calendar instance = Calendar.getInstance();
            //当前年份
            int nowYearTime = instance.getWeekYear();
            Date date = DateUtils.stringToDate(userCertification.getBirth(), DateUtils.DATE_PATTERN);
            if (date != null) {
                instance.setTime(date);
                int birthTime = instance.getWeekYear();
                userCertification.setAge(nowYearTime - birthTime);
            }
            //查询是否实名过
            UserCertification userCertification1 = baseMapper.selectOne(new QueryWrapper<UserCertification>().eq("user_id", userCertification.getUserId()));
            if (userCertification1 == null) {
                userCertification.setCreateTime(DateUtils.format(new Date()));
                userCertification.setStatus(0);
                baseMapper.insert(userCertification);
            } else {
                userCertification.setStatus(0);
                userCertification.setId(userCertification1.getId());
                userCertification.setUpdateTime(DateUtils.format(new Date()));
                baseMapper.updateById(userCertification);
            }
            return Result.success();
        }
    }

    @Override
    public Result isInsert(Long userId) {
        UserCertification userCertification = baseMapper.selectOne(new QueryWrapper<UserCertification>().eq("user_id", userId).eq("status", 1));
        if (userCertification != null) {
            return Result.success();
        } else {
            return Result.error();
        }
    }

    public static Integer getAgeByCertId(String certId) {
        String birthday = "";
        if (certId.length() == 18) {
            birthday = certId.substring(6, 10) + "/"
                    + certId.substring(10, 12) + "/"
                    + certId.substring(12, 14);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        Date now = new Date();
        Date birth = new Date();
        try {
            birth = sdf.parse(birthday);
        } catch (ParseException e) {
        }
        long intervalMilli = now.getTime() - birth.getTime();
        int age = (int) (intervalMilli / (24 * 60 * 60 * 1000)) / 365;
        return age;
    }


    @Override
    public Result queryCertification(Long page, Long limit, String status, String name, Integer authentication) {

        if (page == null || limit == null) {
            return Result.error("分页参数为空");
        } else {
            IPage<UserCertification> iPage = new Page(page, limit);
            String nname = null;
            if (name != null && !(name.equals(""))) {
                nname = "%" + name + "%";
            }
            IPage iPage1 = baseMapper.queryCertification(iPage, status, nname, authentication);
            List<UserCertification> userCertifications = iPage1.getRecords();
            for (UserCertification userCertification : userCertifications) {
                if (userCertification != null) {
                    //关联用户
                    userCertification.setUserEntity(userDao.selectById(userCertification.getUserId()));
                }
            }
            return Result.success().put("data", new PageUtils(iPage1));
        }
    }

    @Override
    public Result queryUserCertification(IPage iPage, String name, String phone, Integer authentication) {
        String nname = null;
        if (name != null && !(name.equals(""))) {
            nname = "%" + name + "%";
        }
        String nphone = null;
        if (phone != null && !(phone.equals(""))) {
            nphone = "%" + phone + "%";
        }

        return Result.success().put("data", baseMapper.queryUserCertification(iPage, nname, nphone, authentication));
    }

    @Override
    public Result auditorUserCertification(Integer status, Long id, String remek) {
        UserCertification userCertification = baseMapper.selectById(id);
        if (userCertification != null) {
            if (status == 1) {
                UserEntity userEntity = userDao.selectById(userCertification.getUserId());
                userEntity.setIsAuthentication(1);
                userEntity.setShopPhone(userCertification.getPhone());
                userEntity.setIsPromotion(1);
                userDao.updateById(userEntity);
            }
            userCertification.setStatus(status);
            userCertification.setRemek(remek);
            baseMapper.updateById(userCertification);
            return Result.success();
        } else {
            return Result.error("要审核的信息不存在！");
        }
    }

    @Override
    public UserCertification getUserCertification(Long userId) {
        return baseMapper.getUserCertification(userId);
    }

    @Override
    public Result queryInsert(Long userId) {
        return Result.success().put("data", baseMapper.selectUserCertificationByUserId(userId));
    }


}
