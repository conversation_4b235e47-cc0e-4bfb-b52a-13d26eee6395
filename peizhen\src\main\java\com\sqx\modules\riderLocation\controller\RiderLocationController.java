package com.sqx.modules.riderLocation.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.riderLocation.entity.RiderLocation;
import com.sqx.modules.riderLocation.service.RiderLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = {"定时任务-定时任务"})
@RestController
@RequestMapping("/timedtask")
public class RiderLocationController {

    @Autowired
    private RiderLocationService riderLocationService;

    @ApiOperation("查骑手即时位置、距离、预计送达时间")
    @GetMapping(value = "selectRiderLocation")
    public Result selectRiderLocation(Long riderUserId, Double lng, Double lat) {

        return riderLocationService.selectRiderLocation(riderUserId, lng, lat);
    }

    @ApiOperation("管理端查看骑手定位骑手")
    @GetMapping(value = "selectAllRiderLocation")
    public Result selectAllRiderLocation(Integer page, Integer limit, RiderLocation riderLocation) {
        return Result.success().put("data", riderLocationService.selectAllRiderLocation(page, limit, riderLocation));
    }
    @ApiOperation("地图调度")
    @GetMapping(value = "getMapOrderData")
    public Result getMapOrderData(String riderPhone,String hospitalName,String city,String ordersNo){
        return Result.success().put("data", riderLocationService.getMapOrderData(riderPhone, hospitalName, city,ordersNo));
    }

}
