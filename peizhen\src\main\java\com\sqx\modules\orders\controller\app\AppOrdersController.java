package com.sqx.modules.orders.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.annotation.LoginUser;
import com.sqx.modules.app.entity.UserEntity;
import com.sqx.modules.app.service.UserService;
import com.sqx.modules.appointInformation.entity.AppointInformation;
import com.sqx.modules.orders.entity.Orders;
import com.sqx.modules.orders.response.MyOrderResponse;
import com.sqx.modules.orders.service.OrdersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/app/orders")
@Api(value = "app订单信息", tags = {"app订单信息"})
public class AppOrdersController {

    @Autowired
    private OrdersService ordersService;
    @Autowired
    private UserService userService;


    @Login
    @ApiOperation("生成订单")
    @GetMapping("/generateOrder")
    public Result generateOrder(@RequestAttribute Long userId, Long orderTakingUserId, Long couponId, Integer serviceType, Integer orderType, AppointInformation information) {
        return ordersService.generateOrder(userId, couponId, serviceType, orderType, orderTakingUserId, information);
    }

    @Login
    @ApiOperation("修改订单地址")
    @PostMapping("/updateOrdersAddress")
    public Result updateOrdersAddress(Long ordersId, Long addressId) {
        return ordersService.updateOrdersAddress(ordersId, addressId);
    }


    /**
     * 查看我的订单
     */
    @Login
    @ApiOperation("查看我的订单")
    @GetMapping("/selectMyOrder")
    public Result selectMyOrder(@RequestAttribute("userId") Long userId, Integer page, Integer limit, Orders orders) {
        orders.setUserId(userId);
        return Result.success().put("data", ordersService.selectMyOrder(page, limit, orders));

    }

    @Login
    @ApiOperation("查询抢单池")
    @GetMapping("/errandGetOrderList")
    public Result errandGetOrderList( Integer page, Integer limit, Double latitude, Double longitude, Integer createTime, Integer hopeTime, String hospitalName,@RequestAttribute("userId")Long userId) {

        return Result.success().put("data", ordersService.errandGetOrderList(page, limit, latitude, longitude, createTime, hopeTime, hospitalName,userId));
    }


    @Login
    @ApiOperation("发起抢单")
    @PostMapping("/insertMyOrders")
    public Result insertMyOrders(Long ordersId, @RequestAttribute Long userId, String startImg) {
        return ordersService.insertMyOrders(ordersId, userId, startImg);
    }


    /**
     * 生成充值订单
     */
    @Login
    @ApiOperation("生成充值订单")
    @GetMapping("/investOrder")
    public Result investOrder(@RequestAttribute Long userId, @RequestParam Double money, @RequestParam Integer classify) {

        return ordersService.investOrder(userId, money, classify);
    }

    /**
     * 修改订单状态
     */
    @Login
    @ApiOperation("修改订单状态")
    @GetMapping("/cancelOrder")
    public Result cancelOrder(@RequestAttribute Long userId, Long id, String status, String code, String endImg, String startImg,BigDecimal refundMoney) {
        Orders orders = ordersService.getById(id);
        if (userId.equals(orders.getUserId())) {
            code = orders.getCode();
        }
        return ordersService.cancelOrder(id, status, code, endImg, startImg,refundMoney);
    }

    @Login
    @ApiOperation("陪诊师傅取消订单")
    @GetMapping("/rideCancelOrder")
    public Result rideCancelOrder(@RequestAttribute Long userId, Long orderId) {
        return ordersService.rideCancelOrder(userId, orderId);
    }

    @Login
    @ApiOperation("转单")
    @PostMapping("/giveOrdersUser")
    public Result giveOrdersUser(String phone, Long ordersId) {
        UserEntity userEntity = userService.queryByPhone(phone);
        return ordersService.giveOrdersUser(userEntity, ordersId);
    }


    /**
     * 假删除订单
     */
    @Login
    @ApiOperation("假删除订单")
    @GetMapping("/deleteOrder")
    public Result deleteOrder(Long id) {

        return ordersService.deleteOrder(id);
    }

    /**
     * 查看订单详情
     */
    @Login
    @ApiOperation("查看订单详情")
    @GetMapping("/queryOrders")
    public Result queryOrders(Long id) {
        return ordersService.queryOrders(id);
    }


    @Login
    @GetMapping("/selectMyTakeOrders")
    @ApiOperation("查询我的接单")
    public Result selectMyTakeOrders(Integer page, Integer limit, @RequestAttribute Long userId, String status) {
        return Result.success().put("data", ordersService.selectMyTakeOrders(page, limit, userId, status));
    }

    @Login
    @GetMapping("/selectNowDayOrders")
    @ApiOperation("今日订单")
    public Result selectNowDayOrders(Integer page, Integer limit, @RequestAttribute Long userId) {
        return Result.success().put("data", ordersService.selectNowDayOrders(page, limit, userId));
    }


    @Login
    @PostMapping("/payMoney")
    @ApiOperation("零钱去支付")
    public Result payMoney(Long ordersId) {
        return ordersService.payMoney(ordersId);
    }


    @Login
    @GetMapping("/getOrdersRemind")
    @ApiOperation("获取服务订单")
    public Result getOrdersRemind(@RequestAttribute Long userId) {
        Integer ordersRemind = ordersService.getOrdersRemind(userId);
        ordersService.updateOrdersIsRemind(userId);
        return Result.success().put("data", ordersRemind);
    }

    @Login
    @GetMapping("/selectTeamOrdersList")
    @ApiOperation("获取团队订单")
    public Result selectTeamOrdersList(@RequestAttribute Long userId, Integer page, Integer limit, Integer type, Integer status) {
        return ordersService.selectTeamOrdersList(page, limit, userId, type, status);
    }

    @Login
    @GetMapping("/selectTeamUserList")
    @ApiOperation("获取团队列表")
    public Result selectTeamUserList(@LoginUser UserEntity user, Integer page, Integer limit, Integer type) {
        return ordersService.selectTeamUserList(page, limit, user.getInvitationCode(), type, user.getUserId());
    }

    @Login
    @GetMapping("/selectTeamStatistics")
    @ApiOperation("团队统计")
    public Result selectTeamStatistics(@LoginUser UserEntity user) {
        Double oneTeamMoney = ordersService.selectOrdersMoneyCountByUserId(user.getUserId(), 1, null);
        Integer oneTeamCount = ordersService.selectUserCountByInvitationCode(user.getInvitationCode(), 1);
        Double twoTeamMoney = ordersService.selectOrdersMoneyCountByUserId(user.getUserId(), 2, null);
        Integer twoTeamCount = ordersService.selectUserCountByInvitationCode(user.getInvitationCode(), 2);
        Integer teamCount = oneTeamCount + twoTeamCount;
        BigDecimal teamMoney = BigDecimal.valueOf(oneTeamMoney).add(BigDecimal.valueOf(twoTeamMoney));
        Map<String, Object> result = new HashMap<>();
        result.put("teamMoney", oneTeamMoney);
        result.put("teamCount", teamCount);
        result.put("oneTeamMoney", oneTeamMoney);
        result.put("oneTeamCount", oneTeamCount);
        result.put("twoTeamMoney", twoTeamMoney);
        result.put("twoTeamCount", twoTeamCount);
        return Result.success().put("data", result);
    }


    @Login
    @GetMapping("/selectShopStatistics")
    @ApiOperation("团队统计")
    public Result selectshopTeamStatistics(Integer page,Integer limit,@LoginUser UserEntity user) {
        //邀请收益
        BigDecimal shopRateMoney = ordersService.selectShopRateMoney(user.getUserId());
        //邀请骑手人数
        int inviteCount = userService.getInviteCount(user.getInvitationCode());
        //收益详情
       IPage<HashMap<String,Object>>  rateUserList =ordersService.getRateUserList(page,limit,user.getUserId());

       IPage<HashMap<String,Object>>  userList =ordersService.getUserList(page,limit,user.getInvitationCode());
        Map<String, Object> result = new HashMap<>();
        result.put("shopRateMoney", shopRateMoney);
        result.put("inviteCount", inviteCount);
        result.put("rateUserList", rateUserList);

        result.put("userList", userList);

        return Result.success().put("data", result);
    }


    @Login
    @GetMapping("/selectTeamStatisticsByType")
    @ApiOperation("团队统计")
    public Result selectTeamStatisticsByType(@LoginUser UserEntity user, Integer type) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Double dayMoney = ordersService.selectOrdersMoneyCountByUserId(user.getUserId(), type, sdf.format(new Date()));
        sdf = new SimpleDateFormat("yyyy-MM");
        Double monthMoney = ordersService.selectOrdersMoneyCountByUserId(user.getUserId(), type, sdf.format(new Date()));
        sdf = new SimpleDateFormat("yyyy");
        Double yearMoney = ordersService.selectOrdersMoneyCountByUserId(user.getUserId(), type, sdf.format(new Date()));
        Map<String, Object> result = new HashMap<>();
        result.put("dayMoney", dayMoney);
        result.put("monthMoney", monthMoney);
        result.put("yearMoney", yearMoney);
        return Result.success().put("data", result);
    }


    @GetMapping("/selectNewestOrders")
    @ApiOperation("获取最新的下单信息")
    public Result selectNewestOrders() {
        return ordersService.selectNewestOrders();
    }

    @GetMapping("/selectOrdersCount")
    @ApiOperation("查询今日收入与订单数")
    @Login
    public Result selectOrdersCountAndMoney(@RequestAttribute Long userId) {
        return ordersService.selectOrdersCountAndMoney(userId);
    }

}
