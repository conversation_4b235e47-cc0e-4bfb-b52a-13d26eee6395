package com.sqx.modules.taking.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.taking.entity.OrderTaking;
import com.sqx.modules.taking.response.OrderTakingResponse;
import com.sqx.modules.taking.service.OrderTakingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@AllArgsConstructor
@Api("app接单详情")
@RequestMapping("/app/orderTaking")
public class AppOrderTakingController {
    private OrderTakingService orderTakingService;

    /**
     * 按照指定条件查询接单
     *
     * @param bannerid
     * @return
     */
    @CrossOrigin
    @Login
    @ApiOperation("按照指定条件查询接单")
    @GetMapping("/queryTaking")
    public Result queryTaking(@ApiParam("城市")String city,@ApiParam("精度") String longitude, @ApiParam("维度") String latitude, @RequestAttribute Long userId, @ApiParam("模糊") String like, @ApiParam("条件") Integer condition, @ApiParam("bannerid") Long bannerid, @ApiParam("推荐") Long isRecommend, @ApiParam("游戏id") String id, @ApiParam("页") Long page, @ApiParam("条") Long limit, @ApiParam("性别") Long sex, @ApiParam("排序") String by,Integer classify,String salesNum) {
        if (page == null || limit == null) {
            return Result.error("分页的条件为空！");
        } else {
            IPage<OrderTakingResponse> iPage = new Page<>(page, limit);
            return orderTakingService.queryTaking(city,longitude, latitude, userId, like, condition, bannerid, isRecommend, id, iPage, sex, by,classify,salesNum);
        }
    }

    @CrossOrigin
    @ApiOperation("按照指定条件查询接单")
    @GetMapping("/queryTakings")
    public Result queryTakings(@ApiParam("城市")String city,@ApiParam("精度") String longitude, @ApiParam("维度") String latitude, Long userId, @ApiParam("模糊") String like, @ApiParam("条件") Integer condition, @ApiParam("bannerid") Long bannerid, @ApiParam("推荐") Long isRecommend, @ApiParam("游戏id") String id, @ApiParam("页") Long page, @ApiParam("条") Long limit, @ApiParam("性别") Long sex, @ApiParam("排序") String by,Integer classify,String salesNum) {
        if (page == null || limit == null) {
            return Result.error("分页的条件为空！");
        } else {
            IPage<OrderTakingResponse> iPage = new Page<>(page, limit);
            return orderTakingService.queryTaking(city,longitude, latitude, userId, like, condition, bannerid, isRecommend, id, iPage, sex, by,classify,salesNum);
        }
    }



    /**
     * 查询低价系列的接单
     */
    @ApiOperation("查询低价系列的接单")
    @GetMapping("/queryLowTaking")
    public Result queryLowTaking(Integer page, Integer limit,String city,@ApiParam("精度") String longitude, @ApiParam("维度") String latitude, Long userId, @ApiParam("模糊") String like, @ApiParam("条件") Integer condition, @ApiParam("bannerid") Long bannerid, @ApiParam("推荐") Long isRecommend, @ApiParam("游戏id") String id, @ApiParam("性别") Long sex, @ApiParam("排序") String by,Integer classify,String salesNum) {
        return orderTakingService.queryLowTaking(page, limit, city, longitude, latitude, userId, like, condition, bannerid, isRecommend, id, sex, by, classify, salesNum);
    }

    /**
     * 查询接单详情
     */
    @ApiOperation("查询接单详情")
    @CrossOrigin
    @GetMapping("/queryTakingDetails")
    @Login
    public Result queryTakingDetails(@RequestParam Long id, @RequestAttribute Long userId,String longitude,String latitude) {
        return orderTakingService.queryTakingDetails(id, userId,longitude,latitude);
    }


    @ApiOperation("查询接单详情")
    @CrossOrigin
    @GetMapping("/queryTakingDetailss")
    public Result queryTakingDetails(@RequestParam Long id,String longitude,String latitude) {
        return orderTakingService.queryTakingDetails(id,longitude,latitude);
    }

    /**
     * 发布接单
     */
    @ApiOperation("发布接单")
    @GetMapping("/insertOrderTaking")
    @Login
    public Result insertOrderTaking(String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money, String voiceIntroduce, String homepageImg, @RequestAttribute Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,String detailsImg,Integer authentication,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard) {
        return orderTakingService.insertOrderTaking(gameId, myLevel, orderLevel, orderTakingTime, orderTakingArea, money,null, voiceIntroduce, homepageImg, userId, city, longitude, latitude,sec,classify,unit,detailsImg,authentication,minNum,region,detailadd,serviceName,carType,safeguard);
    }

    /**
     * 查看我的发布
     */
    @Login
    @ApiOperation("查看我的发布")
    @GetMapping("/selectMyRelease")
    public Result selectMyRelease(@RequestAttribute Long userId, Long page, Long limit, @ApiParam("状态") String status) {
        return orderTakingService.selectMyRelease(userId, page, limit, status);
    }


    @ApiOperation("查看我的发布")
    @GetMapping("/selectMyReleases")
    public Result selectMyReleases(Long userId, Long page, Long limit, @ApiParam("状态") String status) {
        return orderTakingService.selectMyRelease(userId, page, limit, status);
    }

    /**
     * 修改发布状态
     */
    @Login
    @ApiOperation("修改发布状态")
    @GetMapping("/updateTakingStatus")
    public Result updateTakingStatus(Long id, @ApiParam("状态") Integer status,String content) {
        return orderTakingService.updateTakingStatus(id, status,content);
    }

    /**
     * 删除发布接单
     */
    @ApiOperation("删除发布接单")
    @GetMapping("/deleteTaking")
    public Result deleteTaking(Long id) {

        return orderTakingService.deleteTaking(id);
    }

    /**
     * 查询接单想详情
     */
    @ApiOperation("查询接单详情")
    @GetMapping("/queryTakingOrder")
    public Result queryTakingOrder(Long id) {

        return orderTakingService.queryTakingOrder(id);
    }

    /**
     * 重新编辑
     */
    @ApiOperation("重新编辑")
    @GetMapping("/updateTakingOrder")
    @Login
    public Result updateTakingOrder(Long id, String gameId, String myLevel, String orderLevel, String orderTakingTime, String orderTakingArea, BigDecimal money, String voiceIntroduce, String homepageImg, @RequestAttribute Long userId, String city, String longitude, String latitude,Integer sec,Integer classify,String unit,Integer authentication,String detailsImg,Integer minNum,String region,String detailadd,String serviceName,String carType,String safeguard) {
        return orderTakingService.updateTakingOrder(id, gameId, myLevel, orderLevel, orderTakingTime, orderTakingArea, money, voiceIntroduce, homepageImg, userId, city, longitude, latitude,sec,classify,unit,authentication,detailsImg,minNum,region,detailadd,serviceName,carType,safeguard);
    }



    /**
     * 查看我的接单
     */
    @ApiOperation("查看我的接单")
    @GetMapping("/queryMyTakingOrder")
    @Login
    public Result queryMyTakingOrder(@RequestAttribute Long userId, Long page, Long limit,Long status) {
        return orderTakingService.queryMyTakingOrder(userId,page,limit,status);
    }


    @GetMapping("/selectShopData")
    @ApiOperation("商户首页数据统计")
    @Login
    public Result selectShopData(@RequestAttribute Long userId,String startTime,String endTime){
        return orderTakingService.selectShopData(userId, startTime, endTime);
    }



}
