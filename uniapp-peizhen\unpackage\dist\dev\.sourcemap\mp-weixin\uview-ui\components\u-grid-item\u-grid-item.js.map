{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-grid-item/u-grid-item.vue?bc7e", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-grid-item/u-grid-item.vue?ff12", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-grid-item/u-grid-item.vue?41c0", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-grid-item/u-grid-item.vue?07f6", "uni-app:///uview-ui/components/u-grid-item/u-grid-item.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-grid-item/u-grid-item.vue?3d30", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/uview-ui/components/u-grid-item/u-grid-item.vue?255f"], "names": ["name", "props", "bgColor", "type", "default", "index", "customStyle", "padding", "data", "parentData", "hoverClass", "col", "border", "created", "computed", "width", "methods", "updateParentData", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,2rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACa7wB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,gBAUA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;UACAG;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAg7C,CAAgB,0vCAAG,EAAC,C;;;;;;;;;;;ACAp8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-grid-item/u-grid-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-grid-item.vue?vue&type=template&id=54899586&scoped=true&\"\nvar renderjs\nimport script from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-grid-item.vue?vue&type=style&index=0&id=54899586&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"54899586\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-grid-item/u-grid-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=template&id=54899586&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.customStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-grid-item\" :hover-class=\"parentData.hoverClass\"\n\t :hover-stay-time=\"200\" @tap=\"click\" :style=\"{\n\t\t\tbackground: bgColor,\n\t\t\twidth: width,\n\t\t}\">\n\t\t<view class=\"u-grid-item-box\" :style=\"[customStyle]\" :class=\"[parentData.border ? 'u-border-right u-border-bottom' : '']\">\n\t\t\t<slot />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * gridItem 提示\n\t * @description 宫格组件一般用于同时展示多个同类项目的场景，可以给宫格的项目设置徽标组件(badge)，或者图标等，也可以扩展为左右滑动的轮播形式。搭配u-grid使用\n\t * @tutorial https://www.uviewui.com/components/grid.html\n\t * @property {String} bg-color 宫格的背景颜色（默认#ffffff）\n\t * @property {String Number} index 点击宫格时，返回的值\n\t * @property {Object} custom-style 自定义样式，对象形式\n\t * @event {Function} click 点击宫格触发\n\t * @example <u-grid-item></u-grid-item>\n\t */\n\texport default {\n\t\tname: \"u-grid-item\",\n\t\tprops: {\n\t\t\t// 背景颜色\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#ffffff'\n\t\t\t},\n\t\t\t// 点击时返回的index\n\t\t\tindex: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 自定义样式，对象形式\n\t\t\tcustomStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault() {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tpadding: '10rpx 0'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tparentData: {\n\t\t\t\t\thoverClass: '', // 按下去的时候，是否显示背景灰色\n\t\t\t\t\tcol: 3, // 父组件划分的宫格数\n\t\t\t\t\tborder: true, // 是否显示边框，根据父组件决定\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tcreated() {\n\t\t\t// 父组件的实例\n\t\t\tthis.updateParentData();\n\t\t\t// this.parent在updateParentData()中定义\n\t\t\tthis.parent.children.push(this);\n\t\t},\n\t\tcomputed: {\n\t\t\t// 每个grid-item的宽度\n\t\t\twidth() {\n\t\t\t\treturn 100 / Number(this.parentData.col) + '%';\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取父组件的参数\n\t\t\tupdateParentData() {\n\t\t\t\t// 此方法写在mixin中\n\t\t\t\tthis.getParentData('u-grid');\n\t\t\t},\n\t\t\tclick() {\n\t\t\t\tthis.$emit('click', this.index);\n\t\t\t\tthis.parent && this.parent.click(this.index);\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t@import \"../../libs/css/style.components.scss\";\n\t\n\t.u-grid-item {\n\t\tbox-sizing: border-box;\n\t\tbackground: #fff;\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\t\n\t\t/* #ifdef MP */\n\t\tposition: relative;\n\t\tfloat: left;\n\t\t/* #endif */\n\t}\n\n\t.u-grid-item-hover {\n\t\tbackground: #f7f7f7 !important;\n\t}\n\n\t.u-grid-marker-box {\n\t\tposition: absolute;\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: inline-flex;\t\t\n\t\t/* #endif */\n\t\tline-height: 0;\n\t}\n\n\t.u-grid-marker-wrap {\n\t\tposition: absolute;\n\t}\n\n\t.u-grid-item-box {\n\t\t// padding: 30rpx 0;\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex-direction: column;\n\t\tflex: 1;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=style&index=0&id=54899586&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=style&index=0&id=54899586&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627697\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}