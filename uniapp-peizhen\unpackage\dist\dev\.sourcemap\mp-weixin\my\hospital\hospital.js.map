{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/hospital/hospital.vue?8fd9", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/hospital/hospital.vue?466c", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/hospital/hospital.vue?7155", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/hospital/hospital.vue?ca43", "uni-app:///my/hospital/hospital.vue", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/hospital/hospital.vue?dae1", "webpack:///C:/Users/<USER>/Desktop/code/peizhen/uniapp-peizhen/my/hospital/hospital.vue?a9bf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "hospitalId", "hospitalInfo", "onLoad", "methods", "getyiyuanInfo", "uni", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyC3vB;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACAJ;MACA;MACA;QACA;UACA;QACA;UACAK;YACAC;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAA03C,CAAgB,+tCAAG,EAAC,C;;;;;;;;;;;ACA94C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "my/hospital/hospital.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './my/hospital/hospital.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./hospital.vue?vue&type=template&id=3f88914c&\"\nvar renderjs\nimport script from \"./hospital.vue?vue&type=script&lang=js&\"\nexport * from \"./hospital.vue?vue&type=script&lang=js&\"\nimport style0 from \"./hospital.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"my/hospital/hospital.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hospital.vue?vue&type=template&id=3f88914c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hospital.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hospital.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"header flex justify-center\">\n\t\t\t<view class=\"header-box flex align-center\">\n\t\t\t\t<view class=\"header-box-l\">\n\t\t\t\t\t<image :src=\"hospitalInfo.hospitalImg\" mode=\"\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-box-r\">\n\t\t\t\t\t<view class=\"header-box-r-t\">\n\t\t\t\t\t\t{{hospitalInfo.hospitalName}}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"header-box-r-b\">\n\t\t\t\t\t\t<text>{{hospitalInfo.hospitalLevel}}</text>\n\t\t\t\t\t\t<text>{{hospitalInfo.hospitalType}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 重点科室 -->\n\t\t<view class=\"ks flex justify-center\">\n\t\t\t<view class=\"ks-box\">\n\t\t\t\t<text>重点科室：</text>\n\t\t\t\t<text>\n\t\t\t\t\t{{hospitalInfo.departmentDetails}}\n\t\t\t\t</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"jianjie flex justify-center\">\n\t\t\t<view class=\"jianjie-box\">\n\t\t\t\t<view class=\"jianjie-box-title\">\n\t\t\t\t\t医院简介\n\t\t\t\t</view>\n\t\t\t\t<view class=\"jianjie-box-in\">\n\t\t\t\t\t{{hospitalInfo.hospitalDetails}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\thospitalId: '',\n\t\t\t\thospitalInfo: {}\n\t\t\t};\n\t\t},\n\t\tonLoad(option) {\n\t\t\tthis.hospitalId = option.hospitalId\n\t\t\tthis.getyiyuanInfo()\n\t\t},\n\t\tmethods: {\n\t\t\t//医院详情\n\t\t\tgetyiyuanInfo() {\n\t\t\t\tlet data = {\n\t\t\t\t\thospitalId: this.hospitalId\n\t\t\t\t}\n\t\t\t\tthis.$Request.getT('/app/hospital/getHospitalInfo', data).then(res => {\n\t\t\t\t\tif (res.code == 0) {\n\t\t\t\t\t\tthis.hospitalInfo = res.data\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.header{\n\t\twidth: 100%;\n\t\theight: 200rpx;\n\t\t.header-box{\n\t\t\twidth: 686rpx;\n\t\t\theight: 100%;\n\t\t\tborder-radius: 16rpx;\n\t\t\t// background-color: red;\n\t\t\t.header-box-l{\n\t\t\t\twidth: 240rpx;\n\t\t\t\theight: 130rpx;\n\t\t\t\timage{\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t\tborder-radius: 16rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t\t.header-box-r{\n\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t.header-box-r-t{\n\t\t\t\t\tfont-size: 38rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t\t.header-box-r-b{\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\ttext:nth-of-type(1){\n\t\t\t\t\t\tcolor: red;\n\t\t\t\t\t}\n\t\t\t\t\ttext:nth-of-type(2){\n\t\t\t\t\t\tcolor: #999999;\n\t\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t.ks{\n\t\twidth: 100%;\n\t\tmargin-top: 20rpx;\n\t\t.ks-box{\n\t\t\twidth: 686rpx;\n\t\t\theight: 100%;\n\t\t\tfont-size: 26rpx;\n\t\t\ttext:nth-of-type(1){\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t\ttext:nth-of-type(2){\n\t\t\t\tcolor: #999999;\n\t\t\t\tletter-spacing: 3px;\n\t\t\t}\n\t\t}\n\t}\n\t.jianjie{\n\t\twidth: 100%;\n\t\theight: auto;\n\t\tmargin-top: 30rpx;\n\t\t.jianjie-box{\n\t\t\twidth: 686rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\t.jianjie-box-title{\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t\t.jianjie-box-in{\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tcolor: #999999;\n\t\t\t\tletter-spacing: 3px;\n\t\t\t}\n\t\t}\n\t}\n\n\n</style>\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hospital.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hospital.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754447627288\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}