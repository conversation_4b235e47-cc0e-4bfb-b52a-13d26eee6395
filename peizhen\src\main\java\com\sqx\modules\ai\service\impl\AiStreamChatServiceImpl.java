package com.sqx.modules.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sqx.common.utils.Result;
import com.sqx.modules.ai.dao.AiMessageDao;
import com.sqx.modules.ai.entity.AiConversation;
import com.sqx.modules.ai.entity.AiMessage;
import com.sqx.modules.ai.entity.AiModelConfig;
import com.sqx.modules.ai.service.AiConversationService;
import com.sqx.modules.ai.service.AiModelConfigService;
import com.sqx.modules.ai.service.AiStreamChatService;
import com.sqx.modules.ai.utils.OpenAiApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * AI流式聊天服务实现
 * 正确的流式输出逻辑：
 * 1. 前端发送流式请求，后端返回taskId
 * 2. 后端异步调用大模型流式API，实时更新内存中的内容
 * 3. 前端轮询获取实时内容
 * 4. 流式完成后，保存最终结果到ai_message表
 */
@Slf4j
@Service
public class AiStreamChatServiceImpl implements AiStreamChatService {

    @Autowired
    private AiMessageDao messageDao;

    @Autowired
    private AiConversationService conversationService;

    @Autowired
    private AiModelConfigService modelConfigService;

    @Autowired
    private OpenAiApiClient apiClient;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 用于存储正在进行的流式任务和实时内容
    private final Map<String, StreamTaskInfo> activeStreamTasks = new ConcurrentHashMap<>();

    /**
     * 流式任务信息
     */
    public static class StreamTaskInfo {
        private String taskId;
        private Long userId;
        private Long conversationId;
        private Long messageId;
        private String currentContent;
        private boolean isCompleted;
        private String status;
        private String errorMessage;
        private long startTime;
        private long lastUpdateTime; // 最后更新时间，用于判断内容是否有变化

        public StreamTaskInfo(String taskId, Long userId, Long conversationId, Long messageId) {
            this.taskId = taskId;
            this.userId = userId;
            this.conversationId = conversationId;
            this.messageId = messageId;
            this.currentContent = "";
            this.isCompleted = false;
            this.status = "processing";
            this.startTime = System.currentTimeMillis();
            this.lastUpdateTime = System.currentTimeMillis();
        }

        // getter和setter方法
        public String getTaskId() { return taskId; }
        public Long getUserId() { return userId; }
        public Long getConversationId() { return conversationId; }
        public Long getMessageId() { return messageId; }
        public void setMessageId(Long messageId) { this.messageId = messageId; }
        public String getCurrentContent() { return currentContent; }
        public void setCurrentContent(String currentContent) {
            this.currentContent = currentContent;
            this.lastUpdateTime = System.currentTimeMillis();
        }
        public long getLastUpdateTime() { return lastUpdateTime; }
        public boolean isCompleted() { return isCompleted; }
        public void setCompleted(boolean completed) { isCompleted = completed; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public long getStartTime() { return startTime; }
    }



    @Override
    public Result sendMessageStream(Long userId, Long conversationId, String message, String modelCode) {
        try {
            // 验证对话是否存在且属于当前用户
            AiConversation conversation = conversationService.getOne(
                new QueryWrapper<AiConversation>()
                    .eq("id", conversationId)
                    .eq("user_id", userId)
                    .eq("status", 1)
            );

            if (conversation == null) {
                return Result.error("对话不存在或无权限访问");
            }

            // 如果指定了模型代码，验证并更新对话使用的模型
            if (StringUtils.isNotBlank(modelCode) && !modelCode.equals(conversation.getModelCode())) {
                Result switchResult = conversationService.switchModel(conversationId, userId, modelCode);
                if (!"success".equals(switchResult.get("msg"))) {
                    return switchResult;
                }
                conversation.setModelCode(modelCode);
            }

            // 获取模型配置
            AiModelConfig modelConfig = modelConfigService.getByModelCode(conversation.getModelCode());
            if (modelConfig == null) {
                return Result.error("AI模型配置不存在或未启用");
            }

            // 保存用户消息
            AiMessage userMessage = new AiMessage();
            userMessage.setConversationId(conversationId);
            userMessage.setUserId(userId);
            userMessage.setRole("user");
            userMessage.setContent(message);
            userMessage.setModelCode(conversation.getModelCode());
            userMessage.setCreateTime(dateFormat.format(new Date()));
            messageDao.insert(userMessage);

            // 生成任务ID
            String taskId = "stream_" + System.currentTimeMillis() + "_" + userId;

            // 创建流式任务信息（暂时不创建assistant消息记录）
            StreamTaskInfo taskInfo = new StreamTaskInfo(taskId, userId, conversationId, null);
            activeStreamTasks.put(taskId, taskInfo);
            log.info("创建流式任务，taskId: {}, userId: {}, conversationId: {}, 当前活跃任务数: {}",
                taskId, userId, conversationId, activeStreamTasks.size());
            log.info("任务创建后验证，任务是否存在: {}", activeStreamTasks.containsKey(taskId));

            // 异步处理AI回复（确保真正异步执行）
            CompletableFuture.runAsync(() -> {
                processStreamResponseAsync(taskId, modelConfig);
            });

            // 返回任务ID
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("userMessage", userMessage);

            return Result.success().put("data", result);

        } catch (Exception e) {
            log.error("启动流式消息失败", e);
            return Result.error("启动流式消息失败: " + e.getMessage());
        }
    }

    public void processStreamResponseAsync(String taskId, AiModelConfig modelConfig) {
        try {
            log.info("开始处理流式响应，任务ID: {}, 当前活跃任务数: {}", taskId, activeStreamTasks.size());

            StreamTaskInfo taskInfo = activeStreamTasks.get(taskId);
            if (taskInfo == null) {
                log.error("找不到流式任务信息: {}, 当前活跃任务: {}", taskId, activeStreamTasks.keySet());
                return;
            }

            log.info("找到任务信息，开始调用真实流式API");
            log.info("模型配置: modelCode={}, apiUrl={}, apiKey前10位={}",
                modelConfig.getModelCode(), modelConfig.getApiUrl(),
                modelConfig.getApiKey() != null ? modelConfig.getApiKey().substring(0, Math.min(10, modelConfig.getApiKey().length())) + "..." : "null");

            // 获取对话历史（最近20条消息）
            List<AiMessage> historyMessages = messageDao.selectConversationMessages(taskInfo.getConversationId(), 20);

            // 构建API请求消息列表，过滤掉空内容的消息
            List<OpenAiApiClient.ChatMessage> apiMessages = historyMessages.stream()
                .filter(msg -> msg.getContent() != null && !msg.getContent().trim().isEmpty())
                .map(msg -> new OpenAiApiClient.ChatMessage(msg.getRole(), msg.getContent()))
                .collect(Collectors.toList());

            log.info("构建API请求，历史消息数: {}, 有效消息数: {}", historyMessages.size(), apiMessages.size());

            // 详细打印每条消息
            for (int i = 0; i < apiMessages.size(); i++) {
                OpenAiApiClient.ChatMessage msg = apiMessages.get(i);
                log.info("消息 {}: role={}, content长度={}, 内容前50字符: {}",
                    i, msg.getRole(), msg.getContent().length(),
                    msg.getContent().length() > 50 ? msg.getContent().substring(0, 50) + "..." : msg.getContent());
            }

            // 最终验证消息格式
            boolean hasValidMessages = apiMessages.stream().allMatch(msg ->
                msg.getRole() != null && !msg.getRole().trim().isEmpty() &&
                msg.getContent() != null && !msg.getContent().trim().isEmpty()
            );

            if (!hasValidMessages) {
                log.error("API消息验证失败，存在无效消息");
                handleStreamError(taskId, "API消息格式验证失败");
                return;
            }

            if (apiMessages.isEmpty()) {
                log.error("API消息列表为空");
                handleStreamError(taskId, "没有有效的对话消息");
                return;
            }

            log.info("消息验证通过，开始调用流式API");

            // 调用流式API
            apiClient.sendStreamChatRequest(modelConfig, apiMessages, new OpenAiApiClient.StreamCallback() {
                private long startTime = System.currentTimeMillis();

                @Override
                public void onChunk(String chunk, String accumulated) {
                    try {
                        // 检查任务是否被取消
                        StreamTaskInfo currentTask = activeStreamTasks.get(taskId);
                        if (currentTask == null) {
                            log.info("流式任务被取消: {}", taskId);
                            return;
                        }

                        // 实时更新任务信息中的当前内容
                        currentTask.setCurrentContent(accumulated);
                        log.info("实时更新流式内容: 分块='{}', 累积长度={}, 任务ID={}",
                            chunk.length() > 20 ? chunk.substring(0, 20) + "..." : chunk,
                            accumulated.length(), taskId);

                    } catch (Exception e) {
                        log.error("处理流式分块失败", e);
                    }
                }

                @Override
                public void onComplete(String fullContent) {
                    try {
                        long responseTime = System.currentTimeMillis() - startTime;
                        StreamTaskInfo currentTask = activeStreamTasks.get(taskId);
                        if (currentTask == null) {
                            log.warn("任务已被移除: {}", taskId);
                            return;
                        }

                        // 创建AI消息记录（在流式完成时才创建）
                        AiMessage assistantMessage = new AiMessage();
                        assistantMessage.setConversationId(currentTask.getConversationId());
                        assistantMessage.setUserId(currentTask.getUserId());
                        assistantMessage.setRole("assistant");
                        assistantMessage.setContent(fullContent);
                        assistantMessage.setModelCode(modelConfig.getModelCode());
                        assistantMessage.setResponseTime((int) responseTime);
                        assistantMessage.setCreateTime(dateFormat.format(new Date()));
                        messageDao.insert(assistantMessage);

                        // 更新任务信息中的消息ID
                        currentTask.setMessageId(assistantMessage.getId());

                        // 标记任务完成
                        currentTask.setCurrentContent(fullContent);
                        currentTask.setCompleted(true);
                        currentTask.setStatus("completed");

                        // 更新对话统计
                        conversationService.updateConversationStats(currentTask.getConversationId());

                        log.info("流式输出完成，任务ID: {}, 总字符数: {}", taskId, fullContent.length());

                    } catch (Exception e) {
                        log.error("完成流式输出失败", e);
                        handleStreamError(taskId, "完成流式输出失败: " + e.getMessage());
                    }
                    // 不立即移除任务，保留给前端轮询获取完成状态
                    log.info("任务完成但保留在内存中，taskId: {}, 当前活跃任务数: {}", taskId, activeStreamTasks.size());
                }

                @Override
                public void onError(String error) {
                    log.error("流式API调用失败: {}", error);
                    handleStreamError(taskId, "流式API调用失败: " + error);
                }
            });

        } catch (Exception e) {
            log.error("处理流式响应失败", e);
            handleStreamError(taskId, "处理流式响应失败: " + e.getMessage());
        }
    }



    /**
     * 处理流式错误
     */
    private void handleStreamError(String taskId, String errorMessage) {
        log.error("处理流式错误，taskId: {}, 错误: {}", taskId, errorMessage);
        StreamTaskInfo taskInfo = activeStreamTasks.get(taskId);
        if (taskInfo != null) {
            taskInfo.setStatus("error");
            taskInfo.setErrorMessage(errorMessage);
            taskInfo.setCompleted(true);
            taskInfo.setCurrentContent("错误: " + errorMessage);
            log.info("标记任务为错误状态，保留在内存中: {}", taskId);
            // 保留任务在内存中，让前端能获取到错误信息
        } else {
            log.warn("要处理错误的任务不存在: {}", taskId);
        }
    }





    @Override
    public Result getStreamChunks(Long userId, String taskId, Integer fromIndex) {
        try {
            log.info("获取流式分块，taskId: {}, userId: {}, 当前活跃任务数: {}", taskId, userId, activeStreamTasks.size());
            log.info("当前活跃任务列表: {}", activeStreamTasks.keySet());

            // 获取任务信息
            StreamTaskInfo taskInfo = activeStreamTasks.get(taskId);
            if (taskInfo == null) {
                log.warn("流式任务不存在，taskId: {}", taskId);
                return Result.error("流式任务不存在或已完成");
            }

            // 验证用户权限
            if (!taskInfo.getUserId().equals(userId)) {
                return Result.error("无权限访问此任务");
            }

            // 构造当前内容的响应
            List<Map<String, Object>> chunks = new ArrayList<>();

            // 总是返回当前状态，让前端能实时获取
            Map<String, Object> chunk = new HashMap<>();
            chunk.put("chunkIndex", taskInfo.getCurrentContent().length()); // 使用内容长度作为索引
            chunk.put("chunkContent", taskInfo.getCurrentContent()); // 返回完整内容
            chunk.put("accumulatedContent", taskInfo.getCurrentContent());
            chunk.put("isLast", taskInfo.isCompleted());
            chunk.put("status", taskInfo.getStatus());
            chunk.put("lastUpdateTime", taskInfo.getLastUpdateTime());

            chunks.add(chunk);
            log.info("返回流式分块，内容长度: {}, 是否完成: {}, 最后更新: {}",
                taskInfo.getCurrentContent().length(), taskInfo.isCompleted(), taskInfo.getLastUpdateTime());

            Map<String, Object> result = new HashMap<>();
            result.put("chunks", chunks);
            result.put("hasMore", !taskInfo.isCompleted());

            return Result.success().put("data", result);

        } catch (Exception e) {
            log.error("获取流式分块失败", e);
            return Result.error("获取流式分块失败: " + e.getMessage());
        }
    }

    @Override
    public Result getStreamStatus(Long userId, String taskId) {
        try {
            StreamTaskInfo taskInfo = activeStreamTasks.get(taskId);
            if (taskInfo == null) {
                return Result.error("流式任务不存在");
            }

            // 验证用户权限
            if (!taskInfo.getUserId().equals(userId)) {
                return Result.error("无权限访问此任务");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("status", taskInfo.getStatus());
            result.put("isCompleted", taskInfo.isCompleted());
            result.put("currentLength", taskInfo.getCurrentContent().length());
            result.put("errorMessage", taskInfo.getErrorMessage());

            return Result.success().put("data", result);

        } catch (Exception e) {
            log.error("获取流式状态失败", e);
            return Result.error("获取流式状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result cancelStreamTask(Long userId, String taskId) {
        try {
            StreamTaskInfo taskInfo = activeStreamTasks.get(taskId);
            if (taskInfo == null) {
                return Result.error("流式任务不存在或已完成");
            }

            // 验证用户权限
            if (!taskInfo.getUserId().equals(userId)) {
                return Result.error("无权限访问此任务");
            }

            // 标记任务为取消状态，但不立即移除
            taskInfo.setStatus("cancelled");
            taskInfo.setCompleted(true);
            taskInfo.setCurrentContent("任务已取消");

            log.info("流式任务已取消: {}", taskId);
            return Result.success("流式任务已取消");

        } catch (Exception e) {
            log.error("取消流式任务失败", e);
            return Result.error("取消流式任务失败: " + e.getMessage());
        }
    }



    /**
     * 清理已完成的任务（可以定期调用）
     */
    public void cleanupCompletedTasks() {
        long currentTime = System.currentTimeMillis();
        int beforeCount = activeStreamTasks.size();
        activeStreamTasks.entrySet().removeIf(entry -> {
            StreamTaskInfo taskInfo = entry.getValue();
            // 清理超过5分钟的已完成任务
            return taskInfo.isCompleted() && (currentTime - taskInfo.getStartTime()) > 300000;
        });
        int afterCount = activeStreamTasks.size();
        if (beforeCount != afterCount) {
            log.info("清理已完成任务，清理前: {}, 清理后: {}", beforeCount, afterCount);
        }
    }

    /**
     * 手动清理指定任务
     */
    public Result cleanupTask(Long userId, String taskId) {
        try {
            StreamTaskInfo taskInfo = activeStreamTasks.get(taskId);
            if (taskInfo == null) {
                return Result.success("任务不存在或已清理");
            }

            // 验证用户权限
            if (!taskInfo.getUserId().equals(userId)) {
                return Result.error("无权限访问此任务");
            }

            // 只清理已完成的任务
            if (taskInfo.isCompleted()) {
                activeStreamTasks.remove(taskId);
                log.info("手动清理任务: {}", taskId);
                return Result.success("任务已清理");
            } else {
                return Result.error("任务尚未完成，无法清理");
            }

        } catch (Exception e) {
            log.error("清理任务失败", e);
            return Result.error("清理任务失败: " + e.getMessage());
        }
    }
}
