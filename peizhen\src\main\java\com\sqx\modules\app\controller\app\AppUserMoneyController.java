package com.sqx.modules.app.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.app.annotation.Login;
import com.sqx.modules.app.entity.UserMoneyDetails;
import com.sqx.modules.app.service.UserMoneyDetailsService;
import com.sqx.modules.app.service.UserMoneyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequestMapping("/app/userMoney")
@Api("app 钱包 ")
public class AppUserMoneyController {

    @Autowired
    private UserMoneyService userMoneyService;
    @Autowired
    private UserMoneyDetailsService userMoneyDetailsService;


    @Login
    @ApiOperation("钱包明细")
    @GetMapping("/queryUserMoneyDetails")
    public Result queryUserMoneyDetails(Integer page, Integer limit,@RequestAttribute Long userId,Integer classify,Integer type) {

        return userMoneyDetailsService.queryUserMoneyDetails(page, limit, userId,classify,type);
    }


    @GetMapping("/selectMyMoney")
    @Login
    @ApiOperation("我的钱包余额")
    public Result selectMyMoney(@RequestAttribute Long userId) {
        return Result.success().put("data", userMoneyService.selectUserMoneyByUserId(userId));
    }

    /**
     * 我的收益
     */
    @GetMapping("/selectMyProfit")
    @Login
    @ApiOperation("我的收益")
    public Result selectMyProfit(@RequestAttribute Long userId) {
        return Result.success().put("data", userMoneyService.selectMyProfit(userId));
    }

    /**
     * 付款接单
     */
    @GetMapping("/payTakingOrder")
    @Login
    @ApiOperation("付款接单或会员")
    public Result payTakingOrder(@RequestAttribute Long userId, Long orderId) {
        return userMoneyService.payTakingOrder(userId, orderId);
    }



    /**
     * 余额明细
     */
    @GetMapping("/balanceDetailed")
    @Login
    @ApiOperation("余额明细")
    public Result balanceDetailed(@RequestAttribute Long userId,Integer classify, Long page, Long limit) {
        Page<UserMoneyDetails> pages = new Page<>(page, limit);
        return Result.success().put("data", userMoneyService.balanceDetailed(userId,classify, pages));
    }

    @Login
    @ApiOperation("获取保证金明细")
    @GetMapping("/getBondDetails")
    public Result getBondDetails(@RequestAttribute Long userId,Integer page, Integer limit,UserMoneyDetails userMoneyDetails) {

        return Result.success().put("data", userMoneyDetailsService.getBondDetails(userId,page, limit, userMoneyDetails));
    }

    @Login
    @PostMapping("/paySafetyMoney")
    @ApiOperation("缴纳保证金")
    public Result paySafetyMoney(@RequestAttribute Long userId){
        return userMoneyService.paySafetyMoney(userId);
    }

    @Login
    @PostMapping("/refundSafetMoney")
    @ApiOperation("退款保证金")
    public Result refundSafetMoney(@RequestAttribute Long userId){
        return userMoneyService.refundSafetMoney(userId);
    }

    @Login
    @GetMapping("/getAgentProfitList")
    @ApiOperation("获取代理商收益明细")
    public Result getAgentProfitList(@RequestAttribute("userId")Long userId,Integer page,Integer limit){
        return Result.success().put("data", userMoneyDetailsService.getAgentProfitList(userId,page,limit));
    }

}
